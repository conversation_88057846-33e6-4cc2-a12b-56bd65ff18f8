INSERT INTO `erp_configuration`.`erp_configuration_method`(`id`, `system_code`, `method_code`, `method_type`, `back_method_code`, `method_name`, `data_type`, `call_type`, `type`, `type_index`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (66, 'GLP', 'MC66', '3', 'MC73', '新增或修改货品', 1, 1, 1, '0', 1608867356808, 1608867356731, 1, 1, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_method`(`id`, `system_code`, `method_code`, `method_type`, `back_method_code`, `method_name`, `data_type`, `call_type`, `type`, `type_index`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (67, 'ERP', 'MC67', '9', '0', '库存查询返回', 1, 1, 1, '67', 1608867356808, 1608867356731, 1, 1, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_method`(`id`, `system_code`, `method_code`, `method_type`, `back_method_code`, `method_name`, `data_type`, `call_type`, `type`, `type_index`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (68, 'GLP', 'MC68', '30', 'MC74', '库存查询', 1, 1, 1, '0', 1608867356808, 1608867356731, 1, 1, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_method`(`id`, `system_code`, `method_code`, `method_type`, `back_method_code`, `method_name`, `data_type`, `call_type`, `type`, `type_index`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (69, 'GLP', 'MC69', '4', 'MC73', '同步入库单', 1, 1, 1, '0', 1608793132773, 1608793132766, 1, 1, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_method`(`id`, `system_code`, `method_code`, `method_type`, `back_method_code`, `method_name`, `data_type`, `call_type`, `type`, `type_index`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (70, 'GLP', 'MC70', '6', 'MC73', '入库单回传', 1, 1, 1, '0', 1608793132773, 1608793132766, 1, 1, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_method`(`id`, `system_code`, `method_code`, `method_type`, `back_method_code`, `method_name`, `data_type`, `call_type`, `type`, `type_index`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (71, 'GLP', 'MC71', '5', 'MC73', '同步出库单', 1, 1, 1, '0', 1608793132773, 1608793132766, 1, 1, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_method`(`id`, `system_code`, `method_code`, `method_type`, `back_method_code`, `method_name`, `data_type`, `call_type`, `type`, `type_index`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (72, 'GLP', 'MC72', '7', 'MC73', '出库单回传', 1, 1, 1, '0', 1608793132773, 1608793132766, 1, 1, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_method`(`id`, `system_code`, `method_code`, `method_type`, `back_method_code`, `method_name`, `data_type`, `call_type`, `type`, `type_index`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (73, 'GLP', 'MC73', '9', '0', 'GLP统一返回', 1, 1, 2, '73', 1608793132773, 1608793132766, 1, 1, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_method`(`id`, `system_code`, `method_code`, `method_type`, `back_method_code`, `method_name`, `data_type`, `call_type`, `type`, `type_index`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (74, 'GLP', 'MC74', '9', '0', '库存查询返回', 1, 1, 2, '74', 1608793132773, 1608793132766, 1, 1, 1, 1);
UPDATE `erp_configuration`.`erp_configuration_method` SET `system_code` = 'ERP', `method_code` = 'MC62', `method_type` = '30', `back_method_code` = 'MC67', `method_name` = '库存查询', `data_type` = 1, `call_type` = 1, `type` = 1, `type_index` = '0', `create_time` = 0, `update_time` = 0, `create_by` = 0, `update_by` = 0, `deleted` = 1, `version` = 1 WHERE `id` = 62;

INSERT INTO `erp_configuration`.`erp_configuration_mapping`(`id`, `mapping_code`, `source_method_code`, `target_method_code`, `complexity_type`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (30, 'YC30', 'MC66', 'MC3', 3, 0, 0, 0, 0, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_mapping`(`id`, `mapping_code`, `source_method_code`, `target_method_code`, `complexity_type`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (31, 'YC31', 'MC68', 'MC62', 3, 0, 0, 0, 0, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_mapping`(`id`, `mapping_code`, `source_method_code`, `target_method_code`, `complexity_type`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (32, 'YC32', 'MC69', 'MC4', 3, 0, 0, 0, 0, 1, 1);
-- INSERT INTO `erp_configuration`.`erp_configuration_mapping`(`id`, `mapping_code`, `source_method_code`, `target_method_code`, `complexity_type`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (33, 'YC33', 'MC6', 'MC70', 3, 0, 0, 0, 0, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_mapping`(`id`, `mapping_code`, `source_method_code`, `target_method_code`, `complexity_type`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (34, 'YC34', 'MC71', 'MC5', 3, 0, 0, 0, 0, 1, 1);
-- INSERT INTO `erp_configuration`.`erp_configuration_mapping`(`id`, `mapping_code`, `source_method_code`, `target_method_code`, `complexity_type`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (35, 'YC35', 'MC7', 'MC72', 3, 0, 0, 0, 0, 1, 1);

INSERT INTO `erp_configuration`.`erp_configuration_data_dictionary`(`id`, `system_code`, `data_dictionary_code`, `dictionary_type`, `orig_data_value`, `data_value`, `desc`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (1347120260603266060, 'GLP', 'DC202101071757530057551370', 'GLP_METHOD_TYPE', 'wms.entry.in.notice', '4', '同步入库单', 1610013473069, 1612865770610, NULL, NULL, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_data_dictionary`(`id`, `system_code`, `data_dictionary_code`, `dictionary_type`, `orig_data_value`, `data_value`, `desc`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (1347120260603266061, 'GLP', 'DC202101071757530057551371', 'GLP_METHOD_TYPE', 'wms.entry.out.notice', '5', '同步出库单', 1610013473069, 1612865770610, NULL, NULL, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_data_dictionary`(`id`, `system_code`, `data_dictionary_code`, `dictionary_type`, `orig_data_value`, `data_value`, `desc`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (1347120260603266062, 'GLP', 'DC202101071757530057551372', 'GLP_METHOD_TYPE', 'epass.inventory.query.push', '30', '库存查询', 1610013473069, 1612865770610, NULL, NULL, 1, 1);

INSERT INTO `erp_configuration`.`erp_configuration_params`(`id`, `param_code`, `parent_code`, `method_code`, `name`, `type`, `desc`, `subtypes`, `level`, `dictionary_type`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (247, 'PC247', '0', 'MC4', 'sourcePlatform', 3, '平台编码', NULL, 1, NULL, 0, 0, 0, 0, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_params`(`id`, `param_code`, `parent_code`, `method_code`, `name`, `type`, `desc`, `subtypes`, `level`, `dictionary_type`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (248, 'PC248', '0', 'MC4', 'logicWarehouseCode', 3, '逻辑仓编码', NULL, 1, NULL, 0, 0, 0, 0, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_params`(`id`, `param_code`, `parent_code`, `method_code`, `name`, `type`, `desc`, `subtypes`, `level`, `dictionary_type`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (249, 'PC249', '0', 'MC5', 'logicWarehouseCode', 3, '逻辑仓编码', NULL, 1, NULL, 0, 0, 0, 0, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_params`(`id`, `param_code`, `parent_code`, `method_code`, `name`, `type`, `desc`, `subtypes`, `level`, `dictionary_type`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (356, 'PC356', '0', 'MC5', 'businessNo', NULL, 3, '关联单号', NULL, 1, NULL, 0, 0, 0, 0, 1, 1);

ALTER TABLE `erp_configuration`.`erp_configuration_params`
ADD COLUMN `alias_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '参数别名' AFTER `name`,
ADD UNIQUE INDEX `unique_method_alias_level`(`param_code`, `method_code`, `alias_name`, `level`) USING BTREE COMMENT '同方法同层级名称唯一';

ALTER TABLE `erp_order`.`erp_in_order_detail`
MODIFY COLUMN `unit_price` decimal(20, 4) NULL DEFAULT 0 COMMENT '采购价格' AFTER `line_no`,
MODIFY COLUMN `bill_currency` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'RMB' COMMENT '货币类型' AFTER `unit_price`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202004`
MODIFY COLUMN `unit_price` decimal(20, 4) NULL DEFAULT 0 COMMENT '采购价格' AFTER `line_no`,
MODIFY COLUMN `bill_currency` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'RMB' COMMENT '货币类型' AFTER `unit_price`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202101`
MODIFY COLUMN `unit_price` decimal(20, 4) NULL DEFAULT 0 COMMENT '采购价格' AFTER `line_no`,
MODIFY COLUMN `bill_currency` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'RMB' COMMENT '货币类型' AFTER `unit_price`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202102`
MODIFY COLUMN `unit_price` decimal(20, 4) NULL DEFAULT 0 COMMENT '采购价格' AFTER `line_no`,
MODIFY COLUMN `bill_currency` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'RMB' COMMENT '货币类型' AFTER `unit_price`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202103`
MODIFY COLUMN `unit_price` decimal(20, 4) NULL DEFAULT 0 COMMENT '采购价格' AFTER `line_no`,
MODIFY COLUMN `bill_currency` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'RMB' COMMENT '货币类型' AFTER `unit_price`;

ALTER TABLE `erp_order`.`erp_in_order`
ADD COLUMN `source_platform` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源平台' AFTER `inventory_status`;
ALTER TABLE `erp_order`.`erp_in_order_202004`
ADD COLUMN `source_platform` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源平台' AFTER `inventory_status`;
ALTER TABLE `erp_order`.`erp_in_order_202101`
ADD COLUMN `source_platform` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源平台' AFTER `inventory_status`;
ALTER TABLE `erp_order`.`erp_in_order_202102`
ADD COLUMN `source_platform` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源平台' AFTER `inventory_status`;
ALTER TABLE `erp_order`.`erp_in_order_202103`
ADD COLUMN `source_platform` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源平台' AFTER `inventory_status`;

INSERT INTO `erp_configuration`.`erp_configuration_data_dictionary`(`id`, `system_code`, `data_dictionary_code`, `dictionary_type`, `orig_data_value`, `data_value`, `desc`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (1342056256793604091, 'ERP', 'DC202012241835200468527775', 'inventory_type', '0', '1', '返回值转换 正品', 1608806120477, 1608806120471, 1, 1, 1, 1);
INSERT INTO `erp_configuration`.`erp_configuration_data_dictionary`(`id`, `system_code`, `data_dictionary_code`, `dictionary_type`, `orig_data_value`, `data_value`, `desc`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (1342056313198604292, 'ERP', 'DC202012241835330918017916', 'inventory_type', '1', '2', '返回值转换 次品', 1608806133925, 1608806133921, 1, 1, 1, 1);

UPDATE `erp_configuration`.`erp_configuration_params` SET `alias_name` = 'outOrderDetailFromList' WHERE `id` = 120012;
UPDATE `erp_configuration`.`erp_configuration_params` SET `alias_name` = 'inOrderDetailParamList' WHERE `id` = 120112;

UPDATE `erp_configuration`.`erp_configuration_params` SET `dictionary_type` = 'inventory_type' WHERE `id` = 71;
UPDATE `erp_configuration`.`erp_configuration_params` SET `dictionary_type` = 'inventory_type' WHERE `id` = 119;

