-- 修改审核操作备注字段
ALTER TABLE `erp_trade`.`erp_purchase_order`
CHANGE COLUMN `rejected_message` `operator_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作备注' AFTER `remark`;

ALTER TABLE `erp_trade`.`erp_tally_report`
CHANGE COLUMN `rejected_message` `operator_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作备注' AFTER `approval_status`;

ALTER TABLE `erp_trade`.`erp_sale_order`
CHANGE COLUMN `rejected_message` `operator_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作备注' AFTER `remark`;

ALTER TABLE `erp_trade`.`erp_transfer_order`
CHANGE COLUMN `rejected_message` `operator_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作备注' AFTER `create_by_name`;

ALTER TABLE `erp_trade`.`erp_purchase_order`
MODIFY COLUMN `bill_date` bigint(1) NULL DEFAULT NULL COMMENT '单据日期' AFTER `purchase_invoice_no`,
MODIFY COLUMN `delivery_date` bigint(1) NULL DEFAULT NULL COMMENT '交货日期' AFTER `bill_date`;


-- 货主表冗余系统编码字段
ALTER TABLE `erp_common`.`erp_owner`
ADD COLUMN `system_code` varchar(255) NULL COMMENT '系统编码';


-- 账单、结算单唯一索引相关
ALTER TABLE `erp_report`.`user_settlement`
DROP INDEX `idx_settlement_no`,
ADD UNIQUE INDEX `unique_settlement_no`(`user_id`, `settlement_no`) USING BTREE COMMENT '用户+结算单号唯一';

ALTER TABLE `erp_report`.`user_bills`
DROP INDEX `unique_bill_no`,
ADD UNIQUE INDEX `unique_bill_no`(`user_id`, `bill_no`) USING BTREE COMMENT '用户+账单编号唯一';

-- 账单、结算单相关结束

-- 货品利润表
CREATE TABLE `erp_goods_profit` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(11) DEFAULT NULL COMMENT '租户id',
  `user_name` varchar(64) DEFAULT NULL COMMENT '用户名称',
  `goods_code` varchar(64) DEFAULT NULL COMMENT '货品sku',
  `level` varchar(64) DEFAULT NULL COMMENT '等级',
  `premium_type` int(2) DEFAULT NULL COMMENT '加价类型',
  `premium` decimal(16,4) DEFAULT NULL COMMENT '加价幅度',
  `create_time` bigint(1) NOT NULL DEFAULT '0',
  `update_time` bigint(1) NOT NULL DEFAULT '0',
  `create_by` bigint(20) DEFAULT '0',
  `update_by` bigint(20) DEFAULT '0',
  `deleted` tinyint(1) NOT NULL DEFAULT '1',
  `version` bigint(10) NOT NULL DEFAULT '1' COMMENT '版本号乐观锁',
  PRIMARY KEY (`id`),
  KEY `create_time` (`create_time`) USING BTREE,
  KEY `goods_code` (`goods_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1346330610009821187 DEFAULT CHARSET=utf8mb4 COMMENT='货品利润表';

ALTER TABLE `erp_common`.`erp_goods_profit`
ADD COLUMN `approval_status` int(2) NULL DEFAULT 1 COMMENT '审核状态' AFTER `premium`,
ADD COLUMN `operator_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核信息' AFTER `approval_status`;


-- 审核流程表结构修改
ALTER TABLE `erp_flow`.`erp_flow_task`
ADD COLUMN `document_type` tinyint(2) UNSIGNED NOT NULL COMMENT '目标 1、销售单 2、采购单 3、调拨单 4、出库单 5、入库单';
ALTER TABLE `erp_flow`.`erp_flow_task`
DROP INDEX `idx_flow_instance_id`,
ADD INDEX `idx_flow_instance_id`(`user_id`, `flow_instance_id`) USING BTREE COMMENT '流程实例ID索引',
ADD INDEX `idx_document_type`(`document_type`) USING BTREE COMMENT '单据类型';

-- 审核流程表更新数据
UPDATE `erp_flow`.`erp_flow_task` a
INNER JOIN `erp_flow`.`erp_target_flow` b ON a.flow_id=b.flow_id and a.user_id=b.user_id
SET a.document_type=b.document_type
WHERE a.deleted=1;


ALTER TABLE `erp_trade`.`erp_purchase_order`
MODIFY COLUMN `paid_amount` decimal(16, 4) NULL DEFAULT 0.00 COMMENT '已付金额' AFTER `bill_currency`,
MODIFY COLUMN `discount_amount` decimal(16, 4) NULL DEFAULT 0.00 COMMENT '优惠金额' AFTER `paid_amount`,
MODIFY COLUMN `due_amount` decimal(16, 4) NULL DEFAULT 0.00 COMMENT '应付金额' AFTER `discount_amount`;
ALTER TABLE `erp_trade`.`erp_purchase_order_detail`
MODIFY COLUMN `discount_amount` decimal(16, 4) NULL DEFAULT 0.00 COMMENT '折扣金额' AFTER `goods_number`,
MODIFY COLUMN `discount_rate` double(8, 4) NULL DEFAULT 0.00 COMMENT '折扣率' AFTER `discount_amount`,
MODIFY COLUMN `unit_price` decimal(16, 4) NULL DEFAULT 0.00 COMMENT '单价' AFTER `discount_rate`,
MODIFY COLUMN `total_amount` decimal(16, 4) NULL DEFAULT 0.00 COMMENT '采购总价' AFTER `unit_price`;
ALTER TABLE `erp_trade`.`erp_purchase_order_other_charges`
MODIFY COLUMN `other_charges_amount` decimal(16, 4) NULL DEFAULT 0.00 COMMENT '其他费用金额' AFTER `other_charges_name`;
ALTER TABLE `erp_trade`.`erp_sale_order`
MODIFY COLUMN `discount_amount` decimal(16, 4) NOT NULL DEFAULT 0.00 COMMENT '优惠金额' AFTER `logic_warehouse_name`,
MODIFY COLUMN `due_amount` decimal(16, 4) NOT NULL DEFAULT 0.00 COMMENT '应收金额' AFTER `discount_amount`,
MODIFY COLUMN `paid_amount` decimal(16, 4) NULL DEFAULT 0.00 COMMENT '已付金额' AFTER `due_amount`;
ALTER TABLE `erp_trade`.`erp_sale_order_detail`
MODIFY COLUMN `unit_price` decimal(16, 4) NOT NULL DEFAULT 0.00 COMMENT '单价' AFTER `bill_currency`,
MODIFY COLUMN `retail_price` decimal(16, 4) NOT NULL DEFAULT 0.00 COMMENT '零售价格' AFTER `unit_price`,
MODIFY COLUMN `discount_amount` decimal(16, 4) NOT NULL DEFAULT 0.00 COMMENT '折扣金额' AFTER `currency_rate`,
MODIFY COLUMN `discount_rate` double(8, 4) NOT NULL DEFAULT 0.00 COMMENT '折扣率' AFTER `discount_amount`;
ALTER TABLE `erp_trade`.`erp_sale_order_other_charges`
MODIFY COLUMN `other_charges_amount` decimal(16, 4) NULL DEFAULT NULL COMMENT '其他费用金额' AFTER `other_charges_name`;
ALTER TABLE `erp_common`.`erp_goods`
MODIFY COLUMN `retail_price` decimal(16, 4) NULL DEFAULT NULL COMMENT '零售价格' AFTER `barcode`;
ALTER TABLE `erp_common`.`erp_goods_record`
MODIFY COLUMN `consumption_tax` double(16, 4) NULL DEFAULT NULL COMMENT '消费税' AFTER `declare_element`,
MODIFY COLUMN `VAT` double(16, 4) NULL DEFAULT NULL COMMENT '增值税' AFTER `consumption_tax`;

ALTER TABLE `erp_order`.`erp_in_order_detail`
CHANGE COLUMN `retail_price` `unit_price` decimal(20, 4) NOT NULL COMMENT '采购价格' AFTER `line_no`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202002`
CHANGE COLUMN `retail_price` `unit_price` decimal(20, 4) NOT NULL COMMENT '采购价格' AFTER `line_no`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202003`
CHANGE COLUMN `retail_price` `unit_price` decimal(20, 4) NOT NULL COMMENT '采购价格' AFTER `line_no`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202004`
CHANGE COLUMN `retail_price` `unit_price` decimal(20, 4) NOT NULL COMMENT '采购价格' AFTER `line_no`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202101`
CHANGE COLUMN `retail_price` `unit_price` decimal(20, 4) NOT NULL COMMENT '采购价格' AFTER `line_no`;

ALTER TABLE `erp_inventory`.`erp_goods_batch_price`
MODIFY COLUMN `goods_price` decimal(16, 4) NOT NULL COMMENT '采购单价' AFTER `invoice_no`;

ALTER TABLE `erp_report`.`user_bills`
MODIFY COLUMN `bill_amount` decimal(16, 4) NOT NULL DEFAULT 0.00 COMMENT '账单金额' AFTER `bill_with_name`,
MODIFY COLUMN `bill_finished_amount` decimal(16, 4) NOT NULL DEFAULT 0.00 COMMENT '已结算金额' AFTER `bill_amount`;

ALTER TABLE `erp_report`.`user_settlement`
MODIFY COLUMN `settlement_amount` decimal(16, 4) NOT NULL DEFAULT 0.00 COMMENT '结算金额' AFTER `settlement_account`,
MODIFY COLUMN `used_amount` decimal(16, 4) NOT NULL DEFAULT 0.00 COMMENT '已用结算金额（关联账单结算金额）' AFTER `settlement_amount`,
MODIFY COLUMN `settlement_fee` decimal(10, 4) NOT NULL DEFAULT 0.00 COMMENT '手续费' AFTER `used_amount`;

MODIFY COLUMN `settlement_amount` decimal(16, 4) NOT NULL DEFAULT 0.00 COMMENT '结算金额' AFTER `bill_no`;

ALTER TABLE `erp_order`.`erp_receive_send_info`
ADD COLUMN `send_zip_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '发货区邮编' AFTER `send_district_code`,
ADD COLUMN `receive_zip_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '收货区邮编' AFTER `receive_district_code`;
ALTER TABLE `erp_order`.`erp_receive_send_info_202002`
ADD COLUMN `send_zip_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '发货区邮编' AFTER `send_district_code`,
ADD COLUMN `receive_zip_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '收货区邮编' AFTER `receive_district_code`;
ALTER TABLE `erp_order`.`erp_receive_send_info_202003`
ADD COLUMN `send_zip_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '发货区邮编' AFTER `send_district_code`,
ADD COLUMN `receive_zip_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '收货区邮编' AFTER `receive_district_code`;
ALTER TABLE `erp_order`.`erp_receive_send_info_202004`
ADD COLUMN `send_zip_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '发货区邮编' AFTER `send_district_code`,
ADD COLUMN `receive_zip_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '收货区邮编' AFTER `receive_district_code`;
ALTER TABLE `erp_order`.`erp_receive_send_info_202101`
ADD COLUMN `send_zip_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '发货区邮编' AFTER `send_district_code`,
ADD COLUMN `receive_zip_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '收货区邮编' AFTER `receive_district_code`;

ALTER TABLE `erp_configuration`.`erp_configuration_system`
DROP COLUMN `client_id`,
DROP COLUMN `client_secret`,
DROP COLUMN `url`,
CHANGE COLUMN `sign` `system_type` int(1) NULL DEFAULT 2 COMMENT '系统类型 1 仓库，2 其他' AFTER `system_name`;

ALTER TABLE `erp_configuration`.`erp_configuration_method`
CHANGE COLUMN `service_version` `type_index` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '用于唯一索引校验' AFTER `type`,
DROP INDEX `index_system_code_method_type_type`,
ADD UNIQUE INDEX `index_system_code_method_type_type`(`system_code`, `method_type`, `type`, `type_index`) USING BTREE COMMENT '系统编码+方法类型+请求类型';

ALTER TABLE `erp_configuration`.`erp_configuration_request_params`
MODIFY COLUMN `create_by` bigint(20) NULL DEFAULT 0 COMMENT '创建人' AFTER `update_time`,
MODIFY COLUMN `update_by` bigint(20) NULL DEFAULT 0 COMMENT '更新人' AFTER `create_by`;

ALTER TABLE `erp_configuration`.`erp_configuration_params`
ADD UNIQUE INDEX `unique_method_name_level`(`method_code`, `name`, `level`) USING BTREE COMMENT '同方法同层级名称唯一';

ALTER TABLE `erp_configuration`.`erp_configuration_mapping_params`
ADD COLUMN `source_param_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '来源参数名称' AFTER `source_param_code`,
ADD COLUMN `target_param_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '目标参数名称' AFTER `target_param_code`;

ALTER TABLE `erp_configuration`.`erp_configuration_params`
DROP COLUMN `data_convert`;


