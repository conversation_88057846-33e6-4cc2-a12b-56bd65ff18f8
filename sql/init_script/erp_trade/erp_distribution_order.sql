CREATE TABLE `erp_distribution_order` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(11) NOT NULL DEFAULT '0' COMMENT '用户id',
  `create_time` bigint(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_by` bigint(20) DEFAULT '0' COMMENT '创建id',
  `create_by_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作人',
  `update_by` bigint(20) DEFAULT '0' COMMENT '更新id',
  `deleted` tinyint(11) NOT NULL DEFAULT '1' COMMENT '是否删除',
  `version` bigint(10) NOT NULL DEFAULT '1' COMMENT '版本号乐观锁',
  `distribution_no` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配货单号',
  `distribution_type` int(1) DEFAULT NULL COMMENT '配货类型',
  `related_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联单号',
  `distrbution_business_type` int(1) DEFAULT NULL COMMENT '业务类型',
  `customer_area` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '进出境关别',
  `transport_type` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '运输方式',
  `exit_time` bigint(64) DEFAULT NULL COMMENT '出区时间',
  `file_json` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '附件',
  `distribution_status` int(1) DEFAULT NULL COMMENT '配货状态',
  `refuse_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '驳回原因',
  `examine_time` bigint(64) DEFAULT NULL COMMENT '审核时间',
  `finish_time` bigint(11) DEFAULT NULL COMMENT '完成时间',
  `declaration_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '清关单号',
  `ready_country_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '启运国名称',
  `transport_type_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '运输方式名称',
  `sync_status` int(1) DEFAULT '0' COMMENT '同步状态',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注释',
  `logic_warehouse_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '逻辑仓库编码',
  PRIMARY KEY (`id`),
  UNIQUE KEY `distribution_no` (`distribution_no`)
) ENGINE=InnoDB AUTO_INCREMENT=1335847974930386946 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;