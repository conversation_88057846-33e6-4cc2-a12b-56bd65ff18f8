INSERT INTO `erp_configuration`.`erp_configuration_data_dictionary`(`id`, `system_code`, `data_dictionary_code`, `dictionary_type`, `orig_data_value`, `data_value`, `desc`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`, `version`) VALUES (1365165233439936531, 'SJZD', 'DC202109071302090843135629', 'SJZD_METHOD_TYPE', '47', '47', '新增盘点单', 1614315729853, 1614315729848, NULL, NULL, 1, 1);
UPDATE `erp_configuration`.`erp_configuration_data_dictionary` SET `desc` = '新增转移单' WHERE `id` = 1365165233439936523;
UPDATE `erp_configuration`.`erp_configuration_method` SET `method_name` = '新增转移单' WHERE `id` = 122;
UPDATE `erp_configuration`.`erp_configuration_method` SET `method_name` = '新增转移单' WHERE `id` = 128;

DELETE FROM `erp_configuration`.`erp_configuration_params` WHERE id = 10259;
UPDATE `erp_configuration`.`erp_configuration_params` SET `name` = 'receiver' WHERE `id` = 40019;

ALTER TABLE `erp_configuration`.`erp_configuration_params`
DROP INDEX `unique_method_name_level`,
DROP INDEX `unique_method_alias_level`,
ADD UNIQUE INDEX `unique_method_name_level`(`parent_code`, `method_code`, `name`, `level`, `param_status`) USING BTREE,
ADD UNIQUE INDEX `unique_method_alias_level`(`parent_code`, `method_code`, `alias_name`, `level`, `param_status`) USING BTREE;