ALTER TABLE `erp_order`.`erp_out_order_logistics`
<PERSON>ANGE COLUMN `parcel_no` `back_flag` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回传标记' AFTER `order_no`,
MODIFY COLUMN `logistics_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '物流单号' AFTER `back_flag`,
MODIFY COLUMN `waybill_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '运单号' AFTER `logistics_no`;

ALTER TABLE `erp_order`.`erp_out_order_logistics_detail`
DROP COLUMN `logistics_no`,
CHANGE COLUMN `parcel_no` `back_flag` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回传标记' AFTER `order_no`,
MODIFY COLUMN `goods_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '货品编码' AFTER `back_flag`,
ADD COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '外部货品编码' AFTER `goods_code`;

ALTER TABLE `erp_order`.`erp_out_order`
CHANGE COLUMN `create_time` `add_time` bigint(10) NULL DEFAULT 0 COMMENT '下单时间' AFTER `business_only_index`,
ADD COLUMN `pay_time` bigint(10) NULL DEFAULT 0 COMMENT '支付时间' AFTER `add_time`,
ADD COLUMN `create_time` bigint(10) NOT NULL DEFAULT 0 COMMENT '创建时间' AFTER `pay_time`;
ALTER TABLE `erp_order`.`erp_out_order_202102`
CHANGE COLUMN `create_time` `add_time` bigint(10) NULL DEFAULT 0 COMMENT '下单时间' AFTER `business_only_index`,
ADD COLUMN `pay_time` bigint(10) NULL DEFAULT 0 COMMENT '支付时间' AFTER `add_time`,
ADD COLUMN `create_time` bigint(10) NOT NULL DEFAULT 0 COMMENT '创建时间' AFTER `pay_time`;
ALTER TABLE `erp_order`.`erp_out_order_202103`
CHANGE COLUMN `create_time` `add_time` bigint(10) NULL DEFAULT 0 COMMENT '下单时间' AFTER `business_only_index`,
ADD COLUMN `pay_time` bigint(10) NULL DEFAULT 0 COMMENT '支付时间' AFTER `add_time`,
ADD COLUMN `create_time` bigint(10) NOT NULL DEFAULT 0 COMMENT '创建时间' AFTER `pay_time`;
ALTER TABLE `erp_order`.`erp_out_order_202004`
CHANGE COLUMN `create_time` `add_time` bigint(10) NULL DEFAULT 0 COMMENT '下单时间' AFTER `business_only_index`,
ADD COLUMN `pay_time` bigint(10) NULL DEFAULT 0 COMMENT '支付时间' AFTER `add_time`,
ADD COLUMN `create_time` bigint(10) NOT NULL DEFAULT 0 COMMENT '创建时间' AFTER `pay_time`;
ALTER TABLE `erp_order`.`erp_out_order_202101`
CHANGE COLUMN `create_time` `add_time` bigint(10) NULL DEFAULT 0 COMMENT '下单时间' AFTER `business_only_index`,
ADD COLUMN `pay_time` bigint(10) NULL DEFAULT 0 COMMENT '支付时间' AFTER `add_time`,
ADD COLUMN `create_time` bigint(10) NOT NULL DEFAULT 0 COMMENT '创建时间' AFTER `pay_time`;

ALTER TABLE `erp_common`.`erp_goods`
ADD COLUMN `external_sku` varchar(64) NULL COMMENT '菜鸟sku' AFTER `source`;

ALTER TABLE `erp_order`.`erp_out_order_logistics_detail`
ADD COLUMN `barcode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '条形码' AFTER `batch_code`,
ADD COLUMN `is_confirm` tinyint(2) NOT NULL DEFAULT 1 COMMENT '多次确认 0最终状态确认 1中间状态确认' AFTER `inventory_type`,
ADD COLUMN `inventory_status` tinyint(2) NOT NULL DEFAULT 1 COMMENT '库存回传状态 1 待回传 ， 10 成功， 20 失败' AFTER `is_confirm`;

ALTER TABLE `erp_order`.`erp_out_order_logistics`
ADD COLUMN `package_length` bigint(20) NULL DEFAULT 0 COMMENT '长' AFTER `back_status`,
ADD COLUMN `package_width` bigint(20) NULL DEFAULT 0 COMMENT '宽' AFTER `package_length`,
ADD COLUMN `package_height` bigint(20) NULL DEFAULT 0 COMMENT '高' AFTER `package_width`,
ADD COLUMN `package_volume` bigint(20) NULL DEFAULT 0 COMMENT '体积' AFTER `package_height`,
ADD COLUMN `package_weight` bigint(20) NULL DEFAULT 0 COMMENT '重量' AFTER `package_volume`;

CREATE TABLE `erp_out_order_logistics_202004` (
  `id` bigint(11) NOT NULL COMMENT 'ID',
  `order_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `back_flag` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回传标记',
  `logistics_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '物流单号',
  `waybill_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '运单号',
  `logistics_company_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物流公司编码',
  `back_status` tinyint(2) DEFAULT '1' COMMENT '是否回传 1 否， 2是',
  `package_length` bigint(20) DEFAULT '0' COMMENT '长',
  `package_width` bigint(20) DEFAULT '0' COMMENT '宽',
  `package_height` bigint(20) DEFAULT '0' COMMENT '高',
  `package_volume` bigint(20) DEFAULT '0' COMMENT '体积',
  `package_weight` bigint(20) DEFAULT '0' COMMENT '重量',
  `create_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_by` bigint(20) DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) DEFAULT '0' COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT '1' COMMENT '删除标志1正常，2删除',
  `version` bigint(10) NOT NULL DEFAULT '1' COMMENT '版本号乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_no` (`order_no`,`back_flag`,`logistics_no`) USING BTREE COMMENT '单号唯一',
  KEY `index_order_no` (`order_no`) USING BTREE COMMENT '订单号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='多物流信息';
CREATE TABLE `erp_out_order_logistics_202101` (
  `id` bigint(11) NOT NULL COMMENT 'ID',
  `order_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `back_flag` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回传标记',
  `logistics_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '物流单号',
  `waybill_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '运单号',
  `logistics_company_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物流公司编码',
  `back_status` tinyint(2) DEFAULT '1' COMMENT '是否回传 1 否， 2是',
  `package_length` bigint(20) DEFAULT '0' COMMENT '长',
  `package_width` bigint(20) DEFAULT '0' COMMENT '宽',
  `package_height` bigint(20) DEFAULT '0' COMMENT '高',
  `package_volume` bigint(20) DEFAULT '0' COMMENT '体积',
  `package_weight` bigint(20) DEFAULT '0' COMMENT '重量',
  `create_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_by` bigint(20) DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) DEFAULT '0' COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT '1' COMMENT '删除标志1正常，2删除',
  `version` bigint(10) NOT NULL DEFAULT '1' COMMENT '版本号乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_no` (`order_no`,`back_flag`,`logistics_no`) USING BTREE COMMENT '单号唯一',
  KEY `index_order_no` (`order_no`) USING BTREE COMMENT '订单号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='多物流信息';
CREATE TABLE `erp_out_order_logistics_202102` (
  `id` bigint(11) NOT NULL COMMENT 'ID',
  `order_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `back_flag` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回传标记',
  `logistics_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '物流单号',
  `waybill_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '运单号',
  `logistics_company_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物流公司编码',
  `back_status` tinyint(2) DEFAULT '1' COMMENT '是否回传 1 否， 2是',
  `package_length` bigint(20) DEFAULT '0' COMMENT '长',
  `package_width` bigint(20) DEFAULT '0' COMMENT '宽',
  `package_height` bigint(20) DEFAULT '0' COMMENT '高',
  `package_volume` bigint(20) DEFAULT '0' COMMENT '体积',
  `package_weight` bigint(20) DEFAULT '0' COMMENT '重量',
  `create_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_by` bigint(20) DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) DEFAULT '0' COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT '1' COMMENT '删除标志1正常，2删除',
  `version` bigint(10) NOT NULL DEFAULT '1' COMMENT '版本号乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_no` (`order_no`,`back_flag`,`logistics_no`) USING BTREE COMMENT '单号唯一',
  KEY `index_order_no` (`order_no`) USING BTREE COMMENT '订单号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='多物流信息';
CREATE TABLE `erp_out_order_logistics_202103` (
  `id` bigint(11) NOT NULL COMMENT 'ID',
  `order_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `back_flag` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回传标记',
  `logistics_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '物流单号',
  `waybill_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '运单号',
  `logistics_company_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物流公司编码',
  `back_status` tinyint(2) DEFAULT '1' COMMENT '是否回传 1 否， 2是',
  `package_length` bigint(20) DEFAULT '0' COMMENT '长',
  `package_width` bigint(20) DEFAULT '0' COMMENT '宽',
  `package_height` bigint(20) DEFAULT '0' COMMENT '高',
  `package_volume` bigint(20) DEFAULT '0' COMMENT '体积',
  `package_weight` bigint(20) DEFAULT '0' COMMENT '重量',
  `create_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_by` bigint(20) DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) DEFAULT '0' COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT '1' COMMENT '删除标志1正常，2删除',
  `version` bigint(10) NOT NULL DEFAULT '1' COMMENT '版本号乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_no` (`order_no`,`back_flag`,`logistics_no`) USING BTREE COMMENT '单号唯一',
  KEY `index_order_no` (`order_no`) USING BTREE COMMENT '订单号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='多物流信息';

CREATE TABLE `erp_out_order_logistics_detail_202004` (
  `id` bigint(11) NOT NULL COMMENT 'ID',
  `order_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单单号',
  `back_flag` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回传标记',
  `goods_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '货品编码',
  `sku` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '外部货品编码',
  `batch_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '批次号',
  `barcode` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '条形码',
  `actual_quantity` int(8) NOT NULL DEFAULT '0' COMMENT '数量',
  `production_date` datetime DEFAULT NULL COMMENT '生产日期',
  `expire_date` datetime DEFAULT NULL COMMENT '过期日期',
  `inventory_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '正品1，次品2',
  `is_confirm` tinyint(2) NOT NULL DEFAULT '1' COMMENT '多次确认 0最终状态确认 1中间状态确认',
  `inventory_status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '库存回传状态 1 待回传 ， 10 成功， 20 失败',
  `create_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_by` bigint(20) DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) DEFAULT '0' COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT '1' COMMENT '删除标志1正常，2删除',
  `version` bigint(10) NOT NULL DEFAULT '1' COMMENT '版本号乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_no` (`order_no`,`back_flag`) USING BTREE COMMENT '单号唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流与货品关系';
CREATE TABLE `erp_out_order_logistics_detail_202101` (
  `id` bigint(11) NOT NULL COMMENT 'ID',
  `order_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单单号',
  `back_flag` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回传标记',
  `goods_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '货品编码',
  `sku` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '外部货品编码',
  `batch_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '批次号',
  `barcode` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '条形码',
  `actual_quantity` int(8) NOT NULL DEFAULT '0' COMMENT '数量',
  `production_date` datetime DEFAULT NULL COMMENT '生产日期',
  `expire_date` datetime DEFAULT NULL COMMENT '过期日期',
  `inventory_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '正品1，次品2',
  `is_confirm` tinyint(2) NOT NULL DEFAULT '1' COMMENT '多次确认 0最终状态确认 1中间状态确认',
  `inventory_status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '库存回传状态 1 待回传 ， 10 成功， 20 失败',
  `create_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_by` bigint(20) DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) DEFAULT '0' COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT '1' COMMENT '删除标志1正常，2删除',
  `version` bigint(10) NOT NULL DEFAULT '1' COMMENT '版本号乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_no` (`order_no`,`back_flag`) USING BTREE COMMENT '单号唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流与货品关系';
CREATE TABLE `erp_out_order_logistics_detail_202102` (
  `id` bigint(11) NOT NULL COMMENT 'ID',
  `order_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单单号',
  `back_flag` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回传标记',
  `goods_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '货品编码',
  `sku` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '外部货品编码',
  `batch_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '批次号',
  `barcode` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '条形码',
  `actual_quantity` int(8) NOT NULL DEFAULT '0' COMMENT '数量',
  `production_date` datetime DEFAULT NULL COMMENT '生产日期',
  `expire_date` datetime DEFAULT NULL COMMENT '过期日期',
  `inventory_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '正品1，次品2',
  `is_confirm` tinyint(2) NOT NULL DEFAULT '1' COMMENT '多次确认 0最终状态确认 1中间状态确认',
  `inventory_status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '库存回传状态 1 待回传 ， 10 成功， 20 失败',
  `create_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_by` bigint(20) DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) DEFAULT '0' COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT '1' COMMENT '删除标志1正常，2删除',
  `version` bigint(10) NOT NULL DEFAULT '1' COMMENT '版本号乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_no` (`order_no`,`back_flag`) USING BTREE COMMENT '单号唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流与货品关系';
CREATE TABLE `erp_out_order_logistics_detail_202103` (
  `id` bigint(11) NOT NULL COMMENT 'ID',
  `order_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单单号',
  `back_flag` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回传标记',
  `goods_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '货品编码',
  `sku` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '外部货品编码',
  `batch_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '批次号',
  `barcode` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '条形码',
  `actual_quantity` int(8) NOT NULL DEFAULT '0' COMMENT '数量',
  `production_date` datetime DEFAULT NULL COMMENT '生产日期',
  `expire_date` datetime DEFAULT NULL COMMENT '过期日期',
  `inventory_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '正品1，次品2',
  `is_confirm` tinyint(2) NOT NULL DEFAULT '1' COMMENT '多次确认 0最终状态确认 1中间状态确认',
  `inventory_status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '库存回传状态 1 待回传 ， 10 成功， 20 失败',
  `create_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_by` bigint(20) DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) DEFAULT '0' COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT '1' COMMENT '删除标志1正常，2删除',
  `version` bigint(10) NOT NULL DEFAULT '1' COMMENT '版本号乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_no` (`order_no`,`back_flag`) USING BTREE COMMENT '单号唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流与货品关系';

ALTER TABLE `erp_order`.`erp_in_order_detail`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202004`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202101`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202102`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202103`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;

ALTER TABLE `erp_order`.`erp_in_order_detail_batch`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_in_order_detail_batch_202004`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_in_order_detail_batch_202101`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_in_order_detail_batch_202102`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_in_order_detail_batch_202103`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;

ALTER TABLE `erp_order`.`erp_out_order_detail`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_out_order_detail_202004`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_out_order_detail_202101`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_out_order_detail_202102`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_out_order_detail_202103`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;

ALTER TABLE `erp_order`.`erp_out_order_logistics_detail`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_out_order_logistics_detail_202004`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_out_order_logistics_detail_202101`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_out_order_logistics_detail_202102`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;
ALTER TABLE `erp_order`.`erp_out_order_logistics_detail_202103`
ADD COLUMN `external_sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '外部货品ID' AFTER `sku`;

ALTER TABLE `erp_order`.`erp_in_order_detail`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202004`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202101`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202102`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;
ALTER TABLE `erp_order`.`erp_in_order_detail_202103`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;

ALTER TABLE `erp_order`.`erp_in_order_detail_batch`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;
ALTER TABLE `erp_order`.`erp_in_order_detail_batch_202004`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;
ALTER TABLE `erp_order`.`erp_in_order_detail_batch_202101`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;
ALTER TABLE `erp_order`.`erp_in_order_detail_batch_202102`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;
ALTER TABLE `erp_order`.`erp_in_order_detail_batch_202103`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;

ALTER TABLE `erp_order`.`erp_out_order_detail`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;
ALTER TABLE `erp_order`.`erp_out_order_detail_202004`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;
ALTER TABLE `erp_order`.`erp_out_order_detail_202101`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;
ALTER TABLE `erp_order`.`erp_out_order_detail_202102`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;
ALTER TABLE `erp_order`.`erp_out_order_detail_202103`
MODIFY COLUMN `sku` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部货品编码' AFTER `goods_code`;

ALTER TABLE `erp_order`.`erp_in_order`
MODIFY COLUMN `back_flag` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回传标记' AFTER `business_only_index`;
ALTER TABLE `erp_order`.`erp_in_order_202004`
MODIFY COLUMN `back_flag` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回传标记' AFTER `business_only_index`;
ALTER TABLE `erp_order`.`erp_in_order_202101`
MODIFY COLUMN `back_flag` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回传标记' AFTER `business_only_index`;
ALTER TABLE `erp_order`.`erp_in_order_202102`
MODIFY COLUMN `back_flag` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回传标记' AFTER `business_only_index`;
ALTER TABLE `erp_order`.`erp_in_order_202103`
MODIFY COLUMN `back_flag` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回传标记' AFTER `business_only_index`;

ALTER TABLE `erp_order`.`erp_out_order`
MODIFY COLUMN `back_flag` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回传标记' AFTER `create_time`;
ALTER TABLE `erp_order`.`erp_out_order_202004`
MODIFY COLUMN `back_flag` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回传标记' AFTER `create_time`;
ALTER TABLE `erp_order`.`erp_out_order_202101`
MODIFY COLUMN `back_flag` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回传标记' AFTER `create_time`;
ALTER TABLE `erp_order`.`erp_out_order_202102`
MODIFY COLUMN `back_flag` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回传标记' AFTER `create_time`;
ALTER TABLE `erp_order`.`erp_out_order_202103`
MODIFY COLUMN `back_flag` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回传标记' AFTER `create_time`;

ALTER TABLE `erp_order`.`erp_out_order_logistics_detail`
DROP INDEX `index_no`,
ADD INDEX `index_no_flag`(`order_no`, `back_flag`) USING BTREE COMMENT '单号+标记索引',
ADD INDEX `index_order_on`(`order_no`) USING BTREE COMMENT '单号索引';
ALTER TABLE `erp_order`.`erp_out_order_logistics_detail_202004`
DROP INDEX `index_no`,
ADD INDEX `index_no_flag`(`order_no`, `back_flag`) USING BTREE COMMENT '单号+标记索引',
ADD INDEX `index_order_on`(`order_no`) USING BTREE COMMENT '单号索引';
ALTER TABLE `erp_order`.`erp_out_order_logistics_detail_202101`
DROP INDEX `index_no`,
ADD INDEX `index_no_flag`(`order_no`, `back_flag`) USING BTREE COMMENT '单号+标记索引',
ADD INDEX `index_order_on`(`order_no`) USING BTREE COMMENT '单号索引';
ALTER TABLE `erp_order`.`erp_out_order_logistics_detail_202102`
DROP INDEX `index_no`,
ADD INDEX `index_no_flag`(`order_no`, `back_flag`) USING BTREE COMMENT '单号+标记索引',
ADD INDEX `index_order_on`(`order_no`) USING BTREE COMMENT '单号索引';
ALTER TABLE `erp_order`.`erp_out_order_logistics_detail_202103`
DROP INDEX `index_no`,
ADD INDEX `index_no_flag`(`order_no`, `back_flag`) USING BTREE COMMENT '单号+标记索引',
ADD INDEX `index_order_on`(`order_no`) USING BTREE COMMENT '单号索引';

ALTER TABLE `erp_order`.`erp_out_order_logistics`
DROP INDEX `index_no`,
ADD UNIQUE INDEX `index_package`(`order_no`, `back_flag`, `waybill_no`) USING BTREE COMMENT '单号唯一',
ADD INDEX `index_order_back`(`order_no`, `back_status`) USING BTREE COMMENT '单号+回传';
ALTER TABLE `erp_order`.`erp_out_order_logistics_202004`
DROP INDEX `index_no`,
ADD UNIQUE INDEX `index_package`(`order_no`, `back_flag`, `waybill_no`) USING BTREE COMMENT '单号唯一',
ADD INDEX `index_order_back`(`order_no`, `back_status`) USING BTREE COMMENT '单号+回传';
ALTER TABLE `erp_order`.`erp_out_order_logistics_202101`
DROP INDEX `index_no`,
ADD UNIQUE INDEX `index_package`(`order_no`, `back_flag`, `waybill_no`) USING BTREE COMMENT '单号唯一',
ADD INDEX `index_order_back`(`order_no`, `back_status`) USING BTREE COMMENT '单号+回传';
ALTER TABLE `erp_order`.`erp_out_order_logistics_202102`
DROP INDEX `index_no`,
ADD UNIQUE INDEX `index_package`(`order_no`, `back_flag`, `waybill_no`) USING BTREE COMMENT '单号唯一',
ADD INDEX `index_order_back`(`order_no`, `back_status`) USING BTREE COMMENT '单号+回传';
ALTER TABLE `erp_order`.`erp_out_order_logistics_202103`
DROP INDEX `index_no`,
ADD UNIQUE INDEX `index_package`(`order_no`, `back_flag`, `waybill_no`) USING BTREE COMMENT '单号唯一',
ADD INDEX `index_order_back`(`order_no`, `back_status`) USING BTREE COMMENT '单号+回传';

