ALTER TABLE `erp_order`.`erp_in_order_detail_batch`
    ADD COLUMN `inventory_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '库存号' AFTER `is_lack`;
ALTER TABLE `erp_order`.`erp_in_order_detail_batch_202004`
    ADD COLUMN `inventory_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '库存号' AFTER `is_lack`;
ALTER TABLE `erp_order`.`erp_in_order_detail_batch_202101`
    ADD COLUMN `inventory_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '库存号' AFTER `is_lack`;
ALTER TABLE `erp_order`.`erp_in_order_detail_batch_202102`
    ADD COLUMN `inventory_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '库存号' AFTER `is_lack`;
ALTER TABLE `erp_order`.`erp_in_order_detail_batch_202103`
    ADD COLUMN `inventory_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '库存号' AFTER `is_lack`;