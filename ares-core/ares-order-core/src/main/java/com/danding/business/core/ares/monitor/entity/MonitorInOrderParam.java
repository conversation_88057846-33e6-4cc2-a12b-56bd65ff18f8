package com.danding.business.core.ares.monitor.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.danding.component.common.base.DO.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 入库单监控表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_monitor_in_order_param")
public class MonitorInOrderParam extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * WSM实体仓库编码
     */
    private String warehouseCode;

    /**
     * 实体仓库编码
     */
    private String entityWarehouseCode;

    /**
     * 实体仓名称
     */
    private String entityWarehouseName;

    /**
     * 告警分组json
     */
    private String contactGroupJson;

    /**
     * 上架时效
     */
    private Integer shelfHour;

    /**
     * 理货时效
     */
    private Integer tallyHour;

    /**
     * 清关时效
     */
    private Integer customsHour;

    /**
     * 预警时间
     */
    private Integer warningHour;


}
