package com.danding.business.core.ares.adjustOrder.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.danding.business.common.ares.enums.adjustOrder.AdjustType;
import com.danding.business.common.ares.enums.common.ApprovalStatus;
import com.danding.business.common.ares.enums.common.InventoryHandleType;
import com.danding.business.common.ares.enums.common.SceneType;
import com.danding.business.common.ares.enums.trade.AdjustOrderStatus;
import com.danding.component.common.base.DO.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 调整单主表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_adjust_order")
public class AdjustOrder extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 调整单号
     */
    private String adjustOrderNo;

    /**
     * wms调整单号
     */
    private String relatedNo;

    /**
     * 调整类型
     */
    private AdjustType adjustType;

    /**
     * 调整单状态
     */
    private AdjustOrderStatus adjustStatus;

    /**
     * 审批状态
     */
    private ApprovalStatus approvalStatus;

    /**
     * 操作信息
     */
    private String operatorRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作时间
     */
    private Long operateTime;

    /**
     * 创建人姓名
     */
    private String createByName;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 小二操作信息
     */
    private String adminOperatorRemark;

    /**
     * 库存操作信息
     */
    private String optInventoryMessage;

    /**
     * 编辑版本号
     */
    private Integer editVersion;

    /**
     * 库存状态
     */
    private InventoryHandleType inventoryStatus;

    /**
     * 锁定时间
     */
    private Long lockTime;

    /**
     * 完成时间
     */
    private Long finishTime;

    /**
     * 业务场景
     */
    private SceneType sceneType;
}
