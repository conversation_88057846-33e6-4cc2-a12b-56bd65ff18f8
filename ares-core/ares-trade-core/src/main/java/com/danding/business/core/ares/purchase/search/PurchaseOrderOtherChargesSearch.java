package com.danding.business.core.ares.purchase.search;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 采购单其他费用表查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */

@Data
@ApiModel(value="PurchaseOrderOtherCharges对象", description="采购单其他费用表查询对象")
public class PurchaseOrderOtherChargesSearch extends Page {


    private static final long serialVersionUID = 1L;

    private String purchaseOrderNo;

}
