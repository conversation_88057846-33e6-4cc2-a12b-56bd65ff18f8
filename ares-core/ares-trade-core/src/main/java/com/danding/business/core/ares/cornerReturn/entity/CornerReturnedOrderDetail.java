package com.danding.business.core.ares.cornerReturn.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.danding.business.common.ares.enums.inventory.InventoryType;
import com.danding.component.common.base.DO.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 销售单详细表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_corner_returned_order_detail")
public class CornerReturnedOrderDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 分销单号
     */
    private String cornerOrderNo;

    /**
     * 货品sku
     */
    private String goodsCode;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 外部sku
     */
    private String sku;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 货品数量
     */
    private Integer goodsNumber;

    /**
     * 计划出库数量
     */
    private Integer planQuantity;

    /**
     * 实际出库数量
     */
    private Integer actualQuantity;

    /**
     * 库存类型 正品1，次品2
     */
    private InventoryType inventoryType;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 批次id
     */
    private Long batchId;

    /**
     * 批次号
     */
    private String batchCode;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 过期日期
     */
    private Date expireDate;

    /**
     * 备注
     */
    private String remark;
    /**
     * 关联Id
     */
    private Long relatedId;


}
