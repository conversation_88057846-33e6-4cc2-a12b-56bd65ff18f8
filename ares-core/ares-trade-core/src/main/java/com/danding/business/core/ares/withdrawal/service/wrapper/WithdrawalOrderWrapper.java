package com.danding.business.core.ares.withdrawal.service.wrapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.business.core.ares.withdrawal.entity.WithdrawalOrder;
import com.danding.business.core.ares.withdrawal.search.WithdrawalOrderSearch;
import com.danding.component.common.base.DO.BaseEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * <p>
 * 采退单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Component
public class WithdrawalOrderWrapper {

    public LambdaQueryWrapper<WithdrawalOrder> getQueryWrapper(WithdrawalOrderSearch withdrawalOrderSearch) {
        LambdaQueryWrapper<WithdrawalOrder> queryWrapper = new LambdaQueryWrapper<WithdrawalOrder>();
        queryWrapper.orderByDesc(BaseEntity::getCreateTime);
        queryWrapper.eq(withdrawalOrderSearch.getUserId() != null, WithdrawalOrder::getUserId, withdrawalOrderSearch.getUserId());
        if (StringUtils.isNotBlank(withdrawalOrderSearch.getWithdrawalOrderNo())) {
            queryWrapper.in(WithdrawalOrder::getWithdrawalOrderNo, Arrays.asList(withdrawalOrderSearch.getWithdrawalOrderNo().split(",")));
        }
        if (StringUtils.isNotBlank(withdrawalOrderSearch.getPurchaseOrderNo())) {
            queryWrapper.in(WithdrawalOrder::getPurchaseOrderNo, Arrays.asList(withdrawalOrderSearch.getPurchaseOrderNo().split(",")));
        }
        if (StringUtils.isNotBlank(withdrawalOrderSearch.getPurchaseInvoiceNo())) {
            queryWrapper.in(WithdrawalOrder::getPurchaseInvoiceNo, Arrays.asList(withdrawalOrderSearch.getPurchaseInvoiceNo().split(",")));
        }
        if (StringUtils.isNotBlank(withdrawalOrderSearch.getSupplierBusinessNo())) {
            queryWrapper.in(WithdrawalOrder::getSupplierBusinessNo, Arrays.asList(withdrawalOrderSearch.getSupplierBusinessNo().split(",")));
        }
        if (StringUtils.isNotBlank(withdrawalOrderSearch.getSupplementNo())) {
            queryWrapper.in(WithdrawalOrder::getSupplementNo, Arrays.asList(withdrawalOrderSearch.getSupplementNo().split(",")));
        }
        if (StringUtils.isNotBlank(withdrawalOrderSearch.getSupplierCode())) {
            queryWrapper.eq(WithdrawalOrder::getSupplierCode, withdrawalOrderSearch.getSupplierCode());
        }
        if (withdrawalOrderSearch.getPurchaseType() != null) {
            queryWrapper.eq(WithdrawalOrder::getPurchaseType, withdrawalOrderSearch.getPurchaseType());
        }
        if (withdrawalOrderSearch.getInventoryStatus() != null) {
            queryWrapper.eq(WithdrawalOrder::getInventoryStatus, withdrawalOrderSearch.getInventoryStatus());
        }

        return queryWrapper;
    }

    public LambdaQueryWrapper<WithdrawalOrder> getSupplementOrderNoListWrapper(Long userId) {
        LambdaQueryWrapper<WithdrawalOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(BaseEntity::getCreateTime);
        queryWrapper.eq(userId != null, WithdrawalOrder::getUserId, userId);
        queryWrapper.eq(WithdrawalOrder::getIsConfirm, 0);
        queryWrapper.isNull(WithdrawalOrder::getSupplementNo);

        return queryWrapper;
    }
}
