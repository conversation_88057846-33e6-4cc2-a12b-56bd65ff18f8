package com.danding.business.core.ares.supportorder.service.impl;

import com.danding.business.core.ares.supportorder.entity.SupportOrder;
import com.danding.business.core.ares.supportorder.repository.SupportOrderRepository;
import com.danding.business.core.ares.supportorder.search.SupportOrderSearch;
import com.danding.business.core.ares.supportorder.service.ISupportOrderService;
import com.danding.business.core.ares.supportorder.service.wrapper.SupportOrderWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;

/**
* <p>
*  服务实现类
* </p>
*
* <AUTHOR>
* @since 2021-04-13
*/
@Service
public class SupportOrderServiceImpl implements ISupportOrderService {

    @Autowired
    private SupportOrderRepository supportOrderRepository;

    @Autowired
    private SupportOrderWrapper supportOrderWrapper;


    /**
    * 根据主键id查询
    *
    * @param id
    * @return
    */
    @Override
    public SupportOrder selectById(Serializable id) {
        return supportOrderRepository.getById(id);
    }

    /**
    * 根据条件查询单个
    *
    * @param supportOrderSearch
    * @return
    */
    @Override
    public SupportOrder selectBySearch(SupportOrderSearch supportOrderSearch) {
        return supportOrderRepository.getOne(supportOrderWrapper.getQueryWrapper(supportOrderSearch));
    }

    /**
    * 根据条件查询
    *
    * @param supportOrderSearch
    * @return
    */
    @Override
    public List<SupportOrder> selectListBySearch(SupportOrderSearch supportOrderSearch) {
        return supportOrderRepository.list(supportOrderWrapper.getQueryWrapper(supportOrderSearch));
    }

    /**
    * 根据主键修改
    *
    * @param supportOrder
    * @return
    */
    @Override
    public boolean updateById(SupportOrder supportOrder) {
        return supportOrderRepository.updateById(supportOrder);
    }

    /**
    * 根据条件修改
    *
    * @param supportOrderSearch
    * @param supportOrder
    * @return
    */
    @Override
    public boolean updateListBySearch(SupportOrderSearch supportOrderSearch, SupportOrder supportOrder) {
        return supportOrderRepository.update(supportOrder, supportOrderWrapper.getQueryWrapper(supportOrderSearch));
    }

    /**
    * 主键id批量修改
    *
    * @param supportOrderList
    * @return
    */
    @Override
    public boolean updateListById(List<SupportOrder> supportOrderList) {
        return supportOrderRepository.updateBatchById(supportOrderList);
    }

    /**
    * 单条插入
    *
    * @param supportOrder
    * @return
    */
    @Override
    public boolean insert(SupportOrder supportOrder) {
        return supportOrderRepository.save(supportOrder);
    }

    /**
    * 批量插入
    *
    * @param supportOrderList
    * @return
    */
    @Override
    public boolean insertList(List<SupportOrder> supportOrderList) {
    return supportOrderRepository.saveBatch(supportOrderList);
    }

    /**
    * 主键id删除
    *
    * @param id
    * @return
    */
    @Override
    public boolean deleteById(Serializable id) {
        return supportOrderRepository.removeById(id);
    }

    /**
    * 根据条件删除
    *
    * @param supportOrderSearch
    * @return
    */
    @Override
    public boolean deleteBySearch(SupportOrderSearch supportOrderSearch) {
        return supportOrderRepository.remove(supportOrderWrapper.getQueryWrapper(supportOrderSearch));
    }

    /**
    * 主键id批量删除
    *
    * @param idList
    * @return
    */
    @Override
    public boolean deleteByIds(List<Long> idList) {
        return supportOrderRepository.removeByIds(idList);
    }


}
