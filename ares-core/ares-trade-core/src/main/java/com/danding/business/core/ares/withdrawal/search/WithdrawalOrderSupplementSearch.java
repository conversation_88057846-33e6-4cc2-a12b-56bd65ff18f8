package com.danding.business.core.ares.withdrawal.search;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 采退单表查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */

@Data
@ApiModel(value="WithdrawalOrderSupplement对象", description="采退单表查询对象")
public class WithdrawalOrderSupplementSearch implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

}
