<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.business.core.ares.adjustOrder.mapper.AdjustOrderDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.danding.business.core.ares.adjustOrder.entity.AdjustOrderDetail">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="deleted" property="deleted"/>
        <result column="version" property="version"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="adjust_order_no" property="adjustOrderNo"/>
        <result column="inventory_type" property="inventoryType"/>
        <result column="goods_code" property="goodsCode"/>
        <result column="sku" property="sku"/>
        <result column="batch_code" property="batchCode"/>
        <result column="in_order_date" property="inOrderDate"/>
        <result column="logic_warehouse_code" property="logicWarehouseCode"/>
        <result column="logic_warehouse_name" property="logicWarehouseName"/>
        <result column="production_date" property="productionDate"/>
        <result column="expire_date" property="expireDate"/>
        <result column="adjust_num" property="adjustNum"/>
        <result column="remark" property="remark"/>
        <result column="line_no" property="lineNo"/>
        <result column="parent" property="parent"/>
        <result column="parent_id" property="parentId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        parent,
        parent_id,
        create_time,
        line_no,
        update_time,
        create_by,
        update_by,
        deleted,
        version,
        user_id, user_name, adjust_order_no, inventory_type, goods_code, sku, batch_code, in_order_date, logic_warehouse_code, logic_warehouse_name, production_date, expire_date, adjust_num, remark
    </sql>

</mapper>
