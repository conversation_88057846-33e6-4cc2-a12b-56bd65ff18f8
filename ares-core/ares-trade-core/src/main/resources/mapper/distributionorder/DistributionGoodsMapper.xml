<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.business.core.ares.distributionorder.mapper.DistributionGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.danding.business.core.ares.distributionorder.entity.DistributionGoods">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="deleted" property="deleted"/>
        <result column="version" property="version"/>
        <result column="user_id" property="userId"/>
        <result column="goods_name" property="goodsName"/>
        <result column="goods_code" property="goodsCode"/>
        <result column="materialCode" property="materialCode"/>
        <result column="distribution_no" property="distributionNo"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        create_by,
        update_by,
        deleted,
        version,
        user_id, goods_name, goods_code, materialCode, distribution_no
    </sql>

</mapper>
