<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.business.core.ares.distributionorder.mapper.DistributionOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.danding.business.core.ares.distributionorder.entity.DistributionOrder">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="finish_time" property="finishTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="update_by" property="updateBy"/>
        <result column="deleted" property="deleted"/>
        <result column="version" property="version"/>
        <result column="user_id" property="userId"/>
        <result column="distribution_no" property="distributionNo"/>
        <result column="distribution_type" property="distributionType"/>
        <result column="related_no" property="relatedNo"/>
        <result column="distrbution_business_type" property="distrbutionBusinessType"/>
        <result column="customer_area" property="customerArea"/>
        <result column="transport_type" property="transportType"/>
        <result column="exit_time" property="exitTime"/>
        <result column="file_json" property="fileJson"/>
        <result column="distribution_status" property="distributionStatus"/>
        <result column="refuse_reason" property="refuseReason"/>
        <result column="examine_time" property="examineTime"/>
        <result column="declaration_no" property="declarationNo"/>
        <result column="ready_country_name" property="readyCountryName"/>
        <result column="transport_type_name" property="transportTypeName"/>
        <result column="sync_status" property="syncStatus"/>
        <result column="remark" property="remark"/>
        <result column="logic_warehouse_code" property="logicWarehouseCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        create_by,
        update_by,
        deleted,
        version,
        create_by_name,
        finish_time,
        user_id, distribution_no, distribution_type,
         related_no, distrbution_business_type, customer_area, transport_type, exit_time, file_json, distribution_status,
          refuse_reason, examine_time,declaration_no,ready_country_name,transport_type_name,sync_status,remark,logic_warehouse_code
    </sql>

</mapper>
