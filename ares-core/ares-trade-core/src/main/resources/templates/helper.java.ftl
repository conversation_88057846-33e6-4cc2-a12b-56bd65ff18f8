package ${package.Entity?replace("entity","manager")?replace("core","server")}.helper;

import ${package.Entity?replace("entity","service")}.I${entity}Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <p>
 * ${table.comment!} 服务类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
<#if kotlin>
interface ${table.serviceName} : ${superServiceClass}<${entity}>
<#else>
@Component
public class ${entity}ManagerHelper {

    @Autowired
    private I${entity}Service ${entity?substring(0,1)?lower_case}${entity?substring(1)}Service;
}
</#if>
