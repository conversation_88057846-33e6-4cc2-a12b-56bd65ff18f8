<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.business.core.ares.configuration.mapper.ConfigurationMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.danding.business.core.ares.configuration.entity.ConfigurationMapping">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
        <result column="version" property="version" />
        <result column="mapping_code" property="mappingCode" />
        <result column="source_method_code" property="sourceMethodCode" />
        <result column="target_method_code" property="targetMethodCode" />
        <result column="complexity_type" property="complexityType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        create_by,
        update_by,
        deleted,
        version,
        mapping_code, source_method_code, target_method_code, complexity_type
    </sql>

</mapper>
