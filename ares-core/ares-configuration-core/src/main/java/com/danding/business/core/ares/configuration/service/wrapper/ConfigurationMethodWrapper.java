package com.danding.business.core.ares.configuration.service.wrapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.danding.business.core.ares.configuration.entity.ConfigurationMethod;
import com.danding.business.core.ares.configuration.search.ConfigurationMethodSearch;
import com.danding.component.common.base.DO.BaseEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <p>
 * 配置方法表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@Component
public class ConfigurationMethodWrapper {

    public LambdaQueryWrapper<ConfigurationMethod> getQueryWrapper(ConfigurationMethodSearch configurationMethodSearch){
        LambdaQueryWrapper<ConfigurationMethod> queryWrapper = new LambdaQueryWrapper<ConfigurationMethod>();
        queryWrapper.orderByDesc(BaseEntity::getCreateTime);
        if(StringUtils.isNotBlank(configurationMethodSearch.getSystemCode())){
            queryWrapper.eq(ConfigurationMethod::getSystemCode, configurationMethodSearch.getSystemCode());
        }
        if(StringUtils.isNotBlank(configurationMethodSearch.getMethodCode())){
            queryWrapper.eq(ConfigurationMethod::getMethodCode, configurationMethodSearch.getMethodCode());
        }
        if(Objects.nonNull(configurationMethodSearch.getMethodType())){
            queryWrapper.eq(ConfigurationMethod::getMethodType, configurationMethodSearch.getMethodType());
        }
        queryWrapper.eq(StringUtils.isNotBlank(configurationMethodSearch.getMethodName()), ConfigurationMethod::getMethodName, configurationMethodSearch.getMethodName());
        return queryWrapper;
    }

    public LambdaUpdateWrapper<ConfigurationMethod> getUpdateWrapper(String methodCode){
        LambdaUpdateWrapper<ConfigurationMethod> queryWrapper = new LambdaUpdateWrapper<ConfigurationMethod>();
        queryWrapper.eq(ConfigurationMethod::getMethodCode, methodCode);
        return queryWrapper;
    }

}
