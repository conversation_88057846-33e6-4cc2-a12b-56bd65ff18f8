package com.danding.business.core.ares.configuration.service;


import com.danding.business.core.ares.configuration.entity.ConfigurationMethod;
import com.danding.business.core.ares.configuration.search.ConfigurationMethodSearch;

import java.util.List;

/**
 * <p>
 * 配置方法表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
public interface IConfigurationMethodService {

    /**
     * 条件查询
     * @param search
     * @return
     */
    List<ConfigurationMethod> selectListBySearch(ConfigurationMethodSearch search);

    /**
     * 条件查询
     * @param search
     * @return
     */
    ConfigurationMethod selectBySearch(ConfigurationMethodSearch search);

    /**
     * 新增或修改
     * @param configurationMethod
     * @return
     */
    boolean saveOrUpdateByCode(ConfigurationMethod configurationMethod);

}
