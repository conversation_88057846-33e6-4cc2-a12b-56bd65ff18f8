package com.danding.business.core.ares.configuration.service;


import com.danding.business.core.ares.configuration.entity.ConfigurationMapping;
import com.danding.business.core.ares.configuration.search.ConfigurationMappingSearch;

import java.util.List;

/**
 * <p>
 * 配置关系映射表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
public interface IConfigurationMappingService {

    /**
     * 条件查询
     * @param search
     * @return
     */
    ConfigurationMapping selectBySearch(ConfigurationMappingSearch search);

    /**
     * 条件查询
     * @param search
     * @return
     */
    List<ConfigurationMapping> selectListBySearch(ConfigurationMappingSearch search);

    /**
     * 新增或修改
     * @param configurationMapping
     * @return
     */
    boolean insert(ConfigurationMapping configurationMapping);

}
