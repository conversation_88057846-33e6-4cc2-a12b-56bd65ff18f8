package com.danding.business.core.ares.configuration.service;

import com.danding.business.core.ares.configuration.entity.ConfigurationMeta;
import com.danding.business.core.ares.configuration.search.ConfigurationMetaSearch;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 元数据配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-23
 */
public interface IConfigurationMetaService {


    /**
     * 根据主键id查询
     *
     * @param id
     * @return
     */
    ConfigurationMeta selectById(Serializable id);

    /**
     * 根据条件查询单个
     *
     * @param configurationMetaSearch
     * @return
     */
    ConfigurationMeta selectByConfigurationMetaSearch(ConfigurationMetaSearch configurationMetaSearch);

    /**
     * 根据条件查询
     *
     * @param configurationMetaSearch
     * @return
     */
    List<ConfigurationMeta> selectListByConfigurationMetaSearch(ConfigurationMetaSearch configurationMetaSearch);

    /**
     * 根据主键修改
     *
     * @param configurationMeta
     * @return
     */
    boolean updateById(ConfigurationMeta configurationMeta);

    /**
     * 根据条件修改
     *
     * @param configurationMetaSearch
     * @param configurationMeta
     * @return
     */
    boolean updateListByConfigurationMetaSearch(ConfigurationMetaSearch configurationMetaSearch, ConfigurationMeta configurationMeta);

    /**
     * 主键id批量修改
     *
     * @param configurationMetaList
     * @return
     */
    boolean updateListById(List<ConfigurationMeta> configurationMetaList);

    /**
     * 单条插入
     *
     * @param configurationMeta
     * @return
     */
    boolean insert(ConfigurationMeta configurationMeta);

    /**
     * 批量插入
     *
     * @param configurationMetaList
     * @return
     */
    boolean insertList(List<ConfigurationMeta> configurationMetaList);

    /**
     * 主键id删除
     *
     * @param id
     * @return
     */
    boolean deleteById(Serializable id);

    /**
     * 根据条件删除
     *
     * @param configurationMetaSearch
     * @return
     */
    boolean deleteByConfigurationMetaSearch(ConfigurationMetaSearch configurationMetaSearch);

    /**
     * 主键id批量删除
     *
     * @param idList
     * @return
     */
    boolean deleteByIds(List<Long> idList);
}
