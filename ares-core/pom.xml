<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.danding</groupId>
        <artifactId>business-core-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>${revision}</version>
    <artifactId>ares-core</artifactId>
    <packaging>pom</packaging>
    <properties>
        <dt.component.version>1.2.7-RELEASE</dt.component.version>
        <revision>3.1.19-SNAPSHOT</revision>
    </properties>
    <modules>
        <module>ares-trade-core</module>
        <module>ares-config-core</module>
        <module>ares-configuration-core</module>
        <module>ares-flow-core</module>
        <module>ares-goods-core</module>
        <module>ares-inventory-core</module>
        <module>ares-order-core</module>
        <module>ares-report-core</module>
        <module>ares-risk-core</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>mybatis-tenant-component</artifactId>
            <version>1.2.2-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.shardingsphere</groupId>
                    <artifactId>sharding-core-route</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>danding-sharding-core-route</artifactId>
            <version>4.1.1.002-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-core-route</artifactId>
            <version>9999-exist</version>
        </dependency>

        <!--用于覆盖依赖的外部系统可能存在低版本 -->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>uc-component-dubbo</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>uc-component-core</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>saas-manage-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>dt-component-rocketmq</artifactId>
            <version>1.0.9-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>dt-component-xxljob</artifactId>
            <version>${dt.component.version}</version>
        </dependency>

        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>dt-component-saas</artifactId>
            <version>${dt.component.version}</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>dt-core-tenant</artifactId>
            <version>${dt.component.version}</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <!-- 添加flatten-maven-plugin插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.5.0</version>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
                <inherited>true</inherited>
                <configuration>
                    <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>danding</id>
            <name>danding</name>
            <url>http://mvn.yang800.cn/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
