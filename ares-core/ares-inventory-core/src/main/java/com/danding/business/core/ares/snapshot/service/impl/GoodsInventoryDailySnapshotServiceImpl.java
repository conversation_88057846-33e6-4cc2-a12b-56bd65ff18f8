package com.danding.business.core.ares.snapshot.service.impl;

import com.danding.business.core.ares.snapshot.entity.GoodsInventoryDailySnapshot;
import com.danding.business.core.ares.snapshot.mapper.GoodsInventoryDailySnapshotMapper;
import com.danding.business.core.ares.snapshot.repository.GoodsInventoryDailySnapshotRepository;
import com.danding.business.core.ares.snapshot.search.GoodsInventoryDailySnapshotSearch;
import com.danding.business.core.ares.snapshot.service.IGoodsInventoryDailySnapshotService;
import com.danding.business.core.ares.snapshot.service.wrapper.GoodsInventoryDailySnapshotWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.List;

/**
* <p>
* 货品仓库库存每日快照 服务实现类
* </p>
*
* <AUTHOR>
* @since 2021-12-07
*/
@Service
public class GoodsInventoryDailySnapshotServiceImpl implements IGoodsInventoryDailySnapshotService {

    @Autowired
    private GoodsInventoryDailySnapshotRepository goodsInventoryDailySnapshotRepository;

    @Autowired
    private GoodsInventoryDailySnapshotWrapper goodsInventoryDailySnapshotWrapper;

    @Autowired
    private GoodsInventoryDailySnapshotMapper goodsInventoryDailySnapshotMapper;

    /**
    * 根据主键id查询
    *
    * @param id
    * @return
    */
    @Override
    public GoodsInventoryDailySnapshot selectById(Serializable id) {
        return goodsInventoryDailySnapshotRepository.getById(id);
    }

    /**
    * 根据条件查询单个
    *
    * @param goodsInventoryDailySnapshotSearch
    * @return
    */
    @Override
    public GoodsInventoryDailySnapshot selectBySearch(GoodsInventoryDailySnapshotSearch goodsInventoryDailySnapshotSearch) {
        return goodsInventoryDailySnapshotRepository.getOne(goodsInventoryDailySnapshotWrapper.getQueryWrapper(goodsInventoryDailySnapshotSearch));
    }

    /**
    * 根据条件查询
    *
    * @param goodsInventoryDailySnapshotSearch
    * @return
    */
    @Override
    public List<GoodsInventoryDailySnapshot> selectListBySearch(GoodsInventoryDailySnapshotSearch goodsInventoryDailySnapshotSearch) {
        return goodsInventoryDailySnapshotRepository.list(goodsInventoryDailySnapshotWrapper.getQueryWrapper(goodsInventoryDailySnapshotSearch));
    }

    @Override
    public List<GoodsInventoryDailySnapshot> groupByListBySearch(GoodsInventoryDailySnapshotSearch goodsInventoryDailySnapshotSearch) {
        return goodsInventoryDailySnapshotMapper.getListGroupByLogicWarehouseCode(goodsInventoryDailySnapshotWrapper.getQueryWrapper(goodsInventoryDailySnapshotSearch));
    }

    /**
    * 根据主键修改
    *
    * @param goodsInventoryDailySnapshot
    * @return
    */
    @Override
    public boolean updateById(GoodsInventoryDailySnapshot goodsInventoryDailySnapshot) {
        return goodsInventoryDailySnapshotRepository.updateById(goodsInventoryDailySnapshot);
    }

    /**
    * 根据条件修改
    *
    * @param goodsInventoryDailySnapshotSearch
    * @param goodsInventoryDailySnapshot
    * @return
    */
    @Override
    public boolean updateListBySearch(GoodsInventoryDailySnapshotSearch goodsInventoryDailySnapshotSearch, GoodsInventoryDailySnapshot goodsInventoryDailySnapshot) {
        return goodsInventoryDailySnapshotRepository.update(goodsInventoryDailySnapshot, goodsInventoryDailySnapshotWrapper.getQueryWrapper(goodsInventoryDailySnapshotSearch));
    }

    /**
    * 主键id批量修改
    *
    * @param goodsInventoryDailySnapshotList
    * @return
    */
    @Override
    public boolean updateListById(List<GoodsInventoryDailySnapshot> goodsInventoryDailySnapshotList) {
        return goodsInventoryDailySnapshotRepository.updateBatchById(goodsInventoryDailySnapshotList);
    }

    /**
    * 单条插入
    *
    * @param goodsInventoryDailySnapshot
    * @return
    */
    @Override
    public boolean insert(GoodsInventoryDailySnapshot goodsInventoryDailySnapshot) {
        return goodsInventoryDailySnapshotRepository.save(goodsInventoryDailySnapshot);
    }

    /**
    * 批量插入
    *
    * @param goodsInventoryDailySnapshotList
    * @return
    */
    @Override
    public boolean insertList(List<GoodsInventoryDailySnapshot> goodsInventoryDailySnapshotList) {
    return goodsInventoryDailySnapshotRepository.saveBatch(goodsInventoryDailySnapshotList);
    }

    /**
    * 主键id删除
    *
    * @param id
    * @return
    */
    @Override
    public boolean deleteById(Serializable id) {
        return goodsInventoryDailySnapshotRepository.removeById(id);
    }

    /**
    * 根据条件删除
    *
    * @param goodsInventoryDailySnapshotSearch
    * @return
    */
    @Override
    public boolean deleteBySearch(GoodsInventoryDailySnapshotSearch goodsInventoryDailySnapshotSearch) {
        return goodsInventoryDailySnapshotRepository.remove(goodsInventoryDailySnapshotWrapper.getQueryWrapper(goodsInventoryDailySnapshotSearch));
    }

    /**
    * 主键id批量删除
    *
    * @param idList
    * @return
    */
    @Override
    public boolean deleteByIds(List<Long> idList) {
        return goodsInventoryDailySnapshotRepository.removeByIds(idList);
    }

    @Override
    public int getCountBySearch(GoodsInventoryDailySnapshotSearch goodsInventoryDailySnapshotSearch) {
        return goodsInventoryDailySnapshotRepository.count(goodsInventoryDailySnapshotWrapper.getQueryWrapper(goodsInventoryDailySnapshotSearch));
    }

    @Override
    public GoodsInventoryDailySnapshot getBySnapshotDate(GoodsInventoryDailySnapshotSearch snapshotSearch) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String finishTimeStart = sdf.format(snapshotSearch.getSnapshotDateStart());
        String finishTimeEnd = sdf.format(snapshotSearch.getSnapshotDateEnd());
        return goodsInventoryDailySnapshotMapper.getBySnapshotDate(snapshotSearch.getUserId(), snapshotSearch.getGoodsCode(), snapshotSearch.getLogicWarehouseCode(),
                finishTimeStart, finishTimeEnd);
    }
}