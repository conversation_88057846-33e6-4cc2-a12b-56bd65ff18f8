package com.danding.business.core.ares.inventory.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.danding.business.core.ares.inventory.entity.GoodsInventoryGroupbyLogs;
import com.danding.business.core.ares.inventory.mapper.GoodsInventoryGroupbyLogsMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 货品库存日志明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
@Service
public class GoodsInventoryGroupbyLogsRepository extends ServiceImpl<GoodsInventoryGroupbyLogsMapper, GoodsInventoryGroupbyLogs> implements IService<GoodsInventoryGroupbyLogs> {

}
