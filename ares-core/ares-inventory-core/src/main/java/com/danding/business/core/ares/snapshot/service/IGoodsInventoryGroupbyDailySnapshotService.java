package com.danding.business.core.ares.snapshot.service;

import com.danding.business.core.ares.snapshot.entity.GoodsInventoryGroupbyDailySnapshot;
import com.danding.business.core.ares.snapshot.search.GoodsInventoryGroupbyDailySnapshotSearch;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 货品库存每日快照 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
public interface IGoodsInventoryGroupbyDailySnapshotService {

    /**
    * 根据主键id查询
    *
    * @param id
    * @return
    */
    GoodsInventoryGroupbyDailySnapshot selectById(Serializable id) ;

    /**
    * 根据条件查询单个
    *
    * @param goodsInventoryGroupbyDailySnapshotSearch
    * @return
    */
    GoodsInventoryGroupbyDailySnapshot selectBySearch(GoodsInventoryGroupbyDailySnapshotSearch goodsInventoryGroupbyDailySnapshotSearch);

    /**
    * 根据条件查询
    *
    * @param goodsInventoryGroupbyDailySnapshotSearch
    * @return
    */
    List<GoodsInventoryGroupbyDailySnapshot> selectListBySearch(GoodsInventoryGroupbyDailySnapshotSearch goodsInventoryGroupbyDailySnapshotSearch);

    /**
     * 根据条件获取数量
     * @param snapshotSearch
     * @return
     */
    int getCountBySearch(GoodsInventoryGroupbyDailySnapshotSearch snapshotSearch);

    GoodsInventoryGroupbyDailySnapshot getBySnapshotDate(GoodsInventoryGroupbyDailySnapshotSearch snapshotSearch);

    /**
    * 根据主键修改
    *
    * @param goodsInventoryGroupbyDailySnapshot
    * @return
    */
    boolean updateById(GoodsInventoryGroupbyDailySnapshot goodsInventoryGroupbyDailySnapshot) ;

    /**
    * 根据条件修改
    *
    * @param goodsInventoryGroupbyDailySnapshotSearch
    * @param goodsInventoryGroupbyDailySnapshot
    * @return
    */
    boolean updateListBySearch(GoodsInventoryGroupbyDailySnapshotSearch goodsInventoryGroupbyDailySnapshotSearch, GoodsInventoryGroupbyDailySnapshot goodsInventoryGroupbyDailySnapshot);

    /**
    * 主键id批量修改
    *
    * @param goodsInventoryGroupbyDailySnapshotList
    * @return
    */
    boolean updateListById(List<GoodsInventoryGroupbyDailySnapshot> goodsInventoryGroupbyDailySnapshotList) ;

    /**
    * 单条插入
    *
    * @param goodsInventoryGroupbyDailySnapshot
    * @return
    */
    boolean insert(GoodsInventoryGroupbyDailySnapshot goodsInventoryGroupbyDailySnapshot) ;

    /**
    * 批量插入
    *
    * @param goodsInventoryGroupbyDailySnapshotList
    * @return
    */
    boolean insertList(List<GoodsInventoryGroupbyDailySnapshot> goodsInventoryGroupbyDailySnapshotList) ;
    /**
    * 主键id删除
    *
    * @param id
    * @return
    */
    boolean deleteById(Serializable id) ;

    /**
    * 根据条件删除
    *
    * @param goodsInventoryGroupbyDailySnapshotSearch
    * @return
    */
    boolean deleteBySearch(GoodsInventoryGroupbyDailySnapshotSearch goodsInventoryGroupbyDailySnapshotSearch) ;

    /**
    * 主键id批量删除
    *
    * @param idList
    * @return
    */
    boolean deleteByIds(List<Long> idList) ;
}