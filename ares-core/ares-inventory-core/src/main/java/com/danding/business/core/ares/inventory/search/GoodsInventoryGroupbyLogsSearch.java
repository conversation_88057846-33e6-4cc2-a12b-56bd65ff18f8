package com.danding.business.core.ares.inventory.search;

import com.danding.business.common.ares.enums.goods.GoodsBatchManagement;
import com.danding.business.common.ares.enums.inventory.InventoryBusinessType;
import com.danding.business.common.ares.enums.inventory.InventoryType;
import com.danding.business.common.ares.enums.inventory.InventoryUpdateType;
import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <p>
 * 货品库存日志明细查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */

@Data
@ApiModel(value="GoodsInventoryGroupbyLogs对象", description="货品库存日志明细查询对象")
public class GoodsInventoryGroupbyLogsSearch extends Page {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 查询类型:1 单号，2 关联单号
     */
    private Integer queryType;
    /**
     * 查询条件
     */
    private String queryInfo;

    /**
     * 登录用户ID
     */
    @ApiModelProperty(value = "登录用户ID")
    private Long userId;

    /**
     * 登录用户名
     */
    @ApiModelProperty(value = "登录用户名")
    private String userName;

    /**
     * 货品SKU编码
     */
    @ApiModelProperty(value = "货品SKU编码")
    private String goodsCode;

    /**
     * 货品名称
     */
    @ApiModelProperty(value = "货品名称")
    private String goodsName;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchCode;

    /**
     * 是否开启批次管理
     */
    @ApiModelProperty(value = "是否开启批次管理")
    private GoodsBatchManagement batchManagement;

    /**
     * 逻辑仓库编码
     */
    @ApiModelProperty(value = "逻辑仓库编码")
    private String logicWarehouseCode;

    /**
     * 变动开始时间
     */
    private Long startDate;

    /**
     * 变动结束时间
     */
    private Long endDate;

    /**
     * 相关业务单号
     */
    @ApiModelProperty(value = "相关业务单号")
    private String relatedBusinessNo;

    /**
     * 库存更新类型（锁定，出库，入库等）
     */
    @ApiModelProperty(value = "库存更新类型（锁定，出库，入库等）")
    private InventoryUpdateType updateType;
    private Set<InventoryUpdateType> updateTypeSet;

    /**
     * 业务类型
     */
    private InventoryBusinessType businessType;

    /**
     * 变动哪个类型：正品1，次品2
     */
    @ApiModelProperty(value = "变动哪个类型：正品1，次品2")
    private InventoryType inventoryType;

    /**
     * 是否仅取第一条数据：limit 0,1;
     */
    private boolean queryLast;
}
