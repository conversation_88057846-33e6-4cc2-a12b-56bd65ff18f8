package com.danding.business.core.ares.owner.repository;

import com.danding.business.core.ares.owner.entity.OutOwner;
import com.danding.business.core.ares.owner.mapper.OutOwnerMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 淘天货主绑定 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Service
public class OutOwnerRepository extends ServiceImpl<OutOwnerMapper, OutOwner> implements IService<OutOwner> {

}
