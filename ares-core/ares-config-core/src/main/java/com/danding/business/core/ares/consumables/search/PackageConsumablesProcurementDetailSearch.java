package com.danding.business.core.ares.consumables.search;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 包耗材采购明细列表查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */

@Data
@ApiModel(value="PackageConsumablesProcurementDetail对象", description="包耗材采购明细列表查询对象")
public class PackageConsumablesProcurementDetailSearch extends Page {

    private static final long serialVersionUID = 1L;

    /**
     * 包耗材采购单号
     */
    @ApiModelProperty(value = "包耗材采购单号")
    private String pcProcurementNo;
    private Set<String> pcProcurementNoSet;

    /**
     * 包耗材条码
     */
    private String pcBarcode;
    private List<String> pcBarcodeList;
    private Set<String> pcBarcodeSet;

}
