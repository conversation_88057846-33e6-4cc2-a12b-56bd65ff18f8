package com.danding.business.core.ares.supplier.repository;

import com.danding.business.core.ares.supplier.entity.SupplierGood;
import com.danding.business.core.ares.supplier.mapper.SupplierGoodMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-22
 */
@Service
public class SupplierGoodRepository extends ServiceImpl<SupplierGoodMapper, SupplierGood> implements IService<SupplierGood> {

}
