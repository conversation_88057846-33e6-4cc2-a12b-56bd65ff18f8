package com.danding.business.core.ares.consumables.service.impl;

import com.danding.business.core.ares.consumables.entity.PackageConsumablesMoveOrderDetail;
import com.danding.business.core.ares.consumables.repository.PackageConsumablesMoveOrderDetailRepository;
import com.danding.business.core.ares.consumables.search.PackageConsumablesMoveOrderDetailSearch;
import com.danding.business.core.ares.consumables.service.IPackageConsumablesMoveOrderDetailService;
import com.danding.business.core.ares.consumables.service.wrapper.PackageConsumablesMoveOrderDetailWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;

/**
* <p>
* 包耗材移位单明细 服务实现类
* </p>
*
* <AUTHOR>
* @since 2023-06-15
*/
@Service
public class PackageConsumablesMoveOrderDetailServiceImpl implements IPackageConsumablesMoveOrderDetailService {

    @Autowired
    private PackageConsumablesMoveOrderDetailRepository packageConsumablesMoveOrderDetailRepository;

    @Autowired
    private PackageConsumablesMoveOrderDetailWrapper packageConsumablesMoveOrderDetailWrapper;


    /**
    * 根据主键id查询
    *
    * @param id
    * @return
    */
    @Override
    public PackageConsumablesMoveOrderDetail selectById(Serializable id) {
        return packageConsumablesMoveOrderDetailRepository.getById(id);
    }

    /**
    * 根据条件查询单个
    *
    * @param packageConsumablesMoveOrderDetailSearch
    * @return
    */
    @Override
    public PackageConsumablesMoveOrderDetail selectBySearch(PackageConsumablesMoveOrderDetailSearch packageConsumablesMoveOrderDetailSearch) {
        return packageConsumablesMoveOrderDetailRepository.getOne(packageConsumablesMoveOrderDetailWrapper.getQueryWrapper(packageConsumablesMoveOrderDetailSearch));
    }

    /**
    * 根据条件查询
    *
    * @param packageConsumablesMoveOrderDetailSearch
    * @return
    */
    @Override
    public List<PackageConsumablesMoveOrderDetail> selectListBySearch(PackageConsumablesMoveOrderDetailSearch packageConsumablesMoveOrderDetailSearch) {
        return packageConsumablesMoveOrderDetailRepository.list(packageConsumablesMoveOrderDetailWrapper.getQueryWrapper(packageConsumablesMoveOrderDetailSearch));
    }

    /**
    * 根据主键修改
    *
    * @param packageConsumablesMoveOrderDetail
    * @return
    */
    @Override
    public boolean updateById(PackageConsumablesMoveOrderDetail packageConsumablesMoveOrderDetail) {
        return packageConsumablesMoveOrderDetailRepository.updateById(packageConsumablesMoveOrderDetail);
    }

    /**
    * 根据条件修改
    *
    * @param packageConsumablesMoveOrderDetailSearch
    * @param packageConsumablesMoveOrderDetail
    * @return
    */
    @Override
    public boolean updateListBySearch(PackageConsumablesMoveOrderDetailSearch packageConsumablesMoveOrderDetailSearch, PackageConsumablesMoveOrderDetail packageConsumablesMoveOrderDetail) {
        return packageConsumablesMoveOrderDetailRepository.update(packageConsumablesMoveOrderDetail, packageConsumablesMoveOrderDetailWrapper.getQueryWrapper(packageConsumablesMoveOrderDetailSearch));
    }

    /**
    * 主键id批量修改
    *
    * @param packageConsumablesMoveOrderDetailList
    * @return
    */
    @Override
    public boolean updateListById(List<PackageConsumablesMoveOrderDetail> packageConsumablesMoveOrderDetailList) {
        return packageConsumablesMoveOrderDetailRepository.updateBatchById(packageConsumablesMoveOrderDetailList);
    }

    /**
    * 单条插入
    *
    * @param packageConsumablesMoveOrderDetail
    * @return
    */
    @Override
    public boolean insert(PackageConsumablesMoveOrderDetail packageConsumablesMoveOrderDetail) {
        return packageConsumablesMoveOrderDetailRepository.save(packageConsumablesMoveOrderDetail);
    }

    /**
    * 批量插入
    *
    * @param packageConsumablesMoveOrderDetailList
    * @return
    */
    @Override
    public boolean insertList(List<PackageConsumablesMoveOrderDetail> packageConsumablesMoveOrderDetailList) {
    return packageConsumablesMoveOrderDetailRepository.saveBatch(packageConsumablesMoveOrderDetailList);
    }

    /**
    * 主键id删除
    *
    * @param id
    * @return
    */
    @Override
    public boolean deleteById(Serializable id) {
        return packageConsumablesMoveOrderDetailRepository.removeById(id);
    }

    /**
    * 根据条件删除
    *
    * @param packageConsumablesMoveOrderDetailSearch
    * @return
    */
    @Override
    public boolean deleteBySearch(PackageConsumablesMoveOrderDetailSearch packageConsumablesMoveOrderDetailSearch) {
        return packageConsumablesMoveOrderDetailRepository.remove(packageConsumablesMoveOrderDetailWrapper.getQueryWrapper(packageConsumablesMoveOrderDetailSearch));
    }

    /**
    * 主键id批量删除
    *
    * @param idList
    * @return
    */
    @Override
    public boolean deleteByIds(List<Long> idList) {
        return packageConsumablesMoveOrderDetailRepository.removeByIds(idList);
    }
}
