package com.danding.business.core.ares.consumables.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.danding.component.common.base.DO.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 包耗材盘点
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_material_inventory_check")
public class MaterialInventoryCheck extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户id
     */
    private Long userId;

    /**
     * 盘点任务编号
     */
    private String inventoryCheckJob;

    /**
     * 仓库
     */
    private String warehouseCode;

    /**
     * 盘点单号
     */
    private String inventoryCheckCode;

    /**
     * 盘点区域
     */
    private String locationType;

    /**
     * 状态
     */
    private String status;

    /**
     * 盘点工具
     */
    private String checkTool;

    /**
     * 盘点方式
     */
    private String checkType;

    /**
     * 计划品类数
     */
    private BigDecimal planSkuQty;

    /**
     * 计划账面数
     */
    private BigDecimal planQty;

    /**
     * 实盘品类数
     */
    private BigDecimal realSkuQty;

    /**
     * 实盘数
     */
    private BigDecimal realQty;

    /**
     * 创建人
     */
    private String createByName;

    /**
     * 完成盘点人
     */
    private String checkCompleteUser;

    /**
     * 盘点完成时间
     */
    private Long checkCompleteTime;

    /**
     * 关联调整单
     */
    private String adjustCode;


}
