package com.danding.business.core.ares.consumables.repository;

import com.danding.business.core.ares.consumables.entity.PackageConsumablesShelf;
import com.danding.business.core.ares.consumables.mapper.PackageConsumablesShelfMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 包耗材上架单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@Service
public class PackageConsumablesShelfRepository extends ServiceImpl<PackageConsumablesShelfMapper, PackageConsumablesShelf> implements IService<PackageConsumablesShelf> {

}
