package com.danding.business.core.ares.consumables.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.danding.business.common.ares.enums.packageConsumables.MaterialRequisitionStatusEnum;
import com.danding.component.common.base.DO.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 领用单列表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_material_requisition")
public class MaterialRequisition extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 物料领用单号
     */
    private String materialRequisitionNo;

    /**
     * 下游物料领用单号
     */
    private String downstreamNo;

    /**
     * 外部实体编码
     */
    private String warehouseCode;

    /**
     * 实体仓名称
     */
    private String warehouseName;

    /**
     * 状态: 1 已创建, 10 待发放, 20 发放完成, 30 取消
     */
    private MaterialRequisitionStatusEnum status;

    /**
     * 种类数量
     */
    private Integer speciesQuantity;

    /**
     * 计划发放数量
     */
    private BigDecimal planQuantity;

    /**
     * 实际发放数量
     */
    private BigDecimal actualQuantity;

    /**
     * 操作人(发放人)
     */
    private String operator;

    private Long completionTime;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 唯一索引
     */
    private String unIndex;

}
