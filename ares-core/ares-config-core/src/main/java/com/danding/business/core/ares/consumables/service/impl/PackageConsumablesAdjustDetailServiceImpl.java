package com.danding.business.core.ares.consumables.service.impl;

import com.danding.business.core.ares.consumables.entity.PackageConsumablesAdjustDetail;
import com.danding.business.core.ares.consumables.repository.PackageConsumablesAdjustDetailRepository;
import com.danding.business.core.ares.consumables.search.PackageConsumablesAdjustDetailSearch;
import com.danding.business.core.ares.consumables.service.IPackageConsumablesAdjustDetailService;
import com.danding.business.core.ares.consumables.service.wrapper.PackageConsumablesAdjustDetailWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;

/**
* <p>
* 调整单详细表 服务实现类
* </p>
*
* <AUTHOR>
* @since 2023-05-26
*/
@Service
public class PackageConsumablesAdjustDetailServiceImpl implements IPackageConsumablesAdjustDetailService {

    @Autowired
    private PackageConsumablesAdjustDetailRepository packageConsumablesAdjustDetailRepository;

    @Autowired
    private PackageConsumablesAdjustDetailWrapper packageConsumablesAdjustDetailWrapper;


    /**
    * 根据主键id查询
    *
    * @param id
    * @return
    */
    @Override
    public PackageConsumablesAdjustDetail selectById(Serializable id) {
        return packageConsumablesAdjustDetailRepository.getById(id);
    }

    /**
    * 根据条件查询单个
    *
    * @param packageConsumablesAdjustDetailSearch
    * @return
    */
    @Override
    public PackageConsumablesAdjustDetail selectBySearch(PackageConsumablesAdjustDetailSearch packageConsumablesAdjustDetailSearch) {
        return packageConsumablesAdjustDetailRepository.getOne(packageConsumablesAdjustDetailWrapper.getQueryWrapper(packageConsumablesAdjustDetailSearch));
    }

    /**
    * 根据条件查询
    *
    * @param packageConsumablesAdjustDetailSearch
    * @return
    */
    @Override
    public List<PackageConsumablesAdjustDetail> selectListBySearch(PackageConsumablesAdjustDetailSearch packageConsumablesAdjustDetailSearch) {
        return packageConsumablesAdjustDetailRepository.list(packageConsumablesAdjustDetailWrapper.getQueryWrapper(packageConsumablesAdjustDetailSearch));
    }

    /**
    * 根据主键修改
    *
    * @param packageConsumablesAdjustDetail
    * @return
    */
    @Override
    public boolean updateById(PackageConsumablesAdjustDetail packageConsumablesAdjustDetail) {
        return packageConsumablesAdjustDetailRepository.updateById(packageConsumablesAdjustDetail);
    }

    /**
    * 根据条件修改
    *
    * @param packageConsumablesAdjustDetailSearch
    * @param packageConsumablesAdjustDetail
    * @return
    */
    @Override
    public boolean updateListBySearch(PackageConsumablesAdjustDetailSearch packageConsumablesAdjustDetailSearch, PackageConsumablesAdjustDetail packageConsumablesAdjustDetail) {
        return packageConsumablesAdjustDetailRepository.update(packageConsumablesAdjustDetail, packageConsumablesAdjustDetailWrapper.getQueryWrapper(packageConsumablesAdjustDetailSearch));
    }

    /**
    * 主键id批量修改
    *
    * @param packageConsumablesAdjustDetailList
    * @return
    */
    @Override
    public boolean updateListById(List<PackageConsumablesAdjustDetail> packageConsumablesAdjustDetailList) {
        return packageConsumablesAdjustDetailRepository.updateBatchById(packageConsumablesAdjustDetailList);
    }

    /**
    * 单条插入
    *
    * @param packageConsumablesAdjustDetail
    * @return
    */
    @Override
    public boolean insert(PackageConsumablesAdjustDetail packageConsumablesAdjustDetail) {
        return packageConsumablesAdjustDetailRepository.save(packageConsumablesAdjustDetail);
    }

    /**
    * 批量插入
    *
    * @param packageConsumablesAdjustDetailList
    * @return
    */
    @Override
    public boolean insertList(List<PackageConsumablesAdjustDetail> packageConsumablesAdjustDetailList) {
    return packageConsumablesAdjustDetailRepository.saveBatch(packageConsumablesAdjustDetailList);
    }

    /**
    * 主键id删除
    *
    * @param id
    * @return
    */
    @Override
    public boolean deleteById(Serializable id) {
        return packageConsumablesAdjustDetailRepository.removeById(id);
    }

    /**
    * 根据条件删除
    *
    * @param packageConsumablesAdjustDetailSearch
    * @return
    */
    @Override
    public boolean deleteBySearch(PackageConsumablesAdjustDetailSearch packageConsumablesAdjustDetailSearch) {
        return packageConsumablesAdjustDetailRepository.remove(packageConsumablesAdjustDetailWrapper.getQueryWrapper(packageConsumablesAdjustDetailSearch));
    }

    /**
    * 主键id批量删除
    *
    * @param idList
    * @return
    */
    @Override
    public boolean deleteByIds(List<Long> idList) {
        return packageConsumablesAdjustDetailRepository.removeByIds(idList);
    }
}
