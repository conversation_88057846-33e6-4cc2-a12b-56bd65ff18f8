package com.danding.business.core.ares.customer.service.wrapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.business.core.ares.customer.entity.CustomerAccount;
import com.danding.business.core.ares.customer.search.CustomerAccountSearch;
import com.danding.component.common.base.DO.BaseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-22
 */
@Component
public class CustomerAccountWrapper {

    public LambdaQueryWrapper<CustomerAccount> getQueryWrapper(CustomerAccountSearch customerAccountSearch) {
        LambdaQueryWrapper<CustomerAccount> queryWrapper = new LambdaQueryWrapper<CustomerAccount>();
        queryWrapper.orderByDesc(BaseEntity::getCreateTime);
        if (!StringUtils.isEmpty(customerAccountSearch.getCustomerCode())) {
            queryWrapper.eq(CustomerAccount::getCustomerCode, customerAccountSearch.getCustomerCode());
        }

        if (!StringUtils.isEmpty(customerAccountSearch.getAccountBankNum())) {
            queryWrapper.eq(CustomerAccount::getAccountBankNum, customerAccountSearch.getAccountBankNum());
        }
        return queryWrapper;
    }
}
