package com.danding.business.core.ares.consumables.service.wrapper;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.business.core.ares.consumables.entity.MaterialDifference;
import com.danding.business.core.ares.consumables.entity.PackageConsumablesInventoryFlow;
import com.danding.business.core.ares.consumables.search.MaterialDifferenceSearch;
import com.danding.component.common.base.DO.BaseEntity;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <p>
 * 每日包材差异 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@Component
public class MaterialDifferenceWrapper {

    public LambdaQueryWrapper<MaterialDifference> getQueryWrapper(MaterialDifferenceSearch materialDifferenceSearch){
        LambdaQueryWrapper<MaterialDifference> queryWrapper = new LambdaQueryWrapper<MaterialDifference>();
        queryWrapper.orderByDesc(BaseEntity::getId);
        queryWrapper.eq(StrUtil.isNotBlank(materialDifferenceSearch.getWarehouseCode()), MaterialDifference::getWarehouseCode, materialDifferenceSearch.getWarehouseCode());
        queryWrapper.eq(StrUtil.isNotBlank(materialDifferenceSearch.getRealUseWarehouseCode()), MaterialDifference::getRealUseWarehouse, materialDifferenceSearch.getRealUseWarehouseCode());
        queryWrapper.eq(StrUtil.isNotBlank(materialDifferenceSearch.getMaterialCode()), MaterialDifference::getMaterialCode, materialDifferenceSearch.getMaterialCode());
        queryWrapper.in(CollectionUtil.isNotEmpty(materialDifferenceSearch.getMaterialCodeList()), MaterialDifference::getMaterialCode, materialDifferenceSearch.getMaterialCodeList());
        queryWrapper.eq(StrUtil.isNotBlank(materialDifferenceSearch.getBillNo()), MaterialDifference::getBillNo, materialDifferenceSearch.getBillNo())
                 .ge(Objects.nonNull(materialDifferenceSearch.getCreateTimeStart()), MaterialDifference::getCreateTime, materialDifferenceSearch.getCreateTimeStart())
                .le(Objects.nonNull(materialDifferenceSearch.getCreateTimeEnd()), MaterialDifference::getCreateTime, materialDifferenceSearch.getCreateTimeEnd());
        queryWrapper.in(CollectionUtil.isNotEmpty(materialDifferenceSearch.getBillNoList()), MaterialDifference::getBillNo, materialDifferenceSearch.getBillNoList());


        return queryWrapper;
    }
}
