package com.danding.business.core.ares.level.service.impl;


import com.danding.business.core.ares.level.entity.CustomerLevel;
import com.danding.business.core.ares.level.search.CustomerLevelSearch;
import com.danding.business.core.ares.level.service.ICustomerLevelService;
import com.danding.business.core.ares.level.repository.CustomerLevelRepository;
import com.danding.business.core.ares.level.service.wrapper.CustomerLevelWrapper;
import com.danding.business.core.ares.logicwarehouse.service.wrapper.LogicWarehouseWrapper;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.soul.client.common.result.RpcResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@Service
public class CustomerLevelServiceImpl implements ICustomerLevelService {

    @Autowired
    private CustomerLevelRepository customerLevelRepository;


    @Autowired
    private CustomerLevelWrapper customerLevelWrapper;

    @Override
    public Boolean mixPriceWithGood(CustomerLevel customerLevel) {
        customerLevelRepository.save(customerLevel);
        return true;
    }

    @Override
    public List<CustomerLevel> listLevelByUserId() {
        CustomerLevelSearch customerLevelSearch = new CustomerLevelSearch();
        customerLevelSearch.setUserId(SimpleUserHelper.getUserId());
        List<CustomerLevel> customerLevelList = customerLevelRepository.list(customerLevelWrapper.listLevelByUserId(customerLevelSearch));
        return customerLevelList;
    }

    @Override
    public List<CustomerLevel> listLevelByPage(CustomerLevelSearch customerLevelSearch) {
        customerLevelSearch.setUserId(SimpleUserHelper.getUserId());
        List<CustomerLevel> customerLevelList = customerLevelRepository.list(customerLevelWrapper.listLevelByPage(customerLevelSearch));
        return customerLevelList;
    }

    @Override
    public List<CustomerLevel> listLevelBySearch(CustomerLevelSearch customerLevelSearch) {
        List<CustomerLevel> customerLevelList = customerLevelRepository.list(customerLevelWrapper.listLevelBySearch(customerLevelSearch));
        return customerLevelList;
    }

    @Override
    public String getLevelDiscountByLevel(CustomerLevelSearch customerLevelSearch) {
        CustomerLevel customerLevel = customerLevelRepository.getOne(customerLevelWrapper.getQueryWrapper(customerLevelSearch));
        if (!StringUtils.isEmpty(customerLevel)) {
            return customerLevel.getLevelDiscount();
        } else {
            return "0";
        }
    }

    @Override
    public CustomerLevel selectOneBySearch(CustomerLevelSearch customerLevelSearch) {
        CustomerLevel customerLevel = customerLevelRepository.getOne(customerLevelWrapper.getQueryWrapper(customerLevelSearch));
        return customerLevel;
    }


    @Override
    public Boolean updateById(CustomerLevel customerLevel) {
      return   customerLevelRepository.updateById(customerLevel);
    }
}
