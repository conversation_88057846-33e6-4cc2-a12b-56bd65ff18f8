package com.danding.business.core.ares.ewem.search;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import java.util.List;

/**
 * <p>
 * 批次表查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */

@Data
@ApiModel(value="EwemBatch对象", description="批次表查询对象")
public class EwemBatchSearch extends Page {

    private static final long serialVersionUID = 1L;
    private Long id;

    private  List<Long> batchIdList;
    /**
     * 批号
     */
    private String number;

    /**
     * 内部sku
     */
    private String goodsCode;

    /**
     * 状态（0正常 1停用）
     */
    private Integer status;

    /**
     * 用户id
     */
    private Long userId;
}
