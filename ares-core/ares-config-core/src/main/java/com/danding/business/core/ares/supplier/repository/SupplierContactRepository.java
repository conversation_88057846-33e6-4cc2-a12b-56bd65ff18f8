package com.danding.business.core.ares.supplier.repository;

import com.danding.business.core.ares.supplier.entity.SupplierContact;
import com.danding.business.core.ares.supplier.mapper.SupplierContactMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-22
 */
@Service
public class SupplierContactRepository extends ServiceImpl<SupplierContactMapper, SupplierContact> implements IService<SupplierContact> {

}
