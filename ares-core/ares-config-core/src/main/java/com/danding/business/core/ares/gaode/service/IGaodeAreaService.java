package com.danding.business.core.ares.gaode.service;

import com.danding.business.core.ares.gaode.entity.GaodeArea;
import com.danding.business.core.ares.gaode.search.GaodeAreaSearch;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-28
 */
public interface IGaodeAreaService {


    /**
    * 根据主键id查询
    *
    * @param id
    * @return
    */
    public GaodeArea selectById(Serializable id) ;

    /**
    * 根据条件查询单个
    *
    * @param gaodeAreaSearch
    * @return
    */
    GaodeArea selectBySearch(GaodeAreaSearch gaodeAreaSearch);

    /**
    * 根据条件查询
    *
    * @param gaodeAreaSearch
    * @return
    */
    List<GaodeArea> selectListBySearch(GaodeAreaSearch gaodeAreaSearch);

    /**
    * 根据主键修改
    *
    * @param gaodeArea
    * @return
    */
    boolean updateById(GaodeArea gaodeArea) ;

    /**
    * 根据条件修改
    *
    * @param gaodeAreaSearch
    * @param gaodeArea
    * @return
    */
    boolean updateListBySearch(GaodeAreaSearch gaodeAreaSearch, GaodeArea gaodeArea);

    /**
    * 主键id批量修改
    *
    * @param gaodeAreaList
    * @return
    */
    boolean updateListById(List<GaodeArea> gaodeAreaList) ;

    /**
    * 单条插入
    *
    * @param gaodeArea
    * @return
    */
    boolean insert(GaodeArea gaodeArea) ;

    /**
    * 批量插入
    *
    * @param gaodeAreaList
    * @return
    */
    boolean insertList(List<GaodeArea> gaodeAreaList) ;
    /**
    * 主键id删除
    *
    * @param id
    * @return
    */
    boolean deleteById(Serializable id) ;

    /**
    * 根据条件删除
    *
    * @param gaodeAreaSearch
    * @return
    */
    boolean deleteBySearch(GaodeAreaSearch gaodeAreaSearch) ;

    /**
    * 主键id批量删除
    *
    * @param idList
    * @return
    */
    boolean deleteByIds(List<Long> idList) ;

}
