package com.danding.business.core.ares.sequence.mapper;

import com.danding.business.core.ares.sequence.entity.SysSequence;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 单号自增表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
public interface SysSequenceMapper extends BaseMapper<SysSequence> {

    int getSeq(@Param("sequenceKey")String sequenceKey);

    boolean resetSeq(@Param("sequenceKey")String sequenceKey, @Param("sequenceId")Integer sequenceId);

    boolean addSeq(@Param("sequenceKey")String sequenceKey, @Param("sequenceId")Integer sequenceId);

}
