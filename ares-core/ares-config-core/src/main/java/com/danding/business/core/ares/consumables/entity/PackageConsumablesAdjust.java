package com.danding.business.core.ares.consumables.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.danding.component.common.base.DO.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 包耗材调整单主表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_package_consumables_adjust")
public class PackageConsumablesAdjust extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 调整单号
     */
    private String adjustNo;

    /**
     * 关联的任务单号
     */
    private String relatedNo;

    /**
     * 实体仓
     */
    private String warehouseCode;

    /**
     * 实体仓名称
     */
    private String warehouseName;

    /**
     * 调整单状态
     */
    private Integer adjustStatus;

    /**
     * 操作信息
     */
    private String operatorRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 差异原因
     */
    private String differenceReason;

    /**
     * 差异数
     */
    private BigDecimal differenceQty;

    /**
     * 操作时间
     */
    private Long operateTime;

    /**
     * 创建人姓名
     */
    private String createByName;

    private String operator;

    /**
     * 租户id
     */
    private Long tenantId;


}
