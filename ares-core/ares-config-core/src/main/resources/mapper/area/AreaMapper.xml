<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.business.core.ares.area.mapper.AreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.danding.business.core.ares.area.entity.Area">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="area_code" property="areaCode" />
        <result column="area_name" property="areaName" />
        <result column="level" property="level" />
        <result column="city_code" property="cityCode" />
        <result column="center" property="center" />
        <result column="parent_id" property="parentId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        update_by,
        deleted,
        version,
        create_time,
        update_time,
        area_code, area_name, level, city_code, center, parent_id
    </sql>

</mapper>
