<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.business.core.ares.consumables.mapper.MaterialDifferenceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.danding.business.core.ares.consumables.entity.MaterialDifference">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
        <result column="version" property="version" />
        <result column="warehouse_code" property="warehouseCode" />
        <result column="material_code" property="materialCode" />
        <result column="bill_no" property="billNo" />
        <result column="bill_type" property="billType" />
        <result column="difference_qty" property="differenceQty" />
        <result column="user_id" property="userId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        create_by,
        update_by,
        deleted,
        version,
        warehouse_code, material_code, bill_no, bill_type, difference_qty, user_id
    </sql>

</mapper>
