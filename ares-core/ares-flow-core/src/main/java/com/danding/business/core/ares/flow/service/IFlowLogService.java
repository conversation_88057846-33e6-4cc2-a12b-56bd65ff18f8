package com.danding.business.core.ares.flow.service;


import com.danding.business.core.ares.flow.entity.FlowLog;
import com.danding.business.core.ares.flow.search.FlowLogSearch;

import java.util.List;

/**
 * <p>
 * 审核日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
public interface IFlowLogService {

    /**
     * add flow log
     *
     * @param flowLog {@link FlowLog}
     * @return {@link Boolean}
     */
    Boolean addFlowLog(FlowLog flowLog);

    /**
     * 查询
     *
     * @param flowLogSearch
     * @return
     */
    List<FlowLog> listByFlowLogSearch(FlowLogSearch flowLogSearch);
}
