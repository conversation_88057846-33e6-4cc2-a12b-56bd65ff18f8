package com.danding.business.core.ares.flow.search;

import com.danding.business.common.ares.enums.common.DocumentType;
import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 待办查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */

@Data
@ApiModel(value = "FlowTask对象", description = "待办查询对象")
public class FlowTaskSearch extends Page {


    private static final long serialVersionUID = 1L;

    private Long id;

    private Long flowInstanceId;

    /**
     * 操作人
     */
    private Long operator;

    private String businessId;

    /**
     * 单据类型
     */
    private DocumentType documentType;
    private List<DocumentType> notInDocumentTypeList;

    /**
     * 单据类型列表
     */
    private List<DocumentType> typeList;
}
