package com.danding.business.core.ares.newflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.danding.business.common.ares.enums.newflow.FlowTypeEnum;
import com.danding.component.common.base.DO.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 审核类型表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_new_flow_type")
public class NewFlowType extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 商家ID
     */
    private Long userId;

    /**
     * 审核类型
     */
    private FlowTypeEnum flowType;

    /**
     * 历史标志1最新，0历史
     */
    private Boolean ifNew;


}
