package com.danding.business.core.ares.flow.service.wrapper;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.business.core.ares.flow.entity.TargetFlow;
import com.danding.business.core.ares.flow.search.TargetFlowSearch;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 目标流程配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@Component
public class TargetFlowWrapper {

    public LambdaQueryWrapper<TargetFlow> getQueryWrapper(TargetFlowSearch targetFlowSearch) {
        LambdaQueryWrapper<TargetFlow> queryWrapper = new LambdaQueryWrapper<TargetFlow>();
        queryWrapper.eq(targetFlowSearch.getId() != null, TargetFlow::getId, targetFlowSearch.getId());
        queryWrapper.eq(targetFlowSearch.getUserId() != null, TargetFlow::getUserId, targetFlowSearch.getUserId());
        queryWrapper.eq(targetFlowSearch.getDocumentType() != null, TargetFlow::getDocumentType, targetFlowSearch.getDocumentType());
        queryWrapper.eq(targetFlowSearch.getNewest() != null, TargetFlow::getNewest, targetFlowSearch.getNewest());
        queryWrapper.eq(targetFlowSearch.getFlowId() != null, TargetFlow::getFlowId, targetFlowSearch.getFlowId());
        queryWrapper.in(CollectionUtil.isNotEmpty(targetFlowSearch.getFlowIds()), TargetFlow::getFlowId, targetFlowSearch.getFlowIds());
        return queryWrapper;
    }
}
