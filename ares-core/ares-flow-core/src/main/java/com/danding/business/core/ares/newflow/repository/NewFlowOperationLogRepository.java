package com.danding.business.core.ares.newflow.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.danding.business.core.ares.newflow.entity.NewFlowOperationLog;
import com.danding.business.core.ares.newflow.mapper.NewFlowOperationLogMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 审核操作日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */
@Service
public class NewFlowOperationLogRepository extends ServiceImpl<NewFlowOperationLogMapper, NewFlowOperationLog> implements IService<NewFlowOperationLog> {

}
