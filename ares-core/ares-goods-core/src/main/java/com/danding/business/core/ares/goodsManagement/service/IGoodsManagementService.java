package com.danding.business.core.ares.goodsManagement.service;

import com.danding.business.core.ares.goodsManagement.entity.GoodsManagement;
import com.danding.business.core.ares.goodsManagement.search.GoodsManagementSearch;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 货品管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-06
 */
public interface IGoodsManagementService {


    /**
    * 根据主键id查询
    *
    * @param id
    * @return
    */
    public GoodsManagement selectById(Serializable id) ;

    /**
    * 根据条件查询单个
    *
    * @param goodsManagementSearch
    * @return
    */
    GoodsManagement selectBySearch(GoodsManagementSearch goodsManagementSearch);

    /**
    * 根据条件查询
    *
    * @param goodsManagementSearch
    * @return
    */
    List<GoodsManagement> selectListBySearch(GoodsManagementSearch goodsManagementSearch);

    List<GoodsManagement> selectListCustomBySearch(GoodsManagementSearch goodsManagementSearch);

    /**
    * 根据主键修改
    *
    * @param goodsManagement
    * @return
    */
    boolean updateById(GoodsManagement goodsManagement) ;

    boolean updateExternalCodeById(Long id, String externalCode);

    boolean updateExternalSkuById(Long id, String externalSku);

    boolean updateMaterialCodeByOwnerCodeAndGoodsCode(GoodsManagementSearch goodsManagementSearch, String materialCode);

    /**
    * 根据条件修改
    *
    * @param goodsManagementSearch
    * @param goodsManagement
    * @return
    */
    boolean updateListBySearch(GoodsManagementSearch goodsManagementSearch, GoodsManagement goodsManagement);

    /**
    * 主键id批量修改
    *
    * @param goodsManagementList
    * @return
    */
    boolean updateListById(List<GoodsManagement> goodsManagementList) ;




    /**
    * 单条插入
    *
    * @param goodsManagement
    * @return
    */
    boolean insert(GoodsManagement goodsManagement) ;

    /**
    * 批量插入
    *
    * @param goodsManagementList
    * @return
    */
    boolean insertList(List<GoodsManagement> goodsManagementList) ;
    /**
    * 主键id删除
    *
    * @param id
    * @return
    */
    boolean deleteById(Serializable id) ;

    /**
    * 根据条件删除
    *
    * @param goodsManagementSearch
    * @return
    */
    boolean deleteBySearch(GoodsManagementSearch goodsManagementSearch) ;

    /**
    * 主键id批量删除
    *
    * @param idList
    * @return
    */
    boolean deleteByIds(List<Long> idList) ;


    /**
     * 可查詢刪除
     * @param goodsManagementSearch
     * @return
     */
    public GoodsManagement selectBySku(String sku, String ownerCode,String warehouseCode);

    /**
     * 更新空的materialCode用与任务补充
     * @return
     */
    public boolean updateMaterialCodeBySql();


}
