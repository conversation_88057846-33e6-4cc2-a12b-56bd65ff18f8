package com.danding.business.core.ares.record.service;

import com.danding.business.core.ares.record.entity.GoodsRecord;
import com.danding.business.core.ares.record.entity.GoodsRecordPort;
import com.danding.business.core.ares.record.search.GoodsRecordPortSearch;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 货品备案口岸关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-07
 */
public interface IGoodsRecordPortService {

    /**
     * 根据主键id查询
     *
     * @param id
     * @return
     */
    public GoodsRecordPort selectById(Serializable id);

    /**
     * 根据条件查询单个
     *
     * @param goodsRecordPortSearch
     * @return
     */
    GoodsRecordPort selectBySearch(GoodsRecordPortSearch goodsRecordPortSearch);

    /**
     * 根据条件查询
     *
     * @param goodsRecordPortSearch
     * @return
     */
    List<GoodsRecordPort> selectListBySearch(GoodsRecordPortSearch goodsRecordPortSearch);

    /**
     * 根据主键修改
     *
     * @param goodsRecordPort
     * @return
     */
    boolean updateById(GoodsRecordPort goodsRecordPort);

    /**
     * 根据条件修改
     *
     * @param goodsRecordPortSearch
     * @param goodsRecordPort
     * @return
     */
    boolean updateListBySearch(GoodsRecordPortSearch goodsRecordPortSearch, GoodsRecordPort goodsRecordPort);

    /**
     * 主键id批量修改
     *
     * @param goodsRecordPortList
     * @return
     */
    boolean updateListById(List<GoodsRecordPort> goodsRecordPortList);

    /**
     * 单条插入
     *
     * @param goodsRecordPort
     * @return
     */
    boolean insert(GoodsRecordPort goodsRecordPort);

    /**
     * 批量插入备案
     *
     * @param goodsRecordPortList
     * @return
     */
    Integer insertBatchSomeColumn(List<GoodsRecordPort> goodsRecordPortList);

    /**
     * 批量插入
     *
     * @param goodsRecordPortList
     * @return
     */
    boolean insertList(List<GoodsRecordPort> goodsRecordPortList);

    /**
     * 主键id删除
     *
     * @param id
     * @return
     */
    boolean deleteById(Serializable id);

    /**
     * 根据条件删除
     *
     * @param goodsRecordPortSearch
     * @return
     */
    boolean deleteBySearch(GoodsRecordPortSearch goodsRecordPortSearch);

    /**
     * 主键id批量删除
     *
     * @param idList
     * @return
     */
    boolean deleteByIds(List<Long> idList);

    List<GoodsRecordPort> getPortGroupByWrapper(GoodsRecordPortSearch goodsRecordPortSearch);

    /**
     * 更新对应的 GoodsRecordPort 里的 GoodsRecordJson 字段
     *
     * @param parameter
     * @return
     */
    boolean updateGoodsRecordJson(GoodsRecord parameter);
}
