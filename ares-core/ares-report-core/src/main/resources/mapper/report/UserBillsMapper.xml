<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.business.core.ares.report.mapper.UserBillsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.danding.business.core.ares.report.entity.UserBills">
        <result column="id" property="id"/>
        <result column="version" property="version"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="deleted" property="deleted"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="bill_no" property="billNo"/>
        <result column="bill_type" property="billType"/>
        <result column="bill_business_type" property="billBusinessType"/>
        <result column="bill_status" property="billStatus"/>
        <result column="bill_with_type" property="billWithType"/>
        <result column="bill_with_code" property="billWithCode"/>
        <result column="bill_with_name" property="billWithName"/>
        <result column="bill_amount" property="billAmount"/>
        <result column="bill_finished_amount" property="billFinishedAmount"/>
        <result column="bill_currency" property="billCurrency"/>
        <result column="currency_rate" property="currencyRate"/>
        <result column="related_bill_no" property="relatedBillNo"/>
        <result column="related_user_id" property="relatedUserId"/>
        <result column="related_user_name" property="relatedUserName"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        version,
        create_time,
        update_time,
        create_by,
        update_by,
        deleted,
        user_id, user_name, bill_no, bill_type, bill_business_type, bill_status, bill_with_type, bill_with_code, bill_with_name, bill_amount, bill_finished_amount, bill_currency, currency_rate, related_bill_no, related_user_id, related_user_name, remark
    </sql>

</mapper>
