package com.danding.business.core.ares.report.service;

import com.danding.business.core.ares.report.entity.GoodsPriceStockItemsHistory;
import com.danding.business.core.ares.report.search.GoodsPriceStockItemsHistorySearch;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 价盘子表历史更新记录 服务类
 * </p>
 *
 * <AUTHOR> @since 2020-12-22
 */
public interface IGoodsPriceStockItemsHistoryService {


    /**
     * 根据主键id查询
     *
     * @param id
     * @return
     */
    public GoodsPriceStockItemsHistory selectById(Serializable id);

    /**
     * 根据条件查询单个
     *
     * @param goodsPriceStockItemsHistorySearch
     * @return
     */
    GoodsPriceStockItemsHistory selectBySearch(GoodsPriceStockItemsHistorySearch goodsPriceStockItemsHistorySearch);

    /**
     * 根据条件查询
     *
     * @param goodsPriceStockItemsHistorySearch
     * @return
     */
    List<GoodsPriceStockItemsHistory> selectListBySearch(GoodsPriceStockItemsHistorySearch goodsPriceStockItemsHistorySearch);

    /**
     * 根据主键修改
     *
     * @param goodsPriceStockItemsHistory
     * @return
     */
    boolean updateById(GoodsPriceStockItemsHistory goodsPriceStockItemsHistory);

    /**
     * 根据条件修改
     *
     * @param goodsPriceStockItemsHistorySearch
     * @param goodsPriceStockItemsHistory
     * @return
     */
    boolean updateListBySearch(GoodsPriceStockItemsHistorySearch goodsPriceStockItemsHistorySearch, GoodsPriceStockItemsHistory goodsPriceStockItemsHistory);

    /**
     * 主键id批量修改
     *
     * @param goodsPriceStockItemsHistoryList
     * @return
     */
    boolean updateListById(List<GoodsPriceStockItemsHistory> goodsPriceStockItemsHistoryList);

    /**
     * 单条插入
     *
     * @param goodsPriceStockItemsHistory
     * @return
     */
    boolean insert(GoodsPriceStockItemsHistory goodsPriceStockItemsHistory);

    /**
     * 批量插入
     *
     * @param goodsPriceStockItemsHistoryList
     * @return
     */
    boolean insertList(List<GoodsPriceStockItemsHistory> goodsPriceStockItemsHistoryList);

    /**
     * 主键id删除
     *
     * @param id
     * @return
     */
    boolean deleteById(Serializable id);

    /**
     * 根据条件删除
     *
     * @param goodsPriceStockItemsHistorySearch
     * @return
     */
    boolean deleteBySearch(GoodsPriceStockItemsHistorySearch goodsPriceStockItemsHistorySearch);

    /**
     * 主键id批量删除
     *
     * @param idList
     * @return
     */
    boolean deleteByIds(List<Long> idList);

}
