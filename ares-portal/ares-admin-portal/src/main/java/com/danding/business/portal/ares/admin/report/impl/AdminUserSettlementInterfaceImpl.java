package com.danding.business.portal.ares.admin.report.impl;

import com.danding.business.client.ares.report.facade.IUserSettlementFacade;
import com.danding.business.client.ares.report.param.UserSettlementEditParam;
import com.danding.business.client.ares.report.param.UserSettlementParam;
import com.danding.business.client.ares.report.param.UserSettlementQueryParam;
import com.danding.business.client.ares.report.result.UserSettlementResult;
import com.danding.business.common.ares.BO.report.UserSettlementBO;
import com.danding.business.portal.ares.admin.report.Interface.IAdminUserSettlementInterface;
import com.danding.business.portal.ares.admin.report.form.AdminUserSettlementEditForm;
import com.danding.business.portal.ares.admin.report.form.AdminUserSettlementQueryForm;
import com.danding.business.portal.ares.admin.report.response.AdminUserBillsResponse;
import com.danding.business.portal.ares.admin.report.response.AdminUserSettlementResponse;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * <p>
 * 用户结算单 服务实现类
 * </p>
 *
 * <AUTHOR> Xu
 * @since 2020-11-17
 */
@DubboService
@SoulClient(path = "/report", desc = "报表中心")
public class AdminUserSettlementInterfaceImpl implements IAdminUserSettlementInterface {

    @DubboReference
    private IUserSettlementFacade userSettlementFacade;

    @Override
    @SoulClient(path = "/saveSettlement", desc = "新增结算单")
    public RpcResult saveSettlement(UserSettlementParam userSettlementParam) {
        UserSettlementParam userSettlement = BeanUtils.copyProperties(userSettlementParam, UserSettlementParam.class);
        UserSettlementResult userSettlementResult = userSettlementFacade.saveSettlement(userSettlement);
        if (userSettlementResult == null) {
            return RpcResult.error("新增结算单失败！请重试！");
        } else {
            return RpcResult.success(userSettlementResult);
        }
    }

    @Override
    @SoulClient(path = "/editSettlement", desc = "编辑结算单")
    public RpcResult editSettlement(AdminUserSettlementEditForm adminUserSettlementEditForm) {
        UserSettlementEditParam userSettlementEditParam = BeanUtils.copyProperties(adminUserSettlementEditForm, UserSettlementEditParam.class);
        return RpcResult.isSuccess(userSettlementFacade.editSettlement(userSettlementEditParam), "结算单修改失败！请重试！");
    }

    @Override
    @SoulClient(path = "/settlementPageList", desc = "结算单分页列表")
    public RpcResult settlementPageList(AdminUserSettlementQueryForm adminUserSettlementQueryForm) {
        UserSettlementQueryParam userSettlementQueryParam = BeanUtils.copyProperties(adminUserSettlementQueryForm, UserSettlementQueryParam.class);
        ListVO<UserSettlementBO> listVO = userSettlementFacade.settlementPageList(userSettlementQueryParam);
        return RpcResult.success(ListVO.build(listVO.getPage(), BeanUtils.copyProperties(listVO.getDataList(), AdminUserSettlementResponse.class)));
    }

    @Override
    @SoulClient(path = "/showSettlement", desc = "显示结算单详情")
    public RpcResult showSettlementById(Long id) {
        UserSettlementResult settlementResult = userSettlementFacade.getDetailById(id);
        AdminUserSettlementResponse adminUserSettlementResponse = BeanUtils.copyProperties(settlementResult, AdminUserSettlementResponse.class);
        adminUserSettlementResponse.setBillsList(BeanUtils.copyProperties(settlementResult.getBillsList(), AdminUserBillsResponse.class));
        return RpcResult.success(adminUserSettlementResponse);
    }

    @Override
    @SoulClient(path = "/showSettlementByNo", desc = "显示结算单详情")
    public RpcResult showSettlementByNo(String settlementNo) {
        UserSettlementResult settlementResult = userSettlementFacade.getDetailByNo(settlementNo, null);
        AdminUserSettlementResponse adminUserSettlementResponse = BeanUtils.copyProperties(settlementResult, AdminUserSettlementResponse.class);
        adminUserSettlementResponse.setBillsList(BeanUtils.copyProperties(settlementResult.getBillsList(), AdminUserBillsResponse.class));
        return RpcResult.success(adminUserSettlementResponse);
    }

    @Override
    @SoulClient(path = "/getListByBillNo", desc = "显示该单号对应的结算单列表")
    public RpcResult getListByBillNo(String billNo) {
        List<UserSettlementBO> listByBillNo = userSettlementFacade.getListByBillNo(billNo, SimpleUserHelper.getUserId());
        return RpcResult.success(BeanUtils.copyProperties(listByBillNo, AdminUserSettlementResponse.class));
    }
}
