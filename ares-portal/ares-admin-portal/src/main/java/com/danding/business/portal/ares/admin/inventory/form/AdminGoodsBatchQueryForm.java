package com.danding.business.portal.ares.admin.inventory.form;

import com.danding.business.client.ares.inventory.param.PropertyGroupType;
import com.danding.component.common.api.common.page.Page;
import lombok.Data;

@Data
public class AdminGoodsBatchQueryForm extends Page {

    private static final long serialVersionUID = 3222554839309755192L;
    private Long userId;
    private String cargoCode;
    private String inventoryType;
    private Long expireTimeBegin;
    private Long expireTimeEnd;
    private  String  skuLotNo;
    private String externalSkuLotNo;
    private PropertyGroupType group;
    private String goodsCode;
    private String logicWarehouseCode;
    private String batchCode;
    /**
     *"销毁出库 销毁出库 true"
     */
    private Boolean isDestroy;

    /**
     * 入库关联单号
     */
    private String entryRecordNo;
    private Long productionTimeBegin;
    private Long productionTimeEnd;
    /**
     * 失效日期升序:ASC
     * 失效日期降序:DESC
     */
    private String sortExpire = "ASC";
    /**
     * 目前支持: 货品名称, sku, 批次, 效期
     */
    private String likeQuery;

}
