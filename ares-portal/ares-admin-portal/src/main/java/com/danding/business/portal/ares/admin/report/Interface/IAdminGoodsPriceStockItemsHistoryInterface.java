package com.danding.business.portal.ares.admin.report.Interface;


import com.danding.business.portal.ares.admin.report.form.AdminGoodsPriceStockItemsHistoryAddForm;
import com.danding.business.portal.ares.admin.report.form.AdminGoodsPriceStockItemsHistoryQueryForm;
import com.danding.soul.client.common.result.RpcResult;

import javax.validation.Valid;

/**
 * <p>
 * 价盘子表历史更新记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-22
 */
public interface IAdminGoodsPriceStockItemsHistoryInterface {

    /**
     * 查询单条
     *
     * @param queryForm
     * @return
     */
    RpcResult query(AdminGoodsPriceStockItemsHistoryQueryForm queryForm);

    /**
     * 列表查询
     *
     * @param queryForm
     * @return
     */
    RpcResult queryList(AdminGoodsPriceStockItemsHistoryQueryForm queryForm);

    /**
     * 创建
     *
     * @param addForm
     * @return
     */
    RpcResult create(@Valid AdminGoodsPriceStockItemsHistoryAddForm addForm);

    /**
     * 编辑保存
     *
     * @param addForm
     * @return
     */
    RpcResult edit(@Valid AdminGoodsPriceStockItemsHistoryAddForm addForm);

    /**
     * 删除
     *
     * @param queryForm
     * @return
     */
    RpcResult drop(AdminGoodsPriceStockItemsHistoryQueryForm queryForm);

    /**
     * 详情
     *
     * @param queryForm
     * @return
     */
    RpcResult detail(AdminGoodsPriceStockItemsHistoryQueryForm queryForm);

}
