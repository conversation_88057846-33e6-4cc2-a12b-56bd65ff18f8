package com.danding.business.portal.ares.admin.withdrawal.form;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 采退单明细表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */

@Data
@ApiModel(value = "WithdrawalOrderDetail对象", description = "采退单明细表查询")
public class AdminWithdrawalOrderDetailQueryForm extends Page {


    private static final long serialVersionUID = 1L;
    private Long id;
    /**
     * 创建开始时间
     */
    private Long createTimeStart;
    /**
     * 创建开始时间
     */
    private Long createTimeEnd;
    /**
     * 更新时间
     */
    private Long updateTime;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 采退单号
     */
    @ApiModelProperty(value = "采退单号")
    private String withdrawalOrderNo;

    /**
     * 货品sku
     */
    @ApiModelProperty(value = "货品sku")
    private String goodsCode;

    /**
     * 货币类型
     */
    @ApiModelProperty(value = "货币类型")
    private String billCurrency;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    /**
     * 采退数量
     */
    @ApiModelProperty(value = "采退数量")
    private Integer withdrawalNumber;

    /**
     * 采退金额
     */
    @ApiModelProperty(value = "采退金额")
    private BigDecimal withdrawalAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
