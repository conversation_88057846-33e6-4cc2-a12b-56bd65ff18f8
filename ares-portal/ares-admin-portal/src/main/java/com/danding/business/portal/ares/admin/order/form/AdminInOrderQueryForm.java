package com.danding.business.portal.ares.admin.order.form;

import com.danding.business.common.ares.enums.order.OrderFlgStatus;
import com.danding.business.common.ares.enums.trade.MockType;
import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 入库单主表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */

@Data
@ApiModel(value="InOrder对象", description="入库单主表查询")
public class AdminInOrderQueryForm extends Page {

    private static final long serialVersionUID = 1L;

    /**
     * 逻辑仓编码
     */
    private String logicWarehouseCode;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建开始时间
     */
    private Long createTimeStart;
    /**
     * 创建开始时间
     */
    private Long createTimeEnd;
    /**
     * 查询类型
     */
    private Integer queryType;
    /**
     * 查询条件
     */
    private String queryInfo;
    /**
     * 实际入库时间
     */
    private Long actualTimeStart;
    private Long actualTimeEnd;
    /**
     * 入库单号
     */
    private String inOrderNo;
    /**
     * 货品编码
     */
    private String goodsCode;
    /**
     * 外部货品编码
     */
    private String sku;
    /**
     * 货品名称
     */
    private String goodsName;
    /**
     * 关联单号
     */
    private String businessNo;
    /**
     * 预计入库时间
     */
    private Long expectTimeStart;
    private Long expectTimeEnd;
    /**
     * 入库类型
     */
    private Integer type;
    /**
     * 入库状态
     */
    private Integer status;
    private List<Integer> statusList;
    /**
     * 货主编码：云仓对应的货主编码
     */
    private String ownerCode;
    /**
     * 实体仓编码：云仓对应的外部实体仓编码
     */
    private String warehouseCode;
    /**
     * 上游单号
     */
    private String upstreamNo;
    /**
     * 是否异常 0:正常 1:异常
     */
    private Integer exceptionStatus;
    /**
     * 是否模拟回传
     */
    private MockType mockType;
    /**
     * 推送状态
     */
    private Integer pushStatus;
    /**
     * 回传状态
     */
    private Integer backStatus;
    /**
     * 订单标记
     */
    private OrderFlgStatus orderFlag;

}
