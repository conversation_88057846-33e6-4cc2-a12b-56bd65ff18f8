package com.danding.business.portal.ares.admin.purchase.Interface;

import com.danding.business.portal.ares.admin.purchase.form.AdminPurchaseOrderQueryForm;
import com.danding.business.portal.ares.admin.record.form.AdminGoodsRecordQueryForm;
import com.danding.soul.client.common.result.RpcResult;

/**
 * <p>
 * 采购单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
public interface IAdminPurchaseOrderInterface {
    /**
     * 获取采购单详细
     *
     * @param id
     * @return
     */
    RpcResult detail(String id);

    /**
     * 采购单取消
     *
     * @return
     */
    RpcResult cancel(String purchaseOrderNo, String revocation);

    /**
     * 分页条件查询货品
     *
     * @param queryForm
     * @return
     */
    RpcResult selectPage(AdminPurchaseOrderQueryForm queryForm);

    /**
     * 库存状态下拉列表
     *
     * @return
     */
    RpcResult inventoryStatus();

    /**
     * 采购单状态下拉列表
     *
     * @return
     */
    RpcResult purchaseOrderStatus();

    /**
     * 采购类型下拉列表
     *
     * @return
     */
    RpcResult purchaseOrderType();

    /**
     * 支付方式下拉列表
     *
     * @return
     */
    RpcResult paymentMethod();

    /**
     * 账期时间单位下拉列表
     *
     * @return
     */
    RpcResult paymentDaysTimeUnit();

    /**
     * 单号查询条件下拉列表
     *
     * @return
     */
    RpcResult purchaseQueryType();

    /**
     * 新增入库单下采购单货品详细
     *
     * @return
     */
    RpcResult goodsListForInOrder(String purchaseOrderNo);

    /**
     * 提交审核
     *
     * @return
     */
    RpcResult submitAudit(String purchaseOrderNo);

    /**
     * 待入库采购单下拉列表
     *
     * @return
     */
    RpcResult purchaseOrderNoList();

    /**
     * 创建并执行导出任务
     *
     * @param queryForm
     * @return
     */
    RpcResult exportExcel(AdminPurchaseOrderQueryForm queryForm);
}
