package com.danding.business.portal.ares.admin.snapshot.Interface;

import com.danding.business.portal.ares.admin.snapshot.form.AdminGoodsInventoryDailySnapshotAddForm;
import com.danding.business.portal.ares.admin.snapshot.form.AdminGoodsInventoryDailySnapshotQueryForm;
import com.danding.soul.client.common.result.RpcResult;

import javax.validation.Valid;
/**
 * <p>
 * 货品仓库库存每日快照 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
public interface IAdminGoodsInventoryDailySnapshotInterface {

    /**
     * 查询单条
     *
     * @param queryForm
     * @return
     */
    RpcResult query(AdminGoodsInventoryDailySnapshotQueryForm queryForm);

    /**
     * 列表查询
     *
     * @param queryForm
     * @return
     */
    RpcResult queryList(AdminGoodsInventoryDailySnapshotQueryForm queryForm);

    /**
     * 下拉查询
     *
     * @param queryForm
     * @return
     */
    RpcResult selectItem(AdminGoodsInventoryDailySnapshotQueryForm queryForm);

    /**
     * 创建
     *
     * @param addForm
     * @return
     */
    RpcResult create(@Valid AdminGoodsInventoryDailySnapshotAddForm addForm);

    /**
     * 编辑保存
     *
     * @param addForm
     * @return
     */
    RpcResult edit(@Valid AdminGoodsInventoryDailySnapshotAddForm addForm);

    /**
     * 删除
     *
     * @param queryForm
     * @return
     */
    RpcResult drop(AdminGoodsInventoryDailySnapshotQueryForm queryForm);

    /**
     * 详情
     *
     * @param queryForm
     * @return
     */
    RpcResult detail(AdminGoodsInventoryDailySnapshotQueryForm queryForm);
}