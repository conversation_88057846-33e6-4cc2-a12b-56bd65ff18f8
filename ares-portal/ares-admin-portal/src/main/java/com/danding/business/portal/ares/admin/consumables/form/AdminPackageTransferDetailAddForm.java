package com.danding.business.portal.ares.admin.consumables.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 添加
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */

@Data
@ApiModel(value="PackageTransferDetail对象", description="添加")
public class AdminPackageTransferDetailAddForm implements Serializable {


    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 调拨单号
     */
    @ApiModelProperty(value = "调拨单号")
    private String pcTransferNo;

    /**
     * 包耗材条码
     */
    @ApiModelProperty(value = "包耗材条码")
    private String pcBarcode;

    /**
     * 包耗材名称
     */
    @ApiModelProperty(value = "包耗材名称")
    private String pcName;

    /**
     * 属性类型
     */
    @ApiModelProperty(value = "属性类型")
    private String pcType;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String pcUnit;

    /**
     * 计划调拨数量
     */
    @ApiModelProperty(value = "计划调拨数量")
    private BigDecimal planQuantity;

    /**
     * 实际调拨数量
     */
    @ApiModelProperty(value = "实际调拨数量")
    private BigDecimal actualQuantity;

    /**
     * 库位编码
     */
    @ApiModelProperty(value = "库位编码")
    private String locationCode;
}
