package com.danding.business.portal.ares.admin.cornerReturn.Interface;

import com.danding.business.portal.ares.admin.cornerReturn.form.AdminCornerReturnedOrderAddForm;
import com.danding.business.portal.ares.admin.cornerReturn.form.AdminCornerReturnedOrderQueryForm;
import com.danding.soul.client.common.result.RpcResult;

import javax.validation.Valid;

/**
 * <p>
 * 分销单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
public interface IAdminCornerReturnedOrderInterface {

    /**
     * 列表查询
     *
     * @param queryForm
     * @return
     */
    RpcResult queryList(AdminCornerReturnedOrderQueryForm queryForm);


    /**
     * 获取分销退货单详情
     *
     * @param cornerReturnedOrderNo
     * @return
     */
    RpcResult detail(String cornerReturnedOrderNo);

    /**
     * 分销退货单状态下拉列表
     *
     * @return
     */
    RpcResult orderStatusList();
}
