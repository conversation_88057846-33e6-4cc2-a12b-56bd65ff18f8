/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.danding.business.portal.ares.admin;

import com.alibaba.fastjson.JSONObject;
import com.danding.business.client.ares.goods.facade.IGoodsFacade;
import com.danding.business.client.ares.inventory.facade.IGoodsInventoryFacade;
import com.danding.business.client.ares.inventory.facade.IGoodsInventoryLogsFacade;
import com.danding.business.client.ares.inventory.facade.IGoodsInventoryOperationRpcFacade;
import com.danding.business.client.ares.inventory.param.*;
import com.danding.business.client.ares.inventory.result.GoodsInventoryLogsResult;
import com.danding.business.client.ares.order.facade.IOutOrderFacade;
import com.danding.business.client.ares.purchase.facade.IPurchaseOrderFacade;
import com.danding.business.common.ares.enums.HeadEnum;
import com.danding.business.common.ares.enums.inventory.InventoryUpdateType;
import com.danding.business.common.ares.utils.GenerateUtils;
import com.danding.business.portal.ares.admin.consumables.Interface.IAdminMaterialInventoryCheckInterface;
import com.danding.business.portal.ares.admin.consumables.Interface.IAdminPackageConsumablesAdjustInterface;
import com.danding.business.portal.ares.admin.consumables.form.AdminPackageConsumablesCompleteAdjustForm;
import com.danding.business.portal.ares.admin.consumables.form.AdminPackageConsumablesGenerateAdjustForm;
import com.danding.business.portal.ares.admin.inventory.Interface.IAdminGoodsInventoryInterface;
import com.danding.business.portal.ares.admin.inventory.form.AdminGoodsInventoryQueryForSelectForm;
import com.danding.business.portal.ares.admin.inventory.form.AdminGoodsInventoryQueryForm;
import com.danding.business.portal.ares.admin.order.Interface.IAdminOutOrderInterface;

import com.danding.business.portal.ares.admin.regulatory.Interface.IAdminRegulatoryInterface;
import com.danding.business.portal.ares.admin.regulatory.form.AdminRegulatoryQueryForm;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import io.swagger.annotations.Api;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 功能描述: 启动之后的健康检查
 * 创建时间:  2020/8/24 10:02 上午
 *
 * <AUTHOR>
 */
@Controller
@Api(value = "启动之后的健康检查", tags = "启动之后的健康检查")
public class TestController {
    @DubboReference
    IAdminOutOrderInterface iAdminOutOrderInterface;
    @DubboReference
    IAdminRegulatoryInterface iAdminRegulatoryInterface;
    @DubboReference
    IAdminGoodsInventoryInterface iAdminGoodsInventoryInterface;
    @DubboReference
    IPurchaseOrderFacade purchaseOrderFacade;
    @DubboReference
    IGoodsInventoryFacade goodsInventoryFacade;
    @DubboReference
    IAdminMaterialInventoryCheckInterface adminMaterialInventoryCheckInterface;
    @DubboReference
    IAdminPackageConsumablesAdjustInterface adminPackageConsumablesAdjustInterface;
    @DubboReference
    private IGoodsInventoryOperationRpcFacade goodsInventoryOperationRpcFacade;
    @DubboReference
    private IGoodsInventoryLogsFacade goodsInventoryLogsFacade;
    @DubboReference
    private IGoodsFacade goodsFacade;

    @GetMapping("/hello")
    @ResponseBody
    public String hello() {
        return "hello";
    }

    /**
     * 测试入口
     */
    @GetMapping("/test")
    @ResponseBody
    public Object remoteBizFacade(@RequestParam(name = "type", required = false, defaultValue = "1")
                                  int type, @RequestParam(name = "params", required = false, defaultValue = "{}") String params) {
        JSONObject jsonObject = JSONObject.parseObject(params);
        switch (type) {
            case 1:
                return iAdminOutOrderInterface.backFailCount();
            case 2:
                AdminRegulatoryQueryForm adminRegulatoryQueryForm = new AdminRegulatoryQueryForm();
                adminRegulatoryQueryForm.setPageSize(20);
                adminRegulatoryQueryForm.setCurrentPage(1);
                return iAdminRegulatoryInterface.queryList(adminRegulatoryQueryForm);
            case 3:
                AdminGoodsInventoryQueryForm adminGoodsInventoryQueryForSelectForm = new AdminGoodsInventoryQueryForm();
                adminGoodsInventoryQueryForSelectForm.setPageSize(20);
                adminGoodsInventoryQueryForSelectForm.setCurrentPage(1);
                adminGoodsInventoryQueryForSelectForm.setUserId(1L);
                return iAdminGoodsInventoryInterface.selectPage(adminGoodsInventoryQueryForSelectForm);
            case 4:
                System.out.println(GenerateUtils.generateOrderNo(HeadEnum.C));
            case 5:
                return purchaseOrderFacade.getDetailByPurchaseOrderNo(jsonObject.getString("id"));
            case 6:
                GoodsInventoryQueryParam goodsInventoryQueryParam = new GoodsInventoryQueryParam();
                goodsInventoryQueryParam.setCurrentPage(1);
                goodsInventoryQueryParam.setPageSize(30);
                goodsInventoryQueryParam.setUserId(100246L);
                return goodsInventoryFacade.selectGroupByList(goodsInventoryQueryParam);

        }
        return "Ok";
    }

    @PostMapping("/test2")
    public Object test(@RequestBody JSONObject params) {
        int type = params.getInteger("type");
        JSONObject jsonObject = params.getJSONObject("data");
        switch (type) {
            case 1:
                return iAdminOutOrderInterface.backFailCount();
            case 2:
                AdminRegulatoryQueryForm adminRegulatoryQueryForm = new AdminRegulatoryQueryForm();
                adminRegulatoryQueryForm.setPageSize(20);
                adminRegulatoryQueryForm.setCurrentPage(1);
                return iAdminRegulatoryInterface.queryList(adminRegulatoryQueryForm);
            case 3:
                AdminGoodsInventoryQueryForm adminGoodsInventoryQueryForSelectForm = new AdminGoodsInventoryQueryForm();
                adminGoodsInventoryQueryForSelectForm.setPageSize(20);
                adminGoodsInventoryQueryForSelectForm.setCurrentPage(1);
                adminGoodsInventoryQueryForSelectForm.setUserId(1L);
                return iAdminGoodsInventoryInterface.selectPage(adminGoodsInventoryQueryForSelectForm);
            case 4:
                System.out.println(GenerateUtils.generateOrderNo(HeadEnum.C));
            case 5:
                return purchaseOrderFacade.getDetailByPurchaseOrderNo(jsonObject.getString("id"));
            case 6:
                GoodsInventoryQueryParam goodsInventoryQueryParam = new GoodsInventoryQueryParam();
                goodsInventoryQueryParam.setPageSize(20);
                goodsInventoryQueryParam.setCurrentPage(1);
                goodsInventoryQueryParam.setUserId(100246L);
                return goodsInventoryFacade.selectGroupByList(goodsInventoryQueryParam);
            case 7:
                AdminPackageConsumablesGenerateAdjustForm adminPackageConsumablesGenerateAdjustForm = jsonObject.toJavaObject(AdminPackageConsumablesGenerateAdjustForm.class);
                return adminMaterialInventoryCheckInterface.generateAdjust(adminPackageConsumablesGenerateAdjustForm);
            case 8:
                AdminPackageConsumablesCompleteAdjustForm completeAdjustForm = jsonObject.toJavaObject(AdminPackageConsumablesCompleteAdjustForm.class);
                return adminPackageConsumablesAdjustInterface.completeAdjust(completeAdjustForm);

        }
        return "Ok";
    }

    @PostMapping("/test10")
    @ResponseBody
    public Object test10(@RequestBody JSONObject params) {
        int type = params.getInteger("type");
        JSONObject jsonObject = params.getJSONObject("data");
        switch (type) {

            case 1:
                return goodsInventoryOperationRpcFacade.asyncGoodsInventoryLock(jsonObject.toJavaObject(GoodsInventoryRpcLockParam.class));
            case 2:
                return goodsInventoryOperationRpcFacade.goodsInventoryRelease(jsonObject.toJavaObject(GoodsInventoryRpcReleaseParam.class));
            case 3:
                return goodsInventoryOperationRpcFacade.goodsInventoryStockOut(jsonObject.toJavaObject(GoodsInventoryRpcStockOutParam.class));
            case 20:
                GoodsInventoryRpcStockOutParam param = jsonObject.toJavaObject(GoodsInventoryRpcStockOutParam.class);
                return goodsInventoryRelease(param.getLogicWarehouseCode(), param.getUpstreamNo());

        }
        return "Ok";
    }

    private boolean goodsInventoryRelease(String logicWarehouseCode, String upstreamNo) {
        GoodsInventoryLogsQueryParam queryParam = new GoodsInventoryLogsQueryParam();
        queryParam.setLogicWarehouseCode(logicWarehouseCode);
        queryParam.setQueryType(1);
        queryParam.setQueryInfo(upstreamNo);
        queryParam.setUpdateType(InventoryUpdateType.LOCKED_WHK);
        queryParam.setPageSize(100);
        List<GoodsInventoryLogsResult> list = goodsInventoryLogsFacade.listGoodsInventoryLogs(queryParam);

        GoodsInventoryRpcReleaseParam releaseParam = new GoodsInventoryRpcReleaseParam();
        releaseParam.setLogicWarehouseCode(logicWarehouseCode);
        releaseParam.setOutBizCode("1");
        releaseParam.setUpstreamNo(upstreamNo);

        List<InventoryItemsRpcParam> skus = list.stream().map(m -> {
            InventoryItemsRpcParam param = new InventoryItemsRpcParam();
            param.setSku(goodsFacade.getGoodsByParam(m.getUserId(), m.getGoodsCode()).getSku());
            param.setUpdateNum(m.getUpdateNum());
            param.setInventoryType(m.getInventoryType().getValue());
            return param;
        }).collect(Collectors.toList());
        releaseParam.setSkuList(skus);
        goodsInventoryOperationRpcFacade.goodsInventoryRelease(releaseParam);
        return true;
    }

}
