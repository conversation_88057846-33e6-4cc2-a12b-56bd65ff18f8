package com.danding.business.portal.ares.admin.consumables.response;

import com.danding.component.common.rpc.common.annotation.ResultReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 包耗材上架单上架明细返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */

@Data
@ApiModel(value="PackageConsumablesShelfTargetDetail对象", description="包耗材上架单上架明细返回对象")
public class AdminPackageConsumablesShelfTargetDetailResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键使用的雪花算法，转出给前端时需要转成String 类型
    */
    @ResultReference(referenceType = ResultReference.ReferenceType.COPY, localReferProperty = "id")
    private String id;

    /**
    * 创建时间
    */
    private Long createTime;

    /**
    * 更新时间
    */
    private Long updateTime;

    /**
    * 创建人
    */
    private Long createBy;

    /**
    * 更新人
    */
    private Long updateBy;

    /**
     * 上架单号
     */
    @ApiModelProperty(value = "上架单号")
    private String shelfNo;

    /**
     * 包耗材条码
     */
    @ApiModelProperty(value = "包耗材条码")
    private String pcBarcode;

    /**
     * 库位编码
     */
    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    /**
     * 上架数量
     */
    @ApiModelProperty(value = "上架数量")
    private BigDecimal shelfPlanQuantity;

    /**
     * 上架人
     */
    @ApiModelProperty(value = "上架人")
    private String shelfBy;

    @ApiModelProperty(value = "上架时间")
    private Long shelfTime;
    private String shelfTimeDesc;


}
