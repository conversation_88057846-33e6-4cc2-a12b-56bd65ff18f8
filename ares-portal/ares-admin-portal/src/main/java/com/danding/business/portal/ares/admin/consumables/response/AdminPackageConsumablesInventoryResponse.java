package com.danding.business.portal.ares.admin.consumables.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 包耗材库存返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */

@Data
@ApiModel(value="PackageConsumablesInventory对象", description="包耗材库存返回对象")
public class AdminPackageConsumablesInventoryResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外部实体编码
     */
    @ApiModelProperty(value = "外部实体编码")
    private String warehouseCode;

    /**
     * 实体仓名称
     */
    @ApiModelProperty(value = "实体仓名称")
    private String warehouseName;

    /**
     * 包耗材条码
     */
    @ApiModelProperty(value = "包耗材条码")
    private String pcBarcode;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 成本价
     */
    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    /**
     * 包耗材名称
     */
    @ApiModelProperty(value = "包耗材名称")
    private String pcName;

    /**
     * 属性类型
     */
    @ApiModelProperty(value = "属性类型")
    private String pcType;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String pcUnit;

    private String locationCode;
    private String locationTypeDesc;

}
