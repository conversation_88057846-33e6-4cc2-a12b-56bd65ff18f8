package com.danding.business.portal.ares.admin.consumables.Interface;

import com.danding.business.portal.ares.admin.consumables.form.AdminMaterialInventoryCheckDetailAddForm;
import com.danding.business.portal.ares.admin.consumables.form.AdminMaterialInventoryCheckDetailQueryForm;
import com.danding.soul.client.common.result.RpcResult;

import javax.validation.Valid;
/**
 * <p>
 * 包耗材盘点明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
public interface IAdminMaterialInventoryCheckDetailInterface {


    /**
     * 列表查询
     *
     * @param queryForm
     * @return
     */
    RpcResult queryList(AdminMaterialInventoryCheckDetailQueryForm queryForm);

    /**
     * 下拉查询
     *
     * @param queryForm
     * @return
     */
    RpcResult selectDetailStatus(AdminMaterialInventoryCheckDetailQueryForm queryForm);
    RpcResult selectDetailType(AdminMaterialInventoryCheckDetailQueryForm queryForm);

}
