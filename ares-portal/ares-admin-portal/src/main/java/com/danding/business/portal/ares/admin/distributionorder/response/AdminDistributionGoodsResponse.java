package com.danding.business.portal.ares.admin.distributionorder.response;


import com.danding.component.common.rpc.common.annotation.ResultReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-16
 */

@Data
@ApiModel(value = "DistributionGoods对象", description = "返回对象")
public class AdminDistributionGoodsResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键使用的雪花算法，转出给前端时需要转成String 类型
     */
    @ResultReference(referenceType = ResultReference.ReferenceType.COPY, localReferProperty = "id")
    private String id;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long updateTime;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * sku
     */
    @ApiModelProperty(value = "sku")
    private String goodsCode;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String materialCode;

    /**
     * 配货单号
     */
    @ApiModelProperty(value = "配货单号")
    private String distributionNo;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer qty;

    private String sku;

    private Integer actualQty;

    /**
     * 行号
     */
    private String lineNo;
}
