package com.danding.business.portal.ares.admin.withdrawal.response;


import com.danding.component.common.rpc.common.annotation.ResultReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 采退单明细表返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */

@Data
@ApiModel(value = "WithdrawalOrderDetail对象", description = "采退单明细表返回对象")
public class AdminWithdrawalOrderDetailViewResponse implements Serializable {


    private static final long serialVersionUID = 1L;
    /**
     * 主键使用的雪花算法，转出给前端时需要转成String 类型
     */
    @ResultReference(referenceType = ResultReference.ReferenceType.COPY, localReferProperty = "id")
    private String id;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long updateTime;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 采退单号
     */
    @ApiModelProperty(value = "采退单号")
    private String withdrawalOrderNo;

    /**
     * 货品sku
     */
    @ApiModelProperty(value = "货品sku")
    private String goodsCode;

    /**
     * 货币类型
     */
    @ApiModelProperty(value = "货币类型")
    private String billCurrency;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    /**
     * 采退数量
     */
    @ApiModelProperty(value = "采退数量")
    private Integer withdrawalNumber;

    /**
     * 采退金额
     */
    @ApiModelProperty(value = "采退金额")
    private BigDecimal withdrawalAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
