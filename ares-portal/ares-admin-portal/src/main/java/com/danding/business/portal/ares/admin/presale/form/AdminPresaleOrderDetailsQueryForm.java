package com.danding.business.portal.ares.admin.presale.form;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 预售订单详情表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */

@Data
@ApiModel(value = "PresaleOrderDetails对象", description = "预售订单详情表查询")
public class AdminPresaleOrderDetailsQueryForm extends Page {


    private static final long serialVersionUID = 1L;

    /**
     * 预售单号
     */
    @ApiModelProperty(value = "预售单号")
    private String presaleNo;

    /**
     * 货品名称
     */
    @ApiModelProperty(value = "货品名称")
    private String goodsName;

    /**
     * 货品sku
     */
    @ApiModelProperty(value = "货品sku")
    private String goodsCode;

    /**
     * 条形码
     */
    @ApiModelProperty(value = "条形码")
    private String barcode;

    /**
     * 单价（折扣价）
     */
    @ApiModelProperty(value = "单价（折扣价）")
    private BigDecimal unitPrice;

    /**
     * 零售价格
     */
    @ApiModelProperty(value = "零售价格")
    private BigDecimal retailPrice;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer goodsNum;

    /**
     * 总价（单价*数量）
     */
    @ApiModelProperty(value = "总价（单价*数量）")
    private BigDecimal totalAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
