package com.danding.business.portal.ares.admin.snapshot.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 货品仓库库存每日快照添加
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */

@Data
@ApiModel(value="GoodsInventoryDailySnapshot对象", description="货品仓库库存每日快照添加")
public class AdminGoodsInventoryDailySnapshotAddForm implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 快照日期
     */
    @ApiModelProperty(value = "快照日期")
    private Date snapshotDate;

    /**
     * 登录用户ID
     */
    @ApiModelProperty(value = "登录用户ID")
    private Long userId;

    /**
     * 登录用户名
     */
    @ApiModelProperty(value = "登录用户名")
    private String userName;

    /**
     * 货品SKU编码
     */
    @ApiModelProperty(value = "货品SKU编码")
    private String goodsCode;

    /**
     * 货品名称
     */
    @ApiModelProperty(value = "货品名称")
    private String goodsName;

    /**
     * 贸易类型
     */
    @ApiModelProperty(value = "贸易类型")
    private Integer goodsType;

    /**
     * 品牌编码
     */
    @ApiModelProperty(value = "品牌编码")
    private String brandCode;

    /**
     * 条形码
     */
    @ApiModelProperty(value = "条形码")
    private String barcode;

    /**
     * 是否开启批次管理
     */
    @ApiModelProperty(value = "是否开启批次管理")
    private Integer batchManagement;

    /**
     * 逻辑仓库编码
     */
    @ApiModelProperty(value = "逻辑仓库编码")
    private String logicWarehouseCode;

    /**
     * 可售库存（预售+可用）
     */
    @ApiModelProperty(value = "可售库存（预售+可用）")
    private Integer forsaleNum;

    /**
     * 已售库存
     */
    @ApiModelProperty(value = "已售库存")
    private Integer soldNum;

    /**
     * 占用库存
     */
    @ApiModelProperty(value = "占用库存")
    private Integer occupiedNum;

    /**
     * 预售库存
     */
    @ApiModelProperty(value = "预售库存")
    private Integer presaleNum;

    /**
     * 在库库存数
     */
    @ApiModelProperty(value = "在库库存数")
    private Integer instockNum;

    /**
     * 在途库存数
     */
    @ApiModelProperty(value = "在途库存数")
    private Integer ongoingNum;

    /**
     * 正品库存
     */
    @ApiModelProperty(value = "正品库存")
    private Integer qualityNum;

    /**
     * 已用库存（占用+锁定）
     */
    @ApiModelProperty(value = "已用库存（占用+锁定）")
    private Integer usedNum;

    /**
     * 不可用库存（次品+过期：暂时不用）
     */
    @ApiModelProperty(value = "不可用库存（次品+过期：暂时不用）")
    private Integer unavailableNum;

    /**
     * 可用库存（在库-锁定）
     */
    @ApiModelProperty(value = "可用库存（在库-锁定）")
    private Integer availableNum;

    /**
     * 次品库存（现在作为[不可用库存]展示）
     */
    @ApiModelProperty(value = "次品库存（现在作为[不可用库存]展示）")
    private Integer defectiveNum;

    /**
     * 锁定库存
     */
    @ApiModelProperty(value = "锁定库存")
    private Integer lockedNum;

    /**
     * 货主代码
     */
    @ApiModelProperty(value = "货主代码")
    private String ownerCode;

    /**
     * 货主是否管理效期
     */
    @ApiModelProperty(value = "货主是否管理效期")
    private Integer isBatch;

    /**
     * 外部sku
     */
    @ApiModelProperty(value = "外部sku")
    private String sku;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否预售
     */
    @ApiModelProperty(value = "是否预售")
    private Integer ownerPreSaleType;

    /**
     * 预售关联仓
     */
    @ApiModelProperty(value = "预售关联仓")
    private String preSaleRelatedLogicWarehouseCode;

    /**
     * 圈货库存
     */
    @ApiModelProperty(value = "圈货库存")
    private Integer corneredNum;
}