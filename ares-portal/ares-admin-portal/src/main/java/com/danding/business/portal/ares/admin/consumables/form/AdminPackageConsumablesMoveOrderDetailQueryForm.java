package com.danding.business.portal.ares.admin.consumables.form;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 包耗材移位单明细查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */

@Data
@ApiModel(value="PackageConsumablesMoveOrderDetail对象", description="包耗材移位单明细查询")
public class AdminPackageConsumablesMoveOrderDetailQueryForm extends Page {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
    * 创建开始时间
    */
    private Long createTimeStart;

    /**
    * 创建开始时间
    */
    private Long createTimeEnd;

    /**
    * 更新时间
    */
    private Long updateTime;

    /**
    * 创建人
    */
    private Long createBy;

    /**
    * 更新人
    */
    private Long updateBy;

    /**
     * 移位单号
     */
    @ApiModelProperty(value = "移位单号")
    private String moveOrderNo;

    /**
     * 包耗材条码
     */
    @ApiModelProperty(value = "包耗材条码")
    private String pcBarcode;

    /**
     * 包耗材名称
     */
    @ApiModelProperty(value = "包耗材名称")
    private String pcName;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String pcUnit;

    /**
     * 属性类型
     */
    @ApiModelProperty(value = "属性类型")
    private String pcType;

    /**
     * 移位数量
     */
    @ApiModelProperty(value = "移位数量")
    private BigDecimal quantity;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long tenantId;

}
