package com.danding.business.portal.ares.admin.consumables.Interface;

import com.danding.business.portal.ares.admin.consumables.form.AdminPackageConsumablesMoveOrderAddForm;
import com.danding.business.portal.ares.admin.consumables.form.AdminPackageConsumablesMoveOrderQueryForm;
import com.danding.soul.client.common.result.RpcResult;

import javax.validation.Valid;
/**
 * <p>
 * 包耗材移位单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface IAdminPackageConsumablesMoveOrderInterface {

    /**
     * 列表查询
     *
     * @param queryForm
     * @return
     */
    RpcResult queryList(AdminPackageConsumablesMoveOrderQueryForm queryForm);

    /**
     * 下拉类型
     *
     * @return
     */
    RpcResult selectType();

    /**
     * 下啦状态
     * @return
     */
    RpcResult selectStatus();

    /**
     * 创建
     *
     * @param addForm
     * @return
     */
    RpcResult create(@Valid AdminPackageConsumablesMoveOrderAddForm addForm);

    /**
     * 详情
     *
     * @param queryForm
     * @return
     */
    RpcResult detail(AdminPackageConsumablesMoveOrderQueryForm queryForm);

    /**
     * 编辑保存
     *
     * @param addForm
     * @return
     */
    RpcResult edit(@Valid AdminPackageConsumablesMoveOrderAddForm addForm);

    /**
     * 删除
     *
     * @param queryForm
     * @return
     */
    RpcResult drop(AdminPackageConsumablesMoveOrderQueryForm queryForm);

    /**
     * 确认位移
     * @param queryForm
     * @return
     */
    RpcResult confirm(AdminPackageConsumablesMoveOrderQueryForm queryForm);

    /**
     * 移位单导出
     * @param queryForm
     * @return
     */
    RpcResult exportExcel(AdminPackageConsumablesMoveOrderQueryForm queryForm);

}
