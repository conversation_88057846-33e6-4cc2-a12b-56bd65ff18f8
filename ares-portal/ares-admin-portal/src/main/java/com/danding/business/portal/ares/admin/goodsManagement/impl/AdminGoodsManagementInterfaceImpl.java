package com.danding.business.portal.ares.admin.goodsManagement.impl;

import com.danding.business.client.ares.goods.param.GoodsOperationLogParam;
import com.danding.business.client.ares.goodsManagement.facade.IGoodsManagementFacade;
import com.danding.business.client.ares.goodsManagement.param.GoodsManagementAddParam;
import com.danding.business.client.ares.goodsManagement.param.GoodsManagementQueryParam;
import com.danding.business.client.ares.goodsManagement.result.GoodsManagementResult;
import com.danding.business.client.ares.inventory.facade.IGoodsInventoryFacade;
import com.danding.business.client.ares.owner.facade.IOwnerFacade;
import com.danding.business.client.ares.owner.result.OwnerResult;
import com.danding.business.common.ares.enums.ExcelExportEnum;
import com.danding.business.common.ares.enums.goods.GoodsOpenPeriodValidity;
import com.danding.business.common.ares.enums.operationLogs.OperationType;
import com.danding.business.portal.ares.admin.AdminPortalConfig;
import com.danding.business.portal.ares.admin.export.ExportExecutor;
import com.danding.business.portal.ares.admin.goodsManagement.Interface.IAdminGoodsManagementInterface;
import com.danding.business.portal.ares.admin.goodsManagement.form.AdminGoodsManagementAddForm;
import com.danding.business.portal.ares.admin.goodsManagement.form.AdminGoodsManagementQueryForm;
import com.danding.business.portal.ares.admin.goodsManagement.response.AdminGoodsManagementResponse;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.common.utils.EnumUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 货品管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-06
 */
@DubboService
@Slf4j
public class AdminGoodsManagementInterfaceImpl implements IAdminGoodsManagementInterface {

    @DubboReference
    private IGoodsManagementFacade goodsManagementFacade;
    @DubboReference
    private IOwnerFacade ownerFacade;

    @DubboReference
    private IGoodsInventoryFacade goodsInventoryFacade;

    @Resource
    ExportExecutor exportExecutor;

    @Autowired
    private AdminPortalConfig adminPortalConfig;

    @Override
    @SoulClient(path = "/goodsManagement/query", desc = "查询单条")
    public RpcResult query(AdminGoodsManagementQueryForm queryForm) {
        GoodsManagementQueryParam queryParam = BeanUtils.copyProperties(queryForm, GoodsManagementQueryParam.class);
        GoodsManagementResult result = goodsManagementFacade.getDetailByQueryParam(queryParam);
        AdminGoodsManagementResponse adminGoodsManagementResponse = BeanUtils.copyProperties(result, AdminGoodsManagementResponse.class);
        setOwnerName(adminGoodsManagementResponse);
        return RpcResult.success(adminGoodsManagementResponse);
    }

    private void setOwnerName(AdminGoodsManagementResponse adminGoodsManagementResponse) {
        try {
            OwnerResult ownerResult = ownerFacade.getByCode(adminGoodsManagementResponse.getOwnerCode());
            adminGoodsManagementResponse.setOwnerName(ownerResult.getOwnerName());
            //默认不可编辑
            adminGoodsManagementResponse.setIfEdit(false);
            //字节实体仓，可以编辑
            if (StringUtils.isNotBlank(adminPortalConfig.getByteEntityWarehouseCode())) {
                if (Arrays.stream(adminPortalConfig.getByteEntityWarehouseCode().split(",")).collect(Collectors.toList()).contains(ownerResult.getEntityWarehouseCode())) {
                    adminGoodsManagementResponse.setIfEdit(true);
                }
            }
        } catch (Exception e) {
            log.error("[WebGoodsManagementInterfaceImpl-queryList]=====货主数据异常====:{}", adminGoodsManagementResponse.getOwnerCode(), e);
        }
    }

    @Override
    @SoulClient(path = "/goodsManagement/queryList", desc = "列表查询")
    public RpcResult queryList(AdminGoodsManagementQueryForm queryForm) {
        GoodsManagementQueryParam queryParam = BeanUtils.copyProperties(queryForm, GoodsManagementQueryParam.class);
        ListVO<GoodsManagementResult> resultListVO = goodsManagementFacade.pageListByQueryParam(queryParam);
        List<AdminGoodsManagementResponse> adminGoodsManagementResponses = BeanUtils.copyProperties(resultListVO.getDataList(), AdminGoodsManagementResponse.class);
        adminGoodsManagementResponses.forEach(res -> {
            setOwnerName(res);
            calculateVolume(res);
            res.setHasValidStock(!goodsInventoryFacade.getCanModifyFlag(res.getGoodsCode(), res.getUserId(), res.getOwnerCode()));
        });
        return RpcResult.success(ListVO.build(resultListVO.getPage(), adminGoodsManagementResponses));
    }

    private void calculateVolume(AdminGoodsManagementResponse response) {
        if (response == null || response.getVolume() != null ||
                response.getLength() == null || response.getWidth() == null || response.getHeight() == null) {
            return;
        }

        response.setVolume(response.getLength() * response.getWidth() * response.getHeight());
    }

    @Override
    @SoulClient(path = "/goodsManagement/selectItem", desc = "下拉查询")
    public RpcResult selectItem(AdminGoodsManagementQueryForm queryForm) {
        GoodsManagementQueryParam queryParam = BeanUtils.copyProperties(queryForm, GoodsManagementQueryParam.class);
        List<GoodsManagementResult> resultList = goodsManagementFacade.listByQueryParam(queryParam);
        return RpcResult.success(EnumUtils.build(resultList, "id", "saleOrderNo"));
    }

    @Override
    @SoulClient(path = "/goodsManagement/create", desc = "创建")
    public RpcResult create(AdminGoodsManagementAddForm addForm) {
        GoodsManagementAddParam addParam = BeanUtils.copyProperties(addForm, GoodsManagementAddParam.class);
        return RpcResult.isSuccess(goodsManagementFacade.add(addParam), "创建失败。");
    }

    @Override
    @SoulClient(path = "/goodsManagement/edit", desc = "编辑保存")
    public RpcResult edit(AdminGoodsManagementAddForm addForm) {
        GoodsManagementAddParam addParam = BeanUtils.copyProperties(addForm, GoodsManagementAddParam.class);
        addParam.setOperationLogParam(new GoodsOperationLogParam(SimpleUserHelper.getRealUserId(), OperationType.OT_GOODS_MANAGEMENT_EDIT,
                "小二端-货品效期管理界面-编辑效期"));

        return RpcResult.isSuccess(goodsManagementFacade.updateById(addParam), "编辑保存失败。");
    }

    @Override
    @SoulClient(path = "/goodsManagement/editAndJumpInOrderCheck", desc = "编辑保存")
    public RpcResult editAndJumpInOrderCheck(AdminGoodsManagementAddForm addForm) {
        GoodsManagementAddParam addParam = BeanUtils.copyProperties(addForm, GoodsManagementAddParam.class);
        addParam.setOperationLogParam(new GoodsOperationLogParam(SimpleUserHelper.getRealUserId(), OperationType.OT_GOODS_MANAGEMENT_EDIT,
                "小二端-货品效期管理界面-管理货品"));

        return RpcResult.isSuccess(goodsManagementFacade.updateById(addParam, true, true), "编辑保存失败。");
    }

    @Override
    @SoulClient(path = "/goodsManagement/drop", desc = "删除")
    public RpcResult drop(AdminGoodsManagementQueryForm queryForm) {
        GoodsManagementQueryParam queryParam = BeanUtils.copyProperties(queryForm, GoodsManagementQueryParam.class);
        return RpcResult.isSuccess(goodsManagementFacade.removeByQueryParam(queryParam), "删除失败。");
    }

    @Override
    @SoulClient(path = "/goodsManagement/detail", desc = "详情")
    public RpcResult detail(AdminGoodsManagementQueryForm queryForm) {
        GoodsManagementQueryParam queryParam = BeanUtils.copyProperties(queryForm, GoodsManagementQueryParam.class);
        GoodsManagementResult result = goodsManagementFacade.getDetailByQueryParam(queryParam);
        return RpcResult.success(BeanUtils.copyProperties(result, AdminGoodsManagementResponse.class));
    }

    @Override
    @SoulClient(path = "/goodsManagement/exportExcel", desc = "导出货品数据")
    public RpcResult exportExcel(AdminGoodsManagementQueryForm queryForm) {
        return exportExecutor.asyncExportTask( ExcelExportEnum.EXCEL_EXPORT_GOODS_MANAGEMENT,BeanUtils.copyProperties(queryForm, GoodsManagementQueryParam.class));
    }

    @Override
    @SoulClient(path = "/goodsManagement/updateExternalCode", desc = "更新外部货品ID")
    public RpcResult updateExternalCode(AdminGoodsManagementAddForm addForm) {
        GoodsManagementAddParam addParam = BeanUtils.copyProperties(addForm, GoodsManagementAddParam.class);
        addParam.setOperationLogParam(new GoodsOperationLogParam(SimpleUserHelper.getRealUserId(),
                OperationType.OT_GOODS_MANAGEMENT_EDIT, "小二端-更新外部货品ID"));

        boolean updateExternalCode = goodsManagementFacade.updateExternalCode(addParam);
        return RpcResult.isSuccess(updateExternalCode, "更新外部货品ID失败!");
    }

    @Override
    @SoulClient(path = "/goodsManagement/updateExternalSku", desc = "更新菜鸟货品ID")
    public RpcResult updateExternalSku(AdminGoodsManagementAddForm addForm) {
        GoodsManagementAddParam addParam = BeanUtils.copyProperties(addForm, GoodsManagementAddParam.class);
        addParam.setOperationLogParam(new GoodsOperationLogParam(SimpleUserHelper.getRealUserId(),
                OperationType.OT_GOODS_MANAGEMENT_EDIT, "小二端-更新外部货品ID"));

        boolean updateExternalCode = goodsManagementFacade.updateExternalCode(addParam);
        return RpcResult.isSuccess(updateExternalCode, "更新外部货品ID失败!");
    }


}
