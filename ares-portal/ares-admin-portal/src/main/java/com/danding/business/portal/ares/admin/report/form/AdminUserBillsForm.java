package com.danding.business.portal.ares.admin.report.form;

import com.danding.business.common.ares.enums.report.BillType;
import com.danding.business.common.ares.enums.report.BusinessType;
import com.danding.business.common.ares.enums.report.BusinessWithType;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 用户账单
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-17
 */
@Data
public class AdminUserBillsForm implements Serializable {
    private static final long serialVersionUID = 3995107015072082033L;
    /**
     * 账单类型：1应收/2应付
     */
    @NotNull(message = "账单类型不能为空！1应收/2应付")
    private BillType billType;

    /**
     * 业务类型:1销售单/2采购单/3
     */
    @NotNull(message = "业务类型不能为空！")
    private BusinessType billBusinessType;

    /**
     * 结算对象类型：1供应商/2客户/3用户
     */
    @NotNull(message = "结算对象类型不能为空！")
    private BusinessWithType billWithType;

    /**
     * 结算对象编码
     */
    @NotBlank(message = "结算对象编码不能为空！")
    private String billWithCode;

    /**
     * 结算对象名称
     */
    @NotBlank(message = "结算对象名称不能为空！")
    private String billWithName;

    /**
     * 账单金额
     */
    @NotNull(message = "账单金额不能为空！")
    @Min(value = 0, message = "账单金额不能为负数！")
    private BigDecimal billAmount;

    /**
     * 货币类型
     */
    @NotBlank(message = "货币类型不能为空！")
    private String billCurrency;

    /**
     * 汇率
     */
    //@NotNull(message = "汇率不能为空！")
    @Min(value = 0, message = "汇率不能为负数！")
    private BigDecimal currencyRate;

    /**
     * 相关单号
     */
    @NotBlank(message = "相关单号不能为空！")
    private String relatedBillNo;

    /**
     * 相关单号对应的销售或采购人员ID
     */
    @NotNull(message = "相关单号对应的销售或采购人员ID不能为空！")
    private Long relatedUserId;

    /**
     * 相关单号对应的销售或采购人员姓名
     */
    @NotBlank(message = "相关单号对应的销售或采购人员姓名不能为空！")
    private String relatedUserName;

    /**
     * 备注
     */
    private String remark;

}
