package com.danding.business.portal.ares.admin.consumables.Interface;

import com.danding.business.portal.ares.admin.consumables.form.AdminPackageConsumablesCostAddForm;
import com.danding.business.portal.ares.admin.consumables.form.AdminPackageConsumablesCostQueryForm;
import com.danding.soul.client.common.result.RpcResult;

import javax.validation.Valid;
/**
 * <p>
 * 包耗材成本 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
public interface IAdminPackageConsumablesCostInterface {


    /**
     * 列表查询
     *
     * @param queryForm
     * @return
     */
    RpcResult queryList(AdminPackageConsumablesCostQueryForm queryForm);


}
