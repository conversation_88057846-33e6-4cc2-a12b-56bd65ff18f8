package com.danding.business.portal.ares.admin.consumables.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 包耗材盘点添加
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */

@Data
@ApiModel(value="MaterialInventoryCheck对象", description="包耗材盘点添加")
public class AdminMaterialInventoryCheckAddForm implements Serializable {


    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long userId;

    /**
     * 盘点任务编号
     */
    @ApiModelProperty(value = "盘点任务编号")
    private String inventoryCheckJob;

    /**
     * 仓库
     */
    @ApiModelProperty(value = "仓库")
    private String warehouseCode;

    /**
     * 盘点单号
     */
    @ApiModelProperty(value = "盘点单号")
    private String inventoryCheckCode;

    /**
     * 盘点区域
     */
    @ApiModelProperty(value = "盘点区域")
    private String locationType;
    private List<String> locationTypeList;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 盘点工具
     */
    @ApiModelProperty(value = "盘点工具")
    private String checkTool;

    /**
     * 盘点方式
     */
    @ApiModelProperty(value = "盘点方式")
    private String checkType;

    /**
     * 计划品类数
     */
    @ApiModelProperty(value = "计划品类数")
    private BigDecimal planSkuQty;

    /**
     * 计划账面数
     */
    @ApiModelProperty(value = "计划账面数")
    private BigDecimal planQty;

    /**
     * 实盘品类数
     */
    @ApiModelProperty(value = "实盘品类数")
    private BigDecimal realSkuQty;

    /**
     * 实盘数
     */
    @ApiModelProperty(value = "实盘数")
    private BigDecimal realQty;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createByName;

    /**
     * 完成盘点人
     */
    @ApiModelProperty(value = "完成盘点人")
    private String checkCompleteUser;

    /**
     * 盘点完成时间
     */
    @ApiModelProperty(value = "盘点完成时间")
    private Long checkCompleteTime;

    /**
     * 关联调整单
     */
    @ApiModelProperty(value = "关联调整单")
    private String adjustCode;
    /**
     * 生成空库位，1生产 0 不生成，默认生成
     */

    private Integer genEmptyLocation =1;



}
