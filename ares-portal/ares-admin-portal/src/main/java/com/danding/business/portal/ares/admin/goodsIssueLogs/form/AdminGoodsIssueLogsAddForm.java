package com.danding.business.portal.ares.admin.goodsIssueLogs.form;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 货品下发日志表添加
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */

@Data
@ApiModel(value="GoodsIssueLogs对象", description="货品下发日志表添加")
public class AdminGoodsIssueLogsAddForm implements Serializable {


    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 关联id
     */
    @ApiModelProperty(value = "关联id")
    private Long parentId;

    /**
     * 报文
     */
    @ApiModelProperty(value = "报文")
    private String message;

    /**
     * 回执
     */
    @ApiModelProperty(value = "回执")
    private String receipt;

    /**
     * 下发时间
     */
    @ApiModelProperty(value = "下发时间")
    private Long issueTime;

    /**
     * 回执时间
     */
    @ApiModelProperty(value = "回执时间")
    private Long receiptTime;


}
