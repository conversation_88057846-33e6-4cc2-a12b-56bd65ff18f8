package com.danding.business.portal.ares.admin.goods.impl;

import com.danding.business.client.ares.goods.facade.IGoodsProfitFacade;
import com.danding.business.client.ares.goods.param.GoodsProfitAddParam;
import com.danding.business.client.ares.goods.param.GoodsProfitQueryParam;
import com.danding.business.client.ares.goods.param.GoodsQueryParam;
import com.danding.business.client.ares.goods.result.GoodsProfitResult;
import com.danding.business.common.ares.enums.ExcelExportEnum;
import com.danding.business.portal.ares.admin.export.ExportExecutor;
import com.danding.business.portal.ares.admin.goods.Interface.IAdminGoodsProfitInterface;
import com.danding.business.portal.ares.admin.goods.form.AdminGoodsProfitAddForm;
import com.danding.business.portal.ares.admin.goods.form.AdminGoodsProfitQueryForm;
import com.danding.business.portal.ares.admin.goods.form.AdminGoodsQueryForm;
import com.danding.business.portal.ares.admin.goods.response.AdminGoodsProfitResponse;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.common.utils.EnumUtils;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 货品利润表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-05
 */
@DubboService
public class AdminGoodsProfitInterfaceImpl implements IAdminGoodsProfitInterface {

    @DubboReference
    private IGoodsProfitFacade goodsProfitFacade;

    @Resource
    private ExportExecutor exportExecutor;

    @Override
    @SoulClient(path = "/goodsProfit/query", desc = "查询单条")
    public RpcResult query(AdminGoodsProfitQueryForm queryForm) {
        GoodsProfitQueryParam queryParam = BeanUtils.copyProperties(queryForm, GoodsProfitQueryParam.class);
        GoodsProfitResult result = goodsProfitFacade.getByQueryParam(queryParam);
        return RpcResult.success(BeanUtils.copyProperties(result, AdminGoodsProfitResponse.class));
    }

    @Override
    @SoulClient(path = "/goodsProfit/queryList", desc = "列表查询")
    public RpcResult queryList(AdminGoodsProfitQueryForm queryForm) {
        GoodsProfitQueryParam queryParam = BeanUtils.copyProperties(queryForm, GoodsProfitQueryParam.class);
        ListVO<GoodsProfitResult> resultListVO = goodsProfitFacade.pageListByQueryParam(queryParam);
        return RpcResult.success(ListVO.build(resultListVO.getPage(), BeanUtils.copyProperties(resultListVO.getDataList(), AdminGoodsProfitResponse.class)));
    }

    @Override
    @SoulClient(path = "/goodsProfit/selectItem", desc = "下拉查询")
    public RpcResult selectItem(AdminGoodsProfitQueryForm queryForm) {
        GoodsProfitQueryParam queryParam = BeanUtils.copyProperties(queryForm, GoodsProfitQueryParam.class);
        List<GoodsProfitResult> resultList = goodsProfitFacade.listByQueryParam(queryParam);
        return RpcResult.success(EnumUtils.build(resultList, "id", "saleOrderNo"));
    }

    @Override
    @SoulClient(path = "/goodsProfit/create", desc = "创建")
    public RpcResult create(AdminGoodsProfitAddForm addForm) {
        GoodsProfitAddParam addParam = BeanUtils.copyProperties(addForm, GoodsProfitAddParam.class);
        return RpcResult.isSuccess(goodsProfitFacade.add(addParam), "创建失败。");
    }

    @Override
    @SoulClient(path = "/goodsProfit/edit", desc = "编辑保存")
    public RpcResult edit(AdminGoodsProfitAddForm addForm) {
        GoodsProfitAddParam addParam = BeanUtils.copyProperties(addForm, GoodsProfitAddParam.class);
        return RpcResult.isSuccess(goodsProfitFacade.updateById(addParam), "编辑保存失败。");
    }

    @Override
    @SoulClient(path = "/goodsProfit/drop", desc = "删除")
    public RpcResult drop(AdminGoodsProfitQueryForm queryForm) {
        GoodsProfitQueryParam queryParam = BeanUtils.copyProperties(queryForm, GoodsProfitQueryParam.class);
        return RpcResult.isSuccess(goodsProfitFacade.removeByQueryParam(queryParam), "删除失败。");
    }

    @Override
    @SoulClient(path = "/goodsProfit/detail", desc = "详情")
    public RpcResult detail(AdminGoodsProfitQueryForm queryForm) {
        GoodsProfitQueryParam queryParam = BeanUtils.copyProperties(queryForm, GoodsProfitQueryParam.class);
        GoodsProfitResult result = goodsProfitFacade.getByQueryParam(queryParam);
        return RpcResult.success(BeanUtils.copyProperties(result, AdminGoodsProfitResponse.class));
    }

    @Override
    @SoulClient(path = "/goodsProfit/exportExcel", desc = "利润导出")
    public RpcResult exportExcel(AdminGoodsQueryForm queryForm) {
        return exportExecutor.asyncExportTask(ExcelExportEnum.EXCEL_EXPORT_GOODS_PROFIT,BeanUtils.copyProperties(queryForm, GoodsQueryParam.class));
    }
}
