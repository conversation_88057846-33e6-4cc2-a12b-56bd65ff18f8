package com.danding.business.portal.ares.admin.order.form;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 物流与货品关系添加
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-21
 */

@Data
@ApiModel(value="OutOrderLogisticsDetail对象", description="物流与货品关系添加")
public class AdminOutOrderLogisticsDetailAddForm implements Serializable {


    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 订单单号
     */
    @ApiModelProperty(value = "订单单号")
    private String orderNo;

    /**
     * 包裹号
     */
    @ApiModelProperty(value = "包裹号")
    private String parcelNo;

    /**
     * 物流单号
     */
    @ApiModelProperty(value = "物流单号")
    private String logisticsNo;

    /**
     * 货品SKU编码
     */
    @ApiModelProperty(value = "货品SKU编码")
    private String goodsCode;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchCode;

    /**
     * 内部批次号
     */
    private String internalBatchCode;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer actualQuantity;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    private Date productionDate;

    /**
     * 过期日期
     */
    @ApiModelProperty(value = "过期日期")
    private Date expireDate;

    /**
     * 正品1，次品2
     */
    @ApiModelProperty(value = "正品1，次品2")
    private Integer inventoryType;


}
