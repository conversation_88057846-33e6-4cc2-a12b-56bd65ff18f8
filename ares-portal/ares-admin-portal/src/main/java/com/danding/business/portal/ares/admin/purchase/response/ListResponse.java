package com.danding.business.portal.ares.admin.purchase.response;

import com.danding.business.common.ares.enums.goods.GoodsType;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020-11-26 16:50
 */
@Data
public class ListResponse implements Serializable {
    private static final long serialVersionUID = -166290571236643159L;
    private String id;
    private String name;
    private Integer tradeType;

    public static ListResponse build(String id, String name, GoodsType type) {
        ListResponse response = new ListResponse();
        response.setId(id);
        response.setName(name);
        response.setTradeType(type.getValue());
        return response;
    }
}
