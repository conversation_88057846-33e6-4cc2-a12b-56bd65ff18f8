package com.danding.business.portal.ares.admin.tallyReport.Interface;


import com.danding.business.portal.ares.admin.tallyReport.form.AdminTallyReportDetailBatchAddForm;
import com.danding.business.portal.ares.admin.tallyReport.form.AdminTallyReportDetailBatchQueryForm;
import com.danding.soul.client.common.result.RpcResult;

import javax.validation.Valid;
/**
 * <p>
 * 理货报告详细批次表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
public interface IAdminTallyReportDetailBatchInterface {

    /**
     * 查询单条
     *
     * @param queryForm
     * @return
     */
    RpcResult query(AdminTallyReportDetailBatchQueryForm queryForm);

    /**
     * 列表查询
     *
     * @param queryForm
     * @return
     */
    RpcResult queryList(AdminTallyReportDetailBatchQueryForm queryForm);

    /**
     * 下拉查询
     *
     * @param queryForm
     * @return
     */
    RpcResult selectItem(AdminTallyReportDetailBatchQueryForm queryForm);

    /**
     * 创建
     *
     * @param addForm
     * @return
     */
    RpcResult create(@Valid AdminTallyReportDetailBatchAddForm addForm);

    /**
     * 编辑保存
     *
     * @param addForm
     * @return
     */
    RpcResult edit(@Valid AdminTallyReportDetailBatchAddForm addForm);

    /**
     * 删除
     *
     * @param queryForm
     * @return
     */
    RpcResult drop(AdminTallyReportDetailBatchQueryForm queryForm);

    /**
     * 详情
     *
     * @param queryForm
     * @return
     */
    RpcResult detail(AdminTallyReportDetailBatchQueryForm queryForm);

}
