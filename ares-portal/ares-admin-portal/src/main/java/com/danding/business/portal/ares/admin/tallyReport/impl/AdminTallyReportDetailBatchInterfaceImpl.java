package com.danding.business.portal.ares.admin.tallyReport.impl;

import com.danding.business.client.ares.tallyReport.result.TallyReportDetailBatchResult;
import com.danding.business.client.ares.tallyReport.param.TallyReportDetailBatchQueryParam;
import com.danding.business.client.ares.tallyReport.param.TallyReportDetailBatchAddParam;
import com.danding.business.portal.ares.admin.tallyReport.form.AdminTallyReportDetailBatchAddForm;
import com.danding.business.portal.ares.admin.tallyReport.form.AdminTallyReportDetailBatchQueryForm;
import com.danding.business.portal.ares.admin.tallyReport.response.AdminTallyReportDetailBatchResponse;
import com.danding.business.client.ares.tallyReport.facade.ITallyReportDetailBatchFacade;
import com.danding.business.portal.ares.admin.tallyReport.Interface.IAdminTallyReportDetailBatchInterface;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.common.utils.EnumUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;


/**
 * <p>
 * 理货报告详细批次表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */
@DubboService
public class AdminTallyReportDetailBatchInterfaceImpl implements IAdminTallyReportDetailBatchInterface  {

    @DubboReference
    private ITallyReportDetailBatchFacade tallyReportDetailBatchFacade;

    @Override
    @SoulClient(path = "/tallyReportDetailBatch/query", desc = "查询单条")
    public RpcResult query(AdminTallyReportDetailBatchQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        TallyReportDetailBatchQueryParam queryParam = BeanUtils.copyProperties(queryForm, TallyReportDetailBatchQueryParam.class);
        TallyReportDetailBatchResult result = tallyReportDetailBatchFacade.getByQueryParam(queryParam);
        return RpcResult.success(BeanUtils.copyProperties(result, AdminTallyReportDetailBatchResponse.class));
    }

    @Override
    @SoulClient(path = "/tallyReportDetailBatch/queryList", desc = "列表查询")
    public RpcResult queryList(AdminTallyReportDetailBatchQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        TallyReportDetailBatchQueryParam queryParam = BeanUtils.copyProperties(queryForm, TallyReportDetailBatchQueryParam.class);
        ListVO<TallyReportDetailBatchResult> resultListVO = tallyReportDetailBatchFacade.pageListByQueryParam(queryParam);
        return RpcResult.success(ListVO.build(resultListVO.getPage(), BeanUtils.copyProperties(resultListVO.getDataList(), AdminTallyReportDetailBatchResponse.class)));
    }

    @Override
    @SoulClient(path = "/tallyReportDetailBatch/selectItem", desc = "下拉查询")
    public RpcResult selectItem(AdminTallyReportDetailBatchQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        TallyReportDetailBatchQueryParam queryParam = BeanUtils.copyProperties(queryForm, TallyReportDetailBatchQueryParam.class);
        List<TallyReportDetailBatchResult> resultList = tallyReportDetailBatchFacade.listByQueryParam(queryParam);
        return RpcResult.success(EnumUtils.build(resultList, "id", "saleOrderNo"));
    }

    @Override
    @SoulClient(path = "/tallyReportDetailBatch/create", desc = "创建")
    public RpcResult create(AdminTallyReportDetailBatchAddForm addForm) {
        // 网关层统一设置userId
        TallyReportDetailBatchAddParam addParam = BeanUtils.copyProperties(addForm, TallyReportDetailBatchAddParam.class);
        return RpcResult.isSuccess(tallyReportDetailBatchFacade.add(addParam), "创建失败。");
    }


    @Override
    @SoulClient(path = "/tallyReportDetailBatch/edit", desc = "编辑保存")
    public RpcResult edit(AdminTallyReportDetailBatchAddForm addForm) {
        // 网关层统一设置userId
        TallyReportDetailBatchAddParam addParam = BeanUtils.copyProperties(addForm, TallyReportDetailBatchAddParam.class);
        return RpcResult.isSuccess(tallyReportDetailBatchFacade.updateById(addParam), "编辑保存失败。");
    }

    @Override
    @SoulClient(path = "/tallyReportDetailBatch/drop", desc = "删除")
    public RpcResult drop(AdminTallyReportDetailBatchQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        TallyReportDetailBatchQueryParam queryParam = BeanUtils.copyProperties(queryForm, TallyReportDetailBatchQueryParam.class);
        return RpcResult.isSuccess(tallyReportDetailBatchFacade.removeByQueryParam(queryParam), "删除失败。");
    }

    @Override
    @SoulClient(path = "/tallyReportDetailBatch/detail", desc = "详情")
    public RpcResult detail(AdminTallyReportDetailBatchQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        TallyReportDetailBatchQueryParam queryParam = BeanUtils.copyProperties(queryForm, TallyReportDetailBatchQueryParam.class);
        TallyReportDetailBatchResult result = tallyReportDetailBatchFacade.getByQueryParam(queryParam);
        return RpcResult.success(BeanUtils.copyProperties(result, AdminTallyReportDetailBatchResponse.class));
    }
}
