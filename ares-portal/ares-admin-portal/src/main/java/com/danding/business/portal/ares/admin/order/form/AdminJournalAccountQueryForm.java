package com.danding.business.portal.ares.admin.order.form;

import java.util.List;

import com.danding.component.common.api.common.page.Page;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 出入库流水查询param
 * @date 2021-12-27 14:32
 */
@Data
public class AdminJournalAccountQueryForm extends Page {
    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 类型   入库单/出库单
     */
    private String type;

    /**
     * 单据类型   采购入库/调拨入库等
     */
    private List<String> orderType;

    /**
     * 单据号
     */
    private String orderNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * sku
     */
    private String sku;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 仓库编码
     */
    private String logicWarehouseCode;

    /**
     * 关联单号: 采购单号,调拨单号
     */
    private String businessNo;
    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 货品Id
     */
    private String goodsCode;

    /**
     * 单据状态
     */
    private String orderStatus;

    /**
     * 正次品
     */
    private String inventoryType;

    /**
     * 批次Id
     */
    private String batchCode;

    /**
     * 内部批次id
     */
    private String internalBatchCode;

    /**
     * 实际完成时间(开始)
     */
    private Long actualTimeStart;

    /**
     * qu
     * 实际完成时间(结束)
     */
    private Long actualTimeEnd;

    /**
     * 创建时间(开始)
     */
    private Long createTimeStart;

    /**
     * 创建时间(完成)
     */
    private Long createTimeEnd;

    /**
     * 发生时间
     */
    private Long occurrenceTimeStart;

    private Long occurrenceTimeEnd;

    /**
     * GoodsQueryType
     */
    private Integer queryType1;
    private String queryInfo1;
    /**
     * 1 单据号 2 关联单号 3 物流单号
     */
    private Integer queryType2;
    private String queryInfo2;

    /**
     * 失效日期
     */
    private Long expireDateStart;
    private Long expireDateEnd;
}
