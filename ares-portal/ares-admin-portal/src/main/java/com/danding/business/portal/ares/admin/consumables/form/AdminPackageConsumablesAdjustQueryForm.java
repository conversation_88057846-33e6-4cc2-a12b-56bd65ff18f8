package com.danding.business.portal.ares.admin.consumables.form;

import com.danding.business.common.ares.enums.packageConsumables.PackageConsumablesAdjustStatusEnum;
import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 包耗材调整单主表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */

@Data
@ApiModel(value="PackageConsumablesAdjust对象", description="包耗材调整单主表查询")
public class AdminPackageConsumablesAdjustQueryForm extends Page {

    private static final long serialVersionUID = 1L;

    /**
     * 调整单号
     */
    private String adjustNo;
    /**
    * 创建开始时间
    */
    private Long createTimeStart;

    /**
    * 创建开始时间
    */
    private Long createTimeEnd;


    /**
     * 调整单号,多个，','号分割
     */

    private String adjustNos;

    /**
     * 关联的任务单号多个，','号分割
     */
    @ApiModelProperty(value = "关联的任务单号")
    private String relatedNos;

    /**
     * 实体仓
     */
    @ApiModelProperty(value = "实体仓")
    private String warehouseCode;



    /**
     * 调整单状态
     */
    @ApiModelProperty(value = "调整单状态")
    private PackageConsumablesAdjustStatusEnum adjustStatus;





}
