package com.danding.business.portal.ares.admin.suppliers.form;

import com.danding.business.common.ares.enums.common.CustomerStatusType;
import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 供货商表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */

@Data
@ApiModel(value="Suppliers对象", description="供货商表查询")
public class AdminSuppliersQueryForm extends Page {


    private static final long serialVersionUID = 1L;
    private Long id;
    /**
    * 创建开始时间
    */
    private Long createTimeStart;
    /**
    * 创建开始时间
    */
    private Long createTimeEnd;
    /**
    * 更新时间
    */
    private Long updateTime;
    /**
    * 创建人
    */
    private Long createBy;
    /**
    * 更新人
    */
    private Long updateBy;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long userId;

    /**
     * 供货商编码
     */
    @ApiModelProperty(value = "供货商编码")
    private String suppliersCode;

    /**
     * 供货商名称
     */
    @ApiModelProperty(value = "供货商名称")
    private String suppliersName;

    /**
     * 绑定用户id
     */
    @ApiModelProperty(value = "绑定用户id")
    private Long bindingUserId;

    /**
     * 合作开始时间
     */
    @ApiModelProperty(value = "合作开始时间")
    private Long cooperationStart;

    /**
     * 合作结束时间
     */
    @ApiModelProperty(value = "合作结束时间")
    private Long cooperationEnd;

    /**
     * 供货商状态
     */
    @ApiModelProperty(value = "供货商状态")
    private CustomerStatusType suppliersStatus;

    /**
     * 附件信息
     */
    @ApiModelProperty(value = "附件信息")
    private String fileJson;


}
