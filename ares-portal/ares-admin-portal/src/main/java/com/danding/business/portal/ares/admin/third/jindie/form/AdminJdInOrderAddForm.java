package com.danding.business.portal.ares.admin.third.jindie.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class AdminJdInOrderAddForm implements Serializable {

    private static final long serialVersionUID = -2817961418444800971L;

    @ApiModelProperty("来源系统")
    private String origSystem;

    @ApiModelProperty("外部单号")
    private String externalNo;

    /**
     * 金蝶传输【XSTH】，代塔内部转为【其它入库】
     */
    @ApiModelProperty("入库类型")
    private String orderType;

    @ApiModelProperty("云仓")
    private String logicWarehouseCode;

    @ApiModelProperty("实体仓")
    private String warehouseCode;

    @ApiModelProperty("预计入库时间")
    private Long expectTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("明细")
    @Valid
    @NotNull(message = "入库单明细不能为空！")
    @Size(min = 1, message = "入库单明细数量不能小于1！")
    private List<AdminJdOrderDetailAddForm> inOrderDetailParamList;

}
