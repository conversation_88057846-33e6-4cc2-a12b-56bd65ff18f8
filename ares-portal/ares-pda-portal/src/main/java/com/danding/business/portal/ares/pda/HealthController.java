/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.danding.business.portal.ares.admin;

import com.danding.soul.client.common.result.RpcResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 功能描述: 启动之后的健康检查
 * 创建时间:  2020/8/24 10:02 上午
 *
 * <AUTHOR>
 */
@Controller
@Api(value = "启动之后的健康检查", tags = "启动之后的健康检查")
public class HealthController {

    /**
     * 启动之后的健康检查
     */
    @GetMapping("/health")
    @ResponseBody
    @ApiOperation(value = "启动之后的健康检查", notes = "")
    public RpcResult<Boolean> health() {
        return RpcResult.success("成功");
    }

}
