package com.danding.business.portal.ares.web.logicwarehouse.response;


import com.danding.business.common.ares.BO.report.BaseBO;
import com.danding.business.common.ares.enums.common.*;
import com.danding.component.common.rpc.common.annotation.ResultReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 云仓表返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */

@Data
@ApiModel(value = "LogicWarehouse对象", description = "云仓表返回对象")
public class WebLogicWarehouseResponse extends BaseBO implements Serializable {

    @ApiModelProperty(value = "id")
    @ResultReference(referenceType = ResultReference.ReferenceType.COPY, localReferProperty = "id")
    private String id;

    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long userId;

    /**
     * 逻辑仓库编码
     */
    @ApiModelProperty(value = "逻辑仓库编码")
    private String logicWarehouseCode;

    /**
     * 逻辑仓库名称
     */
    @ApiModelProperty(value = "逻辑仓库名称")
    private String logicWarehouseName;

    /**
     * 逻辑仓库类型
     */
    @ApiModelProperty(value = "逻辑仓库类型")
    private LogicWarehouseType logicWarehouseType;
    private Integer logicWarehouseTypeCode;
    private String logicWarehouseTypeName;

    /**
     * 实体仓库编码
     */
    @ApiModelProperty(value = "实体仓库编码")
    private String entityWarehouseCode;
    private String EntityWarehouseName;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String ownerCode;

    /**
     * 快递公司编码
     */
    @ApiModelProperty(value = "快递公司编码")
    private String expressCode;
    private List<String> expressCodeList;

    /**
     * 快递公司名称
     */
    @ApiModelProperty(value = "快递公司名称")
    private String expressName;
    private List<String> expressNameList;

    /**
     * 账册号
     */
    @ApiModelProperty(value = "账册号")
    private String accountCode;

    /**
     * 清关企业编码
     */
    @ApiModelProperty(value = "清关企业编码")
    private String declareCompanyCode;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码")
    private Integer provinceCode;

    private String provinceId;

    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    private String province;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    private Integer cityCode;

    private String cityId;

    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 区编码
     */
    @ApiModelProperty(value = "区编码")
    private Integer zoneCode;

    private String zoneId;

    /**
     * 区
     */
    @ApiModelProperty(value = "区")
    private String zone;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String address;

    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String postcode;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String linkman;

    /**
     * 联系手机
     */
    @ApiModelProperty(value = "联系手机")
    private String telephone;

    /**
     * 贸易类型
     */
    @ApiModelProperty(value = "贸易类型")
    private TradeType tradeType;

    /**
     * 贸易类型
     */
    @ApiModelProperty(value = "贸易类型")
    private Integer tradeTypeCode;

    /**
     * 贸易类型
     */
    @ApiModelProperty(value = "贸易类型")
    private String tradeTypeName;

    /**
     * 开启状态
     */
    @ApiModelProperty(value = "开启状态")
    private Integer openStatus;
    private String openStatusName;

    private String ownerName;

    private Long createTime;

    private Long updateTime;

    /**
     * 口岸
     */
    private String port;

    /**
     * 批次规则， 1 效期有限， 5 先入先出
     */
    private BatchRule batchRule;

    private String batchRuleName;

    private Integer batchRuleCode;

    /**
     * 是否管理效期 0：关闭 1：开启
     */
    private Integer isBatch;

    private TrusteeshipType isTrusteeship;
    private String isTrusteeshipName;

    /**
     * 是否自动下发
     */
    private AutoPushType isAutoPush;
    private String isAutoPushName;

    /**
     * 是否使用默认地址
     */
    private DefaultAddressType defaultAddressType;

    /**
     * 是否监管
     */
    private RegulatoryType isRegulatory;

    /**
     * 关联仓库
     */
    private String mixLogicWarehouseCode;

    /**
     * 对接系统
     */
    private String systemCode;

    /**
     * 库存分组编码
     */
    private String channelCode;

    /**
     * 预售
     */
    private OwnerPreSaleType ownerPreSaleType;
    private String ownerPreSaleTypeName;

    /**
     * 关联预售仓库编码
     */
    private String preSaleRelatedLogicWarehouseCode;
    private String preSaleRelatedLogicWarehouseCodeName;

    /**
     * 上游实体仓编码
     */
    private String upEntityWarehouseCode;

    /**
     * 是否 4pl: 1是, 0否
     */
    private Integer fourPl;
    /**
     * 外部货主code
     */
    private String outOwnerCode;
    /**
     * 外部货主类型
     */
    private String outOwnerType;

    /**
     * 绑定的外部仓库编码，只用于新增，没有存储
     */
    private String outWarehouseCode;

}
