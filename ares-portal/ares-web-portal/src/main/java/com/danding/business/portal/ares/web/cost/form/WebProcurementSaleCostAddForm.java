package com.danding.business.portal.ares.web.cost.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 添加
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-16
 */

@Data
@ApiModel(value="ProcurementSaleCost对象", description="添加")
public class WebProcurementSaleCostAddForm implements Serializable {


    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 计算时间
     */
    @ApiModelProperty(value = "计算时间")
    private Long dayTime;

    /**
     * 逻辑仓编码
     */
    @ApiModelProperty(value = "逻辑仓编码")
    private String logicWarehouseCode;

    /**
     * 货品编码
     */
    @ApiModelProperty(value = "货品编码")
    private String goodsCode;

    /**
     * 货品名称
     */
    @ApiModelProperty(value = "货品名称")
    private String goodsName;

    /**
     * 外部货品编码
     */
    @ApiModelProperty(value = "外部货品编码")
    private String sku;

    /**
     * 条形码
     */
    @ApiModelProperty(value = "条形码")
    private String barcode;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    private String model;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 计算方式：1 批次，2 加权平均
     */
    @ApiModelProperty(value = "计算方式：1 批次，2 加权平均")
    private Integer type;

    /**
     * 期初数量
     */
    @ApiModelProperty(value = "期初数量")
    private Integer earlyQty;

    /**
     * 期初金额
     */
    @ApiModelProperty(value = "期初金额")
    private BigDecimal earlyTotalPrice;

    /**
     * 期除单价
     */
    @ApiModelProperty(value = "期除单价")
    private BigDecimal earlyPrice;

    /**
     * 本期入库数量
     */
    @ApiModelProperty(value = "本期入库数量")
    private Integer inQty;

    /**
     * 本期入库金额
     */
    @ApiModelProperty(value = "本期入库金额")
    private BigDecimal inTotalPrice;

    /**
     * 本期入库单价
     */
    @ApiModelProperty(value = "本期入库单价")
    private BigDecimal inPrice;

    /**
     * 本期出库数量
     */
    @ApiModelProperty(value = "本期出库数量")
    private Integer outQty;

    /**
     * 本期出库金额
     */
    @ApiModelProperty(value = "本期出库金额")
    private BigDecimal outTotalPrice;

    /**
     * 本期出库单价
     */
    @ApiModelProperty(value = "本期出库单价")
    private BigDecimal outPrice;

    /**
     * 期末数量
     */
    @ApiModelProperty(value = "期末数量")
    private Integer endQty;

    /**
     * 期末金额
     */
    @ApiModelProperty(value = "期末金额")
    private BigDecimal endTotalPrice;

    /**
     * 期末单价
     */
    @ApiModelProperty(value = "期末单价")
    private BigDecimal endPrice;


}
