package com.danding.business.portal.ares.web.sendExpire.impl;

import com.danding.business.client.ares.sendExpire.facade.IGoodsInventorySendExpireFacade;
import com.danding.business.client.ares.sendExpire.param.GoodsInventorySendExpireAddParam;
import com.danding.business.client.ares.sendExpire.param.GoodsInventorySendExpireHideParam;
import com.danding.business.client.ares.sendExpire.param.GoodsInventorySendExpireQueryParam;
import com.danding.business.client.ares.sendExpire.result.GoodsInventorySendExpireResult;
import com.danding.business.portal.ares.web.sendExpire.Interface.IWebGoodsInventorySendExpireInterface;
import com.danding.business.portal.ares.web.sendExpire.form.WebGoodsInventorySendExpireAddForm;
import com.danding.business.portal.ares.web.sendExpire.form.WebGoodsInventorySendExpireHideForm;
import com.danding.business.portal.ares.web.sendExpire.form.WebGoodsInventorySendExpireQueryForm;
import com.danding.business.portal.ares.web.sendExpire.response.WebGoodsInventorySendExpireResponse;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * <p>
 * 发货效期 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-10
 */
@DubboService
public class WebGoodsInventorySendExpireInterfaceImpl implements IWebGoodsInventorySendExpireInterface  {

    @DubboReference
    private IGoodsInventorySendExpireFacade goodsInventorySendExpireFacade;

    @Override
    @SoulClient(path = "/goodsInventorySendExpire/queryList", desc = "列表查询")
    public RpcResult queryList(WebGoodsInventorySendExpireQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        GoodsInventorySendExpireQueryParam queryParam = BeanUtils.copyProperties(queryForm, GoodsInventorySendExpireQueryParam.class);
        ListVO<GoodsInventorySendExpireResult> resultListVO = goodsInventorySendExpireFacade.pageListByQueryParam(queryParam);
        return RpcResult.success(ListVO.build(resultListVO.getPage(), BeanUtils.copyProperties(resultListVO.getDataList(), WebGoodsInventorySendExpireResponse.class)));
    }

    @Override
    @SoulClient(path = "/goodsInventorySendExpire/create", desc = "创建")
    public RpcResult create(WebGoodsInventorySendExpireAddForm addForm) {
        GoodsInventorySendExpireAddParam addParam = BeanUtils.copyProperties(addForm, GoodsInventorySendExpireAddParam.class);
        addParam.setUserId(SimpleUserHelper.getUserId());
        addParam.setUserName(SimpleUserHelper.getUserName());
        addParam.setCreateByName(SimpleUserHelper.getRealUserName());
        addParam.setStatusTime(System.currentTimeMillis());
        addParam.setShopName(addParam.getShopName().trim());
        return RpcResult.isSuccess(goodsInventorySendExpireFacade.add(addParam), "创建失败。");
    }

    @Override
    @SoulClient(path = "/goodsInventorySendExpire/operatingStatus", desc = "状态操作")
    public RpcResult operatingStatus(WebGoodsInventorySendExpireQueryForm addForm) {
        GoodsInventorySendExpireAddParam addParam = BeanUtils.copyProperties(addForm, GoodsInventorySendExpireAddParam.class);
        return RpcResult.isSuccess(goodsInventorySendExpireFacade.operatingStatus(addParam), "操作状态失败!");
    }

    @Override
    @SoulClient(path = "/goodsInventorySendExpire/batch", desc = "查询批次")
    public RpcResult listGoodsInventoryBatch(WebGoodsInventorySendExpireQueryForm addForm) {
        GoodsInventorySendExpireAddParam addParam = BeanUtils.copyProperties(addForm, GoodsInventorySendExpireAddParam.class);
        addParam.setUserId(SimpleUserHelper.getUserId());
        List<GoodsInventorySendExpireResult> expireResultList = goodsInventorySendExpireFacade.listGoodsInventoryBatch(addParam);
        return RpcResult.success(BeanUtils.copyProperties(expireResultList, WebGoodsInventorySendExpireResponse.class));
    }

    @Override
    @SoulClient(path = "/goodsInventorySendExpire/edit", desc = "编辑保存")
    public RpcResult edit(WebGoodsInventorySendExpireAddForm addForm) {
        // 网关层统一设置userId
        GoodsInventorySendExpireAddParam addParam = BeanUtils.copyProperties(addForm, GoodsInventorySendExpireAddParam.class);
        addParam.setUserId(SimpleUserHelper.getUserId());
        addParam.setShopName(addParam.getShopName().trim());
        return RpcResult.isSuccess(goodsInventorySendExpireFacade.updateById(addParam), "编辑保存失败。");
    }

    @Override
    @SoulClient(path = "/goodsInventorySendExpire/drop", desc = "删除")
    public RpcResult drop(WebGoodsInventorySendExpireQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        GoodsInventorySendExpireQueryParam queryParam = BeanUtils.copyProperties(queryForm, GoodsInventorySendExpireQueryParam.class);
        return RpcResult.isSuccess(goodsInventorySendExpireFacade.removeByQueryParam(queryParam), "删除失败。");
    }

    @Override
    @SoulClient(path = "/goodsInventorySendExpire/batchHide", desc = "批量隐藏")
    public RpcResult batchHide(WebGoodsInventorySendExpireHideForm hideForm) {
        GoodsInventorySendExpireHideParam hideParam = BeanUtils.copyProperties(hideForm, GoodsInventorySendExpireHideParam.class);
        hideParam.setUserId(SimpleUserHelper.getUserId());
        return RpcResult.isSuccess(goodsInventorySendExpireFacade.batchHide(hideParam), "批量隐藏失败");
    }

    @Override
    @SoulClient(path = "/goodsInventorySendExpire/recovery", desc = "恢复")
    public RpcResult recovery(WebGoodsInventorySendExpireQueryForm form) {
        GoodsInventorySendExpireAddParam addParam = BeanUtils.copyProperties(form, GoodsInventorySendExpireAddParam.class);
        return RpcResult.isSuccess(goodsInventorySendExpireFacade.recovery(addParam), "操作恢复失败!");
    }
}
