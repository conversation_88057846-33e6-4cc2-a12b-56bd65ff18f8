package com.danding.business.portal.ares.web.brand.response;

import com.danding.business.common.ares.enums.goods.BrandAuthorizationType;
import com.danding.business.common.ares.enums.goods.BrandStatus;
import com.danding.business.common.ares.enums.goods.BrandWaringType;
import com.danding.component.common.api.common.file.FileDto;
import com.danding.component.common.base.DO.BaseEntity;
import com.danding.component.common.rpc.common.annotation.ResultReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 品牌表返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */

@Data
@ApiModel(value = "Brand对象", description = "品牌表返回对象")
public class WebBrandResponse implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "更新时间")
    private Long updateTime;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "更新人")
    private Long updateBy;
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @ResultReference(referenceType = ResultReference.ReferenceType.COPY, localReferProperty = "id")
    private String id;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 品牌编码
     */
    @ApiModelProperty(value = "品牌编码")
    private String brandCode;

    /**
     * 品牌名称
     */
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    /**
     * 品牌名称(英文)
     */
    @ApiModelProperty(value = "品牌名称(英文)")
    private String brandNameEn;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private BrandStatus status;
    private String statusName;

    /**
     * 授权类型
     */
    @ApiModelProperty(value = "授权类型")
    private BrandAuthorizationType authorizationType;
    private String authorizationTypeName;

    /**
     * 授权开始时间
     */
    @ApiModelProperty(value = "授权开始时间")
    private Long authorizationBegin;

    /**
     * 授权结束时间
     */
    @ApiModelProperty(value = "授权结束时间")
    private Long authorizationEnd;

    /**
     * 是否开启品牌预警
     */
    @ApiModelProperty(value = "是否开启品牌预警")
    private BrandWaringType waringType;
    private String waringTypeName;

    /**
     * 预警天数
     */
    @ApiModelProperty(value = "预警天数")
    private Integer warningDate;

    /**
     * 授权文件
     */
    @ApiModelProperty(value = "授权文件")
    private List<FileDto> authorizationFile;

    /**
     * 授权货品(数量)
     */
    @ApiModelProperty(value = "授权数量")
    private Integer authorizationNum;
}
