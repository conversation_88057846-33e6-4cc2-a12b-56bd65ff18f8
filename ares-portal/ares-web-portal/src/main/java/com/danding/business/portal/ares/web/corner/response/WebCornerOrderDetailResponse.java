package com.danding.business.portal.ares.web.corner.response;

import com.danding.business.common.ares.enums.inventory.InventoryType;
import com.danding.business.common.ares.enums.trade.CornerMappingStatus;
import com.danding.business.common.ares.enums.trade.CornerOrderStatus;
import com.danding.component.common.rpc.common.annotation.ResultReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 销售单详细表返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-19
 */

@Data
@ApiModel(value="CornerOrderDetail对象", description="销售单详细表返回对象")
public class WebCornerOrderDetailResponse implements Serializable {


    private static final long serialVersionUID = 1L;
    /**
    * 主键使用的雪花算法，转出给前端时需要转成String 类型
    */
    @ResultReference(referenceType = ResultReference.ReferenceType.COPY, localReferProperty = "id")
    private String id;
    /**
    * 创建时间
    */
    private Long createTime;
    /**
    * 更新时间
    */
    private Long updateTime;
    /**
    * 创建人
    */
    private Long createBy;
    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 分销单号
     */
    @ApiModelProperty(value = "分销单号")
    private String cornerOrderNo;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private CornerOrderStatus status;
    private String statusName;

    /**
     * 分销映射状态
     */
    @ApiModelProperty(value = "分销映射状态")
    private CornerMappingStatus mappingStatus;
    private String mappingStatusName;

    /**
     * 发货逻辑仓库编码
     */
    @ApiModelProperty(value = "发货逻辑仓库编码")
    private String logicWarehouseCode;

    /**
     * 发货逻辑仓库名称
     */
    @ApiModelProperty(value = "发货逻辑仓库名称")
    private String logicWarehouseName;

    /**
     * 收货逻辑仓库编码
     */
    @ApiModelProperty(value = "收货逻辑仓库编码")
    private String receiveLogicWarehouseCode;

    /**
     * 收货逻辑仓库名称
     */
    @ApiModelProperty(value = "收货逻辑仓库名称")
    private String receiveLogicWarehouseName;

    /**
     * 货品sku
     */
    @ApiModelProperty(value = "货品sku")
    private String goodsCode;

    /**
     * 货品名称
     */
    @ApiModelProperty(value = "货品名称")
    private String goodsName;

    /**
     * 外部sku
     */
    @ApiModelProperty(value = "外部sku")
    private String sku;

    /**
     * 品牌编码
     */
    @ApiModelProperty(value = "品牌编码")
    private String brandCode;

    /**
     * 品牌名称
     */
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    /**
     * 货品数量
     */
    @ApiModelProperty(value = "货品数量")
    private Integer goodsNumber;

    /**
     * 计划出库数量
     */
    @ApiModelProperty(value = "计划出库数量")
    private Integer planQuantity;

    /**
     * 实际出库数量
     */
    @ApiModelProperty(value = "实际出库数量")
    private Integer actualQuantity;

    /**
     * 已映射数量
     */
    @ApiModelProperty(value = "已映射数量")
    private Integer mappedQuantity;

    /**
     * 计划退货数量
     */
    @ApiModelProperty(value = "计划退货数量")
    private Integer planReturnedQuantity;

    /**
     * 已退货数量
     */
    @ApiModelProperty(value = "已退货数量")
    private Integer returnedQuantity;

    /**
     * 可退货/可映射数量
     */
    @ApiModelProperty(value = "可退货/可映射数量")
    private Integer usableQuantity;

    /**
     * 实际出库时间
     */
    @ApiModelProperty(value = "实际出库时间")
    private Long actualTime;

    /**
     * 库存类型 正品1，次品2
     */
    @ApiModelProperty(value = "库存类型 正品1，次品2")
    private InventoryType inventoryType;
    private String inventoryTypeName;

    /**
     * 条形码
     */
    @ApiModelProperty(value = "条形码")
    private String barcode;

    /**
     * 批次id
     */
    @ApiModelProperty(value = "批次id")
    private Long batchId;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchCode;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    private Date productionDate;

    /**
     * 过期日期
     */
    @ApiModelProperty(value = "过期日期")
    private Date expireDate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
