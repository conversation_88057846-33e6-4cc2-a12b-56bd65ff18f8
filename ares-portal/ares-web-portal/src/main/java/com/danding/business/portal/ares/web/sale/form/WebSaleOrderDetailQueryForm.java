package com.danding.business.portal.ares.web.sale.form;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 销售单详细表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */

@Data
@ApiModel(value="SaleOrderDetail对象", description="销售单详细表查询")
public class WebSaleOrderDetailQueryForm extends Page {


    private static final long serialVersionUID = 1L;

    /**
     * 销售单号
     */
    @ApiModelProperty(value = "销售单号")
    private String saleOrderNo;

    /**
     * 货品名称
     */
    @ApiModelProperty(value = "货品名称")
    private String goodsName;

    /**
     * 货品sku
     */
    @ApiModelProperty(value = "货品sku")
    private String goodsCode;

    /**
     * 条形码
     */
    @ApiModelProperty(value = "条形码")
    private String barcode;

    /**
     * 货币类型
     */
    @ApiModelProperty(value = "货币类型")
    private String billCurrency;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    /**
     * 零售价格
     */
    @ApiModelProperty(value = "零售价格")
    private BigDecimal retailPrice;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchCode;

    /**
     * 内部批次号
     */
    @ApiModelProperty(value = "内部批次号")
    private String internalBatchCode;

    /**
     * 生产批次号
     */
    @ApiModelProperty(value = "生产批次号")
    private String productBatchCode;

    /**
     * 汇率
     */
    @ApiModelProperty(value = "汇率")
    private BigDecimal currencyRate;

    /**
     * 折扣金额
     */
    @ApiModelProperty(value = "折扣金额")
    private BigDecimal discountAmount;

    /**
     * 折扣率
     */
    @ApiModelProperty(value = "折扣率")
    private Double discountRate;

    /**
     * 货品数量
     */
    @ApiModelProperty(value = "货品数量")
    private Integer goodsNumber;

    /**
     * 实际售价
     */
    @ApiModelProperty(value = "实际售价")
    private BigDecimal totalAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
