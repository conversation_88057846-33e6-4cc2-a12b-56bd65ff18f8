package com.danding.business.portal.ares.web.tallyReport.response;


import com.danding.business.common.ares.enums.inventory.InventoryType;
import com.danding.component.common.rpc.common.annotation.ResultReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 理货报告详细批次表返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-27
 */

@Data
@ApiModel(value="TallyReportDetailBatch对象", description="理货报告详细批次表返回对象")
public class WebTallyReportDetailBatchResponse implements Serializable {


    private static final long serialVersionUID = 1L;
    /**
    * 主键使用的雪花算法，转出给前端时需要转成String 类型
    */
    @ResultReference(referenceType = ResultReference.ReferenceType.COPY, localReferProperty = "id")
    private String id;
    /**
    * 创建时间
    */
    private Long createTime;
    /**
    * 更新时间
    */
    private Long updateTime;
    /**
    * 创建人
    */
    private Long createBy;
    /**
    * 更新人
    */
    private Long updateBy;

    /**
     * 理货编号
     */
    @ApiModelProperty(value = "理货编号")
    private String tallyOrderNo;

    /**
     * 货品sku
     */
    @ApiModelProperty(value = "货品sku")
    private String goodsCode;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchCode;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    private Long productionDate;

    /**
     * 过期日期
     */
    @ApiModelProperty(value = "过期日期")
    private Long expireDate;

    /**
     * 正品1，次品2
     */
    @ApiModelProperty(value = "正品1，次品2")
    private InventoryType inventoryType;

    /**
     * 实际理货数量
     */
    @ApiModelProperty(value = "实际理货数量")
    private Integer tallyNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否超传: 1 是, 默认0
     */
    private Integer exceeded;

    /**
     * 内部批次号
     */
    private String internalBatchCode;

    /**
     * 生产批次号
     */
    private String productBatchCode;
}
