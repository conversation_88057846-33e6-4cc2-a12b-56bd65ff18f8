package com.danding.business.portal.ares.web.report.impl;

import com.alibaba.fastjson.JSON;
import com.danding.business.common.ares.utils.HttpRequestUtils;
import com.danding.business.portal.ares.web.WebPortalConfig;
import com.danding.business.portal.ares.web.report.Interface.IWebTaoTianReportInterface;
import com.danding.business.portal.ares.web.report.form.WebTaoTianFileBodyForm;
import com.danding.saas.manage.api.TenantRpcFacade;
import com.danding.saas.manage.dto.TenantRpcDTO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.exception.BusinessException;
import com.danding.soul.client.common.result.RpcResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@DubboService
public class WebTaoTianReportInterfaceImpl implements IWebTaoTianReportInterface {

    @Autowired
    private WebPortalConfig webPortalConfig;
    @DubboReference
    private TenantRpcFacade tenantRpcFacade;

    @Override
    @SoulClient(path = "/report/getTaoTianFileInfo", desc = "获取淘天上传文件")
    public RpcResult getTaoTianFileInfo(WebTaoTianFileBodyForm taoTianFileBody) {
        if (Objects.isNull(taoTianFileBody)) {
            throw new BusinessException("请求参数不能为空!");
        }
        TenantRpcDTO tenantRpcDTO = tenantRpcFacade.getByDomain(taoTianFileBody.getDomain());
        if (Objects.nonNull(tenantRpcDTO)) {
            taoTianFileBody.setTenantId(tenantRpcDTO.getTenantId());
        }
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("bizData", JSON.toJSONString(taoTianFileBody));
        requestMap.put("method", "taobao.file.upload.apply");
        String postForm = HttpRequestUtils.postForm(requestMap, webPortalConfig.getV3CallbackUrl());
        return RpcResult.success("success", postForm);
    }

}
