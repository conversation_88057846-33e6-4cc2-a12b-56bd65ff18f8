package com.danding.business.portal.ares.web.report.Interface;

import com.danding.soul.client.common.result.RpcResult;

/**
 * <p>
 * 结算单关联账单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-17
 */
public interface IWebUserSettlementBillsInterface {
    /**
     * 账单状态列表
     *
     * @return
     */
    RpcResult billStatusList();

    /**
     * 账单类型列表
     *
     * @return
     */
    RpcResult billTypeList();

    /**
     * 业务类型列表
     *
     * @return
     */
    RpcResult businessWithTypeList();

    /**
     * 结算单状态列表
     *
     * @return
     */
    RpcResult settlementStatusList();

    /**
     * 结算单类型列表
     *
     * @return
     */
    RpcResult settlementTypeList();

    /**
     * 业务类型
     *
     * @return
     */
    RpcResult businessTypeList();

}
