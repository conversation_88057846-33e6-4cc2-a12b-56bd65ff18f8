package com.danding.business.portal.ares.web.transferorder.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.distributionorder.facade.IDistributionOrderFacade;
import com.danding.business.client.ares.goods.facade.IGoodsFacade;
import com.danding.business.client.ares.goods.result.GoodsResult;
import com.danding.business.client.ares.inventory.facade.IGoodsBatchInventoryFacade;
import com.danding.business.client.ares.inventory.facade.IGoodsInventoryFacade;
import com.danding.business.client.ares.inventory.facade.IWsmStockQueryFacade;
import com.danding.business.client.ares.inventory.param.GoodsBatchInventoryQueryParam;
import com.danding.business.client.ares.inventory.param.GoodsInventoryQueryParam;
import com.danding.business.client.ares.inventory.result.GoodsBatchInventoryResult;
import com.danding.business.client.ares.inventory.result.GoodsInventoryResult;
import com.danding.business.client.ares.logicwarehouse.facade.ILogicWarehouseFacade;
import com.danding.business.client.ares.logicwarehouse.result.LogicWarehouseResult;
import com.danding.business.client.ares.readyorder.facade.IReadyOrderFacade;
import com.danding.business.client.ares.transferorder.facade.ITransferGoodsFacade;
import com.danding.business.client.ares.transferorder.facade.ITransferOrderFacade;
import com.danding.business.client.ares.transferorder.facade.ITransferOrderImportFacade;
import com.danding.business.client.ares.transferorder.param.TransferGoodsAddParam;
import com.danding.business.client.ares.transferorder.param.TransferGoodsQueryParam;
import com.danding.business.client.ares.transferorder.param.TransferOrderAddParam;
import com.danding.business.client.ares.transferorder.param.TransferOrderQueryParam;
import com.danding.business.client.ares.transferorder.result.TransferGoodsResult;
import com.danding.business.client.ares.transferorder.result.TransferOrderResult;
import com.danding.business.common.ares.config.CurrencyConfig;
import com.danding.business.common.ares.enums.ExcelExportEnum;
import com.danding.business.common.ares.enums.common.OwnerPreSaleType;
import com.danding.business.common.ares.enums.common.TradeType;
import com.danding.business.common.ares.enums.common.TransferOrderType;
import com.danding.business.common.ares.enums.goods.SpecifyAttributesEnum;
import com.danding.business.common.ares.enums.inventory.InventoryType;
import com.danding.business.common.ares.enums.trade.TallyReportStatus;
import com.danding.business.common.ares.enums.trade.TransferOrderStatus;
import com.danding.business.common.ares.excelV2.param.ImportParam;
import com.danding.business.common.ares.excelV2.param.LoadTaskInfoBaseDTO;
import com.danding.business.portal.ares.web.dataPermission.LGDataPermission;
import com.danding.business.portal.ares.web.export.ExportExecutor;
import com.danding.business.portal.ares.web.transferorder.Interface.IWebTransferOrderInterface;
import com.danding.business.portal.ares.web.transferorder.form.WebTransferOrderAddForm;
import com.danding.business.portal.ares.web.transferorder.form.WebTransferOrderQueryForm;
import com.danding.business.portal.ares.web.transferorder.response.WebTransferBondOrderListResponse;
import com.danding.business.portal.ares.web.transferorder.response.WebTransferGoodsResponse;
import com.danding.business.portal.ares.web.transferorder.response.WebTransferOrderResponse;
import com.danding.component.common.api.common.file.FileDto;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.common.utils.EnumUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.ParkClient;
import com.danding.park.client.core.exception.ParkException;
import com.danding.park.client.core.load.bean.LoadTaskHeader;
import com.danding.park.client.core.load.dto.LoadTaskDTO;
import com.danding.park.client.core.load.dto.LoadTaskInfoDTO;
import com.danding.park.client.core.load.form.LoadTaskCreateForm;
import com.danding.park.client.core.load.query.CurrentLoadTaskQuery;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.exception.BusinessException;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.client.secruity.helper.UserHelper;
import com.danding.ucenter.core.annotation.UCData;
import com.danding.ucenter.core.security.model.data.DataAuthFilterResult;
import com.danding.ucenter.core.security.model.data.DataAuthRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-24
 */
@DubboService
@Slf4j
@SoulClient(path = "/transferOrder", desc = "")
public class WebTransferOrderInterfaceImpl implements IWebTransferOrderInterface {
    @DubboReference
    private ITransferOrderFacade transferOrderFacade;
    @DubboReference
    private ITransferGoodsFacade iTransferGoodsFacade;
    @DubboReference
    private ILogicWarehouseFacade logicWarehouseFacade;
    @DubboReference
    private IGoodsFacade iGoodsFacade;
    @DubboReference
    private IGoodsInventoryFacade iGoodsInventoryFacade;
    @DubboReference
    private IGoodsBatchInventoryFacade iGoodsBatchInventoryFacade;
    @DubboReference
    private ITransferOrderImportFacade iTransferOrderImportFacade;
    @DubboReference
    private IReadyOrderFacade readyOrderFacade;
    @DubboReference
    private IDistributionOrderFacade distributionOrderFacade;

    @Autowired
    private CurrencyConfig billConfig;

    @Resource
    private ExportExecutor exportExecutor;

    @Override
    @SoulClient(path = "/transfer/buildInOut", desc = "一键生成出入库单")
    public RpcResult buildInOut(String transferOrderNo) {
        return RpcResult.success(transferOrderFacade.buildInOut(transferOrderNo));
    }

    @Override
    @SoulClient(path = "/transfer/save", desc = "web端新建调拨单")
    //先出再入
    public RpcResult saveTransferOrder(WebTransferOrderAddForm form) {
        TransferOrderAddParam param = getSaleOrderAddParam(form);
        return RpcResult.success("保存成功", transferOrderFacade.addTransferOrderV2(param));
    }

    private TransferOrderAddParam getSaleOrderAddParam(WebTransferOrderAddForm form) {
        form.setUserId(SimpleUserHelper.getUserId());
        form.setCreateByName(SimpleUserHelper.getRealUserName());
        form.setUserName(SimpleUserHelper.getUserName());
        form.setTransferUser(SimpleUserHelper.getUserName());
        TransferOrderAddParam transferOrderAddParam = BeanUtils.copyProperties(form, TransferOrderAddParam.class);
        List<TransferGoodsAddParam> goodsAddParamList = BeanUtils.copyProperties(form.getTransferGoodsFormList(), TransferGoodsAddParam.class);
        transferOrderAddParam.setTransferGoodsFormList(goodsAddParamList);
        return transferOrderAddParam;
    }

    @Override
    @SoulClient(path = "/transfer/pageByList", desc = "web端分页查询调拨单")
    @LGDataPermission
    @UCData(group = "logicWarehouseFirst", types = {"logicWarehouseSecond"})
    public RpcResult transferOrderPageByList(WebTransferOrderQueryForm form) {
        TransferOrderQueryParam param = BeanUtils.copyProperties(form, TransferOrderQueryParam.class);
        param.setUserId(SimpleUserHelper.getUserId());
        ListVO<TransferOrderResult> resultList = transferOrderFacade.pageListTransferOrderByTransferOrderQueryParam(param);
        List<WebTransferOrderResponse> responseList = BeanUtils.copyProperties(resultList.getDataList(), WebTransferOrderResponse.class);
        if (!CollectionUtils.isEmpty(responseList)) {
            Set<String> logicWarehouseCodeSet = responseList.stream().map(o -> o.getFromWarehouseCode()).collect(Collectors.toSet());
            List<LogicWarehouseResult> logicWarehouseResultList = logicWarehouseFacade.getLogicWarehouseListByCodes(logicWarehouseCodeSet, param.getUserId());
            if (!CollectionUtils.isEmpty(logicWarehouseResultList)) {
                Map<String, LogicWarehouseResult> logicWarehouseResultMap = logicWarehouseResultList.stream().collect(Collectors.toMap(LogicWarehouseResult::getLogicWarehouseCode, o -> o, (k1, k2) -> k1));
                responseList.stream().forEach(response -> {
                    LogicWarehouseResult result = logicWarehouseResultMap.get(response.getFromWarehouseCode());
                    if (result != null) {
                        if (Objects.nonNull(result.getTradeType())) {
                            response.setTradeType(result.getTradeType().getDesc());
                        }
                        response.setFromWarehouseName(result.getLogicWarehouseName());
                        response.setIsRegulatory(result.getIsRegulatory());
                    }
                });
            }
        }
        ListVO<WebTransferOrderResponse> responseListVO = new ListVO<>();
        responseListVO.setDataList(responseList);
        responseListVO.setPage(resultList.getPage());
        return RpcResult.success(responseListVO);
    }

    @DubboReference
    private IWsmStockQueryFacade wsmStockQueryFacade;

    @Override
    @SoulClient(path = "/transfer/getDetailByOrderNo", desc = "详情信息")
    public RpcResult getDetailByOrderNo(String transferOrderNo) {
        TransferOrderQueryParam param = new TransferOrderQueryParam();
        param.setTransferNo(transferOrderNo);
        TransferOrderResult result = transferOrderFacade.getTransferOrderByTransferOrderQueryParam(param);
        LogicWarehouseResult fromLogicWarehouse = logicWarehouseFacade.getDetailByCode(result.getFromWarehouseCode());
        LogicWarehouseResult toLogicWarehouse = logicWarehouseFacade.getDetailByCode(result.getToWarehouseCode());
        TransferGoodsQueryParam param1 = new TransferGoodsQueryParam();
        param1.setTransferNo(transferOrderNo);
        List<TransferGoodsResult> transferGoodsResults = iTransferGoodsFacade.listTransferGoodsByTransferGoodsQueryParam(param1);
        List<WebTransferGoodsResponse> goodsResponseList = BeanUtils.copyProperties(transferGoodsResults, WebTransferGoodsResponse.class);
        for (WebTransferGoodsResponse goodsResponse : goodsResponseList) {
            goodsResponse.setBillCurrencyName(billConfig.getNameByCode(goodsResponse.getBillCurrency()));
            GoodsResult goodsResult = iGoodsFacade.getGoodsByParam(result.getUserId(), goodsResponse.getGoodsCode());
            if (Objects.isNull(goodsResult)) {
                throw new BusinessException(goodsResponse.getGoodsCode() + ":货品查询不存在!");
            }
            goodsResponse.setBrandName(goodsResult.getBrandName());
            goodsResponse.setBarcode(goodsResult.getBarcode());
            goodsResponse.setSku(goodsResult.getSku());
            if (Objects.equals(SpecifyAttributesEnum.YES, result.getSpecifyAttributes())) {
                GoodsBatchInventoryQueryParam batchInventoryQueryParam = new GoodsBatchInventoryQueryParam();
                batchInventoryQueryParam.setSku(goodsResponse.getSku());
                batchInventoryQueryParam.setGoodsCode(goodsResponse.getGoodsCode());
                batchInventoryQueryParam.setBatchCode(goodsResponse.getBatchCode());
                batchInventoryQueryParam.setInternalBatchCode(goodsResponse.getInternalBatchCode());
                batchInventoryQueryParam.setInventoryType(InventoryType.getByCode(goodsResponse.getInventoryType()));
                batchInventoryQueryParam.setLogicWarehouseCode(result.getFromWarehouseCode());
                batchInventoryQueryParam.setOwnerCode(fromLogicWarehouse.getOwnerCode());
                GoodsBatchInventoryResult goodsBatchInventoryResult = wsmStockQueryFacade.getBatchInventory(batchInventoryQueryParam);
                if (goodsBatchInventoryResult != null) {
                    InventoryType inventoryType = InventoryType.getByCode(goodsResponse.getInventoryType());
                    if (inventoryType == InventoryType.QUALITY) {
                        goodsResponse.setAvailableNum(goodsBatchInventoryResult.getAvailableNum());
                        goodsResponse.setUnavailableNum(0);
                    } else {
                        goodsResponse.setAvailableNum(0);
                        goodsResponse.setUnavailableNum(goodsBatchInventoryResult.getAvailableNum());
                    }
                    goodsResponse.setProductionDate(goodsBatchInventoryResult.getProductionDate());
                    goodsResponse.setProductionDateTime(goodsBatchInventoryResult.getProductionDate() == null ? null : goodsBatchInventoryResult.getProductionDate().getTime());
                    goodsResponse.setExpireDate(goodsBatchInventoryResult.getExpireDate());
                    goodsResponse.setExpireDateTime(goodsBatchInventoryResult.getExpireDate() == null ? null : goodsBatchInventoryResult.getExpireDate().getTime());


                    goodsResponse.setInventoryTypeName(InventoryType.getByCode(goodsResponse.getInventoryType()).getDes());
                }
            } else {
                GoodsInventoryQueryParam queryParam = new GoodsInventoryQueryParam();
                queryParam.setUserId(SimpleUserHelper.getUserId());
                queryParam.setGoodsCode(goodsResponse.getGoodsCode());
                queryParam.setLogicWarehouseCode(fromLogicWarehouse.getLogicWarehouseCode());
                queryParam.setInventoryType(InventoryType.getByCode(goodsResponse.getInventoryType()));
                queryParam.setNeedOmsInventory(Boolean.TRUE);
                GoodsInventoryResult result2 = iGoodsInventoryFacade.getGoodsInventory(queryParam);
                if (result2 != null) {
                    goodsResponse.setOmsUnLockNum(result2.getOmsUnLockNum());
                    goodsResponse.setOmsDefectiveUnLockNum(result2.getOmsDefectiveUnLockNum());
                    goodsResponse.setUnavailableNum(result2.getUnavailableNum());
                    goodsResponse.setAvailableNum(result2.getAvailableNum());
                }
            }
        }
        WebTransferOrderResponse webTransferOrderResponse = BeanUtils.copyProperties(result, WebTransferOrderResponse.class);
        if (fromLogicWarehouse.getOwnerPreSaleType() == OwnerPreSaleType.YES || toLogicWarehouse.getOwnerPreSaleType() == OwnerPreSaleType.YES) {
            webTransferOrderResponse.setOwnerPreSaleType(OwnerPreSaleType.YES);
        } else {
            webTransferOrderResponse.setOwnerPreSaleType(OwnerPreSaleType.NO);
        }
        webTransferOrderResponse.setFromWarehouseName(fromLogicWarehouse.getLogicWarehouseName());
        webTransferOrderResponse.setGoodsResponseList(goodsResponseList);
        List<FileDto> fileForms = JSON.parseArray(result.getFileJson(), FileDto.class);
        webTransferOrderResponse.setFileFormList(fileForms);
        return RpcResult.success(webTransferOrderResponse);
    }

    @Override
    @SoulClient(path = "/transfer/getFlowByOrderNo", desc = "调拨单单据流")
    public RpcResult getFlowByOrderNo(String transferOrderNo) {
        return RpcResult.success(transferOrderFacade.getFlowByOrderNo(transferOrderNo));
    }

    @Override
    @SoulClient(path = "/transfer/getTransferMixDetail", desc = "详情信息")
    public RpcResult getTransferMixDetail(String transferOrderNo) {
        TransferOrderQueryParam param = new TransferOrderQueryParam();
        param.setTransferNo(transferOrderNo);
        TransferOrderResult result = transferOrderFacade.getTransferOrderByTransferOrderQueryParam(param);
        LogicWarehouseResult fromLogicWarehouse = logicWarehouseFacade.getDetailByCode(result.getFromWarehouseCode());
        TransferGoodsQueryParam param1 = new TransferGoodsQueryParam();
        param1.setTransferNo(transferOrderNo);
        List<TransferGoodsResult> transferGoodsResults = iTransferGoodsFacade.listTransferGoodsByTransferGoodsQueryParam(param1);
        List<WebTransferGoodsResponse> goodsResponse = BeanUtils.copyProperties(transferGoodsResults, WebTransferGoodsResponse.class);
        Map<String, WebTransferGoodsResponse> map = new HashMap<>();
        for (WebTransferGoodsResponse response : goodsResponse) {
            if (map.get(response.getGoodsCode()) != null) {
                WebTransferGoodsResponse response1 = map.get(response.getGoodsCode());
                response1.setQty(response1.getQty() + response.getQty());
            } else {
                map.put(response.getGoodsCode(), response);
            }
        }
        List<WebTransferGoodsResponse> mixGoodsList = new ArrayList<>();
        for (String key : map.keySet()) {
            mixGoodsList.add(map.get(key));
        }
        for (WebTransferGoodsResponse responseList : mixGoodsList) {
            GoodsResult result1 = iGoodsFacade.getGoodsByParam(SimpleUserHelper.getUserId(), responseList.getGoodsCode());
            if (result1 != null) {
                responseList.setBarcode(result1.getBarcode());
            }
        }
        WebTransferOrderResponse responseList = BeanUtils.copyProperties(result, WebTransferOrderResponse.class);
        responseList.setFromWarehouseName(fromLogicWarehouse.getLogicWarehouseName());
        responseList.setGoodsResponseList(mixGoodsList);
        List<FileDto> fileForms = JSON.parseArray(result.getFileJson(), FileDto.class);
        responseList.setFileFormList(fileForms);
        return RpcResult.success(responseList);
    }

    @Override
    @SoulClient(path = "/transfer/tallyReportStatus", desc = "审核状态下拉")
    public RpcResult tallyReportStatusList() {
        List<EnumUtils> enumUtils = new ArrayList<>();
        enumUtils.add(EnumUtils.build(String.valueOf(TallyReportStatus.SP_DRAFT.getCode()), TallyReportStatus.SP_DRAFT.getDes()));
        enumUtils.add(EnumUtils.build(String.valueOf(TallyReportStatus.SP_IS.getCode()), TallyReportStatus.SP_IS.getDes()));
        enumUtils.add(EnumUtils.build(String.valueOf(TallyReportStatus.SP_SUCCESS.getCode()), TallyReportStatus.SP_SUCCESS.getDes()));
        enumUtils.add(EnumUtils.build(String.valueOf(TallyReportStatus.SP_FAIL.getCode()), TallyReportStatus.SP_FAIL.getDes()));
        enumUtils.add(EnumUtils.build(String.valueOf(TallyReportStatus.SP_FINISH.getCode()), TallyReportStatus.SP_FINISH.getDes()));
        enumUtils.add(EnumUtils.build(String.valueOf(TallyReportStatus.SP_CANCEL.getCode()), TallyReportStatus.SP_CANCEL.getDes()));
        return RpcResult.success(enumUtils);
    }

    @Override
    @SoulClient(path = "/status", desc = "调拨单状态下拉")
    public RpcResult transferOrderStatus() {
        List<EnumUtils> list = Arrays.stream(TransferOrderStatus.values())
                .map(TransferOrderStatus -> EnumUtils.build(String.valueOf(TransferOrderStatus.getCode()), String.valueOf(TransferOrderStatus.getDes())))
                .collect(Collectors.toList());
        return RpcResult.success(list);
    }

    @Override
    @SoulClient(path = "/submitAudit", desc = "提交审批")
    public RpcResult submitAudit(String transferOrderNo) {
        return transferOrderFacade.submitAudit(transferOrderNo) ? RpcResult.success("提交审批成功") : RpcResult.error("提交审批失败");
    }

    /**
     * 订单状态为状态待调拨
     *
     * @param type
     * @return
     */
    @Override
    @SoulClient(path = "/listTransferOrderNoByBond", desc = "新建出入库-调拨单号下拉")
    @UCData(group = "logicWarehouseFirst", types = {"logicWarehouseSecond"})
    public RpcResult listTransferOrderNoByBond(String type) {
        // 云仓权限过滤
        List<TransferOrderResult> transferOrderResultList = transferOrderFacade.listTransferOrderNoByINOUT();
        List<String> dataKeyList = new ArrayList<>();
        if (!SimpleUserHelper.isMaster()) {
            // 获取指定数据的权限规则对象
            try {
                DataAuthRule dataAuthRule = UserHelper.getDataInfo().getDataAuthRule("logicWarehouseSecond");
                // 根据数据权限规则过滤数据bizList, 这里bizList可以为null，
                DataAuthFilterResult filterResult = dataAuthRule.filter(null);
                // 过滤结果
                if (filterResult.hasAuthData()) {
                    dataKeyList = filterResult.parseDataList(String.class);
                }
            } catch (Exception e) {
                log.error("[DataPermissionAspect-around]------数据权限添加失败-----", e);
            }
        }
        List<String> finalDataKeyList = dataKeyList;
        if (CollectionUtil.isNotEmpty(dataKeyList)) {
            transferOrderResultList = transferOrderResultList.stream().filter(a -> finalDataKeyList.contains(a.getFromWarehouseCode())).collect(Collectors.toList());
            transferOrderResultList = transferOrderResultList.stream().filter(a -> finalDataKeyList.contains(a.getToWarehouseCode())).collect(Collectors.toList());
        }
        List<WebTransferOrderResponse> responseList = BeanUtils.copyProperties(transferOrderResultList, WebTransferOrderResponse.class);
        List<WebTransferBondOrderListResponse> lists = new ArrayList<>();
        if ("in".equals(type)) {
            for (WebTransferOrderResponse response : responseList) {
                LogicWarehouseResult logicWarehouseResult = logicWarehouseFacade.getDetailByCode(response.getToWarehouseCode());
                if (StringUtils.isNotBlank(response.getInOrderNo())) {
                    continue;
                }
                WebTransferBondOrderListResponse response1 = new WebTransferBondOrderListResponse();
                if (logicWarehouseResult != null) {
                    response1.setLogicWarehouseName(logicWarehouseResult.getLogicWarehouseName());
                }
                response1.setTransferOrderType(response.getTransferOrderType());
                response1.setId(response.getId());
                response1.setName(response.getTransferNo());
                response1.setLogicWarehouseCode(response.getToWarehouseCode());
                response1.setTradeType(Integer.valueOf(response.getTradeType()));
                response1.setTradeTypeName(TradeType.getTradeType(Integer.valueOf(response.getTradeType())).getDesc());
                response1.setSameAccountCode(response.getSameAccountCode());
                lists.add(response1);
            }
        } else {
            for (WebTransferOrderResponse response : responseList) {
                LogicWarehouseResult logicWarehouseResult = logicWarehouseFacade.getDetailByCode(response.getFromWarehouseCode());
                if (StringUtils.isNotBlank(response.getOutOrderNo())) {
                    continue;
                }
                WebTransferBondOrderListResponse response1 = new WebTransferBondOrderListResponse();
                if (logicWarehouseResult != null) {
                    response1.setLogicWarehouseName(logicWarehouseResult.getLogicWarehouseName());
                }
                response1.setTransferOrderType(response.getTransferOrderType());
                response1.setId(response.getId());
                response1.setTradeType(Integer.valueOf(response.getTradeType()));
                response1.setName(response.getTransferNo());
                response1.setLogicWarehouseCode(response.getFromWarehouseCode());
                response1.setTradeTypeName(TradeType.getTradeType(Integer.valueOf(response.getTradeType())).getDesc());
                response1.setSameAccountCode(response.getSameAccountCode());
                lists.add(response1);
            }
        }
        return RpcResult.success(lists);
    }

    @Override
    @SoulClient(path = "/getLogicWarehouseByTransferOrderNo", desc = "根据调拨单获取仓库")
    public RpcResult getLogicWarehouseByTransferOrderNo(String transferOrderNo, String type) {
        EnumUtils enumUtils = transferOrderFacade.getLogicWarehouseByTransferOrderNo(transferOrderNo, type);
        return RpcResult.success(enumUtils);
    }

    /**
     * 新增成功出入库的话会把调拨单的状态变成调拨中
     *
     * @param transferOrderNo
     * @param type
     * @return
     */
    @Override
    @SoulClient(path = "/updateTransferOrderAndStatus", desc = "新增出调拨类型的入库单更新报税得调拨单的状态和出库入库单号")
    public RpcResult updateTransferOrderAndStatus(String transferOrderNo, String type, String orderNo) {
        Boolean flag = transferOrderFacade.updateTransferOrderAndStatus(transferOrderNo, type, orderNo);
        return RpcResult.success(flag);
    }

    @Override
    @SoulClient(path = "/exportExcel", desc = "导出调拨单")
    @LGDataPermission
    @UCData(group = "logicWarehouseFirst", types = {"logicWarehouseSecond"})
    public RpcResult exportExcel(WebTransferOrderQueryForm queryForm) {
        // 必须设置当前用户带入异步线程查询，否则无法获取userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        if (!SimpleUserHelper.isMaster()) {
            // 获取指定数据的权限规则对象
            DataAuthRule dataAuthRule = UserHelper.getDataInfo().getDataAuthRule("logicWarehouseSecond");
            // 根据数据权限规则过滤数据bizList, 这里bizList可以为null，
            DataAuthFilterResult filterResult = dataAuthRule.filter(null);
            // 过滤结果
            if (filterResult.hasAuthData()) {
                // 有数据权限：
                List<String> dataKeyList = filterResult.parseDataList(String.class);
                queryForm.setLogicWarehouseCodes(StringUtils.join(dataKeyList, ","));
                log.info("[WebTransferOrderInterfaceImpl-exportExcel]------数据权限-----{}", StringUtils.join(dataKeyList, ","));
            }
        }
        return exportExecutor.asyncExportTask(ExcelExportEnum.EXCEL_EXPORT_TRANSFER_EXPORT, BeanUtils.copyProperties(queryForm, TransferOrderQueryParam.class));
    }

    @Override
    @SoulClient(path = "/import/query", desc = "调拨单导入任务查询")
    public RpcResult importQuery() {
        CurrentLoadTaskQuery currentLoadTaskQuery = new CurrentLoadTaskQuery();
//        currentLoadTaskQuery.setTemplateUrl("https://ares-oss.oss-cn-shanghai.aliyuncs.com/ERP%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88V2/%E8%B0%83%E6%8B%A8%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88V2.xlsx");
//        currentLoadTaskQuery.setTemplateUrl("https://ares-oss.oss-cn-shanghai.aliyuncs.com/ERP%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88V2/%E8%B0%83%E6%8B%A8%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88V3.xlsx");
//        currentLoadTaskQuery.setTemplateUrl("https://ares-oss.oss-cn-shanghai.aliyuncs.com/ERP%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88V2/%E8%B0%83%E6%8B%A8%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88V4.xlsx");
//        currentLoadTaskQuery.setTemplateUrl("https://dante-img.oss-cn-hangzhou.aliyuncs.com/28024920842.xlsx");
//        currentLoadTaskQuery.setTemplateUrl("https://ares-oss.oss-cn-shanghai.aliyuncs.com/ERP%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88V2/%E8%B0%83%E6%8B%A8%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BFV5.xlsx");
        currentLoadTaskQuery.setTemplateUrl("https://dante-img.oss-cn-hangzhou.aliyuncs.com/61112861470.xlsx");
        currentLoadTaskQuery.setFuncCode("TRANSFER_IMPORT");
        currentLoadTaskQuery.setMasterUserId(SimpleUserHelper.getUserId());
        currentLoadTaskQuery.setUserId(SimpleUserHelper.getRealUserId());
        LoadTaskInfoDTO loadTaskInfoDTO = ParkClient.loadClient().getCurrentTaskInfo(currentLoadTaskQuery);
        LoadTaskInfoBaseDTO loadTaskInfoBaseDTO = BeanUtils.copyProperties(loadTaskInfoDTO, LoadTaskInfoBaseDTO.class);
        return RpcResult.success(loadTaskInfoBaseDTO);
    }

    @Override
    @SoulClient(path = "/import", desc = "调拨单导入")
    public RpcResult doImport(String url) {
        LoadTaskCreateForm loadTaskCreateForm = new LoadTaskCreateForm();
        loadTaskCreateForm.setUserId(SimpleUserHelper.getRealUserId());
        loadTaskCreateForm.setMasterUserId(SimpleUserHelper.getUserId());
        loadTaskCreateForm.setFuncCode("TRANSFER_IMPORT");
        // 业务名称
        loadTaskCreateForm.setName("导入调拨单");

        LoadTaskInfoDTO loadTaskInfoDTO = new LoadTaskInfoDTO();
        loadTaskInfoDTO.setUrl(url);
        // 表头开始行: 默认0
        loadTaskInfoDTO.setHeaderRow(1);
        // 数据开始行：默认 表头行+1
        loadTaskInfoDTO.setDataStartRow(2);
        // 记录总数量
        loadTaskInfoDTO.setTotal(100);
        // 添加表头信息
        loadTaskInfoDTO.addHeader(LoadTaskHeader.build("transferNo", "调拨单号"));
        // 任务详情
        loadTaskCreateForm.setTaskInfo(loadTaskInfoDTO);
        // 任务创建完成仅能返回任务uid
        try {

            LoadTaskDTO loadTaskDTO = ParkClient.loadClient().create(loadTaskCreateForm);
            // 根据uid去做其他逻辑
            // 创建单线程异步执行任务，无返回值
            Long userId = SimpleUserHelper.getUserId();
            String userName = SimpleUserHelper.getUserName();
            Long realUserId = SimpleUserHelper.getRealUserId();
            String realUserName = SimpleUserHelper.getRealUserName();
            ExecutorService executorService = Executors.newSingleThreadExecutor();
            ImportParam importParam = ImportParam.builder().url(url).uid(loadTaskDTO.getUid()).userId(userId).userName(userName).realUserId(realUserId).realUserName(realUserName).lineNo1(2).lineNo2(2).build();
            Long tenantId = SimpleTenantHelper.getTenantId();
            CompletableFuture cf = CompletableFuture.runAsync(() -> {
                SimpleTenantHelper.setTenantId(tenantId);
                iTransferOrderImportFacade.asyncImportTask(importParam);
            }, executorService);
            return RpcResult.success(loadTaskDTO);
        } catch (ParkException pe) {
            return RpcResult.error(pe.getMessage());
        }
    }

    @Override
    @SoulClient(path = "/import/confirm", desc = "调拨单导入任务查询")
    public RpcResult importConfirm() {
        CurrentLoadTaskQuery currentLoadTaskQuery = new CurrentLoadTaskQuery();
        currentLoadTaskQuery.setFuncCode("TRANSFER_IMPORT");
        // 主账号id，小二系统使用0L
        currentLoadTaskQuery.setMasterUserId(SimpleUserHelper.getUserId());
        // 当前操作用户id
        currentLoadTaskQuery.setUserId(SimpleUserHelper.getRealUserId());
        LoadTaskDTO loadTaskDTO = ParkClient.loadClient().confirmCurrent(currentLoadTaskQuery);
        return RpcResult.success(loadTaskDTO);
    }

    @Override
    @SoulClient(path = "/cancel", desc = "调拨单取消")
    public RpcResult cancel(String transferOrderNo, String revocation) {
        return RpcResult.success(transferOrderFacade.cancel(transferOrderNo, revocation));
    }

    @Override
    @SoulClient(path = "/test", desc = "测试")
    public RpcResult test(String json) {
        transferOrderFacade.buildRegulatoryTransferOrder(json);
        return RpcResult.success();
    }

    @Override
    @SoulClient(path = "/transfer/transferOrderTypeList", desc = "调拨类型下拉")
    public RpcResult transferOrderTypeList() {
        return RpcResult.success(EnumUtils.buildName(TransferOrderType.class));
    }
}
