package com.danding.business.portal.ares.web.ewem.form;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 码申请表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */

@Data
@ApiModel(value="EwemApply对象", description="码申请表查询")
public class WebEwemApplyQueryForm extends Page {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
    * 创建开始时间
    */
    private Long createTimeStart;

    /**
    * 创建开始时间
    */
    private Long createTimeEnd;

    /**
    * 更新时间
    */
    private Long updateTime;

    /**
    * 创建人
    */
    private Long createBy;

    /**
    * 更新人
    */
    private Long updateBy;

    /**
     * 申请名称
     */
    @ApiModelProperty(value = "申请名称")
    private String name;

    /**
     * 申请数量
     */
    @ApiModelProperty(value = "申请数量")
    private Long quantity;

    /**
     * 申请状态
     */
    @ApiModelProperty(value = "申请状态")
    private Integer applyStatus;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID")
    private Long batchId;

    private String number;

    /**
     * 状态（0正常 1停用）
     */
    @ApiModelProperty(value = "状态（0正常 1停用）")
    private Integer status;

    /**
     * 防伪码长度
     */
    @ApiModelProperty(value = "防伪码长度")
    private Integer antiLength;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
