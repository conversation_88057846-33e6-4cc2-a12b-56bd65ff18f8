package com.danding.business.portal.ares.web.flow.form;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 流程类型查询
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */

@Data
@ApiModel(value = "FlowType对象", description = "流程类型查询")
public class WebFlowTypeQueryForm extends Page {


    private static final long serialVersionUID = 1L;

    /**
     * 商家ID
     */
    @ApiModelProperty(value = "商家ID")
    private Long userId;

    /**
     * 目标 1、销售单 2、采购单 3、调拨单 4、出库单 5、入库单
     */
    @ApiModelProperty(value = "目标 1、销售单 2、采购单 3、调拨单 4、出库单 5、入库单")
    private Integer documentType;

    /**
     * 流程ID
     */
    @ApiModelProperty(value = "流程ID")
    private Long flowId;


}
