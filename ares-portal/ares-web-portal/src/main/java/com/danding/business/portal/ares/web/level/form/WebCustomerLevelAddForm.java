package com.danding.business.portal.ares.web.level.form;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 添加
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */

@Data
@ApiModel(value = "CustomerLevel对象", description = "添加")
public class WebCustomerLevelAddForm implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long userId;

    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    private String level;

    /**
     * 等级折扣率
     */
    @ApiModelProperty(value = "等级折扣率")
    private String levelDiscount;

    @ApiModelProperty(value = "利润率")
    private BigDecimal profitMargin;


}
