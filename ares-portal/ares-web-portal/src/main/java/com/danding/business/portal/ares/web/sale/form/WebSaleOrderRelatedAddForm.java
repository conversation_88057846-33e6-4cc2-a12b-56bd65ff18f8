package com.danding.business.portal.ares.web.sale.form;


import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>
 * 销售单表添加
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */

@Data
@ApiModel(value = "SaleOrderRelated对象", description = "销售单表添加")
public class WebSaleOrderRelatedAddForm implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 快递公司编码
     */
    private String expressCode;

    /**
     * 快递公司名称
     */
    private String expressName;

    /**
     * 联系人id
     */
    @NotBlank(message = "联系人id不能为空。")
    private String contactId;

    /**
     * 联系人名称
     */
    @NotBlank(message = "联系人名称不能为空。")
    private String contactName;

    /**
     * 联系人手机
     */
    private String contactPhone;

    /**
     * 联系人电话
     */
    private String contactNumber;

    /**
     * 省编码
     */
    private Integer provinceCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市编码
     */
    private Integer cityCode;

    /**
     * 市
     */
    private String city;

    /**
     * 区编码
     */
    private Integer zoneCode;

    /**
     * 区
     */
    private String zone;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 1 联系人  2 收货人
     */
    private Integer type;
}
