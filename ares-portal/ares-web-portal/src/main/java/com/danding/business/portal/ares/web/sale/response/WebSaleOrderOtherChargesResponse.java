package com.danding.business.portal.ares.web.sale.response;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 销售单其他费用表返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-20
 */

@Data
@ApiModel(value = "SaleOrderOtherCharges对象", description = "销售单其他费用表返回对象")
public class WebSaleOrderOtherChargesResponse implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 销售单号
     */
    @ApiModelProperty(value = "销售单号")
    private String saleOrderNo;

    /**
     * 其他费用名称
     */
    @ApiModelProperty(value = "其他费用名称")
    private String otherChargesName;

    /**
     * 其他费用金额
     */
    @ApiModelProperty(value = "其他费用金额")
    private BigDecimal otherChargesAmount;

    /**
     * 其他费用类型
     */
    private String otherChargesType;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
