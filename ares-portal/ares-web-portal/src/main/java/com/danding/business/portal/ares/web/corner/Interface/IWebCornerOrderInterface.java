package com.danding.business.portal.ares.web.corner.Interface;


import com.danding.business.portal.ares.web.corner.form.WebCornerOrderAddForm;
import com.danding.business.portal.ares.web.corner.form.WebCornerOrderQueryForm;
import com.danding.soul.client.common.result.RpcResult;

import javax.validation.Valid;
/**
 * <p>
 * 分销单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-19
 */
public interface IWebCornerOrderInterface {

    /**
     * 查询实体仓列表
     *
     * @return
     */
    RpcResult queryEntityWarehouseList();

    /**
     * 查询实体仓列表下拉
     *
     * @return
     */
    RpcResult queryEntityWarehouseListItem();

    /**
     * 查询供货商列表
     *
     * @return
     */
    RpcResult querySuppliersList();

    /**
     * 查询指定供货商的货主列表
     *
     * @return
     */
    RpcResult getSuppliersOwnerList(String suppliersCode, String entityWarehouseCode);

    /**
     * 查询收货云仓列表
     * @return
     */
    RpcResult getReceiveLogicWarehouseList();

    /**
     * 列表查询
     *
     * @param queryForm
     * @return
     */
    RpcResult queryList(WebCornerOrderQueryForm queryForm);

    /**
     * 创建
     *
     * @param addForm
     * @return
     */
    RpcResult create(@Valid WebCornerOrderAddForm addForm);

    /**
     * 编辑保存
     *
     * @param addForm
     * @return
     */
    RpcResult edit(@Valid WebCornerOrderAddForm addForm);

    /**
     * 删除
     *
     * @param queryForm
     * @return
     */
    RpcResult drop(WebCornerOrderQueryForm queryForm);

    /**
     * 分销单订单状态下拉
     *
     * @return
     */
    RpcResult cornerOrderStatusList();

    /**
     * 库存状态下拉
     *
     * @return
     */
    RpcResult inventoryTypeList();

    /**
     * 详情
     *
     * @param queryForm
     * @return
     */
    RpcResult detail(WebCornerOrderQueryForm queryForm);

    /**
     * 分销单提交审核
     *
     * @param queryForm
     * @return
     */
    RpcResult approval(WebCornerOrderQueryForm queryForm);

    /**
     * 分销单取消
     *
     * @param queryForm
     * @return
     */
    RpcResult cancel(WebCornerOrderQueryForm queryForm);

    /**
     * 分销单生成配货单
     *
     * @param queryForm
     * @return
     */
    RpcResult addDistributionOrder(WebCornerOrderQueryForm queryForm);

    /**
     * 获取分销单生成配货单固定信息
     *
     * @param queryForm
     * @return
     */
    RpcResult getAddDistributionOrderInfo(WebCornerOrderQueryForm queryForm);

    /**
     * 分销单创建出库单
     *
     * @param queryForm
     * @return
     */
    RpcResult createOutOrder(WebCornerOrderQueryForm queryForm);

    /**
     * 分销单创建入库单
     *
     * @param queryForm
     * @return
     */
    RpcResult createInOrder(WebCornerOrderQueryForm queryForm);

    /**
     * 根据分销单号获取云仓
     *
     * @param queryForm
     * @return
     */
    RpcResult getLogicWarehouseByCornerOrderNo(WebCornerOrderQueryForm queryForm);

    /**
     * 查询可退货分销单列表
     *
     * @return
     */
    RpcResult getUsableReturnedList();

    /**
     * 分销单库存状态下拉列表
     *
     * @return
     */
    RpcResult inventoryStatus();

}
