package com.danding.business.portal.ares.web.flow.response;


import com.danding.component.common.rpc.common.annotation.ResultReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 目标流程配置返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */

@Data
@ApiModel(value = "TargetFlow对象", description = "目标流程配置返回对象")
public class WebTargetFlowResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商家ID
     */
    @ApiModelProperty(value = "商家ID")
    private Long userId;

    /**
     * 目标 1、销售单 2、采购单 3、调拨单 4、出库单 5、入库单
     */
    private Integer documentTypeCode;
    private String documentTypeName;

    /**
     * 流程ID
     */
    @ApiModelProperty(value = "流程ID")
    @ResultReference(referenceType = ResultReference.ReferenceType.COPY, localReferProperty = "flowId")
    private String flowId;

    private List<FlowStepResponse> flowStepVOS;
}
