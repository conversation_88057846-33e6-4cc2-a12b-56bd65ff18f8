package com.danding.business.portal.ares.web.level.impl;


import com.danding.business.client.ares.level.facade.ICustomerLevelFacade;
import com.danding.business.client.ares.level.param.CustomerLevelAddParam;
import com.danding.business.client.ares.level.param.CustomerLevelQueryParam;
import com.danding.business.client.ares.level.result.CustomerLevelResult;
import com.danding.business.portal.ares.web.level.Interface.IWebCustomerLevelInterface;
import com.danding.business.portal.ares.web.level.form.WebCustomerLevelAddForm;
import com.danding.business.portal.ares.web.level.form.WebCustomerLevelQueryForm;
import com.danding.business.portal.ares.web.level.response.LevelListResponse;
import com.danding.business.portal.ares.web.level.response.WebCustomerLevelResponse;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.common.utils.EnumUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@DubboService
public class WebCustomerLevelInterfaceImpl implements IWebCustomerLevelInterface {

    @DubboReference
    private ICustomerLevelFacade iCustomerLevelFacade;


    @Override
    @SoulClient(path = "/level/addCustomerLevel", desc = "增加经销商等级")
    public RpcResult mixPriceWithGood(WebCustomerLevelAddForm addForm) {
        CustomerLevelAddParam addParam = BeanUtils.copyProperties(addForm, CustomerLevelAddParam.class);
        addParam.setUserId(SimpleUserHelper.getUserId());
        return RpcResult.success(iCustomerLevelFacade.mixPriceWithGood(addParam));
    }


    @Override
    @SoulClient(path = "/level/listLevelByUserId", desc = "用户折扣等级列表")
    public RpcResult listLevelByUserId() {
        List<CustomerLevelResult> resultList = iCustomerLevelFacade.listLevelByUserId();
        List<LevelListResponse> responseList = new ArrayList<>();
        for (CustomerLevelResult customerLevelResult : resultList) {
            LevelListResponse response = new LevelListResponse();
            response.setId(customerLevelResult.getLevel());
            response.setName(customerLevelResult.getLevel());
            response.setProfitMargin(customerLevelResult.getProfitMargin());
            responseList.add(response);
        }
        return RpcResult.success(responseList);
    }


    @Override
    @SoulClient(path = "/level/listLevelByPage", desc = "等级列表分页查询")
    public RpcResult listLevelByPage(WebCustomerLevelQueryForm webCustomerLevelQueryForm) {
        CustomerLevelQueryParam customerLevelQueryParam = BeanUtils.copyProperties(webCustomerLevelQueryForm, CustomerLevelQueryParam.class);
        ListVO<WebCustomerLevelResponse> responseListVO = new ListVO<>();
        ListVO<CustomerLevelResult> resultListVO = iCustomerLevelFacade.listLevelByPage(customerLevelQueryParam);
        if (CollectionUtils.isNotEmpty(resultListVO.getDataList())) {
            List<WebCustomerLevelResponse> results = BeanUtils.copyProperties(resultListVO.getDataList(), WebCustomerLevelResponse.class);
            responseListVO.setDataList(results);
        }
        responseListVO.setPage(resultListVO.getPage());
        return RpcResult.success(responseListVO);
    }


    @Override
    @SoulClient(path = "/level/openAndClosed", desc = "等级开启关闭")
    public RpcResult openAndClosed(String level) {
        return RpcResult.success(iCustomerLevelFacade.openAndClosed(level));
    }
}
