package com.danding.business.portal.ares.web.order.form;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 查询
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */

@Data
@ApiModel(value="WmsOperationLogs对象", description="查询")
public class WebWmsOperationLogsQueryForm extends Page {


    private static final long serialVersionUID = 1L;
    private Long id;
    /**
    * 创建开始时间
    */
    private Long createTimeStart;
    /**
    * 创建开始时间
    */
    private Long createTimeEnd;
    /**
    * 更新时间
    */
    private Long updateTime;
    /**
    * 创建人
    */
    private Long createBy;
    /**
    * 更新人
    */
    private Long updateBy;

    /**
     * 单号
     */
    @ApiModelProperty(value = "单号")
    private String orderNo;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private Long operationTime;

    /**
     * 操作轨迹描述
     */
    @ApiModelProperty(value = "操作轨迹描述")
    private String operationDescription;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
