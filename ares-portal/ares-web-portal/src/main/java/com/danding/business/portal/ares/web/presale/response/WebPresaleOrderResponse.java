package com.danding.business.portal.ares.web.presale.response;


import com.danding.business.common.ares.enums.trade.PresaleOrderStatus;
import com.danding.component.common.api.common.file.FileDto;
import com.danding.component.common.rpc.common.annotation.ResultReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 预售订单表返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */

@Data
@ApiModel(value="PresaleOrder对象", description="预售订单表返回对象")
public class WebPresaleOrderResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ResultReference(referenceType = ResultReference.ReferenceType.COPY, localReferProperty = "id")
    private String id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 预售单号
     */
    @ApiModelProperty(value = "预售单号")
    private String presaleNo;

    /**
     * 经销商编码
     */
    @ApiModelProperty(value = "经销商编码")
    private String dealerCode;

    /**
     * 经销商名称
     */
    @ApiModelProperty(value = "经销商名称")
    private String dealerName;

    /**
     * 公司地址
     */
    @ApiModelProperty(value = "公司地址")
    private String companyAddress;

    /**
     * 联系人id
     */
    @ApiModelProperty(value = "联系人id")
    private String contactId;

    /**
     * 联系人名称
     */
    @ApiModelProperty(value = "联系人名称")
    private String contactName;

    /**
     * 联系人手机
     */
    @ApiModelProperty(value = "联系人手机")
    private String contactPhone;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String contactNumber;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码")
    private String provinceCode;

    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    private String province;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    private String cityCode;

    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 区编码
     */
    @ApiModelProperty(value = "区编码")
    private String zoneCode;

    /**
     * 区
     */
    @ApiModelProperty(value = "区")
    private String zone;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String address;

    /**
     * 附件，使用json存储附件地址url
     */
    @ApiModelProperty(value = "附件，使用json存储附件地址url")
    private List<FileDto> annex;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 货币类型
     */
    @ApiModelProperty(value = "货币类型")
    private String billCurrency;
    private String billCurrencyName;

    /**
     * 汇率
     */
    @ApiModelProperty(value = "汇率")
    private BigDecimal currencyRate;

    /**
     * 订单总金额
     */
    @ApiModelProperty(value = "订单总金额")
    private BigDecimal totalAmount;

    /**
     * 创建的销售单号
     */
    @ApiModelProperty(value = "创建的销售单号")
    private String saleOrderNo;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private PresaleOrderStatus orderStatus;
    private String orderStatusName;

    /**
     * 预售单明细
     */
    List<WebPresaleOrderDetailsResponse> items;

    /**
     * 创建时间
     */
    private Long createTime;
}
