package com.danding.business.portal.ares.web.flow.Interface;


import com.danding.business.portal.ares.web.flow.form.*;
import com.danding.business.portal.ares.web.flow.response.*;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.utils.EnumUtils;
import com.danding.soul.client.common.result.RpcResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 审核流模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@Validated
public interface IWebFlowInterface {

    /**
     * 商户下所有角色
     *
     * @return 商户下角色列表
     */
    RpcResult<List<RoleResponse>> selectRoles();

    /**
     * 商户下所有用户
     *
     * @return 商户下所有用户
     */
    RpcResult<List<UserResponse>> selectUsers();

    /**
     * 单据类型列表
     *
     * @return 单据类型列表
     */
    RpcResult<List<EnumUtils>> documentTypeList();


    /**
     * 流转类型列表
     *
     * @return 流转类型列表
     */
    RpcResult<List<EnumUtils>> candidateTypeList();

    /**
     * 删除目标审批流模板
     *
     * @param webTargetFlowDelForm
     * @return
     */
    RpcResult<String> delTargetFlowTemplate(@Valid WebTargetFlowDelForm webTargetFlowDelForm);

    /**
     * 创建目标审批流模板
     *
     * @param webTargetFlowAddForm 审批流模板表单
     * @return 流程模板编号
     */
    RpcResult<String> saveTargetFlowTemplate(@Valid WebTargetFlowAddForm webTargetFlowAddForm);

    /**
     * 商户单据审批流列表
     *
     * @return 审批流信息列表
     */
    RpcResult<ListVO<WebTargetFlowResponse>> targetFlowList(Integer page, Integer pageSize);

    /**
     * 查询用户待办
     *
     * @param webFlowTaskQueryForm
     * @return
     */
    RpcResult<ListVO<WebFlowTaskResponse>> userTaskList(WebFlowTaskQueryForm webFlowTaskQueryForm);

    /**
     * 修改待办
     *
     * @param webFlowTaskUpdateForm 任务提交表单
     * @return 成功、失败
     */
    RpcResult<String> updateTask(@Valid WebFlowTaskUpdateForm webFlowTaskUpdateForm);

    /**
     * 批量更新代办
     *
     * @param webFlowTaskBatchUpdateForm 任务提交表单
     * @return
     */
    RpcResult<String> batchUpdateTask(@Valid WebFlowTaskBatchUpdateForm webFlowTaskBatchUpdateForm);

    /**
     * 流程操作日志
     *
     * @param webFlowLogQueryForm
     * @return
     */
    RpcResult<List<WebFlowLogResponse>> flowLogs(WebFlowLogQueryForm webFlowLogQueryForm);
}
