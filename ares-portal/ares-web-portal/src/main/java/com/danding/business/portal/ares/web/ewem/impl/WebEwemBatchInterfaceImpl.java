package com.danding.business.portal.ares.web.ewem.impl;

import com.danding.business.client.ares.ewem.result.EwemBatchResult;
import com.danding.business.client.ares.ewem.param.EwemBatchQueryParam;
import com.danding.business.client.ares.ewem.param.EwemBatchAddParam;
import com.danding.business.common.ares.enums.ewem.ApplyStatus;
import com.danding.business.common.ares.enums.ewem.Rule;
import com.danding.business.common.ares.enums.ewem.Status;
import com.danding.business.common.ares.enums.goods.GoodsStatus;
import com.danding.business.portal.ares.web.ewem.form.WebEwemBatchAddForm;
import com.danding.business.portal.ares.web.ewem.form.WebEwemBatchQueryForm;
import com.danding.business.portal.ares.web.ewem.response.WebEwemBatchResponse;
import com.danding.business.client.ares.ewem.facade.IEwemBatchFacade;
import com.danding.business.portal.ares.web.ewem.Interface.IWebEwemBatchInterface;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.base.Interface.EnumInterface;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.common.utils.EnumUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 批次表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@DubboService
public class WebEwemBatchInterfaceImpl implements IWebEwemBatchInterface  {

    @DubboReference
    private IEwemBatchFacade ewemBatchFacade;

    @Override
    @SoulClient(path = "/ewemBatch/query", desc = "查询单条")
    public RpcResult query(WebEwemBatchQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        EwemBatchQueryParam queryParam = BeanUtils.copyProperties(queryForm, EwemBatchQueryParam.class);
        EwemBatchResult result = ewemBatchFacade.getByQueryParam(queryParam);
        return RpcResult.success(BeanUtils.copyProperties(result, WebEwemBatchResponse.class));
    }

    @Override
    @SoulClient(path = "/ewemBatch/queryList", desc = "列表查询")
    public RpcResult queryList(WebEwemBatchQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        EwemBatchQueryParam queryParam = BeanUtils.copyProperties(queryForm, EwemBatchQueryParam.class);
        ListVO<EwemBatchResult> resultListVO = ewemBatchFacade.pageListByQueryParam(queryParam);
        List<WebEwemBatchResponse> responsesList = BeanUtils.copyProperties(resultListVO.getDataList(), WebEwemBatchResponse.class);
        responsesList.forEach(m -> {
                      m.setStatusDesc(m.getStatus() == null ? "" : Optional.ofNullable(Status.getStatus(m.getStatus())).map(EnumInterface::getDes).orElse(""));
                }
        );
        return RpcResult.success(ListVO.build(resultListVO.getPage(),responsesList));
    }

    @Override
    @SoulClient(path = "/ewemBatch/selectItem", desc = "下拉查询")
    public RpcResult selectItem(WebEwemBatchQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        EwemBatchQueryParam queryParam = BeanUtils.copyProperties(queryForm, EwemBatchQueryParam.class);
        List<EwemBatchResult> resultList = ewemBatchFacade.listByQueryParam(queryParam);
        return RpcResult.success(EnumUtils.build(resultList, "id", "saleOrderNo"));
    }

    @Override
    @SoulClient(path = "/ewemBatch/create", desc = "创建")
    public RpcResult create(WebEwemBatchAddForm addForm) {
        // 网关层统一设置userId
        addForm.setUserId(SimpleUserHelper.getUserId());
        EwemBatchAddParam addParam = BeanUtils.copyProperties(addForm, EwemBatchAddParam.class);
        return RpcResult.isSuccess(ewemBatchFacade.add(addParam), "创建失败。");
    }


    @Override
    @SoulClient(path = "/ewemBatch/edit", desc = "编辑保存")
    public RpcResult edit(WebEwemBatchAddForm addForm) {
        // 网关层统一设置userId
        addForm.setUserId(SimpleUserHelper.getUserId());
        EwemBatchAddParam addParam = BeanUtils.copyProperties(addForm, EwemBatchAddParam.class);
        return RpcResult.isSuccess(ewemBatchFacade.updateById(addParam), "编辑保存失败。");
    }

    @Override
    @SoulClient(path = "/ewemBatch/drop", desc = "删除")
    public RpcResult drop(WebEwemBatchQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        if (Objects.isNull(queryForm.getId())){
            return RpcResult.error("id不能为空");
        }
        EwemBatchQueryParam queryParam = BeanUtils.copyProperties(queryForm, EwemBatchQueryParam.class);
        return RpcResult.isSuccess(ewemBatchFacade.removeByQueryParam(queryParam), "删除失败。");
    }

    @Override
    @SoulClient(path = "/ewemBatch/detail", desc = "详情")
    public RpcResult detail(WebEwemBatchQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        EwemBatchQueryParam queryParam = BeanUtils.copyProperties(queryForm, EwemBatchQueryParam.class);
        EwemBatchResult result = ewemBatchFacade.getByQueryParam(queryParam);
        return RpcResult.success(BeanUtils.copyProperties(result, WebEwemBatchResponse.class));
    }

    @SoulClient(path = "/ewemBatch/status", desc = "启用状态下拉列表")
    public RpcResult status() {
        return RpcResult.success(EnumUtils.buildCode(Status.class));
    }
    @SoulClient(path = "/ewemBatch/goods", desc = "启用状态下拉列表")
    @Override
    public RpcResult goods() {
        return RpcResult.success(ewemBatchFacade.getGoods(SimpleUserHelper.getUserId()));
    }
    @SoulClient(path = "/ewemBatch/batch", desc = "启用状态下拉列表")
    @Override
    public RpcResult batch() {
        return RpcResult.success(ewemBatchFacade.getBatch(SimpleUserHelper.getUserId()));
    }


}
