<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>business-portal-parent</artifactId>
        <groupId>com.danding</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>${revision}</version>
    <artifactId>ares-portal</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>ares-web-portal</module>
        <module>ares-admin-portal</module>
        <module>ares-pda-portal</module>
    </modules>
    <properties>
        <revision>3.1.18-SNAPSHOT</revision>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.plugin.version>3.8.1</maven.plugin.version>
        <java.version>1.8</java.version>
        <component.version>2.0-SNAPSHOT</component.version>
        <uc.user.rpc.version>1.1-SNAPSHOT</uc.user.rpc.version>
        <wms.platform.rpc.version>3.2.9-RELEASE</wms.platform.rpc.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>log-component</artifactId>
            <version>${component.version}</version>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>pac.sdk</groupId>
                <artifactId>pac.sdk.cp</artifactId>
                <version>1.0.7-WB166075</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>com.danding.component</groupId>
                <artifactId>web-component</artifactId>
                <version>1.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>park-client</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>park-client-autoconfigure</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>dt-component-saas</artifactId>
                <version>1.2.7-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>uc-component-dubbo</artifactId>
                <version>1.1.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ucenter-client-autoconfigure</artifactId>
                <version>1.1.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>uc-component-core</artifactId>
                <version>1.1.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <artifactId>ares-order-rpc-client</artifactId>
                <groupId>com.danding</groupId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <artifactId>ares-report-rpc-client</artifactId>
                <groupId>com.danding</groupId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <artifactId>ares-risk-rpc-client</artifactId>
                <groupId>com.danding</groupId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <artifactId>ares-rpc-configuration-client</artifactId>
                <groupId>com.danding</groupId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <artifactId>ares-goods-center-rpc-client</artifactId>
                <groupId>com.danding</groupId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-config-rpc-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <artifactId>ares-rpc-trade-client</artifactId>
                <groupId>com.danding</groupId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <artifactId>ares-goods-rpc-client</artifactId>
                <groupId>com.danding</groupId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <artifactId>ares-inventory-rpc-client</artifactId>
                <groupId>com.danding</groupId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-configuration-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-goods-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-risk-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-report-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-order-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-inventory-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-flow-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-trade-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-config-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ares-common</artifactId>
                <version>${project.version}</version>
                <scope>compile</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>utils-common</artifactId>
                        <groupId>com.danding</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>ucenter-client-dubbo</artifactId>
                <version>1.1.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>uc-user-rpc-client</artifactId>
                <version>${uc.user.rpc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>uc-auth-rpc-client</artifactId>
                <version>${uc.user.rpc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>dt-core-tenant</artifactId>
                <version>1.0.7-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.dt</groupId>
                <artifactId>wms-platform-rpc-client</artifactId>
                <version>${wms.platform.rpc.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.baomidou</groupId>
                        <artifactId>mybatis-plus-extension</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.cloud</groupId>
                        <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.2.5.RELEASE</version>
                    <configuration>
                        <fork>true</fork>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <tasks>
                                    <!--suppress UnresolvedMavenProperty -->
                                    <copy overwrite="true"
                                          tofile="${session.executionRootDirectory}/target/${project.artifactId}.jar"
                                          file="${project.build.directory}/${project.artifactId}.jar"/>
                                </tasks>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <!--版本统一修改器  命令  mvn versions:set -DnewVersion=1.0-SNAPSHOT -->
            <plugin>
                <!-- https://mvnrepository.com/artifact/org.codehaus.mojo/versions-maven-plugin -->
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.7</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.5.0</version>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
                <inherited>true</inherited>
                <configuration>
                    <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>danding</id>
            <name>danding</name>
            <url>http://mvn.yang800.cn/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
