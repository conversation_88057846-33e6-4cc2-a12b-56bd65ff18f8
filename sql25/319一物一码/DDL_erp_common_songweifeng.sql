CREATE TABLE `erp_ewem_apply` (
	`id` BIGINT ( 20 ) NOT NULL AUTO_INCREMENT,
	`name` VARCHAR ( 255 ) NOT NULL COMMENT '申请名称',
	`quantity` BIGINT ( 20 ) DEFAULT NULL COMMENT '申请数量',
	`apply_status` TINYINT ( 2 ) DEFAULT NULL COMMENT '申请状态',
	`batch_id` BIGINT ( 20 ) DEFAULT NULL COMMENT '批次ID',
	`status` TINYINT ( 1 ) NOT NULL COMMENT '状态（0正常 1停用）',
	`rule` TINYINT ( 2 ) NOT NULL COMMENT '码规则',
	`length` INT ( 10 ) DEFAULT NULL COMMENT '码长度',
	`anti_length` INT ( 10 ) DEFAULT NULL COMMENT '防伪码长度',
	`anti_rule` TINYINT ( 2 ) DEFAULT NULL COMMENT '防伪码规则',
	`user_id` BIGINT ( 11 ) DEFAULT NULL COMMENT '用户id',
	`create_time` BIGINT ( 1 ) NOT NULL DEFAULT '0',
	`update_time` BIGINT ( 1 ) NOT NULL DEFAULT '0',
	`create_by` BIGINT ( 20 ) DEFAULT '0',
	`update_by` BIGINT ( 20 ) DEFAULT '0',
	`deleted` TINYINT ( 1 ) NOT NULL DEFAULT '1',
	`version` BIGINT ( 10 ) NOT NULL DEFAULT '1' COMMENT '版本号乐观锁',
	`tenant_id` BIGINT ( 20 ) NOT NULL COMMENT '租户id',
	`remark` VARCHAR ( 500 ) DEFAULT NULL COMMENT '备注',
	PRIMARY KEY ( `id` )
) ENGINE = INNODB AUTO_INCREMENT = 4 DEFAULT CHARSET = utf8mb4 COMMENT = '码申请表';
CREATE TABLE `erp_ewem_batch` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `number` varchar(255) DEFAULT NULL COMMENT '批号',
  `goods_code` varchar(64) DEFAULT NULL COMMENT '内部sku',
  `status` tinyint(2) NOT NULL COMMENT '状态（0正常 1停用）',
  `user_id` bigint(11) DEFAULT NULL COMMENT '用户id',
  `create_time` bigint(1) NOT NULL DEFAULT '0',
  `update_time` bigint(1) NOT NULL DEFAULT '0',
  `create_by` bigint(20) DEFAULT '0',
  `update_by` bigint(20) DEFAULT '0',
  `deleted` tinyint(1) NOT NULL DEFAULT '1',
  `version` bigint(10) NOT NULL DEFAULT '1' COMMENT '版本号乐观锁',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户id',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_number_goods_code` (`number`,`goods_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1902630705779097602 DEFAULT CHARSET=utf8mb4 COMMENT='批次表';
CREATE TABLE `erp_ewem_code` (
	`id` BIGINT ( 22 ) NOT NULL AUTO_INCREMENT,
	`code` VARCHAR ( 50 ) DEFAULT NULL COMMENT '码',
	`batch_id` BIGINT ( 20 ) DEFAULT NULL COMMENT '申请ID',
	`scan_num` INT ( 10 ) DEFAULT '0' COMMENT '扫码次数',
	`first_scan_time` BIGINT ( 1 ) DEFAULT NULL COMMENT '首次扫码时间',
	`anti_code` VARCHAR ( 50 ) DEFAULT NULL COMMENT '防伪码',
	`status` TINYINT ( 2 ) NOT NULL COMMENT '状态（0正常 1停用）',
	`user_id` BIGINT ( 11 ) DEFAULT NULL COMMENT '用户id',
	`create_time` BIGINT ( 1 ) NOT NULL DEFAULT '0',
	`update_time` BIGINT ( 1 ) NOT NULL DEFAULT '0',
	`create_by` BIGINT ( 20 ) DEFAULT '0',
	`update_by` BIGINT ( 20 ) DEFAULT '0',
	`deleted` TINYINT ( 1 ) NOT NULL DEFAULT '1',
	`version` BIGINT ( 10 ) NOT NULL DEFAULT '1' COMMENT '版本号乐观锁',
	`tenant_id` BIGINT ( 20 ) NOT NULL COMMENT '租户id',
	`remark` VARCHAR ( 500 ) DEFAULT NULL COMMENT '备注',
	PRIMARY KEY ( `id` ),
	UNIQUE KEY `idx_code` ( `code` )
) ENGINE = INNODB AUTO_INCREMENT = 259 DEFAULT CHARSET = utf8mb4 COMMENT = '码数据表';
CREATE TABLE `erp_ewem_scan_log` (
	`id` BIGINT ( 20 ) NOT NULL AUTO_INCREMENT,
	`code` VARCHAR ( 255 ) DEFAULT NULL COMMENT '码',
	`goods_code` varchar(64) DEFAULT NULL COMMENT '内部sku',
	`goods_name` varchar(255) DEFAULT NULL COMMENT '货品名称',
	`longitude` VARCHAR ( 255 ) DEFAULT NULL COMMENT '经度',
	`latitude` VARCHAR ( 255 ) DEFAULT NULL COMMENT '维度',
	`user_name` VARCHAR ( 255 ) DEFAULT NULL COMMENT '用户名',
	`scan_time` BIGINT ( 1 ) DEFAULT NULL COMMENT '扫码时间',
	`user_id` BIGINT ( 11 ) DEFAULT NULL COMMENT '用户id',
	`create_time` BIGINT ( 1 ) NOT NULL DEFAULT '0',
	`update_time` BIGINT ( 1 ) NOT NULL DEFAULT '0',
	`create_by` BIGINT ( 20 ) DEFAULT '0',
	`update_by` BIGINT ( 20 ) DEFAULT '0',
	`deleted` TINYINT ( 1 ) NOT NULL DEFAULT '1',
	`version` BIGINT ( 10 ) NOT NULL DEFAULT '1' COMMENT '版本号乐观锁',
	`tenant_id` BIGINT ( 20 ) NOT NULL COMMENT '租户id',
PRIMARY KEY ( `id` )
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COMMENT = '扫码记录';