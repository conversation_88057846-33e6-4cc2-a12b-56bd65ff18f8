package com.danding.business.client.ares.corner.facade;


import com.danding.business.client.ares.corner.param.CornerOrderRelatedAddParam;
import com.danding.business.client.ares.corner.param.CornerOrderRelatedQueryParam;
import com.danding.business.client.ares.corner.result.CornerOrderRelatedResult;
import com.danding.component.common.api.common.response.ListVO;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 分销单收发货信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-19
 */
public interface ICornerOrderRelatedFacade {

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    CornerOrderRelatedResult getById(Serializable id);

    /**
     * 条件查询单个
     *
     * @param cornerOrderRelatedQueryParam
     * @return
     */
    CornerOrderRelatedResult getByQueryParam(CornerOrderRelatedQueryParam cornerOrderRelatedQueryParam);

    /**
     * 分销单号查询单个
     *
     * @param cornerOrderNo
     * @return
     */
    CornerOrderRelatedResult getByCornerOrderNo(String cornerOrderNo);

    /**
     * 条件查询list
     *
     * @param cornerOrderRelatedQueryParam
     * @return
     */
    List<CornerOrderRelatedResult> listByQueryParam(CornerOrderRelatedQueryParam cornerOrderRelatedQueryParam);

    /**
     * 条件分页查询
     *
     * @param cornerOrderRelatedQueryParam
     * @return
     */
    ListVO<CornerOrderRelatedResult> pageListByQueryParam(CornerOrderRelatedQueryParam cornerOrderRelatedQueryParam) ;

    /**
     * 插入
     *
     * @param cornerOrderRelatedAddParam
     * @return
     */
    boolean add(CornerOrderRelatedAddParam cornerOrderRelatedAddParam) ;

    /**
     * 批量插入
     *
     * @param cornerOrderRelatedAddParamList
     * @return
     */
    boolean addList(List<CornerOrderRelatedAddParam> cornerOrderRelatedAddParamList) ;

    /**
     * 根据主键id修改
     *
     * @param cornerOrderRelatedAddParam
     * @return
     */
    boolean updateById(CornerOrderRelatedAddParam cornerOrderRelatedAddParam) ;

    /**
     * 根据主键id批量修改
     *
     * @param cornerOrderRelatedAddParamList
     * @return
     */
    boolean updateListById(List<CornerOrderRelatedAddParam> cornerOrderRelatedAddParamList) ;

    /**
     * 根据条件修改
     *
     * @param cornerOrderRelatedQueryParam
     * @param cornerOrderRelatedAddParam
     * @return
     */
    boolean updateListByQueryParam(CornerOrderRelatedQueryParam cornerOrderRelatedQueryParam, CornerOrderRelatedAddParam cornerOrderRelatedAddParam) ;

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    boolean removeById(Serializable id) ;

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    boolean removeByIds(List<Long> idList) ;

    /**
     * 根据条件删除
     *
     * @param cornerOrderRelatedQueryParam
     * @return
     */
    boolean removeByQueryParam(CornerOrderRelatedQueryParam cornerOrderRelatedQueryParam) ;

}
