package com.danding.business.client.ares.sale.param;


import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 销售单详细表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-20
 */

@Data
@ApiModel(value="SaleOrderDetail对象", description="销售单详细表查询")
public class SaleOrderDetailQueryParam extends Page {


    private static final long serialVersionUID = 1L;

    /**
     * 销售单号
     */
    private String saleOrderNo;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 货品sku
     */
    private String goodsCode;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 货币类型
     */
    private String billCurrency;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 零售价格
     */
    private BigDecimal retailPrice;

    /**
     * 批次号
     */
    private String batchCode;

    /**
     * 内部批次号
     */
    @ApiModelProperty(value = "内部批次号")
    private String internalBatchCode;

    /**
     * 生产批次号
     */
    @ApiModelProperty(value = "生产批次号")
    private String productBatchCode;

    /**
     * 汇率
     */
    private BigDecimal currencyRate;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;

    /**
     * 折扣率
     */
    private Double discountRate;

    /**
     * 货品数量
     */
    private Integer goodsNumber;

    /**
     * 实际售价
     */
    private BigDecimal totalAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 版本号乐观锁
     */
    private Long version;


}
