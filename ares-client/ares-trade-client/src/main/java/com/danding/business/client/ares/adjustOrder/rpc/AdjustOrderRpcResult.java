package com.danding.business.client.ares.adjustOrder.rpc;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AdjustOrderRpcResult implements Serializable {

    private static final long serialVersionUID = -8614548255053979180L;
    /**
     * 租户id
     */
    private Long userId;

    /**
     * 调整单号
     */
    private String adjustOrderNo;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 调整类型
     */
    private Integer adjustType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 唯一标识
     */
    private String idempotentNo;

    /**
     * 外部实体仓编码
     */
    private String upWarehouseCode;

    /**
     * 2-待审核 1-已完成 5-已取消
     */
    private Integer status;

    /**
     * 业务场景
     */
    private Integer adjustBizType;
    
    /**
     * 调整单详细
     */
    private List<AdjustOrderRpcDetailResult> details;
}
