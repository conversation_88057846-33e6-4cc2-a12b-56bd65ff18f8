package com.danding.business.client.ares.circleorder.param;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 圈货单明细表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */

@Data
@ApiModel(value="CircleOrderDetail对象", description="圈货单明细表查询")
public class CircleOrderDetailQueryParam extends Page {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
    * 创建时间
    */
    private Long createTime;

    /**
    * 更新时间
    */
    private Long updateTime;

    /**
    * 创建人
    */
    private Long createBy;

    /**
    * 更新人
    */
    private Long updateBy;

    /**
     * 圈货单号
     */
    @ApiModelProperty(value = "圈货单号")
    private String circleOrderNo;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 货品编码
     */
    @ApiModelProperty(value = "货品编码")
    private String goodsCode;

    /**
     * sku编码
     */
    @ApiModelProperty(value = "sku编码")
    private String sku;

    /**
     * 货品名称
     */
    @ApiModelProperty(value = "货品名称")
    private String goodsName;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchCode;

    /**
     * 逻辑仓编码
     */
    @ApiModelProperty(value = "逻辑仓编码")
    private String logicWarehouseCode;

    /**
     * 货主代码
     */
    @ApiModelProperty(value = "货主代码")
    private String ownerCode;

    /**
     * 库存类型
     */
    @ApiModelProperty(value = "库存类型")
    private Integer inventoryType;

    /**
     * 锁定数量
     */
    @ApiModelProperty(value = "锁定数量")
    private Integer lockNum;

    /**
     * 解锁数量
     */
    @ApiModelProperty(value = "解锁数量")
    private Integer unlockNum;


}
