package com.danding.business.client.ares.withdrawal.result;


import com.danding.business.common.ares.enums.common.InventoryHandleType;
import com.danding.component.common.api.common.file.FileDto;
import com.danding.component.common.rpc.common.annotation.ResultReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 采退单表返回结果
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */

@Data
@ApiModel(value="WithdrawalOrder对象", description="采退单表返回结果")
public class WithdrawalOrderViewResult implements Serializable {


    private static final long serialVersionUID = 1L;
    private Long id;
    /**
    * 创建时间
    */
    private Long createTime;
    /**
    * 更新时间
    */
    private Long updateTime;
    /**
    * 创建人
    */
    private Long createBy;
    /**
    * 更新人
    */
    private Long updateBy;

   /**
    * 租户id
    */
    @ApiModelProperty(value = "租户id")
    private Long userId;

   /**
    * 用户名称
    */
    @ApiModelProperty(value = "用户名称")
    private String userName;

   /**
    * 采退单号
    */
    @ApiModelProperty(value = "采退单号")
    private String withdrawalOrderNo;

   /**
    * 采购单号
    */
    @ApiModelProperty(value = "采购单号")
    private String purchaseOrderNo;

   /**
    * 采购发票号
    */
    @ApiModelProperty(value = "采购发票号")
    private String purchaseInvoiceNo;

   /**
    * 补充单号
    */
    @ApiModelProperty(value = "补充单号")
    private String supplementNo;

   /**
    * 单据日期
    */
    @ApiModelProperty(value = "单据日期")
    private Long billDate;

   /**
    * 供应商编码
    */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商名称")
    @ResultReference(referenceType = ResultReference.ReferenceType.RPC, boostApplicationName = "ares-config-server", boostService = "ISupplierService", localReferProperty = "supplierCode", remoteReferProperty = "supplierCode", fetchProperty = "supplierName")
    private String supplierName;

   /**
    * 供应商业务单号
    */
    @ApiModelProperty(value = "供应商业务单号")
    private String supplierBusinessNo;

   /**
    * 采购金额
    */
    @ApiModelProperty(value = "采购金额")
    private BigDecimal purchaseAmount;

   /**
    * 采退金额
    */
    @ApiModelProperty(value = "采退金额")
    private BigDecimal withdrawalAmount;

   /**
    * 采购类型
    */
    @ApiModelProperty(value = "采购类型")
    private Integer purchaseType;

    /**
     * 采购类型名称
     */
    @ApiModelProperty(value = "采购类型名称")
    private String purchaseTypeName;

   /**
    * 货币类型
    */
    @ApiModelProperty(value = "货币类型")
    private String billCurrency;

    /**
     * 货币名称
     */
    @ApiModelProperty(value = "货币名称")
    private String billCurrencyName;

   /**
    * 是否确认: 0未确认 1已确认
    */
    @ApiModelProperty(value = "是否确认: 0未确认 1已确认")
    private Integer isConfirm;

   /**
    * 附件
    */
   @ApiModelProperty(value = "附件")
   private List<FileDto> attachment;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 库存状态 HANDLING:处理中  RELEASE_SUCCESS:释放成功  RELEASE_FAIL:释放失败
     */
    private InventoryHandleType inventoryStatus;

    /**
     * 库存操作信息
     */
    private String optInventoryMessage;

    /**
     * 采退单明细列表
     */
    @ApiModelProperty(value = "采退单明细列表")
    private List<WithdrawalOrderDetailViewResult> withdrawalOrderDetailList;

}
