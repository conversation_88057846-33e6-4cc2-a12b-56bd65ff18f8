package com.danding.business.client.ares.newflow.result;


import com.danding.business.common.ares.enums.newflow.FlowLogStatus;
import com.danding.business.common.ares.enums.newflow.FlowTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 审核日志表返回结果
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */

@Data
@ApiModel(value="NewFlowLog对象", description="审核日志表返回结果")
public class NewFlowLogResult implements Serializable {


    private static final long serialVersionUID = 1L;
    private Long id;
    /**
    * 创建时间
    */
    private Long createTime;
    /**
    * 更新时间
    */
    private Long updateTime;
    /**
    * 创建人
    */
    private Long createBy;
    /**
    * 更新人
    */
    private Long updateBy;

   /**
    * 商家ID
    */
    @ApiModelProperty(value = "商家ID")
    private Long userId;

   /**
    * 审核类型表ID
    */
    @ApiModelProperty(value = "审核类型表ID")
    private Long flowTypeId;

   /**
    * 流程ID
    */
    @ApiModelProperty(value = "流程ID")
    private Long flowStepId;

   /**
    * 关联单据
    */
    @ApiModelProperty(value = "关联单据")
    private String businessNo;

   /**
    * 审核类型
    */
    @ApiModelProperty(value = "审核类型")
    private FlowTypeEnum flowType;

   /**
    * 审核步骤，从0开始递增
    */
    @ApiModelProperty(value = "审核步骤，从0开始递增")
    private Integer step;

    /**
     * 待审核【默认】、审核中、通过、驳回
     */
    @ApiModelProperty(value = "待审核【默认】、审核中、通过、驳回")
    private FlowLogStatus status;

   /**
    * 操作人
    */
    @ApiModelProperty(value = "操作人")
    private Long operator;

   /**
    * 操作备注
    */
    @ApiModelProperty(value = "操作备注")
    private String operatorRemark;


}
