package com.danding.business.client.ares.flow.param;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 审核流模板添加
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */

@Data
@ApiModel(value = "Flow对象", description = "审核流模板添加")
public class FlowAddParam implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 商家ID
     */
    @ApiModelProperty(value = "商家ID")
    private Long userId;


}
