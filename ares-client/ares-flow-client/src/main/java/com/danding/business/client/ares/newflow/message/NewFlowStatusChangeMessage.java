package com.danding.business.client.ares.newflow.message;

import com.danding.business.common.ares.enums.newflow.FlowLogStatus;
import com.danding.business.common.ares.enums.newflow.FlowTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class NewFlowStatusChangeMessage implements Serializable {
    public static final long serialVersionUID = 42L;

    /**
     * 关联单据
     */
    private String businessNo;

    /**
     * 审核日志状态
     */
    private FlowLogStatus flowLogStatus;

    /**
     * 单据类型
     */
    private FlowTypeEnum flowType;

    /**
     * 审批人
     */
    private Long operator;

    /**
     * 审批人名称
     */
    private String operatorName;

    /**
     * 审批备注
     */
    private String operatorRemark;

    /**
     * 审批时间
     */
    private Long operatorTime;
}