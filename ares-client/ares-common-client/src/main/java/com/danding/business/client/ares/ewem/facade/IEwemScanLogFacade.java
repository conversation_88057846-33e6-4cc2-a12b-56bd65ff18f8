package com.danding.business.client.ares.ewem.facade;

import com.danding.business.client.ares.ewem.result.EwemScanLogResult;
import com.danding.business.client.ares.ewem.param.EwemScanLogQueryParam;
import com.danding.business.client.ares.ewem.param.EwemScanLogAddParam;

import com.danding.component.common.api.common.response.ListVO;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 扫码记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface IEwemScanLogFacade {

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    EwemScanLogResult getById(Serializable id);

    /**
     * 条件查询单个
     *
     * @param ewemScanLogQueryParam
     * @return
     */
    EwemScanLogResult getByQueryParam(EwemScanLogQueryParam ewemScanLogQueryParam);

    /**
     * 条件查询list
     *
     * @param ewemScanLogQueryParam
     * @return
     */
    List<EwemScanLogResult> listByQueryParam(EwemScanLogQueryParam ewemScanLogQueryParam);

    /**
     * 条件分页查询
     *
     * @param ewemScanLogQueryParam
     * @return
     */
    ListVO<EwemScanLogResult> pageListByQueryParam(EwemScanLogQueryParam ewemScanLogQueryParam);

    /**
     * 插入
     *
     * @param ewemScanLogAddParam
     * @return
     */
    boolean add(EwemScanLogAddParam ewemScanLogAddParam);

    /**
     * 批量插入
     *
     * @param ewemScanLogAddParamList
     * @return
     */
    boolean addList(List<EwemScanLogAddParam> ewemScanLogAddParamList);

    /**
     * 根据主键id修改
     *
     * @param ewemScanLogAddParam
     * @return
     */
    boolean updateById(EwemScanLogAddParam ewemScanLogAddParam);

    /**
     * 根据主键id批量修改
     *
     * @param ewemScanLogAddParamList
     * @return
     */
    boolean updateListById(List<EwemScanLogAddParam> ewemScanLogAddParamList);

    /**
     * 根据条件修改
     *
     * @param ewemScanLogQueryParam
     * @param ewemScanLogAddParam
     * @return
     */
    boolean updateListByQueryParam(EwemScanLogQueryParam ewemScanLogQueryParam, EwemScanLogAddParam ewemScanLogAddParam);

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    boolean removeById(Serializable id);

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    boolean removeByIds(List<Long> idList);

    /**
     * 根据条件删除
     *
     * @param ewemScanLogQueryParam
     * @return
     */
    boolean removeByQueryParam(EwemScanLogQueryParam ewemScanLogQueryParam);
}
