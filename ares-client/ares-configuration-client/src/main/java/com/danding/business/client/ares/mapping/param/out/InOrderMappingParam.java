package com.danding.business.client.ares.mapping.param.out;

import com.danding.business.common.ares.enums.order.OrderFlgStatus;
import com.danding.business.common.ares.enums.order.PortAreaEnum;
import com.danding.business.common.ares.enums.trade.ReadyBusinessType;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 描述:
 *    入库单下发接口参数
 * <AUTHOR>
 * @date 2020/11/18 下午5:27
 */
@Data
public class InOrderMappingParam extends InSpecialParam {

    private static final long serialVersionUID = -5821336475439897894L;

    /**
     * 系统: OMS，淘宝等, 默认: ARES
     */
    private String origSystem = "ARES";

    /**
     * 关联单号
     */
    private String businessNo;

    /**
     * 上游单号
     */
    private String upstreamNo;

    /**
     * 外部单号
     */
    private String externalNo;

    /**
     * 下游单号
     */
    private String downstreamNo;

    /**
     * 入库单号
     */
    @NotBlank(message = "入库单号不能为空!")
    private String inOrderNo;

    /**
     * 单据类型
     */
    private Integer orderType;

    /**
     * 业务类型  01 入库 02 出库, 03 调拨
     */
    private String businessType = "01";

    /**
     * 实体仓编码
     */
    @NotBlank(message = "实体仓编码不能为空!")
    private String warehouseCode;

    /**
     * TODO 实体仓编码(发货仓) 兼容菜鸟大贸 B单和调拨单 业务使用
     */
    private String sendWarehouseCode;

    /**
     * 货主编码
     */
    @NotBlank(message = "货主编码不能为空!")
    private String ownerCode;

    /**
     * 预计入库时间
     */
    private Long expectTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 包材
     */
    private String packingMaterials;

    /**
     * 贸易类型-1保税/2完税
     */
    private Integer tradeType;

    /**
     * 正向出库单号
     */
    private String outOrderNo;

    /**
     * 海关入区类型
     */
    private ReadyBusinessType readyBusinessType;

    /**
     * 订单标记
     */
    private OrderFlgStatus orderFlg;

    /**
     * json扩展字段
     */
    private String extensionJson;

    /**
     * 区港联动
     */
    private PortAreaEnum portArea;

    /**
     * 备货单号
     */
    private String readyNo;

    /**
     * 清关状态
     */
    private Integer readyStatus;

    /**
     * 全局单号
     */
    private String globalNo;

    /**
     * 来源系统(上游来源系统)
     */
    private String upOrigSystem;

    /**
     * 推送类型
     */
    private Integer isPush;

    /**
     * 收发货及物流信息
     */
    private ReceiveSendInfoMappingParam receiveSendInfoMappingParam;

    /**
     * sku详情
     */
    private List<ItemSkuMappingParam> itemSkuList;

}
