package com.danding.business.client.ares.configuration.param;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 配置方法表添加
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */

@Data
@ApiModel(value="ConfigurationMethod对象", description="配置方法表添加")
public class ConfigurationMethodAddParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 系统编码
     */
    @ApiModelProperty(value = "系统编码")
    private String systemCode;

    /**
     * 方法编码
     */
    @ApiModelProperty(value = "方法编码")
    private String methodCode;

    /**
     * 方法类型
     */
    @ApiModelProperty(value = "方法类型")
    private String methodType;

    /**
     * 方法名称
     */
    @ApiModelProperty(value = "方法名称")
    private String methodName;

    /**
     * 映射类型 1 json, 2 xml
     */
    @ApiModelProperty(value = "映射类型 1 json, 2 xml")
    private Integer dataType;

    /**
     * 是否映射 1 是，2 否
     */
    @ApiModelProperty(value = "是否映射 1 是，2 否")
    private Integer callType;

}
