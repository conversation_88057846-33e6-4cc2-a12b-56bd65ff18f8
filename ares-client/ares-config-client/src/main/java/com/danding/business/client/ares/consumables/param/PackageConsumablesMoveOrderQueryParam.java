package com.danding.business.client.ares.consumables.param;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <p>
 * 包耗材移位单查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */

@Data
@ApiModel(value="PackageConsumablesMoveOrder对象", description="包耗材移位单查询")
public class PackageConsumablesMoveOrderQueryParam extends Page {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 移位单号
     */
    @ApiModelProperty(value = "移位单号")
    private String moveOrderNo;
    private Set<String> moveOrderNoSet;

    /**
     * 包耗材条码
     */
    @ApiModelProperty(value = "包耗材条码")
    private Set<String> pcBarcodeSet;

    /**
     * 外部实体编码
     */
    @ApiModelProperty(value = "外部实体编码")
    private String warehouseCode;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String moveType;

    /**
     * 状态: 1 创建, 10 取消, 20 移位完成
     */
    @ApiModelProperty(value = "状态: 1 创建, 10 取消, 20 移位完成")
    private Integer status;

    /**
     * 创建开始时间
     */
    private Long createTimeStart;
    private Long createTimeEnd;

    /**
     * 更新时间
     */
    private Long updateTimeStart;
    private Long updateTimeEnd;

}
