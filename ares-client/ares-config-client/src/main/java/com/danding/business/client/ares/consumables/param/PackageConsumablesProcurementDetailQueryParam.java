package com.danding.business.client.ares.consumables.param;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 包耗材采购明细列表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */

@Data
@ApiModel(value="PackageConsumablesProcurementDetail对象", description="包耗材采购明细列表查询")
public class PackageConsumablesProcurementDetailQueryParam extends Page {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
    * 创建时间
    */
    private Long createTime;

    /**
    * 更新时间
    */
    private Long updateTime;

    /**
    * 创建人
    */
    private Long createBy;

    /**
    * 更新人
    */
    private Long updateBy;

    /**
     * 包耗材采购单号
     */
    @ApiModelProperty(value = "包耗材采购单号")
    private String pcProcurementNo;

    /**
     * 包耗材条码
     */
    @ApiModelProperty(value = "包耗材条码")
    private String pcBarcode;

    /**
     * 包耗材名称
     */
    @ApiModelProperty(value = "包耗材名称")
    private String pcName;

    /**
     * 属性类型
     */
    @ApiModelProperty(value = "属性类型")
    private String pcType;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String pcUnit;

    /**
     * 计划采购数量
     */
    @ApiModelProperty(value = "计划采购数量")
    private BigDecimal planQuantity;

    /**
     * 采购价格
     */
    @ApiModelProperty(value = "采购价格")
    private BigDecimal unitPrice;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long tenantId;


}
