package com.danding.business.client.ares.consumables.param;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 供应商包耗材绑定关系查询
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */

@Data
@ApiModel(value="SuppliersPcRelationship对象", description="供应商包耗材绑定关系查询")
public class SuppliersPcRelationshipQueryParam extends Page {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
    * 创建时间
    */
    private Long createTime;

    /**
    * 更新时间
    */
    private Long updateTime;

    /**
    * 创建人
    */
    private Long createBy;

    /**
    * 更新人
    */
    private Long updateBy;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String suppliersCode;

    /**
     * 包耗材条码
     */
    @ApiModelProperty(value = "包耗材条码")
    private String pcBarcode;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long tenantId;


}
