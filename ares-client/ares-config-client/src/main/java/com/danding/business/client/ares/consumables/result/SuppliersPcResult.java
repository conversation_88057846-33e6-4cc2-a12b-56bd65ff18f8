package com.danding.business.client.ares.consumables.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 包耗材供应商返回结果
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */

@Data
@ApiModel(value="SuppliersPc对象", description="包耗材供应商返回结果")
public class SuppliersPcResult implements Serializable {

    private static final long serialVersionUID = 1L;

   /**
    * 供货商编码
    */
    @ApiModelProperty(value = "供货商编码")
    private String suppliersCode;

   /**
    * 供货商名称
    */
    @ApiModelProperty(value = "供货商名称")
    private String suppliersName;

   /**
    * 租户id
    */
    @ApiModelProperty(value = "租户id")
    private Long tenantId;


}
