package com.danding.business.client.ares.owner.facade;


import com.danding.business.client.ares.owner.param.OwnerAddParam;
import com.danding.business.client.ares.owner.param.OwnerPlegedEditParam;
import com.danding.business.client.ares.owner.param.OwnerQueryParam;
import com.danding.business.client.ares.owner.result.GetEntityByOwnerResult;
import com.danding.business.client.ares.owner.result.OwnerGoodsSyncStatusResult;
import com.danding.business.client.ares.owner.result.OwnerIdNameResult;
import com.danding.business.client.ares.owner.result.OwnerResult;
import com.danding.business.common.ares.BO.report.OwnerCallbackResponse;
import com.danding.business.common.ares.enums.common.OwnerEntryStatus;
import com.danding.component.common.api.common.response.ListVO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
public interface IOwnerFacade {

    GetEntityByOwnerResult getEntityCodeByOwnerCode(String ownerCode);

    List<OwnerIdNameResult> listOwnerByUserId(Long userId);

    List<OwnerIdNameResult> listOwner();

    Boolean saveOwner(OwnerAddParam addParam);

    ListVO<OwnerResult> listOwnerByPage(OwnerQueryParam queryParam);

    List<OwnerResult> listOwnerByParam(OwnerQueryParam queryParam);

    OwnerCallbackResponse ownerOutExecute(List<String> codeList);

    OwnerResult getByCode(String ownerCode);

    Boolean openAndClosed(String ownerCode);

    Boolean update(OwnerResult ownerResult);

    /**
     * 融资人质押/冻结/解押状态修改
     *
     * @param ownerPlegedEditParam
     * @return
     */
    Boolean editOwnerByPlegde(OwnerPlegedEditParam ownerPlegedEditParam);

    /**
     * 货主内部审核增加理货报告审核流程
     *
     * @return
     */
    void ownerTallyReportFlowAdd(String ownerCode);

    /**
     * 获取审核id
     *
     * @param businessId
     * @param userId
     * @return
     */
    String getTaskId(OwnerEntryStatus ownerEntryStatus, String businessId, Long userId);

    /**
     * 根据当前用户获取合同列表
     *
     * @param userId
     * @return
     */
    List<String> getContractListByUserId(Long userId);

    /**
     * 查询指定货主的货品同步状态信息
     *
     * @param userId
     * @return
     */
    List<OwnerGoodsSyncStatusResult> getGoodsSyncStatusInfo(Long userId);

    /**
     * 临时同步风险货主
     */
    void addRiskOwner();

    /**
     * 贷前信息同步
     * @param ownerCode
     */
    void synPreLoanInfo(String ownerCode);

    List<OwnerResult> listOwnerByReturn(OwnerQueryParam queryParam);

    List<OwnerResult> financingRelationshipList();

    boolean checkReturnEntryArea(String ownerCode, String entityWarehouseCode);

    boolean addTag(List<Integer> tagList, List<String> ownerCodeList);

    /**
     * 根据货主查询退货货主
     *
     * @param ownerCode
     * @return
     */
    OwnerResult getReturnOwnerByCode(String ownerCode);

}
