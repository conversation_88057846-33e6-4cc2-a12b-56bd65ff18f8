package com.danding.business.client.ares.supplier.param;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Set;

/**
 * <p>
 * 查询
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-22
 */

@Data
@ApiModel(value="Supplier对象", description="查询")
public class SupplierQueryParam extends Page {


    private static final long serialVersionUID = 1L;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;
    private Set<String> supplierCodeSet;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 供应商公司
     */
    @ApiModelProperty(value = "供应商公司")
    private String supplierCompany;

    /**
     * 供应商公司地址
     */
    @ApiModelProperty(value = "供应商公司地址")
    private String supplierCompanyAddress;

    /**
     * 合作开始时间
     */
    @ApiModelProperty(value = "合作开始时间")
    private Long cooperationStart;

    /**
     * 合作结束时间
     */
    @ApiModelProperty(value = "合作结束时间")
    private Long cooperationEnd;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long userId;

    /**
     * 附件信息
     */
    @ApiModelProperty(value = "附件信息")
    private String fileJson;

    /**
     * 合作次数
     */
    @ApiModelProperty(value = "合作次数")
    private Integer tradeNum;

    @ApiModelProperty(value = "")
    private BigDecimal tradePrice;

    // 1 合作中，2 合作终止
    private Integer status ;
}
