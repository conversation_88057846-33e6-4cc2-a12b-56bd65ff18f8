package com.danding.business.client.ares.consumables.facade;

import com.danding.business.client.ares.consumables.param.MaterialRequisitionAddParam;
import com.danding.business.client.ares.consumables.param.MaterialRequisitionQueryParam;
import com.danding.business.client.ares.consumables.param.PackageConsumablesProcurementAddParam;
import com.danding.business.client.ares.consumables.result.MaterialRequisitionResult;
import com.danding.component.common.api.common.response.ListVO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 领用单列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
@Validated
public interface IMaterialRequisitionFacade {

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    MaterialRequisitionResult getById(Serializable id);

    /**
     * 条件查询单个
     *
     * @param materialRequisitionQueryParam
     * @return
     */
    MaterialRequisitionResult getByQueryParam(MaterialRequisitionQueryParam materialRequisitionQueryParam);

    /**
     * 条件查询list
     *
     * @param materialRequisitionQueryParam
     * @return
     */
    List<MaterialRequisitionResult> listByQueryParam(MaterialRequisitionQueryParam materialRequisitionQueryParam);

    /**
     * 条件分页查询
     *
     * @param materialRequisitionQueryParam
     * @return
     */
    ListVO<MaterialRequisitionResult> pageListByQueryParam(MaterialRequisitionQueryParam materialRequisitionQueryParam);

    /**
     * 插入
     *
     * @param materialRequisitionAddParam
     * @return
     */
    String add(MaterialRequisitionAddParam materialRequisitionAddParam);

    /**
     * 批量插入
     *
     * @param materialRequisitionAddParamList
     * @return
     */
    boolean addList(List<MaterialRequisitionAddParam> materialRequisitionAddParamList);

    /**
     * 根据主键id修改
     *
     * @param materialRequisitionAddParam
     * @return
     */
    boolean updateById(MaterialRequisitionAddParam materialRequisitionAddParam);

    /**
     * 根据主键id批量修改
     *
     * @param materialRequisitionAddParamList
     * @return
     */
    boolean updateListById(List<MaterialRequisitionAddParam> materialRequisitionAddParamList);

    /**
     * 根据条件修改
     *
     * @param materialRequisitionQueryParam
     * @param materialRequisitionAddParam
     * @return
     */
    boolean updateListByQueryParam(MaterialRequisitionQueryParam materialRequisitionQueryParam, MaterialRequisitionAddParam materialRequisitionAddParam);

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    boolean removeById(Serializable id);

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    boolean removeByIds(List<Long> idList);

    /**
     * 根据条件删除
     *
     * @param materialRequisitionQueryParam
     * @return
     */
    boolean removeByQueryParam(MaterialRequisitionQueryParam materialRequisitionQueryParam);

    /**
     * 确认
     * @param materialRequisitionNo
     * @return
     */
    boolean confirm(String materialRequisitionNo, String opt);

    /**
     * 发放
     * @param materialRequisitionAddParam
     * @return
     */
    boolean materialRequisitionOut(@Valid MaterialRequisitionAddParam materialRequisitionAddParam);

    /**
     * 明细查询
     *
     * @param materialRequisitionQueryParam
     * @return
     */
    MaterialRequisitionResult getDetailByQueryParam(MaterialRequisitionQueryParam materialRequisitionQueryParam);

    /**
     * 取消
     *
     * @param materialRequisitionAddParam
     * @return
     */
    boolean cancel(MaterialRequisitionAddParam materialRequisitionAddParam);



}
