package com.danding.business.client.ares.consumables.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 移位操作的返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */

@Data
@ApiModel(value = "PackageConsumablesShiftResult", description = "移位操作的返回对象")
public class PackageConsumablesShiftResult implements Serializable {

    private static final long serialVersionUID = 1L;
    private String message;
    private boolean checkPassed;
    /**
     * 包耗材条码
     */
    @ApiModelProperty(value = "包耗材条码")
    private String pcBarcode;
    private String sourceLocationCode;
    private String targetLocationCode;
    private String operationType;
    private String operationTypeName;
    private String locationType;
    private String locationTypeName;

    //移位单使用
    private String moveOrderNo;
    private String warehouseCode;
    private String warehouseName;
    private String moveType;

    public static PackageConsumablesShiftResult build(boolean checkPassed) {
        PackageConsumablesShiftResult result = new PackageConsumablesShiftResult();
        result.setCheckPassed(checkPassed);
        return result;
    }

    public static PackageConsumablesShiftResult build(String message, boolean checkPassed) {
        PackageConsumablesShiftResult result = build(checkPassed);
        result.setMessage(message);
        return result;
    }
}
