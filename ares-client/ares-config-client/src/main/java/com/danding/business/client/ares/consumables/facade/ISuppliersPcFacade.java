package com.danding.business.client.ares.consumables.facade;

import com.danding.business.client.ares.consumables.param.SuppliersPcAddParam;
import com.danding.business.client.ares.consumables.param.SuppliersPcQueryParam;
import com.danding.business.client.ares.consumables.result.SuppliersPcResult;
import com.danding.component.common.api.common.response.ListVO;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 包耗材供应商 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
public interface ISuppliersPcFacade {

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    SuppliersPcResult getById(Serializable id);

    /**
     * 条件查询单个
     *
     * @param suppliersPcQueryParam
     * @return
     */
    SuppliersPcResult getByQueryParam(SuppliersPcQueryParam suppliersPcQueryParam);

    /**
     * 条件查询list
     *
     * @param suppliersPcQueryParam
     * @return
     */
    List<SuppliersPcResult> listByQueryParam(SuppliersPcQueryParam suppliersPcQueryParam);

    /**
     * 条件分页查询
     *
     * @param suppliersPcQueryParam
     * @return
     */
    ListVO<SuppliersPcResult> pageListByQueryParam(SuppliersPcQueryParam suppliersPcQueryParam);

    /**
     * 插入
     *
     * @param suppliersPcAddParam
     * @return
     */
    boolean add(SuppliersPcAddParam suppliersPcAddParam);

    /**
     * 批量插入
     *
     * @param suppliersPcAddParamList
     * @return
     */
    boolean addList(List<SuppliersPcAddParam> suppliersPcAddParamList);

    /**
     * 根据主键id修改
     *
     * @param suppliersPcAddParam
     * @return
     */
    boolean updateById(SuppliersPcAddParam suppliersPcAddParam);

    /**
     * 根据主键id批量修改
     *
     * @param suppliersPcAddParamList
     * @return
     */
    boolean updateListById(List<SuppliersPcAddParam> suppliersPcAddParamList);

    /**
     * 根据条件修改
     *
     * @param suppliersPcQueryParam
     * @param suppliersPcAddParam
     * @return
     */
    boolean updateListByQueryParam(SuppliersPcQueryParam suppliersPcQueryParam, SuppliersPcAddParam suppliersPcAddParam);

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    boolean removeById(Serializable id);

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    boolean removeByIds(List<Long> idList);

    /**
     * 根据条件删除
     *
     * @param suppliersPcQueryParam
     * @return
     */
    boolean removeByQueryParam(SuppliersPcQueryParam suppliersPcQueryParam);

    /**
     * 批次插入供应商
     * @param suppliersPcAddParamList
     * @return
     */
    boolean addListSuppliersPc(List<SuppliersPcAddParam> suppliersPcAddParamList);

}
