package com.danding.business.client.ares.customer.result;


import com.danding.business.common.ares.BO.report.BaseBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 返回结果
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-22
 */

@Data
@ApiModel(value="CustomerLevel对象", description="返回结果")
public class CustomerLevelResult  extends BaseBO implements Serializable {


    private static final long serialVersionUID = 1L;

   /**
    * 租户id
    */
    @ApiModelProperty(value = "租户id")
    private Long userId;

   /**
    * 等级
    */
    @ApiModelProperty(value = "等级")
    private String level;

   /**
    * 等级折扣率
    */
    @ApiModelProperty(value = "等级折扣率")
    private String levelDiscount;


}
