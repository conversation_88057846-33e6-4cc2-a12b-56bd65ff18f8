package com.danding.business.client.ares.supplier.param;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 添加
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-22
 */

@Data
@ApiModel(value = "SupplierContact对象", description = "添加")
public class SupplierContactAddParam implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long userId;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    /**
     * 联系人职位
     */
    @ApiModelProperty(value = "联系人职位")
    private String contactPost;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @ApiModelProperty(value = "联系人邮箱")
    private String contactEmail;

    /**
     * 省编码
     */
    private Integer provinceCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市编码
     */
    private Integer cityCode;

    /**
     * 市
     */
    private String city;

    /**
     * 区编码
     */
    private Integer zoneCode;

    /**
     * 区
     */
    private String zone;

    /**
     * 详细地址
     */
    private String address;

}
