package com.danding.business.client.ares.goods.param;

import com.danding.business.common.ares.enums.common.ApprovalStatus;
import com.danding.business.common.ares.enums.goods.PremiumType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 货品利润表添加
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-05
 */

@Data
@ApiModel(value = "GoodsProfit对象", description = "货品利润表添加")
public class GoodsProfitAddParam implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long updateTime;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 货品sku
     */
    @ApiModelProperty(value = "货品sku")
    private String goodsCode;

    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    private String level;

    /**
     * 加价类型
     */
    @ApiModelProperty(value = "加价类型")
    private PremiumType premiumType;

    /**
     * 加价幅度
     */
    @ApiModelProperty(value = "加价幅度")
    private BigDecimal premium;

    /**
     * 审批状态
     */
    private ApprovalStatus approvalStatus;

    /**
     * 审核信息
     */
    private String operatorRemark;
    /**
     * 外部sku
     */
    private String sku;
}
