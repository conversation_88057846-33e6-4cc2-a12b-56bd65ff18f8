package com.danding.business.client.ares.goods.facade;

import com.danding.business.client.ares.goods.result.GoodsProfitResult;
import com.danding.business.client.ares.goods.param.GoodsProfitQueryParam;
import com.danding.business.client.ares.goods.param.GoodsProfitAddParam;

import com.danding.business.common.ares.excelV2.param.ImportParam;
import com.danding.component.common.api.common.response.ListVO;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 货品利润表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-05
 */
public interface IGoodsProfitFacade {

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    GoodsProfitResult getById(Serializable id);

    /**
     * 主键查询
     *
     * @param ids
     * @return
     */
    List<GoodsProfitResult> getByIds(List<String> ids);

    /**
     * 条件查询单个
     *
     * @param goodsProfitQueryParam
     * @return
     */
    GoodsProfitResult getByQueryParam(GoodsProfitQueryParam goodsProfitQueryParam);

    /**
     * 条件查询list
     *
     * @param goodsProfitQueryParam
     * @return
     */
    List<GoodsProfitResult> listByQueryParam(GoodsProfitQueryParam goodsProfitQueryParam);

    /**
     * 条件分页查询
     *
     * @param goodsProfitQueryParam
     * @return
     */
    ListVO<GoodsProfitResult> pageListByQueryParam(GoodsProfitQueryParam goodsProfitQueryParam);

    /**
     * 插入
     *
     * @param goodsProfitAddParam
     * @return
     */
    boolean add(GoodsProfitAddParam goodsProfitAddParam);

    /**
     * 批量插入
     *
     * @param goodsProfitAddParamList
     * @return
     */
    boolean addList(List<GoodsProfitAddParam> goodsProfitAddParamList);

    /**
     * 根据主键id修改
     *
     * @param goodsProfitAddParam
     * @return
     */
    boolean updateById(GoodsProfitAddParam goodsProfitAddParam);

    /**
     * 根据主键id批量修改
     *
     * @param goodsProfitAddParamList
     * @return
     */
    boolean updateListById(List<GoodsProfitAddParam> goodsProfitAddParamList);

    /**
     * 根据条件修改
     *
     * @param goodsProfitQueryParam
     * @param goodsProfitAddParam
     * @return
     */
    boolean updateListByQueryParam(GoodsProfitQueryParam goodsProfitQueryParam, GoodsProfitAddParam goodsProfitAddParam);

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    boolean removeById(Serializable id);

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    boolean removeByIds(List<Long> idList);

    /**
     * 根据条件删除
     *
     * @param goodsProfitQueryParam
     * @return
     */
    boolean removeByQueryParam(GoodsProfitQueryParam goodsProfitQueryParam);

    /**
     * 导入异步实现
     */
    void asyncImportTask(ImportParam importParam);

    /**
     * 保存/编辑货品利润
     *
     * @param copyProperties
     * @return
     */
    boolean save(GoodsProfitAddParam copyProperties);

    /**
     * 货品利润提交审核
     *
     * @param purchaseOrderNo
     * @return
     */
    boolean submitAudit(String purchaseOrderNo);
}
