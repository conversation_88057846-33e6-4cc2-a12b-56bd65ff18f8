package com.danding.business.client.ares.goodsManagement.facade;

import com.danding.business.client.ares.goods.param.GoodsOperationLogParam;
import com.danding.business.client.ares.goodsManagement.param.GoodsManagementAddParam;
import com.danding.business.client.ares.goodsManagement.param.GoodsManagementQueryParam;
import com.danding.business.client.ares.goodsManagement.param.GoodsManagementWmsUpdParam;
import com.danding.business.client.ares.goodsManagement.param.PeriodValidityUpdateParam;
import com.danding.business.client.ares.goodsManagement.result.GoodsManagementResult;
import com.danding.component.common.api.common.response.ListVO;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 货品管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-06
 */
public interface IGoodsManagementFacade {

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    GoodsManagementResult getById(Serializable id);

    /**
     * 条件查询单个(附带货品信息)
     *
     * @param goodsManagementQueryParam
     * @return
     */
    GoodsManagementResult getDetailByQueryParam(GoodsManagementQueryParam goodsManagementQueryParam);

    List<GoodsManagementResult> getDetailListByQueryParamForT(List<String> goodsCodeList, String ownerCode, Long userId);

    GoodsManagementResult getDetailByQueryParamForT(String goodsCode, String ownerCode, Long userId);

    GoodsManagementResult getDetailByQueryParam(String goodsCode, String ownerCode, Long userId);

    GoodsManagementResult getDetailByQueryParam(String goodsCode, String sku, String ownerCode, Long userId);

    GoodsManagementResult getDetailByQueryParam(String sku, String ownerCode, String warehouseCode);

    /**
     * 条件查询单个（不查询货品信息）
     *
     * @param queryParam
     * @return
     */
    GoodsManagementResult getByQueryParam(GoodsManagementQueryParam queryParam);

    /**
     * 条件查询list
     *
     * @param goodsManagementQueryParam
     * @return
     */
    List<GoodsManagementResult> listByQueryParam(GoodsManagementQueryParam goodsManagementQueryParam);

    /**
     * 条件查询list
     *
     * @param list      相关list
     * @param ownerCode 货主编码
     * @param type      1 goodsCode 2 sku
     * @return
     */
    @Deprecated
    List<GoodsManagementResult> listByQueryParam(Long userId, List<String> list, String ownerCode, Integer type);

    /**
     * 条件分页查询
     *
     * @param goodsManagementQueryParam
     * @return
     */
    ListVO<GoodsManagementResult> pageListByQueryParam(GoodsManagementQueryParam goodsManagementQueryParam);

    /**
     * 插入
     *
     * @param goodsManagementAddParam
     * @return
     */
    boolean add(GoodsManagementAddParam goodsManagementAddParam);

    /**
     * 插入
     *
     * @param goodsManagementAddParam
     * @param updateIfPresent
     * @return
     */
    boolean add(GoodsManagementAddParam goodsManagementAddParam, boolean updateIfPresent);
    boolean add(GoodsManagementAddParam goodsManagementAddParam, boolean updateIfPresent,boolean addIssueRecord);
    /**
     * 插入
     *
     * @param goodsManagementAddParam
     * @return
     */
    boolean syncAdd(GoodsManagementAddParam goodsManagementAddParam);

    /**
     * 插入
     *
     * @param goodsManagementAddParam
     * @return
     */
   // boolean syncAdd(GoodsManagementAddParam goodsManagementAddParam, boolean updateIfPresent);

    /**
     * 批量插入
     *
     * @param goodsManagementAddParamList
     * @param syncRecordWarehouse
     * @return
     */
    boolean addList(List<GoodsManagementAddParam> goodsManagementAddParamList, boolean syncRecordWarehouse);

    /**
     * 根据主键id修改
     *
     * @param goodsManagementAddParam
     * @return
     */
    boolean updateById(GoodsManagementAddParam goodsManagementAddParam);

    /**
     * 根据主键id修改
     *
     * @param goodsManagementAddParam
     * @param jumpInOrderCheck        若为true, 则在修改效期信息时，不要校验入库单，否则要校验入库单
     * @param checkInventory          若为true, 则在修改效期信息时，要校验库存，否则不校验库库
     * @return
     */
    boolean updateById(GoodsManagementAddParam goodsManagementAddParam, boolean jumpInOrderCheck, boolean checkInventory);

    /**
     * 根据主键id批量修改
     *
     * @param goodsManagementAddParamList
     * @return
     */
    boolean updateListById(List<GoodsManagementAddParam> goodsManagementAddParamList);

    /**
     * 根据条件修改
     *
     * @param goodsManagementQueryParam
     * @param goodsManagementAddParam
     * @return
     */
    boolean updateListByQueryParam(GoodsManagementQueryParam goodsManagementQueryParam, GoodsManagementAddParam goodsManagementAddParam);

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    boolean removeById(Serializable id);

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    boolean removeByIds(List<Long> idList);

    /**
     * 根据条件删除
     *
     * @param goodsManagementQueryParam
     * @return
     */
    boolean removeByQueryParam(GoodsManagementQueryParam goodsManagementQueryParam);

    /**
     * 批量启用货品
     *
     * @param ids
     * @return
     */
    boolean enableList(List<String> ids);
    boolean enableByGoodsCode(String goodsCode);
    boolean forbiddenGoodsByGoodsCode(String goodsCode);
    /**
     * 禁用货品
     *
     * @param id
     * @return
     */
    boolean forbiddenGoods(Long id);


    /**
     * 导入异步实现
     *
     * @param urlAddr excel路径
     * @param uid     任务ID
     */
    void asyncImportTask(String urlAddr, String uid);

    /**
     * 新增货品时异步处理货品管理
     *
     * @param userId
     * @param goodsCode
     * @return
     */
    boolean asycnGoodsManagement(Long userId, String goodsCode);

    /**
     * 新增货品时异步处理货品管理，并下发给wms
     *
     * @param userId
     * @param goodsCode
     * @return
     */
    boolean asyncGoodsManagementAndWms(Long userId, String goodsCode);
    /**
     * 新增货品时处理货品管理，并下发给wms
     *
     * @param userId
     * @param goodsCode
     * @return
     */
    boolean syncGoodsManagementAndWms(Long userId, String goodsCode,String ownerCode);


    boolean syncGoodsManagementAndWms(Long userId, String goodsCode,String ownerCode, String externalCode);

    /**
     * 新增货品时异步处理货品管理
     *
     * @param userId
     * @param goodsCode
     * @param updateIfPresent
     * @return
     */
    boolean asycnGoodsManagement(Long userId, String goodsCode, boolean updateIfPresent,
                                 GoodsOperationLogParam logParam);

    boolean updatePledgeStatus(List<GoodsManagementAddParam> addParams);

    /**
     * 条件查询list
     *
     * @param list      相关list
     * @param ownerCode 货主编码
     * @param type      1 goodsCode 2 sku 3 externalCode
     * @return
     */
    List<GoodsManagementResult> listGoodsManagementByQuery(Long userId, List<String> list, String ownerCode, Integer type);

    /**
     * externalCode 外部货品Id
     *
     * @param list
     * @return
     */
    boolean addExternalCode(List<GoodsManagementAddParam> list);

    /**
     * 更新外部货品ID
     *
     * @param addParam
     * @return
     */
    boolean updateExternalCode(GoodsManagementAddParam addParam);

    /**
     * wms按【货主+SKU维度】更新erp货主效期属性【长、宽、高、体积、净重、毛重】
     *
     * @param goodsManagementWmsUpdParam
     * @return
     */
    boolean updGoodsManagementInfoByWsm(GoodsManagementWmsUpdParam goodsManagementWmsUpdParam);

    /**
     * 更新效期信息
     *
     * @param updateParam
     * @return
     */
    boolean updatePeriodValidity(Long operatorUserId, List<PeriodValidityUpdateParam> updateParam);

    void syncMaterialCodeTask();

    void syncGoodsToReturnOwner(List<String> ownerCodes);
}
