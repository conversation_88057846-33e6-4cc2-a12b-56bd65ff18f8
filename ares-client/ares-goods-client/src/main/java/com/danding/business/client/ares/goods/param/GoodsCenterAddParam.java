package com.danding.business.client.ares.goods.param;


import com.danding.business.common.ares.BO.report.BaseBO;
import com.danding.business.common.ares.enums.goods.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

@Data
public class GoodsCenterAddParam extends BaseBO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "修改来源业务系统")
    private String originAppName;


    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long userId;

    /**
     * 货品Id
     */
    @ApiModelProperty(value = "货品Id")
    private String goodsCode;

    /**
     * sku
     */
    @ApiModelProperty(value = "sku")
    private String sku;

    /**
     * 货品名称
     */
    @ApiModelProperty(value = "货品名称")
    private String goodsName;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String ownerCode;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private GoodsStatus status;

    /**
     * 条形码
     */
    @ApiModelProperty(value = "条形码")
    private String barcode;

    /**
     * 贸易类型
     */
    @ApiModelProperty(value = "贸易类型")
    private GoodsType type;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否开启批次管理
     */
    @ApiModelProperty(value = "是否开启批次管理")
    private GoodsBatchManagement batchManagement;

    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    private Integer shelfLife;

    /**
     * 禁售天数
     */
    @ApiModelProperty(value = "禁售天数")
    private Integer noSellDate;

    /**
     * 禁收天数
     */
    @ApiModelProperty(value = "禁收天数")
    private Integer noCollectDate;

    /**
     * 预警天数
     */
    @ApiModelProperty(value = "预警天数")
    private Integer warningDate;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    private String model;

    /**
     * 品牌编码
     */
    @ApiModelProperty(value = "品牌编码")
    private String brandCode;

    /**
     * 品牌名称
     */
    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    /**
     * 品牌名称(英文)
     */
    @ApiModelProperty(value = "品牌名称(英文)")
    private String brandNameEn;

    /**
     * 长
     */
    @ApiModelProperty(value = "长")
    private Double length;

    /**
     * 宽
     */
    @ApiModelProperty(value = "宽")
    private Double width;

    /**
     * 高
     */
    @ApiModelProperty(value = "高")
    private Double height;

    /**
     * 毛重
     */
    @ApiModelProperty(value = "毛重")
    private Double grossWeight;

    /**
     * 净重
     */
    @ApiModelProperty(value = "净重")
    private Double netWeight;

    @ApiModelProperty(value = "耗材是否计重")
    private Integer materialAddWeight;
    /**
     * 提交
     */
    private Double volume;
    /**
     * 零售价格
     */
    @ApiModelProperty(value = "零售价格")
    private BigDecimal retailPrice;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 外部货品Id
     */
    private String externalCode;
    @ApiModelProperty(value = "是否是新记录：默认是新:1,[1:新,-1:旧]")
    private Integer isNewRecord;
    /**
     * 货品中心迁移自 wms 字段
     */

    /**
     * 批次规则 不允许为空，默认标准规则(批次规则档案)
     */
    private String lotRuleCode;

    /**
     * 分配规则 取值分配规则档案
     */
    private String allocationRuleCode;

    /**
     * 周转规则 取值周转规则档案
     */
    private String turnoverRuleCode;

    /**
     * 商品包装：整箱、拆零、异形
     */
    private String skuWrap;

    /**
     * 订单拆分规则
     */
    private BigDecimal wrapQty;

    /**
     * 标准方案长(cm)
     */
    private BigDecimal standardLength;

    /**
     * 标准方案宽(cm)
     */
    private BigDecimal standardWidth;

    /**
     * 标准方案高(cm)
     */
    private BigDecimal standardHeight;

    /**
     * 标准方案体积(cm³)
     */
    private BigDecimal standardVolume;

    /**
     * 托规(托规只能填正整数)
     */
    private Integer bracketGauge;

    /**
     * 颜色
     */
    private String colour;

    /**
     * 颜色代码
     */
    private String colourCode;

    /**
     * 款号
     */
    private String style;

    /**
     * 尺码
     */
    private String skuSize;

    /**
     * 尺码代码
     */
    private String sizeCode;

    /**
     * 年份
     */
    private String year;

    /**
     * 季节
     */
    private String season;

    /**
     * 货品中心迁移自 wms 字段 结束
     */

    //增加同样扩展字段
    /**
     * 商品标记1位标记
     */
    private Long tag1;

    /**
     * 商品标记2位标记
     */
    private Long tag2;



    /**
     * 扩展jsonkv存储
     */
    private Map<String, Object> feature;
    @ApiModelProperty(value = "实体仓编码")
    private String warehouseCode;

    /**
     * 来源
     */
    private String source;

    @ApiModelProperty(value = "订单标记")
    private Integer skuTag;
    @ApiModelProperty(value = "是否预包商品")
    private String isPre;
    /**
     * 记录操作日志需要的信息
     */
    private GoodsOperationLogParam operationLogParam;
    @ApiModelProperty(value = "是否为效期品")
    private GoodsOpenPeriodValidity openPeriodValidity;

    private boolean syncData=false;
    private String fromSource;

    private String wmsMaterialCode;


}
