package com.danding.business.client.ares.report.param;

import com.danding.business.common.ares.enums.report.BusinessWithType;
import com.danding.business.common.ares.enums.report.SettlementType;
import com.danding.component.common.api.common.file.FileDto;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 用户结算单
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-17
 */
@Data
public class UserSettlementParam implements Serializable {

    private static final long serialVersionUID = -6459248347725255766L;
    /**
     * 编辑时传入：结算单ID
     */
    private Long id;

    /**
     * 结算类型：1收入/2支出
     */
    @NotNull(message = "结算类型不能为空！1收入/2支出")
    private SettlementType settlementType;

    /**
     * 结算对象类型：1供应商/2客户/3其他
     */
    @NotNull(message = "结算对象类型不能为空！1供应商/2客户/3其他")
    private BusinessWithType settlementWithType;

    /**
     * 结算对象编码
     */
    @NotBlank(message = "结算对象编码不能为空！")
    private String settlementWithCode;

    /**
     * 结算对象名称
     */
    @NotBlank(message = "结算对象名称不能为空！")
    private String settlementWithName;

    /**
     * 结算方式ID（银行账户ID）
     */
    @NotNull(message = "银行账户ID不能为空！")
    private Long settlementMethod;

    /**
     * 开户行名称
     */
    @NotBlank(message = "开户行名称不能为空！")
    private String settlementMethodName;

    /**
     * 结算账户
     */
    @NotBlank(message = "结算账户不能为空！")
    private String settlementAccount;

    /**
     * 结算金额
     */
    @NotNull(message = "结算金额称不能为空！")
    @Min(value = 0, message = "结算金额不能为负数！")
    private BigDecimal settlementAmount;

    /**
     * 手续费
     */
    @Min(value = 0, message = "手续费不能为负数！")
    private BigDecimal settlementFee;

    /**
     * 货币类型
     */
    @NotBlank(message = "货币类型不能为空！")
    private String billCurrency;

    private BigDecimal currencyRate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件，使用json存储附件地址url
     */
    private List<FileDto> annex;
}
