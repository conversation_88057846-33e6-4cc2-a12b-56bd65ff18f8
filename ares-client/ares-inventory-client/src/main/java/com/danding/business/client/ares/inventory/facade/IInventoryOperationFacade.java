package com.danding.business.client.ares.inventory.facade;

import com.danding.business.client.ares.inventory.message.InventoryProcessMessage;
import com.danding.business.client.ares.inventory.param.InventoryGoodsInfoUpdateParam;
import com.danding.business.client.ares.inventory.param.InventoryModelParam;
import com.danding.soul.client.common.result.RpcResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/23 13:38
 */

@Validated
public interface IInventoryOperationFacade {

    /**
     * 带幂等操作的库存操作：批量处理库存数据，事务模式
     *
     * @param inventoryModelParamList
     * @param message
     * @return
     */
    RpcResult optInventoryByList(List<InventoryModelParam> inventoryModelParamList, InventoryProcessMessage message);

    /**
     * 带幂等操作的库存操作
     *
     * @param inventoryModelParam
     * @param message
     * @return
     */
    RpcResult optInventory(@Valid InventoryModelParam inventoryModelParam, InventoryProcessMessage message);

    /**
     * 供外部模块调用的操作库存接口(批量)
     *
     * @param inventoryModelParam
     * @return
     */
    @Deprecated
    RpcResult optBatchGoodsInventory(@Valid InventoryModelParam inventoryModelParam);

    /**
     * 货品基础信息变更后调用修改库存冗余字段
     * @param updateParam
     * @return
     */
    RpcResult updateInventoryGoodsInfo(@Valid InventoryGoodsInfoUpdateParam updateParam);
}
