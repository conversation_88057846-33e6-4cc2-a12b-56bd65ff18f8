package com.danding.business.client.ares.order.result;


import com.danding.component.canal.annotation.CanalEnableTenant;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 出库单主表返回结果(不带任何枚举类型)
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-17
 */

@Data
@CanalEnableTenant(fieldName = "tenantId")
@ApiModel(value = "OutOrder对象", description = "出库单主表返回结果")
public class NativeOutOrderResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 出库单号
     */
    private String outOrderNo;

    /**
     * 关联单号
     */
    private String businessNo;

    /**
     * 下游单号
     */
    private String downstreamNo;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 店铺名称(如  某某旗舰店)
     */
    private String shopName;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 实际出库时间
     */
    private Long actualTime;

    /**
     * 预计入库时间
     */
    private Long expectTime;

    /**
     * 计划入库数量
     */
    private Integer planQuantity;

    /**
     * 实际入库数量
     */
    private Integer actualQuantity;

    /**
     * 出库类型
     */
    private Integer type;

    /**
     * 逻辑仓编码
     */
    private String logicWarehouseCode;

    /**
     * 逻辑仓类型 1 虚拟, 2 实体
     */
    private Integer logicWarehouseType;

    /**
     * 出库单状态描述
     */
    private Integer status;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 审批状态
     */
    private Integer approvalStatus;

    /**
     * 驳回原因
     */
    private String operatorRemark;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 备注
     */
    private String remark;

    /**
     * 内部备注
     */
    private String internalRemark;

    /**
     * 下发状态
     */
    private Integer pushStatus;

    /**
     * 是否需要下发 1 是,其他否
     */
    private Integer isPush;

    /**
     * 推送回执
     */
    private String pushReturn;

    /**
     * 来源系统
     */
    private String origSystem;

    /**
     * 贸易类型-保税/完税
     */
    private Integer tradeType;

    /**
     * 创建人用户名
     */
    private String createByName;
    private Long createBy;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 出库单sku列表
     */
    private List<OutOrderDetailResult> detailResultList;

    /**
     * 经销商编码
     */
    private String dealerCode;

    /**
     * 经销商名称
     */
    private String dealerName;

    /**
     * 实体仓编码
     */
    private String warehouseCode;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 1 正品, 2 次品
     */
    private Integer inventoryType;

    /**
     * 是否自动拆分 1 是, 其他否
     */
    private Integer isAutoSplit;

    /**
     * 是否勾选批次 1 是, 0 否
     */
    private Integer isCheckBatch;

    /**
     * 逻辑仓是否开启批次 1 是, 0 否
     */
    private Integer isBatch;

    /**
     * 地址异常状态： 0:正常 1:异常
     */
    private Integer addressStatus;

    /**
     * 库存回传状态，1待回传，10回传成功，20有异常
     */
    private Integer inventoryStatus;

    /**
     * 上游锁库存状态 1 库存锁定中, 10 锁定成功, 20 失败带重试
     */
    private Integer lockInventoryStatus;

    /**
     * 上游回传状态，backStatus，如果isBack为2，无需回传，为1的时候，判断backStatus，1待回传，10回传成功，20失败
     */
    private Integer backStatus;

    /**
     * 回传上游回执
     */
    private String backReturn;

    /**
     * 上游单号
     */
    private String upstreamNo;

    /**
     * 原始订单号
     */
    private String origOrderNo;

    /**
     * 是否异常 0:正常 1:异常
     */
    private Integer exceptionStatus;
    /**
     * 异常信息
     */
    private String exceptionMsg;

    /**
     * 库存操作版本号
     */
    private Integer inventoryVersion;

    /**
     * 下单时间
     */
    private Long addTime;

    /**
     * 库存分组编码
     */
    private String channelCode;

    /**
     * 库存分组编码
     */
    private String toChannelCode;

    /**
     * 调拨类型
     */
    private Integer transferOrderType;

    /**
     * 质押状态：默认 0， 1 质押待审核， 10 审核成功， 20 审核失败
     */
    private Integer pledgeStatus;

    /**
     * 默认 0, 质押审核失败后，版本号 +1
     */
    private Long orderNoVersion;

    /**
     * 默认 0, 撤销时，版本号 +1
     */
    private Long repealVersion;

    /**
     * 货品状态: 0 现货, 1 期货
     */
    private Integer spotStatus;

    /**
     * 最后发货时间
     */
    private Long lastSendTime;

    /**
     * 0 未加密, 1 奇门隐私加密
     */
    private Integer secureType;

    /**
     * 平台编码
     */
    private String sourcePlatform;

    /**
     * 是否能够编辑地址
     */
    private Boolean editAddressFlg;

    /**
     * 是否可用解密地址
     */
    private Boolean addressDecode;

    /**
     * 锁单重推
     */
    private Integer pushLockStatus;

    /**
     * 是否已回传接单轨迹 0 否， 1 是
     */
    private Integer receiveTraceStatus;

    /**
     * 是否已回传取消轨迹 0 否， 1 是
     */
    private Integer cancelTraceStatus;


    private Long tenantId;
    /**
     * json扩展字段
     */
    private String extensionJson;

}
