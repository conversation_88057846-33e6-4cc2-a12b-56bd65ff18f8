package com.danding.business.client.ares.order.result;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 出库单主表返回结果
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-12
 */

@Data
@ApiModel(value = "OutOrderApi对象", description = "出库单主表返回结果")
public class OutOrderApiV2Result implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 出库单号
     */
    private String outOrderNo;
    /**
     * 原始订单号
     */
    private String origOrderNo;
    /**
     * 关联订单号
     */
    private String businessNo;
    /**
     * 货主编码
     */
    private String ownerCode;
    /**
     * 实际出库时间
     */
    private Long actualTime;
    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 店铺名称
     */
    private String shopName;


    /**
     * 运费
     */
    private BigDecimal postage;

    /**
     * 店铺优惠折扣
     */
    private BigDecimal sellerDiscount;

    /**
     * 收货方联系人
     */
    private String receiveContactName;

    /**
     * 收货方联系电话
     */
    private String receiveContactPhone;
    /**
     * 收货省
     */
    private String receiveProvince;

    /**
     * 收货市
     */
    private String receiveCity;

    /**
     * 收货区
     */
    private String receiveDistrict;
    /**
     * 收货地址
     */
    private String receiveAddress;
    /**
     * 收货邮编
     */
    private String receiveZipCode;
    /**
     * 下单时间
     */
    private Long addTime;
    /**
     * 支付时间
     */
    private Long payTime;
    /**
     * 总价
     */
    private BigDecimal totalPrice;
    /**
     * 物流编码
     */
    private String logisticsCompanyCode;
    /**
     * 物流名称
     */
    private String logisticsCompanyName;
    /**
     * 物流单号
     */
    private String logisticsNo;
    /**
     * 贸易类型-保税/完税
     */
    private Integer tradeType;
    private String tradeTypeDesc;

    /**
     * 出库类型
     */
    private Integer type;
    private String typeDesc;

    /**
     * 出库状态
     */
    private Integer status;
    private String statusDesc;
    /**
     * 明细
     */
    private List<OutOrderApiDetailResult> detailList;

    /**
     * 订单综合税
     */
    private BigDecimal orderTotalTax;

    /**
     * 订单优惠
     */
    private BigDecimal orderCoupon;

    /**
     * 内部店铺编码
     */
    private String internalShopCode;

    /**
     * 内部店铺名称
     */
    private String internalShopName;

    /**
     * 平台编码
     */
    private String sourcePlatform;

}
