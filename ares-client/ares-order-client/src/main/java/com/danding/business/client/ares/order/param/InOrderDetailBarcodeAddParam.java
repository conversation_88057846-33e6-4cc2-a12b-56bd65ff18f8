package com.danding.business.client.ares.order.param;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 添加
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */

@Data
@ApiModel(value="InOrderDetailBarcode对象", description="添加")
public class InOrderDetailBarcodeAddParam implements Serializable {


    private static final long serialVersionUID = 1L;
    private Long id;
    /**
    * 创建时间
    */
    private Long createTime;
    /**
    * 更新时间
    */
    private Long updateTime;
    /**
    * 创建人
    */
    private Long createBy;
    /**
    * 更新人
    */
    private Long updateBy;
    /**
    * 乐观锁版本号
    */
    private Long version;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String ownerCode;

    /**
     * 分组编码
     */
    @ApiModelProperty(value = "分组编码")
    private String channelCode;

    /**
     * 条形码
     */
    @ApiModelProperty(value = "条形码")
    private String barcode;

    /**
     * 外部sku
     */
    @ApiModelProperty(value = "外部sku")
    private String sku;

    /**
     * 实体仓库编码
     */
    @ApiModelProperty(value = "实体仓库编码")
    private String warehouseCode;

    /**
     * 分表规则时间
     */
    @ApiModelProperty(value = "分表规则时间")
    private Long tableTime;


}
