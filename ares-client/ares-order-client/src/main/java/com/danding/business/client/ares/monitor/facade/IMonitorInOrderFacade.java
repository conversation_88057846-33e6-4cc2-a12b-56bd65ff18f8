package com.danding.business.client.ares.monitor.facade;

import com.danding.business.client.ares.monitor.param.MonitorInOrderAddParam;
import com.danding.business.client.ares.monitor.param.MonitorInOrderQueryParam;
import com.danding.business.client.ares.monitor.result.MonitorInOrderResult;
import com.danding.component.common.api.common.response.ListVO;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 入库单监控表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-12
 */
public interface IMonitorInOrderFacade {

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    MonitorInOrderResult getById(Serializable id);

    /**
     * 条件查询单个
     *
     * @param monitorInOrderQueryParam
     * @return
     */
    MonitorInOrderResult getByQueryParam(MonitorInOrderQueryParam monitorInOrderQueryParam);

    /**
     * 条件查询list
     *
     * @param monitorInOrderQueryParam
     * @return
     */
    List<MonitorInOrderResult> listByQueryParam(MonitorInOrderQueryParam monitorInOrderQueryParam);

    /**
     * 条件分页查询
     *
     * @param monitorInOrderQueryParam
     * @return
     */
    ListVO<MonitorInOrderResult> pageListByQueryParam(MonitorInOrderQueryParam monitorInOrderQueryParam);

    /**
     * 插入
     *
     * @param monitorInOrderAddParam
     * @return
     */
    boolean add(MonitorInOrderAddParam monitorInOrderAddParam);

    /**
     * 批量插入
     *
     * @param monitorInOrderAddParamList
     * @return
     */
    boolean addList(List<MonitorInOrderAddParam> monitorInOrderAddParamList);

    /**
     * 根据主键id修改
     *
     * @param monitorInOrderAddParam
     * @return
     */
    boolean updateById(MonitorInOrderAddParam monitorInOrderAddParam);

    /**
     * 根据主键id批量修改
     *
     * @param monitorInOrderAddParamList
     * @return
     */
    boolean updateListById(List<MonitorInOrderAddParam> monitorInOrderAddParamList);

    /**
     * 根据条件修改
     *
     * @param monitorInOrderQueryParam
     * @param monitorInOrderAddParam
     * @return
     */
    boolean updateListByQueryParam(MonitorInOrderQueryParam monitorInOrderQueryParam, MonitorInOrderAddParam monitorInOrderAddParam);

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    boolean removeById(Serializable id);

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    boolean removeByIds(List<Long> idList);

    /**
     * 根据条件删除
     *
     * @param monitorInOrderQueryParam
     * @return
     */
    boolean removeByQueryParam(MonitorInOrderQueryParam monitorInOrderQueryParam);

    /**
     * 更新入库单监控
     *
     * @param inOrderNo
     * @param userId
     */
    void refreshMonitorInOrder(String inOrderNo, Long userId);

    /**
     * 入库单监控节点超时计算
     *
     * @param inOrderNo
     * @param userId
     */
    void monitorInOrderTimeOutCalc(String inOrderNo, Long userId);

    /**
     * 入库单监控节点超时计算
     *
     * @param inOrderNo
     * @param userId
     */
    void monitorInOrderTimeOutCalcNotSendMsg(String inOrderNo, Long userId);

    /**
     * 入库单监控节点更新
     *
     * @param inOrderNo
     * @param userId
     * @param startDate
     * @param endDate
     */
    void monitorInOrderTimeOutRefresh(String inOrderNo, Long userId, Long startDate, Long endDate);

    /**
     * 入库单监控节点更新
     *
     * @param entityWarehouseCode
     */
    void monitorInOrderTimeOutRefresh(String entityWarehouseCode);

    /**
     * 查询个数
     *
     * @param monitorInOrderQueryParam
     * @return
     */
    MonitorInOrderResult monitorInOrderCount(MonitorInOrderQueryParam monitorInOrderQueryParam);
}
