package com.danding.business.rpc.client.ares.order.facade;

import com.danding.business.rpc.client.ares.order.param.CancelOrderRpcParam;
import com.danding.business.rpc.client.ares.order.param.OutOrderAddRpcParam;
import com.danding.business.rpc.client.ares.order.param.OutOrderApiQueryRpcParam;
import com.danding.business.rpc.client.ares.order.param.OutOrderRpcAddParam;
import com.danding.business.rpc.client.ares.order.result.OutOrderRpcResult;
import com.danding.soul.client.common.result.RpcResult;

import java.util.List;
import java.util.Set;

/**
 * 描述:
 *     出库单
 * <AUTHOR>
 * @date 2020/11/30 下午3:25
 */
public interface IOutOrderRpcFacade {

    /**
     * 新增出库单
     * @param outOrderRpcAddParam
     * @return
     */
    RpcResult<String> addUpToOutOrder(OutOrderRpcAddParam outOrderRpcAddParam);

    /**
     * 取消出库订单
     * @param cancelOrderRpcParam
     * @return
     */
    RpcResult cancelOutOrder(CancelOrderRpcParam cancelOrderRpcParam);

    /**
     * 查询出库单及详情
     * @param orderNo
     * @param userId
     * @return
     */
    RpcResult getOutOrderByNo(String orderNo, Long userId);

    /**
     * 查询出库单列表
     *
     * @param outOrderApiQueryRpcParam
     * @return
     */
    RpcResult apiOutOrderList(OutOrderApiQueryRpcParam outOrderApiQueryRpcParam);

    /**
     * 查询 4pl 和 3pl 关系
     *      如果没有关系,默认值 0
     *      如果有关系, 3pl云仓-4pl云仓
     * @return
     */
    RpcResult<String> getLogicalWarehouseInfo();

    /**
     * 新增出库单(内部系统使用)
     * @param addParam
     * @return
     */
    String addOutOrder(OutOrderAddRpcParam addParam);

    /**
     * 查询出库单
     * @param orderNo
     * @return
     */
    RpcResult getOutOrderByNo(String orderNo);

    /**
     * 通过上游单号查询下游单号
     * @param upstreamNo
     * @return
     */
    RpcResult getDownstreamNo(String upstreamNo);

    /**
     * 检测上游单据是否存在
     * @param gsOrderList
     * @return
     */
    RpcResult<List<String>> checkOutOrder(List<String> gsOrderList, Integer pageSize);

    /**
     * 查询包裹信息
     *
     * @param outOrderNoSet
     * @return
     */
    RpcResult<List<OutOrderRpcResult>> listOutOrderLogistics(Set<String> outOrderNoSet);

}
