package com.danding.business.rpc.client.ares.configuration.param;

import com.danding.business.rpc.client.ares.configuration.enums.IsConfirmType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 描述:
 *     出库单回传
 * <AUTHOR>
 * @date 2020/11/19 上午9:52
 */
@Data
public class OutOrderBackMappingParam implements Serializable {

    private static final long serialVersionUID = -3196368534094071032L;

    /**
     * 下游单号
     */
    private String downstreamNo;

    /**
     * 出库单号
     */
    private String outOrderNo;

    /**
     * 实体仓编码
     */
    private String warehouseCode;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 操作日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    private Date operationTime;

    /**
     * 实际出库时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    private Date actualDate;

    /**
     * 多次确认 0最终状态确认 1中间状态确认
     * {@link IsConfirmType}
     */
    private Integer isConfirm;

    /**
     * 物流公司编码
     */
    private String logisticsCompanyCode;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 外部单号
     */
    private String externalNo;

    /**
     * json扩展字段
     */
    private String extensionJson;

    /**
     * 多包裹列表
     */
    private List<LogisticsBackMappingParam> logisticsParamList;

}
