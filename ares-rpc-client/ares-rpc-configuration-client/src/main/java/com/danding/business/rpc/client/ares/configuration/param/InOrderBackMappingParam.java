package com.danding.business.rpc.client.ares.configuration.param;

import com.danding.business.rpc.client.ares.configuration.enums.IsConfirmType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 描述:
 *    入库单回传
 * <AUTHOR>
 * @date 2020/11/19 上午9:52
 */
@Data
public class InOrderBackMappingParam implements Serializable {

    private static final long serialVersionUID = -5747346147025725142L;

    /**
     * 下游单号
     */
    private String downstreamNo;

    /**
     * 入库单号
     */
    private String inOrderNo;

    /**
     * 实体仓编码
     */
    private String warehouseCode;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 操作日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    private Date operationTime;

    /**
     * 实际入库时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    private Date actualDate;

    /**
     * 多次确认 0最终状态确认 1中间状态确认
     * {@link IsConfirmType}
     */
    private Integer IsConfirm;

    /**
     * 回传唯一标记
     */
    private String backFlag;

    /**
     * 外部单号
     */
    private String externalNo;

    /**
     * json扩展字段
     */
    private String extensionJson;

    /**
     * 校验理货报告 1 : 是, 其他 : 否
     */
    private Integer isCheckTally;

    /**
     * sku列表
     */
    private List<ItemSkuBackMappingParam> skuParamList;

}
