<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>business-client-parent</artifactId>
        <groupId>com.danding</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>${revision}</version>
    <artifactId>ares-rpc-client</artifactId>
    <packaging>pom</packaging>
    <properties>
        <revision>3.1.19-SNAPSHOT</revision>
    </properties>
    <modules>
        <module>ares-rpc-trade-client</module>
        <module>ares-rpc-configuration-client</module>
        <module>ares-report-rpc-client</module>
        <module>ares-inventory-rpc-client</module>
        <module>ares-order-rpc-client</module>
        <module>ares-goods-rpc-client</module>
        <module>ares-goods-center-rpc-client</module>
        <module>ares-config-rpc-client</module>
        <module>ares-risk-rpc-client</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-common</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>api-common</artifactId>
            <version>2.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>utils-common</artifactId>
            <version>2.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <version>2.5.5</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.2.5.RELEASE</version>
                <configuration>
                    <skip>true</skip>
                    <finalName>${project.name}</finalName>

                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.5.0</version>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
                <inherited>true</inherited>
                <configuration>
                    <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Implementation-Version>${project.version}</Implementation-Version>
                            <Specification-Version>${project.version}</Specification-Version>
                            <Implementation-Vendor>${project.groupId}</Implementation-Vendor>
                            <Implementation-Title>${project.artifactId}</Implementation-Title>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>danding</id>
            <name>danding</name>
            <url>http://mvn.yang800.cn/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
