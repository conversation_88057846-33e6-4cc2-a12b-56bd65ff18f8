package com.danding.business.client.rpc.goods.facade;

import com.danding.business.client.rpc.goods.result.GoodsManagementRpcResult;

import java.util.List;

/**
 * <p>
 * 货品 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-30
 */
public interface IGoodsManagementRpcFacade {

    /**
     * 单个查询
     * @param sku
     * @param ownerCode
     * @return
     */
    List<GoodsManagementRpcResult> queryRpcGoodsBy(String sku, String ownerCode);

    /**
     * 集合查询
     *
     * @param skuList
     * @param ownerCodeList
     * @return
     */
    List<GoodsManagementRpcResult> listRpcGoodsBy(List<String> skuList, List<String> ownerCodeList);


    /**
     * 同步逆向货主货品信息
     * @param ownerCode
     * @param skuList
     */
    void syncReturnOwnerGoods(String ownerCode, List<String> skuList);

}
