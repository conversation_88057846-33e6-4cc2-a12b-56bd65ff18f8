package com.danding.business.client.rpc.goods.facade;

import com.danding.business.client.rpc.goods.param.GoodsRecordRpcDeleteParam;
import com.danding.business.client.rpc.goods.result.GoodsPortRpcResult;
import com.danding.business.client.rpc.goods.result.GoodsRecordCssV1RpcResult;
import com.danding.business.client.rpc.goods.result.GoodsRecordRpcResult;
import com.danding.business.client.rpc.goods.result.SimpleGoodsRecordRpcResult;
import com.danding.business.common.ares.enums.goods.RecordSource;
import com.danding.business.common.ares.enums.goods.RecordStatus;
import com.danding.soul.client.common.result.RpcResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 货品备案 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-14
 */
@Validated
public interface IGoodsRecordRpcFacade {

    /**
     * 删除货品备案信息
     * 数据删除-无需原因-因为无法查看删除的数据
     *
     * @param deleteParam
     * @return
     */
    RpcResult deleteGoodsRecord(@Valid GoodsRecordRpcDeleteParam deleteParam);

    /**
     * 查询备案信息
     *
     *
     * @param goodsCode
     * @param userId
     * @param recordStatus
     * @return
     */
    List<GoodsRecordRpcResult> listGoodsRecordByGoodsCodeList(List<String> goodsCode, Long userId, RecordStatus recordStatus);

    /**
     * 根据货品ID和口岸编码获取备案缓存数据
     *
     * @param goodsCode
     * @param port
     * @return
     */
    GoodsRecordRpcResult getGoodsRecordCache(String goodsCode, String port);

    /**
     * 更新备案缓存数据
     *中心化后不在需要
     * @param goodsCode
     * @param port
     * @param goodsRecordRpcResult
     * @return
     */
   @Deprecated
    boolean updateGoodsRecordCache(String goodsCode, String port, GoodsRecordRpcResult goodsRecordRpcResult);

    /**
     * 查询备案
     * @param userId
     * @param goodsCode
     * @param logicWarehouseCode
     * @return
     */
    GoodsRecordRpcResult getByGoodsCodeAndLogicWarehouseCode(Long userId, String goodsCode, String logicWarehouseCode);

    /**
     * 口岸+用户id+条码+逻辑仓库编码维度下，货品报备与检查。
     * 返回检查 返回已成功申报的货品口岸
     *
     * @param list
     * @return
     */
    List<GoodsRecordRpcResult> checkAndReportGoodsRecord(List<GoodsRecordRpcResult> list);
    /**
     * 口岸+用户id+条码维度下，对用户Id下所有逻辑仓库编报备，返回已报备的记录。
     * 返回检查 返回已成功申报的货品口岸
     *
     * @param goodsRecordRpcResult
     * @param source  ZJ，CN
     * @return
     */
    List<GoodsRecordRpcResult> checkAndReportGoodsRecord(GoodsRecordRpcResult goodsRecordRpcResult, RecordSource source);
    List<GoodsRecordCssV1RpcResult> checkAndReportGoodsRecord(GoodsRecordCssV1RpcResult goodsRecordRpcResult, RecordSource source);
    /**
     * 口岸+货品id维护下，修改货品审批记录，注意！！只做数据的更新，无其他业务逻辑处理。只用于数据同步
     * 中心化后不在需要
     * @param goodsRecordRpcResult
     * @return
     */
   @Deprecated
    RpcResult updateGoodsRecord(GoodsRecordRpcResult goodsRecordRpcResult);

    /**
     * 查询备案简单数据，料号等
     * @param ownerCode
     * @param sku
     * @param warehouseCode
     * @return
     */

    SimpleGoodsRecordRpcResult getBySkuAndWarehouseCode(String ownerCode,String sku, String warehouseCode);


    public GoodsPortRpcResult getRpcGoodsPortByParam(Long userId, String goodsCode) ;




}
