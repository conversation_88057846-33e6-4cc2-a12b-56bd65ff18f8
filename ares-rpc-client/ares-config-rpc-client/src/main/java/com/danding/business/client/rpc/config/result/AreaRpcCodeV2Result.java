package com.danding.business.client.rpc.config.result;

import lombok.Data;

import java.io.Serializable;

@Data
public class AreaRpcCodeV2Result implements Serializable {

    private static final long serialVersionUID = 7655087957848720692L;
    private String provinceCode;
    private String cityCode;
    private String zoneCode;
    private String provinceName;
    private String cityName;
    private String zoneName;

    private String largeAreaName;
    private String largeAreaCode;

}
