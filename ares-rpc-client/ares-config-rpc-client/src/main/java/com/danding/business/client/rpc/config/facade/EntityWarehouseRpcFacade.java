package com.danding.business.client.rpc.config.facade;

import com.danding.business.client.rpc.config.param.EntityWarehouseRpcParam;
import com.danding.business.client.rpc.config.result.EntityWarehouseRpcResult;
import com.danding.business.client.rpc.config.result.FinanceWarehouseRpcResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface EntityWarehouseRpcFacade {

    /**
     * 获取实体仓信息
     *
     * @param entityCode
     * @return
     */
    EntityWarehouseRpcResult getOneByEntityCode(String entityCode);

    /**
     * 获取实体仓列表
     *
     * @param entityCode 非必填
     * @param userId     非必填
     * @return
     */
    List<EntityWarehouseRpcResult> getEntityWarehouseList(String entityCode, Long userId);

    /**
     * 获取实体仓列表
     *
     * @param entityWarehouseRpcParam
     * @return
     */
    List<EntityWarehouseRpcResult> getAllEntityWarehouseList(EntityWarehouseRpcParam entityWarehouseRpcParam);

    /**
     * 根据erp实体仓编码更新账册号
     *
     * @param entityCode
     * @param accountCode
     * @return
     */
    boolean updateEntityWarehouseAccountCode(String entityCode, String accountCode);

    /**
     * 获取实体仓信息
     *
     * @param warehouseCode
     * @return
     */
    EntityWarehouseRpcResult getWarehouseByCode(String warehouseCode);

    /**
     * 查询金融监管仓
     * @return
     */
    List<EntityWarehouseRpcResult> getFinanceWarehouseList(Long userId);

    /**
     * 查询金融监管仓(含所有云仓)
     * @return
     */
    List<FinanceWarehouseRpcResult> getFinanceLogicWarehouseList(Long userId, Integer financeModeType);

    /**
     * 查询非金融云仓
     * @return
     */
    List<FinanceWarehouseRpcResult> getNoFinanceLogicWarehouseList(Long userId);

    /**
     * 根据erp实体仓编码更新账册号和口岸
     *
     * @param entityCode
     * @param accountCode
     * @return
     */
    boolean updateEntityWarehouseAccountCode(String entityCode, String accountCode, String port);

    /**
     * 退货仓
     * @return
     */
    Map<String, String> returnWarehouseCode();

    /**
     * 退货仓查询实体仓
     * @param returnCode
     * @return
     */
    Map<String, String> listWarehouseByReturn(String returnCode);

    /**
     * 实体仓查询退货仓
     * @return
     */
    String getReturnWarehouseBy(String warehouseCode);

}
