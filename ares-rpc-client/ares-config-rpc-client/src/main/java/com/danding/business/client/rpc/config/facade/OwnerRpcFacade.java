package com.danding.business.client.rpc.config.facade;

import com.danding.business.client.rpc.config.param.OwnerAddRpcParam;
import com.danding.business.client.rpc.config.param.OwnerCrmRpcParam;
import com.danding.business.client.rpc.config.param.OwnerRpcParam;
import com.danding.business.client.rpc.config.result.OwnerRpcResult;
import com.danding.business.client.rpc.config.result.WarehouseInfoResult;
import com.danding.business.common.ares.enums.common.IsTallyType;
import com.danding.soul.client.common.result.RpcResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
public interface OwnerRpcFacade {

    /**
     * 获取实体仓信息
     *
     * @param ownerCode
     * @return
     */
    OwnerRpcResult getOneByOwnerCode(String ownerCode);


    /**
     * 更新货主是否开启理货报告
     * @param ownerCode
     * @param isTallyType
     * @return
     */
    Boolean updateOwnerIsTally(String ownerCode, IsTallyType isTallyType);

    /**
     * 获取货主信息
     *
     * @param userId
     * @return
     */
    List<OwnerRpcResult> litOwnerByUserId(Long userId);

    /**
     * 获取货主信息
     *
     * @param ownerRpcParam
     * @return
     */
    List<OwnerRpcResult> listOwnerByParam(OwnerRpcParam ownerRpcParam);

    /**
     * 获取货主信息
     *
     * @param ownerRpcParam
     * @return
     */
    List<OwnerRpcResult> listOwnerByParamForCrm(OwnerCrmRpcParam ownerRpcParam);

    /**
     * 新增/更新货主
     *
     * @param ownerAddRpcParam
     * @return
     */
    Boolean addOwner(@Valid OwnerAddRpcParam ownerAddRpcParam);

    /**
     * 查询仓库货主信息
     *
     * @param ownerCode
     * @return
     */
    RpcResult<WarehouseInfoResult> getUserIdByCode(String ownerCode);

    String getTaoTianSalePlatformNameByOwner(String ownerCode);

    /**
     * 根据货主查询退货货主
     *
     * @param ownerCode
     * @return
     */
    OwnerRpcResult getReturnOwnerByCode(String ownerCode);

}
