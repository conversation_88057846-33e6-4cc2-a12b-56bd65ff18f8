from registry.cn-shanghai.aliyuncs.com/danding/corp:openjdk-8-agent-1.2
VOLUME /home/<USER>
RUN curl -Os https://arthas.aliyun.com/arthas-boot.jar
ADD target/ares-inventory-server.jar ares-inventory-server.jar
EXPOSE 8098
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai/etc/localtime &&  \
echo "Asia/Shanghai" > /etc/timezone
ENV JAVA_OPTS="$JAVA_OPTS -Dfile.encoding=UTF8 -Duser.timezone=GMT+08"
ENTRYPOINT exec java $JAVA_OPTS -Djava.security.edg=file:/dev/./urandom -jar /ares-inventory-server.jar