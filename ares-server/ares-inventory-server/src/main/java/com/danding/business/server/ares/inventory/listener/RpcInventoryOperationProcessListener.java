package com.danding.business.server.ares.inventory.listener;

import com.alibaba.fastjson.JSON;
import com.danding.business.rpc.client.oms.order.facade.IContactRpcFacade;
import com.danding.business.server.ares.inventory.BO.GoodsInventoryOperationBO;
import com.danding.business.server.ares.inventory.BO.GoodsInventoryOperationMessageBO;
import com.danding.business.server.ares.inventory.config.InventoryNacosConfig;
import com.danding.business.server.ares.inventory.facade.rpc.GoodsInventoryOperationManager;
import com.danding.component.rocketmq.consume.PushMQConsumer;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Rpc 异步库存消息处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/30 9:22
 */
@Service
@Slf4j
@RocketMQMessageListener(topic = "ares-rpc-inventory-operation-topic", consumerGroup = "consumer-group-prc-inventory-process", consumeMode = ConsumeMode.ORDERLY)
public class RpcInventoryOperationProcessListener extends PushMQConsumer<MessageExt> {

    @Autowired
    private GoodsInventoryOperationManager goodsInventoryOperationManager;
    @DubboReference
    IContactRpcFacade contactRpcFacade;
    @Autowired
    private InventoryNacosConfig inventoryNacosConfig;

    @Override
    public void handleMessage(MessageExt message) throws RuntimeException {
        GoodsInventoryOperationMessageBO goodsInventoryOperationMessageBO = JSON.parseObject(message.getBody(), GoodsInventoryOperationMessageBO.class);
        log.debug("======RpcInventoryOperationProcessListener.onMessage=========== message={}", JSON.toJSONString(goodsInventoryOperationMessageBO));
        log.info("RpcInventoryOperationProcessListener.onMessage UpstreamNo:{},reconsumeTimes:{}", goodsInventoryOperationMessageBO.getUpstreamNo(), message.getReconsumeTimes());
        if (inventoryNacosConfig.isSkipInventoryLock()) {
            log.debug("======RpcInventoryOperationProcessListener== skipProcess");
            return;
        }
        //用户测试mock
        int messageWait = inventoryNacosConfig.getMessageWait();
        if (messageWait > 0) {
            try {
                Thread.sleep(messageWait * 1000);
            } catch (Throwable e) {

            }
        }
        GoodsInventoryOperationBO operationBO =JSON.parseObject(message.getBody(), GoodsInventoryOperationBO.class);
        try {
            goodsInventoryOperationManager.goodsInventoryLock(operationBO);
        } catch (BusinessException e) {
            //
            if (message.getReconsumeTimes() >= inventoryNacosConfig.getMaxReconsumeTimes()) {
                String error = String.format("库存操作失败 业务单号%s", operationBO.getUpstreamNo());
                contact("INVENTORY_OPERATION_EXCEPTION", operationBO.getUpstreamNo(), error);
                goodsInventoryOperationManager.sendCallBackErrMessage(e, operationBO);
                return;
            }
            if (inventoryNacosConfig.isShowReconsumeTimes()) {
                String error = String.format("库存操作失败重试次数%s 业务单号%s", message.getReconsumeTimes() + 1, operationBO.getUpstreamNo());
                contact("INVENTORY_OPERATION_EXCEPTION", operationBO.getUpstreamNo(), error);
            }
            throw e;
        } catch (Exception e) {
            if (message.getReconsumeTimes() >= inventoryNacosConfig.getMaxReconsumeTimes()) {
                try {
                    String error = String.format("库存操作失败 业务单号%s", operationBO.getUpstreamNo());
                    contact("INVENTORY_OPERATION_EXCEPTION", operationBO.getUpstreamNo(), error);
                    goodsInventoryOperationManager.sendCallBackErrMessage(e, operationBO);
                }catch (Exception ee){
                    log.info("RpcInventoryOperationProcessListener callBack error  UpstreamNo:{}", operationBO.getUpstreamNo());
                }
                return;
            }
            if (inventoryNacosConfig.isShowReconsumeTimes()) {
                String error = String.format("库存操作失败重试次数%s 业务单号%s", message.getReconsumeTimes() + 1, operationBO.getUpstreamNo());
                contact("INVENTORY_OPERATION_EXCEPTION", operationBO.getUpstreamNo(), error);
            }
            throw e;
        }


    }

    public void contact(String code, String requestId, String message) {
        try {
            contactRpcFacade.contact(code, requestId, message);
        } catch (Throwable e) {
            log.warn("通知异常", e);
        }
    }
}
