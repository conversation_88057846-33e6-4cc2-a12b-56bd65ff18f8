package com.danding.business.server.ares.inventory.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 批次库存wms 依赖的结构
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/02/01 10:15
 */

@Data
public class GoodsBatchInventoryWsmExportExcel {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "货主名称")
    private String cargoName;
    @ExcelProperty(value = "货主编码")
    private String cargoCode;

    @ExcelProperty(value = "所属实体仓")
    private String warehouseName;

    @ExcelProperty(value = "货品ID")
    private String goodsId;

    @ExcelProperty(value = "货品名称")
    private String goodsName;

    @ExcelProperty(value = "SKU")
    private String skuCode;

    @ExcelProperty(value = "条形码")
    private String upcCode;

    @ExcelProperty(value = "批次ID")
    private String skuLotNo;




    @ExcelProperty(value = "在仓数")
    private BigDecimal physicalQty;
    @ExcelProperty(value = "可用数量")
    private BigDecimal availableQty;


    @ExcelProperty(value = "占用数量")
    private BigDecimal occupyQty;

    @ExcelProperty(value = "冻结数量")
    private BigDecimal frozenQty;


    @ExcelProperty(value = "商品属性")
    private String skuQuality;

    @ExcelProperty(value = "失效日期")
    private String expireDateDesc;

    @ExcelProperty(value = "生产日期")
    private String manufDateDesc;


    @ExcelProperty(value = "入库日期")
    private String receiveDateDesc;

    @ExcelProperty(value = "生产批次号")
    private String productionNo;

    @ExcelProperty(value = "禁售日期")
    private String withdrawDateDesc;

    @ExcelProperty(value = "外部批次号")
    private String externalSkuLotNo;

    @ExcelProperty(value = "效期状态")
    private String validityStatusDesc;
    @ExcelProperty(value = "入库关联号")
    private String externalLinkBillNo;
    @ExcelProperty(value = "贸易类型")
    private String goodsTypeDesc;
}
