package com.danding.business.server.ares.inventory.listener.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.entitywarehouse.result.EntityWarehouseResult;
import com.danding.business.client.ares.inventory.message.InventoryProcessMessage;
import com.danding.business.client.ares.inventory.param.InventoryModelItemsParam;
import com.danding.business.client.ares.inventory.param.InventoryModelParam;
import com.danding.business.client.ares.logicwarehouse.result.LogicWarehouseResult;
import com.danding.business.client.ares.order.facade.IOutOrderDetailFacade;
import com.danding.business.client.ares.order.facade.IOutOrderFacade;
import com.danding.business.client.ares.order.facade.IOutOrderLogisticsDetailFacade;
import com.danding.business.client.ares.order.result.OutOrderDetailResult;
import com.danding.business.client.ares.order.result.OutOrderLogisticsDetailResult;
import com.danding.business.client.ares.order.result.OutOrderResult;
import com.danding.business.common.ares.context.AresContext;
import com.danding.business.common.ares.enums.common.ApprovalStatus;
import com.danding.business.common.ares.enums.goods.SpecifyAttributesEnum;
import com.danding.business.common.ares.enums.inventory.BusinessBillType;
import com.danding.business.common.ares.enums.inventory.InventoryBusinessType;
import com.danding.business.common.ares.enums.inventory.InventoryType;
import com.danding.business.common.ares.enums.inventory.InventoryUpdateType;
import com.danding.business.common.ares.enums.order.*;
import com.danding.business.common.ares.utils.Assert;
import com.danding.business.core.ares.inventory.search.InventoryProcessLogSearch;
import com.danding.business.server.ares.inventory.BO.InventoryProcessLogBO;
import com.danding.business.server.ares.inventory.manager.DimensionInventoryManager;
import com.danding.business.server.ares.inventory.manager.InventoryProcessLogManager;
import com.danding.business.server.ares.inventory.remote.RemoteLogicWarehouseFacade;
import com.danding.business.server.ares.utils.InventoryRedisLockUtils;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.soul.client.common.exception.BusinessException;
import com.danding.soul.client.common.result.RpcResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.RedissonMultiLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.danding.business.common.ares.context.AresContext.ERP_CIRCLE_DIMENSION_START_TAG;
import static com.danding.business.common.ares.context.AresContext.SYSTEM_DT;

/**
 * 出库单库存业务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/30 10:50
 */
@Component
@Slf4j
public class OutStockInventoryHandler extends AbstractInventoryHandler<OutOrderResult> {


    @DubboReference
    private IOutOrderFacade outOrderFacade;
    @DubboReference
    private IOutOrderDetailFacade outOrderDetailFacade;
    @DubboReference
    private IOutOrderLogisticsDetailFacade outOrderLogisticsDetailFacade;
    @Autowired
    private RemoteLogicWarehouseFacade remoteLogicWarehouseFacade;
    @Autowired
    private InventoryProcessLogManager inventoryProcessLogManager;
    @Autowired
    private DimensionInventoryManager dimensionInventoryManager;

    @Autowired
    private CallCircleInventoryTransactional callCircleInventoryTransactional;
    @Autowired
    private InventoryRedisLockUtils redisLockUtils;
    @Override
    public List<InventoryModelParam> buildParameters(OutOrderResult outOrderResult, InventoryProcessMessage message) {
        List<InventoryModelParam> paramList = Lists.newArrayList();
        try {
            if (InventoryUpdateType.LOCKED_WHK.equals(message.getUpdateType())) {
                // 出库锁定
                InventoryModelParam inventoryModelParam = buildParam(outOrderResult, message);
                if (checkIsERPAutoSplit(outOrderResult)) {
                    inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.OUT_ORDER_CREATED_BATCH);
                    message.setInventoryBusinessType(InventoryBusinessType.OUT_ORDER_CREATED_BATCH);
                } else {
                    if (OutOrderType.XH_CK.getValue().equals(outOrderResult.getType())) {
                        inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.OUT_ORDER_CREATED);
                        message.setInventoryBusinessType(InventoryBusinessType.OUT_ORDER_CREATED);
                    } else if (OutOrderType.SH_CK.getValue().equals(outOrderResult.getType()) || OutOrderType.WY_CK.getValue().equals(outOrderResult.getType())) {
                        inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.REPLACE_BUY_ORDER_OUT_LOCKED);
                        inventoryModelParam.setUpdateType(InventoryUpdateType.REPLACE_FROZEN_OUT_LOCKED);
                        message.setInventoryBusinessType(InventoryBusinessType.REPLACE_BUY_ORDER_OUT_LOCKED);
                    } else {
                        inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.QD_LOCKED);
                        message.setInventoryBusinessType(InventoryBusinessType.QD_LOCKED);
                    }
                }
                List<InventoryModelParam> circleInventoryModelParamList = buildCircleParameters(inventoryModelParam, message.getUpdateType());
                log.info("buildCircleParameters--{}", JSON.toJSONString(circleInventoryModelParamList));
                //圈货出库单处理，按货品标记处理为二个队列
                if (CollectionUtils.isNotEmpty(circleInventoryModelParamList)) {
                    paramList.addAll(circleInventoryModelParamList);
                } else {
                    paramList.add(inventoryModelParam);
                }
            } else if (InventoryUpdateType.OUT_STOCK_HK.equals(message.getUpdateType())) {
                // 出库核扣
                InventoryModelParam inventoryModelParam = buildParam(outOrderResult, message);
                if (Objects.equals(OrderFlgStatus.CORNER, outOrderResult.getOrderFlg())
                        && Objects.equals(OutOrderType.XS_CK.getValue(), outOrderResult.getType())) {
                    // 综保A出库需要核减圈存库存数量-对应销售出库类型，T的C单还是用老逻辑
                    inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.CORNERING_RETURN_FINISHED);
                } else if (Objects.equals(1, outOrderResult.getCircleGoodsType())) {
                    //圈货核扣
                    inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.CIRCLE_ORDER_OUT_FINISHED);
                } else if (OutOrderType.SH_CK.getValue().equals(outOrderResult.getType()) || OutOrderType.WY_CK.getValue().equals(outOrderResult.getType())) {
                    inventoryModelParam.setUpdateType(InventoryUpdateType.REPLACE_ORDER_OUT_FINISHED);
                    inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.REPLACE_BUY_ORDER_OUT_FINISHED);
                } else {
                    inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.RETURN_FINISHED);
                }
                message.setInventoryBusinessType(InventoryBusinessType.RETURN_FINISHED);
                List<InventoryModelParam> circleInventoryModelParamList = buildCircleParameters(inventoryModelParam, message.getUpdateType());
                //圈货出库单处理，按货品标记处理为二个队列
                //核扣需同时处理 货品库存与圈货库存
                if (CollectionUtils.isNotEmpty(circleInventoryModelParamList)) {
                    paramList.addAll(circleInventoryModelParamList);
                } else {
                    paramList.add(inventoryModelParam);
                }

                //构建少回传的回滚库存；2023-11-20 说明 实际应用场景中无少货逻辑 宋卫锋 说明来源 李玲钰 许梦竹
                InventoryModelParam lessParam = buildLessParam(outOrderResult);
                if (Objects.nonNull(lessParam)) {
                    paramList.add(lessParam);
                }
            } else if (InventoryUpdateType.RELEASE_ADDED.equals(message.getUpdateType())) {
                // 释放锁定
                InventoryModelParam inventoryModelParam = buildParam(outOrderResult, message);
                if (InventoryBusinessType.OUT_ORDER_ERROR_ROLLBACK_BATCH.equals(message.getInventoryBusinessType())
                        || InventoryBusinessType.OUT_ORDER_ERROR_ROLLBACK.equals(message.getInventoryBusinessType())) {
                    //异常释放
                    if (checkIsERPAutoSplit(outOrderResult)) {
                        inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.OUT_ORDER_ERROR_ROLLBACK_BATCH);
                        message.setInventoryBusinessType(InventoryBusinessType.OUT_ORDER_ERROR_ROLLBACK_BATCH);
                    } else {
                        inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.OUT_ORDER_ERROR_ROLLBACK);
                        message.setInventoryBusinessType(InventoryBusinessType.OUT_ORDER_ERROR_ROLLBACK);
                    }
                } else if (InventoryBusinessType.OUT_ORDER_REJECTED_BATCH.equals(message.getInventoryBusinessType())
                        || InventoryBusinessType.OUT_ORDER_REJECTED.equals(message.getInventoryBusinessType())) {
                    //驳回释放
                    if (checkIsERPAutoSplit(outOrderResult)) {
                        inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.OUT_ORDER_REJECTED_BATCH);
                        message.setInventoryBusinessType(InventoryBusinessType.OUT_ORDER_REJECTED_BATCH);
                    } else {
                        inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.OUT_ORDER_REJECTED);
                        message.setInventoryBusinessType(InventoryBusinessType.OUT_ORDER_REJECTED);
                    }
                } else {
                    //其他释放
                    if (checkIsERPAutoSplit(outOrderResult)) {
                        inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.OUT_ORDER_CANCELED_BATCH);
                        message.setInventoryBusinessType(InventoryBusinessType.OUT_ORDER_CANCELED_BATCH);
                    } else {
                        if (OutOrderType.XH_CK.getValue().equals(outOrderResult.getType())) {
                            inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.OUT_ORDER_CANCELED);
                            message.setInventoryBusinessType(InventoryBusinessType.OUT_ORDER_CANCELED);
                        } else {
                            inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.QD_CANCELED);
                            message.setInventoryBusinessType(InventoryBusinessType.QD_CANCELED);
                        }
                    }
                }
                List<InventoryModelParam> circleInventoryModelParamList = buildCircleParameters(inventoryModelParam, message.getUpdateType());
                //圈货出库单处理，按货品标记处理为二个队列
                if (CollectionUtils.isNotEmpty(circleInventoryModelParamList)) {
                    paramList.addAll(circleInventoryModelParamList);
                } else {
                    paramList.add(inventoryModelParam);
                }
            } else {
                throw new BusinessException("不支持该库存更新类型！updateType=" + message.getUpdateType().getDes());
            }
        } catch (Exception e) {
            log.error("[OutStockInventoryHandler-buildParameters]==================库存操作,数据组装异常==================ex:", e);
            throw new BusinessException("库存操作,数据组装异常!");
        }
        log.info("OutStockInventoryHandler-buildParameters paramList=" + JSON.toJSONString(paramList));
        return paramList;
    }

    /**
     * 圈货订单，按货品标记拆分 库存处理产生
     *
     * @return
     */
    public List<InventoryModelParam> buildCircleParameters(InventoryModelParam inventoryModelParam, InventoryUpdateType updateType) {
        List<InventoryModelParam> paramList = new ArrayList<>();
        List<InventoryModelItemsParam> circleDimensionNoList = inventoryModelParam.getItems().stream().filter(m -> StringUtils.isNotBlank(m.getCircleDimensionNo())).collect(Collectors.toList());

        //圈货出库单处理，按货品标记处理为二个队列
        if (CollectionUtils.isNotEmpty(circleDimensionNoList)) {
            //拦截同货品
            Map<String, Long> collect = inventoryModelParam.getItems().stream().collect(Collectors.groupingBy(InventoryModelItemsParam::getGoodsCode,
                    Collectors.counting()));
            //collect.keySet().stream().forEach(goodsCode -> Assert.isTrue(collect.get(goodsCode) >= 1L, "圈货货品订单不能有相同货品，goodsCode=" + goodsCode));


            collect.keySet().stream().forEach(goodsCode -> {
                if (collect.get(goodsCode) >= 1L) {
                    if (InventoryUpdateType.LOCKED_WHK.equals(updateType)) {
                        inventoryModelParam.setNeedCombineOccupyStock(true);
                    }
                }
            });

            //普通队列
            InventoryModelParam inventoryModelParam1 = BeanUtils.copyProperties(inventoryModelParam, InventoryModelParam.class);
            inventoryModelParam1.setItems(inventoryModelParam.getItems().stream().filter(m -> StringUtils.isBlank(m.getCircleDimensionNo()) || StringUtils.startsWith(m.getCircleDimensionNo(), ERP_CIRCLE_DIMENSION_START_TAG)).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(inventoryModelParam1.getItems())) {
                paramList.add(inventoryModelParam1);
            }
            //圈货队列
            InventoryModelParam inventoryModelParam2 = BeanUtils.copyProperties(inventoryModelParam, InventoryModelParam.class);
            List<InventoryModelItemsParam> items = inventoryModelParam2.getItems().stream().filter(m -> StringUtils.isNotBlank(m.getCircleDimensionNo()) && !m.getCircleDimensionNo().startsWith(ERP_CIRCLE_DIMENSION_START_TAG)).collect(Collectors.toList());
            inventoryModelParam2.setItems(items);
            if (CollectionUtils.isNotEmpty(inventoryModelParam2.getItems())) {
                if (InventoryUpdateType.LOCKED_WHK.equals(updateType)) {
                    inventoryModelParam2.setUpdateType(InventoryUpdateType.LOCKED_QF);
                } else if (InventoryUpdateType.OUT_STOCK_HK.equals(updateType)) {
                    //出库 圈货 货品同时操作 2 为 圈货 3位货品
                    inventoryModelParam2.setUpdateType(InventoryUpdateType.OUT_STOCK_QF_HK);
                    inventoryModelParam2.setRelatedBusinessType(InventoryBusinessType.CIRCLE_ORDER_OUT_FINISHED);
                    InventoryModelParam inventoryModelParam3 = BeanUtils.copyProperties(inventoryModelParam, InventoryModelParam.class);
                    inventoryModelParam3.setItems(items);
                    inventoryModelParam3.setRelatedBusinessType(InventoryBusinessType.CIRCLE_ORDER_OUT_FINISHED);
                    paramList.add(inventoryModelParam3);
                } else if (InventoryUpdateType.RELEASE_ADDED.equals(updateType)) {
                    inventoryModelParam2.setUpdateType(InventoryUpdateType.RELEASE_QF_ADDED);
                }
                paramList.add(inventoryModelParam2);
            }
        }
        return paramList;
    }

    /**
     * 正常的锁定队列
     *
     * @param outOrderResult
     * @param message
     * @return
     */
    private InventoryModelParam buildParam(OutOrderResult outOrderResult, InventoryProcessMessage message) {
        InventoryModelParam inventoryModelParam = new InventoryModelParam();
        inventoryModelParam.setUserId(outOrderResult.getUserId());
        inventoryModelParam.setUserName(outOrderResult.getUserName());
        inventoryModelParam.setRelatedBusinessNo(outOrderResult.getOutOrderNo());
        inventoryModelParam.setGlobalNo(outOrderResult.getGlobalNo());
        inventoryModelParam.setLogicWarehouseCode(outOrderResult.getLogicWarehouseCode());
        inventoryModelParam.setUpdateType(message.getUpdateType());
        if (StringUtils.isNotBlank(outOrderResult.getWarehouseCode()) && !outOrderResult.getWarehouseCode().startsWith("DT_")) {
            EntityWarehouseResult warehouseResult = remoteLogicWarehouseFacade.getWarehouseByLogicCode(outOrderResult.getLogicWarehouseCode());
            if (Objects.nonNull(warehouseResult) && !Objects.equals(SYSTEM_DT, warehouseResult.getSystemCode())) {
                inventoryModelParam.setNeedOccupyStock(false);
            }
        }
        //虚拟仓判断
        LogicWarehouseResult logicWarehouseResult = remoteLogicWarehouseFacade.getLogicWarehouseByCode(outOrderResult.getLogicWarehouseCode());
        if (Objects.nonNull(logicWarehouseResult) && Objects.equals(1, logicWarehouseResult.getLogicWarehouseType().getCode())) {
            inventoryModelParam.setNeedOccupyStock(false);
        }

        //出库核扣,需要要实际回传库存数
        boolean outStockHK = InventoryUpdateType.OUT_STOCK_HK.equals(message.getUpdateType());

        List<OutOrderDetailResult> detailBOList = outOrderResult.getDetailResultList();
        List<InventoryModelItemsParam> collect = detailBOList.stream().map(detailBO -> {
            InventoryModelItemsParam inventoryModelItemsParam = new InventoryModelItemsParam();
            inventoryModelItemsParam.setUpdateNum(outStockHK ? detailBO.getActualQuantity() : detailBO.getPlanQuantity());
            inventoryModelItemsParam.setGoodsCode(detailBO.getGoodsCode());
            inventoryModelItemsParam.setBatchCode(detailBO.getBatchCode());
            inventoryModelItemsParam.setInternalBatchCode(detailBO.getInternalBatchCode());
            inventoryModelItemsParam.setInventoryNo(detailBO.getInventoryNo() + "_" + outOrderResult.getInventoryVersion());
            inventoryModelItemsParam.setInventoryType(Objects.equals(InventoryType.DEFECTIVE.getValue(), detailBO.getInventoryType()) ? InventoryType.DEFECTIVE : InventoryType.QUALITY);
            inventoryModelItemsParam.setCircleDimensionNo(detailBO.getCircleDimensionNo());
            return inventoryModelItemsParam;
        }).collect(Collectors.toList());
        inventoryModelParam.setItems(collect);
        if (Objects.equals(SpecifyAttributesEnum.YES, outOrderResult.getSpecifyAttributes())) {
            inventoryModelParam.setOccupyLotStock(Boolean.TRUE);
        }
        //销毁单锁库存
        if (OutOrderType.XH_CK.getValue().equals(outOrderResult.getType())) {
            inventoryModelParam.setOccupyDestroy(true);
        }

        if (OutOrderType.SH_CK.getCode().equals(outOrderResult.getType())) {
            inventoryModelParam.setOccupyFinance(true);
            inventoryModelParam.setOccupyLotStock(true);
        }
        if (OutOrderType.WY_CK.getCode().equals(outOrderResult.getType())) {
            inventoryModelParam.setNeedOccupyStock(false);
        }
        return inventoryModelParam;
    }

    /**
     * 构建少回传的数据
     *
     * @param outOrderResult
     * @return
     */
    private InventoryModelParam buildLessParam(OutOrderResult outOrderResult) {
        if (!Objects.equals(OutOrderStatus.CK_ALL.getValue(), outOrderResult.getStatus())) {
            return null;
        }
        //查询老的货品,检测是否有少的
        List<OutOrderDetailResult> outOrderDetailResultList = outOrderDetailFacade.listOutOrderDetailByNO(outOrderResult.getOutOrderNo());
        List<OutOrderDetailResult> orderDetailResultList = outOrderDetailResultList.stream().filter(outOrderDetailResult -> outOrderDetailResult.getActualQuantity() < outOrderDetailResult.getPlanQuantity()).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(orderDetailResultList)) {
            return null;
        }

        InventoryModelParam inventoryModelParam = new InventoryModelParam();
        inventoryModelParam.setUserId(outOrderResult.getUserId());
        inventoryModelParam.setUserName(outOrderResult.getUserName());
        inventoryModelParam.setRelatedBusinessNo(outOrderResult.getOutOrderNo());
        inventoryModelParam.setLogicWarehouseCode(outOrderResult.getLogicWarehouseCode());
        inventoryModelParam.setUpdateType(InventoryUpdateType.RELEASE_ADDED);

        if (Objects.equals(OutOrderType.XS_CK.getValue(), outOrderResult.getType())) {
            inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.XSD_CANCELED);
        } else if (Objects.equals(OutOrderType.DB_CK.getValue(), outOrderResult.getType())) {
            inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.DBCK_CANCELED);
        } else if (Objects.equals(OutOrderType.CD_CK.getValue(), outOrderResult.getType()) || Objects.equals(OutOrderType.CD_FX_CK.getValue(), outOrderResult.getType()) || Objects.equals(OutOrderType.QT_CK.getValue(), outOrderResult.getType())) {
            inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.QD_CANCELED);
        } else if (Objects.equals(OutOrderType.XH_CK.getValue(), outOrderResult.getType())) {
            inventoryModelParam.setRelatedBusinessType(InventoryBusinessType.OUT_ORDER_CANCELED);
        }

        List<InventoryModelItemsParam> collect = orderDetailResultList.stream().map(detailBO -> {
            InventoryModelItemsParam inventoryModelItemsParam = new InventoryModelItemsParam();
            inventoryModelItemsParam.setUpdateNum(detailBO.getPlanQuantity() - detailBO.getActualQuantity());
            inventoryModelItemsParam.setGoodsCode(detailBO.getGoodsCode());
            inventoryModelItemsParam.setBatchCode(detailBO.getBatchCode());
            inventoryModelItemsParam.setInternalBatchCode(detailBO.getInternalBatchCode());
            inventoryModelItemsParam.setInventoryNo("LESS_" + outOrderResult.getOutOrderNo());
            inventoryModelItemsParam.setInventoryType(Objects.equals(InventoryType.DEFECTIVE.getValue(), detailBO.getInventoryType()) ? InventoryType.DEFECTIVE : InventoryType.QUALITY);
            return inventoryModelItemsParam;
        }).collect(Collectors.toList());
        inventoryModelParam.setItems(collect);
        return inventoryModelParam;
    }

    /**
     * 检测是否erp内部系统拆分批
     *
     * @param outOrderResult
     * @return
     */
    private boolean checkIsERPAutoSplit(OutOrderResult outOrderResult) {
        return (Objects.equals(OutOrderType.XS_CK.getValue(), outOrderResult.getType()) || Objects.equals(OutOrderType.DB_CK.getValue(), outOrderResult.getType())) && Objects.equals(1, outOrderResult.getIsAutoSplit()) && StringUtils.isBlank(outOrderResult.getOrigSystem());
    }

    @Override
    protected OutOrderResult getBusinessObj(InventoryProcessMessage message) {
        OutOrderResult outOrderResult = outOrderFacade.getOutOrderByOrderNo(message.getBusinessNo(), null);
        try {
            if (InventoryUpdateType.LOCKED_WHK.equals(message.getUpdateType())) {
//                outOrderSplitBatch(outOrderResult);//拆分批次 TODO 不在拆分批次
                // 出库锁定
                List<OutOrderDetailResult> detailResultList = outOrderDetailFacade.listOutOrderDetailByNO(outOrderResult.getOutOrderNo(), OrderLockInventoryStatus.ORDER_SD_WAIT.getValue());
                outOrderResult.setDetailResultList(detailResultList);
            } else if (InventoryUpdateType.RELEASE_ADDED.equals(message.getUpdateType())) {
                // 释放锁定
                List<OutOrderDetailResult> detailResultList = outOrderDetailFacade.listOutOrderDetailByNO(outOrderResult.getOutOrderNo(), OrderLockInventoryStatus.ORDER_SD_SUCCESS.getValue());
                outOrderResult.setDetailResultList(detailResultList);
            } else if (InventoryUpdateType.OUT_STOCK_HK.equals(message.getUpdateType())) {
                // 出库核扣
                List<OutOrderLogisticsDetailResult> logisticsDetailResultList = outOrderLogisticsDetailFacade.listOutOrderLogisticsDetailByNO(outOrderResult.getOutOrderNo(), OrderStatus.ORDER_WAIT.getValue());
                List<OutOrderDetailResult> detailResultList = logisticsDetailResultList.stream().map(logisticsDetailResult -> {
                    OutOrderDetailResult result = new OutOrderDetailResult();
                    result.setActualQuantity(logisticsDetailResult.getActualQuantity());
                    result.setGoodsCode(logisticsDetailResult.getGoodsCode());
                    result.setBatchCode(logisticsDetailResult.getBatchCode());
                    result.setInternalBatchCode(logisticsDetailResult.getInternalBatchCode());
                    result.setInventoryType(logisticsDetailResult.getInventoryType());
                    result.setInventoryNo(logisticsDetailResult.getInventoryNo());
                    result.setCircleDimensionNo(logisticsDetailResult.getCircleDimensionNo());
                    return result;
                }).collect(Collectors.toList());
                outOrderResult.setDetailResultList(detailResultList);
            } else {
                throw new BusinessException("不支持该库存更新类型！updateType=" + message.getUpdateType().getDes());
            }
        } catch (BusinessException e) {
            log.error("[OutStockInventoryHandler-getBusinessObj]==================库存操作,查询明细异常==================ex:", e);
            throw e;
        } catch (Exception e) {
            log.error("[OutStockInventoryHandler-getBusinessObj]==================库存操作,查询明细异常==================ex:", e);
            throw new BusinessException("库存操作,查询明细异常");
        }
        log.info("[OutStockInventoryHandler-getBusinessObj]==================库存操作,原始数据==================:{}", JSON.toJSONString(outOrderResult));
        return outOrderResult;
    }

    /**
     * 拆分批次
     *
     * @param outOrderResult
     */
    @Deprecated
    private void outOrderSplitBatch(OutOrderResult outOrderResult) {
        if (Objects.equals(OrderLockInventoryStatus.ORDER_SP_WAIT.getValue(), outOrderResult.getLockInventoryStatus())) {
            outOrderFacade.outOrderSplitBatch(outOrderResult.getOutOrderNo());
            OutOrderResult order = outOrderFacade.getOutOrderByOrderNo(outOrderResult.getOutOrderNo(), null);
            outOrderResult.setLockInventoryStatus(order.getLockInventoryStatus());
        } else {
            //外部批次转内部批次
            outOrderFacade.outOrderConversionBatch(outOrderResult.getOutOrderNo());
        }
    }

    @Override
    protected BusinessBillType getSupportType() {
        return BusinessBillType.BILL_OUT_STOCK;
    }

    @Override
    protected RpcResult validateBusinessStatus(OutOrderResult outOrderResult, InventoryProcessMessage message) {
        try {
            if (InventoryUpdateType.LOCKED_WHK.equals(message.getUpdateType())) {
                // 出库锁定
                if (Objects.equals(OrderLockInventoryStatus.ORDER_NORMAL.getValue(), outOrderResult.getLockInventoryStatus())) {
                    log.error("[OutStockInventoryHandler-validateBusinessStatus]==================出库单无需锁定!==================orderNo:" + outOrderResult.getOutOrderNo());
                    return RpcResult.error(AresContext.NO_LOCK_INVENTORY, outOrderResult.getOutOrderNo() + ":订单无需锁定!");
                }
                if (!Objects.equals(ApprovalStatus.SP_IS_PROCESSING.getValue(), outOrderResult.getApprovalStatus()) && !Objects.equals(ApprovalStatus.SP_SUCCESS.getValue(), outOrderResult.getApprovalStatus())) {
                    log.error("[OutStockInventoryHandler-validateBusinessStatus]==================出库单未审核,禁止锁库存!==================orderNo:" + outOrderResult.getOutOrderNo());
                    return RpcResult.error(AresContext.DONOT_SEND_MQ_INVENTORY, outOrderResult.getOutOrderNo() + ":订单未审核!");
                }
                if (!Objects.equals(OrderLockInventoryStatus.ORDER_SD_WAIT.getValue(), outOrderResult.getLockInventoryStatus()) && !Objects.equals(OrderLockInventoryStatus.ORDER_SD_FAILED.getValue(), outOrderResult.getLockInventoryStatus())) {
                    log.error("[OutStockInventoryHandler-validateBusinessStatus]==================库存不是待锁定状态!==================orderNo:" + outOrderResult.getOutOrderNo());
                    return RpcResult.error(AresContext.DONOT_SEND_MQ_INVENTORY, outOrderResult.getOutOrderNo() + ":订单库存不是待锁定状态!");
                }
                if (Objects.equals(OutOrderStatus.CK_REVOCATION.getValue(), outOrderResult.getStatus())) {
                    log.error("[OutStockInventoryHandler-validateBusinessStatus]==================订单已取消,禁止锁库存!==================orderNo:" + outOrderResult.getOutOrderNo());
                    return RpcResult.error(AresContext.DONOT_SEND_MQ_INVENTORY, outOrderResult.getOutOrderNo() + ":订单已取消,禁止锁库存!");
                }
                if (CollectionUtil.isEmpty(outOrderResult.getDetailResultList())) {
                    log.error("[OutStockInventoryHandler-validateBusinessStatus]==================订单无锁定明细!==================orderNo:" + outOrderResult.getOutOrderNo());
                    return RpcResult.error(AresContext.ORDER_LOCK_NO_DETAIL, outOrderResult.getOutOrderNo() + ":订单无锁定明细!");
                }
            } else if (InventoryUpdateType.OUT_STOCK_HK.equals(message.getUpdateType())) {
                // 出库核扣
                if (Objects.equals(OrderStatus.ORDER_NORMAL.getValue(), outOrderResult.getInventoryStatus())) {
                    log.error("[OutStockInventoryHandler-validateBusinessStatus]=========库存无需已核扣=========orderNo:" + outOrderResult.getOutOrderNo());
                    return RpcResult.error(AresContext.DONOT_SEND_MQ_INVENTORY, outOrderResult.getOutOrderNo() + ":订单库存无需核扣!");
                }
                if (Objects.equals(OrderStatus.ORDER_SUCCESS_ALL.getValue(), outOrderResult.getInventoryStatus())) {
                    log.error("[OutStockInventoryHandler-validateBusinessStatus]=========库存已核扣成功=========orderNo:" + outOrderResult.getOutOrderNo());
                    return RpcResult.error(AresContext.DONOT_SEND_MQ_INVENTORY, outOrderResult.getOutOrderNo() + ":订单库存已经核扣成功!");
                }
                if (!Objects.equals(OutOrderStatus.CK_PART.getValue(), outOrderResult.getStatus())
                        && !Objects.equals(OutOrderStatus.CK_ALL.getValue(), outOrderResult.getStatus())) {
                    log.error("[OutStockInventoryHandler-validateBusinessStatus]=========非出库完成状态=========orderNo:" + outOrderResult.getOutOrderNo());
                    return RpcResult.error(AresContext.DONOT_SEND_MQ_INVENTORY, outOrderResult.getOutOrderNo() + ":订单非完成状态!");
                }
            } else if (InventoryUpdateType.RELEASE_ADDED.equals(message.getUpdateType())) {
                // 出库释放
                if (!Objects.equals(OutOrderStatus.CK_REVOCATION.getValue(), outOrderResult.getStatus())
                        && !Objects.equals(OutOrderStatus.CK_REPEAL.getValue(), outOrderResult.getStatus())
                        && !Objects.equals(OutOrderStatus.CK_FAIL_PROCESSING.getValue(), outOrderResult.getStatus())
                        && !Objects.equals(OutOrderStatus.CK_WAIT_PROCESSING.getValue(), outOrderResult.getStatus())) {
                    log.error("[OutStockInventoryHandler-validateBusinessStatus]==================出库单状态错误!==================orderNo:" + outOrderResult.getOutOrderNo());
                    return RpcResult.error(AresContext.DONOT_SEND_MQ_INVENTORY, outOrderResult.getOutOrderNo() + ":订单状态错误!");
                }

                //校验未锁定状态的明细,是否已经锁定过, 如果锁定过,把锁定未更新的返回
                List<OutOrderDetailResult> detailResultList = outOrderDetailFacade.listOutOrderDetailByNO(outOrderResult.getOutOrderNo(), OrderLockInventoryStatus.ORDER_SD_WAIT.getValue());
                Set<String> inventoryNoSet = detailResultList.stream().map(OutOrderDetailResult::getInventoryNo).collect(Collectors.toSet());
                List<String> inventoryNoList = new ArrayList<>();
                inventoryNoSet.forEach(k -> {
                    InventoryProcessLogSearch logSearch = BeanUtils.copyProperties(message, InventoryProcessLogSearch.class);
                    logSearch.setUpdateType(InventoryUpdateType.LOCKED_WHK);
                    logSearch.setInventoryNo(k + "_" + outOrderResult.getInventoryVersion());
                    List<InventoryProcessLogBO> listBySearch = inventoryProcessLogManager.listBySearch(logSearch);
                    if (CollectionUtil.isNotEmpty(listBySearch)) {
                        inventoryNoList.add(k);
                    }
                });
                if (inventoryNoList.size() > 0) {
                    log.error("[OutStockInventoryHandler-validateBusinessStatus]==================订单明细库存锁定状态未更新==================orderNo:{},inventoryNoList:{}", outOrderResult.getOutOrderNo(), JSON.toJSONString(inventoryNoList));
                    return RpcResult.error(AresContext.LOCK_INVENTORY, "订单明细库存锁定状态未更新!", inventoryNoList);
                }

                // 如果没有 锁定成功, 也没有 待锁定状态 已经被锁定的,说明订单没有锁定库存,直接返回
                List<OutOrderDetailResult> detailResults = outOrderResult.getDetailResultList();
                if (inventoryNoList.size() == 0 && detailResults.size() == 0) {
                    log.error("[OutStockInventoryHandler-validateBusinessStatus]==================订单未锁定,无需释放库存==================orderNo:{}", outOrderResult.getOutOrderNo());
                    return RpcResult.error(AresContext.UN_LOCK_INVENTORY, "订单未锁定,无需释放库存!", inventoryNoList);
                }
            } else {
                return RpcResult.error("库存更新类型错误!updateType=" + message.getUpdateType().getDes());
            }
        } catch (Exception e) {
            log.error("[OutStockInventoryHandler-validateBusinessStatus]==================库存操作,状态校验异常==================ex:", e);
            return RpcResult.error(AresContext.DONOT_SEND_MQ_INVENTORY, "库存操作,状态校验异常!");
        }
        return RpcResult.success();
    }

    /**
     * Override this method for calling inventoryOperationFacadeV2
     *
     * @param inventoryModelParamList
     * @param message
     * @param outOrderResult
     * @return
     */
    @Override
    protected RpcResult callInventory(List<InventoryModelParam> inventoryModelParamList, InventoryProcessMessage message, OutOrderResult outOrderResult) {

        if (isCircle(inventoryModelParamList)) {
            BusinessException bex = null;
            RedissonMultiLock goodsRLock = null;
            try {
                //商品维度锁
                Set<String> goodsCodeList = Sets.newHashSet();
                for (InventoryModelParam inventoryModelParam : inventoryModelParamList) {
                    Set<String> tempSet = inventoryModelParam.getItems().stream().map(InventoryModelItemsParam::getGoodsCode).collect(Collectors.toSet());
                    goodsCodeList.addAll(tempSet);
                }
                goodsRLock = redisLockUtils.addLockSkuList(outOrderResult.getUserId(), goodsCodeList);
                goodsRLock.lock(10, TimeUnit.MINUTES);
                RpcResult result = callCircleInventoryTransactional.callCircleInventory(inventoryModelParamList, message, outOrderResult);
                return result;
            } catch (BusinessException e) {
                log.error("[OutStockInventoryHandler-callCircleInventory]==================库存操作异常==================bex:", e);
                if (Objects.equals(AresContext.INSUFFICIENT_INVENTORY, e.getCode())) {
                    //库存不足或批次不存在
                    bex = e;
                }
            } catch (Exception e) {
                log.error("[OutStockInventoryHandler-callCircleInventory]==================库存操作异常==================ex:", e);
            }
            finally {
                try {
                    if (null != goodsRLock) {
                        log.info("======OutStockInventoryHandler-callCircleInventory=========== release redis goodsRLock  lock. lockKey={}");
                        goodsRLock.unlock();
                    }
                } catch (Exception e) {
                    log.info("======OutStockInventoryHandler-callCircleInventory=========== release redis goodsRLock unlock error. lockKey={}", e);
                }
            }
            //不为空,说明库存不足 或 批次不存在
            if (Objects.nonNull(bex)) {
                return RpcResult.error(bex.getCode(), bex.getMessage());
            }

        }
        try {
            //正常
            InventoryModelParam inventoryModelParam = inventoryModelParamList.get(0);
            //少回传
            InventoryModelParam inventoryModelParam2 = null;
            if (inventoryModelParamList.size() == 2) {
                inventoryModelParam2 = inventoryModelParamList.get(1);
            }

            List<InventoryModelItemsParam> items = inventoryModelParam.getItems();
            BusinessException bex = null;

            //如果items是空的,说明没有可操作的单据明细,直接返回成功
            List<String> inventoryNoList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(items)) {
                Map<String, List<InventoryModelItemsParam>> result = items.stream().collect(Collectors.groupingBy(InventoryModelItemsParam::getInventoryNo));
                for (List<InventoryModelItemsParam> v : result.values()) {
                    try {
                        inventoryModelParam.setItems(v);
                        log.info("[OutStockInventoryHandler-callInventory]==================库存操作==================inventoryModelParam:" + JSONUtil.toJsonStr(inventoryModelParam));
                        if (Objects.nonNull(inventoryModelParam2)) {
                            //出库核扣少回传情况
                            List<InventoryModelParam> list = new ArrayList();
                            list.add(inventoryModelParam);
                            list.add(inventoryModelParam2);
                            RpcResult rpcResult = inventoryOperationFacadeV2.optInventoryByList(list, message);
                            if (Objects.equals(AresContext.ARES_SUCCESS, rpcResult.getCode()) && Objects.nonNull(rpcResult.getData())) {
                                List<String> list1 = (List) rpcResult.getData();
                                inventoryNoList.addAll(list1);
                            }
                            inventoryModelParam2 = null;
                        } else {
                            RpcResult<String> rpcResult;
                            //正常情况
                            rpcResult = inventoryOperationFacadeV2.optInventory(inventoryModelParam, message);
                            if (Objects.equals(AresContext.ARES_SUCCESS, rpcResult.getCode())) {
                                inventoryNoList.add(rpcResult.getData());
                            }
                        }
                    } catch (BusinessException e) {
                        log.error("[OutStockInventoryHandler-callInventory]==================库存操作异常==================bex:", e);
                        if (Objects.equals(AresContext.INSUFFICIENT_INVENTORY, e.getCode())) {
                            //库存不足或批次不存在
                            bex = e;
                        }
                    } catch (Exception e) {
                        log.error("[OutStockInventoryHandler-callInventory]==================库存操作异常==================ex:", e);
                    }
                }

                //不为空,说明库存不足 或 批次不存在
                if (Objects.nonNull(bex)) {
                    return RpcResult.error(bex.getCode(), bex.getMessage());
                }

                if (inventoryNoList.size() == 0) {
                    log.error("[OutStockInventoryHandler-callInventory]==================库存操作异常==================inventoryNoList:0");
                    return RpcResult.error(AresContext.DONOT_SEND_MQ_INVENTORY, "库存操作失败,系统异常!");
                }
            }
            return RpcResult.success(buildInventoryVersion(inventoryNoList, outOrderResult.getInventoryVersion()));
        } catch (Exception e) {
            log.error("[OutStockInventoryHandler-callInventory]==================库存操作异常==================ex:", e);
            return RpcResult.error(AresContext.DONOT_SEND_MQ_INVENTORY, "库存操作失败,系统异常!");
        }
    }


    //圈货的处理，不处理少货情况，业务上没有实际场景
    protected RpcResult callCircleInventory(List<InventoryModelParam> inventoryModelParamList, InventoryProcessMessage message, OutOrderResult outOrderResult) {
        try {

            List<String> inventoryNoList = new ArrayList<>();

            for (InventoryModelParam inventoryModelParam : inventoryModelParamList) {

                List<InventoryModelItemsParam> items = inventoryModelParam.getItems();
                BusinessException bex = null;

                //如果items是空的,说明没有可操作的单据明细,直接返回成功
                if (CollectionUtil.isNotEmpty(items)) {
                    Map<String, List<InventoryModelItemsParam>> result = items.stream().collect(Collectors.groupingBy(InventoryModelItemsParam::getInventoryNo));
                    for (List<InventoryModelItemsParam> v : result.values()) {
                        try {

                            log.info("[OutStockInventoryHandler-callCircleInventory]==================库存操作==================inventoryModelParam:" + JSONUtil.toJsonStr(inventoryModelParam));
                            List<InventoryModelParam> list = new ArrayList();
                            inventoryModelParam.setItems(v);
                            list.add(inventoryModelParam);
                            RpcResult rpcResult = inventoryOperationFacadeV2.optInventoryByList(list, message);
                            if (Objects.equals(AresContext.ARES_SUCCESS, rpcResult.getCode()) && Objects.nonNull(rpcResult.getData())) {
                                List<String> list1 = (List) rpcResult.getData();
                                inventoryNoList.addAll(list1);
                            }

                        } catch (BusinessException e) {
                            log.error("[OutStockInventoryHandler-callCircleInventory]==================库存操作异常==================bex:", e);
                            if (Objects.equals(AresContext.INSUFFICIENT_INVENTORY, e.getCode())) {
                                //库存不足或批次不存在
                                bex = e;
                            }
                        } catch (Exception e) {
                            log.error("[OutStockInventoryHandler-callCircleInventory]==================库存操作异常==================ex:", e);
                        }
                    }

                    //不为空,说明库存不足 或 批次不存在
                    if (Objects.nonNull(bex)) {
                        return RpcResult.error(bex.getCode(), bex.getMessage());
                    }

                }

            }
            if (inventoryNoList.size() == 0) {
                log.error("[OutStockInventoryHandler-callCircleInventory]==================库存操作异常==================inventoryNoList:0");
                return RpcResult.error(AresContext.DONOT_SEND_MQ_INVENTORY, "库存操作失败,系统异常!");
            }
            return RpcResult.success(buildInventoryVersion(inventoryNoList, outOrderResult.getInventoryVersion()));
        } catch (Exception e) {
            log.error("[OutStockInventoryHandler-callCircleInventory]==================库存操作异常==================ex:", e);
            return RpcResult.error(AresContext.DONOT_SEND_MQ_INVENTORY, "库存操作失败,系统异常!");
        }
    }

    private boolean isCircle(List<InventoryModelParam> inventoryModelParamList) {
        AtomicBoolean isCircle = new AtomicBoolean(false);
        inventoryModelParamList.stream().forEach(inventoryModelParam -> {

            inventoryModelParam.getItems().stream().forEach(m -> {
                if (StringUtils.isNotBlank(m.getCircleDimensionNo())) {
                    isCircle.set(true);
                }
            });
        });
        return isCircle.get();
    }

    private List<String> buildInventoryVersion(List<String> inventoryNoList, Integer inventoryVersion) {
        return inventoryNoList.stream().map(inventoryNo -> inventoryNo.replace("_" + inventoryVersion, "")).collect(Collectors.toList());
    }
}
