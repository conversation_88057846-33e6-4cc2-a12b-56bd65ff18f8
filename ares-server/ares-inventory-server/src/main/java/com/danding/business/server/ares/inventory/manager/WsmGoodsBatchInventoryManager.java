package com.danding.business.server.ares.inventory.manager;

import com.danding.business.core.ares.inventory.service.IGoodsBatchInventoryService;
import com.danding.business.server.ares.inventory.BO.StockLotBO;
import com.danding.business.server.ares.inventory.param.WsmStockQueryParam;
import com.danding.business.server.ares.inventory.remote.wsm.RemoteStockQueryFacade;
import com.danding.business.server.ares.inventory.remote.wsm.RemoveStockSearchFacade;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.elasticsearch.stock.lot.StockLotIndexDTO;
import com.dt.elasticsearch.stock.lot.StockLotParam;
import com.dt.platform.wms.rpc.client.stock.*;
import com.dt.platform.wms.rpc.dto.PageDTO;
import com.dt.platform.wms.rpc.dto.StockRpcDTO;
import com.dt.platform.wms.rpc.param.StockRpcParam;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 货品批次库存(batch+sku+logic_warehouse) 服务类
 * </p>
 *
 * <AUTHOR> Xu
 * @since 2020-11-22
 */
@Component
@Log4j
public class WsmGoodsBatchInventoryManager {

    @Autowired
    private IGoodsBatchInventoryService goodsBatchInventoryService;

    @Autowired
    RemoteStockQueryFacade remoteStockQueryFacade;
    @Resource
    RemoveStockSearchFacade removeStockSearchFacade;

    public Result<PageVO<StockLotIndexDTO>> stockLotPage(StockLotParam param) {
        return removeStockSearchFacade.stockLotPage(param);
    }

    public List<StockLotBO> queryStockLotList(WsmStockQueryParam queryParam) {
        return remoteStockQueryFacade.queryStockLotList(queryParam);
    }

    public Result<PageVO<StockLotIndexDTO>> pageListTaoTianInventory(StockLotParam param) {
        return removeStockSearchFacade.pageListTaoTianInventory(param);
    }

    public Result<PageVO<TaoTianStockSnapshotDTO>> pageListTaoTianInventorySnapShot(TaoTianStockSnapshotParam param) {
        return removeStockSearchFacade.pageListTaoTianInventorySnapShot(param);
    }

    public Result<PageVO<TaoTianStockLotSnapshotDTO>> pageListTaoTianBatchInventorySnapShot(TaoTianStockLotSnapshotParam param) {
        return removeStockSearchFacade.pageListTaoTianBatchInventorySnapShot(param);
    }

    public Result<PageVO<TaoTianStockLotFlowDTO>> pageListTaoTianInventoryFlow(TaoTianStockLotFlowParam param) {
        return removeStockSearchFacade.pageListTaoTianInventoryFlow(param);
    }

    public PageDTO<StockRpcDTO> pageListDyStockInventory(StockRpcParam param) {
        return removeStockSearchFacade.pageListDyStockInventory(param);
    }

}
