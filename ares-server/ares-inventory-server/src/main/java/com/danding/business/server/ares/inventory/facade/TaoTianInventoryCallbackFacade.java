package com.danding.business.server.ares.inventory.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.danding.business.client.ares.entitywarehouse.result.EntityWarehouseResult;
import com.danding.business.client.ares.inventory.facade.ITaoTianInventoryCallback;
import com.danding.business.client.ares.owner.result.OutOwnerResult;
import com.danding.business.client.ares.owner.result.OwnerResult;
import com.danding.business.common.ares.utils.DateUtils;
import com.danding.business.common.ares.utils.HttpRequestUtils;
import com.danding.business.common.ares.utils.MD5V2Utils;
import com.danding.business.common.ares.utils.Md5Utils;
import com.danding.business.server.ares.inventory.BO.TaoTianUploadFileBO;
import com.danding.business.server.ares.inventory.config.InventoryNacosConfig;
import com.danding.business.server.ares.inventory.manager.WsmGoodsBatchInventoryManager;
import com.danding.business.server.ares.inventory.remote.RemoteLogicWarehouseFacade;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.soul.client.common.exception.BusinessException;
import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.platform.wms.rpc.client.stock.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.FileEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.danding.business.common.ares.utils.HttpRequestUtils.postForm;

@Slf4j
@DubboService
public class TaoTianInventoryCallbackFacade implements ITaoTianInventoryCallback {

    @Autowired
    private RemoteLogicWarehouseFacade remoteLogicWarehouseFacade;
    @Autowired
    private WsmGoodsBatchInventoryManager wsmGoodsBatchInventoryManager;

    @Autowired
    private InventoryNacosConfig inventoryNacosConfig;

    @Override
    public void callBackInventorySnapshot(Date defDate, String ownerCode) {
        List<OwnerResult> ownerResultList = remoteLogicWarehouseFacade.listOwnerByCode(ownerCode);
        TaoTianStockSnapshotParam snapshotParam = new TaoTianStockSnapshotParam();
        String dateTime = DateUtils.defaultFormat(defDate.getTime());
        for (OwnerResult ownerResult: ownerResultList) {
            try {
                EntityWarehouseResult entityWarehouse = remoteLogicWarehouseFacade.getEntityWarehouse(ownerResult.getEntityWarehouseCode());
                OutOwnerResult outOwnerResult = remoteLogicWarehouseFacade.getOutOwnerCodeMapping(ownerResult.getOwnerCode(), entityWarehouse.getWarehouseCode());

                String fileName = outOwnerResult.getOutOwnerCode() + "_" + outOwnerResult.getOutWarehouseCode() + "_" + dateTime + "snapshot.csv";
                String filePath = buildFilePath(defDate, fileName);

                snapshotParam.setWarehouseCode(entityWarehouse.getWarehouseCode());
                snapshotParam.setOwnerCode(ownerResult.getOwnerCode());
                snapshotParam.setCurrentPage(1);
                snapshotParam.setPageSize(2000);
                snapshotParam.setSnapshotDate(dateTime);
                Result<PageVO<TaoTianStockSnapshotDTO>> pageVOResult = wsmGoodsBatchInventoryManager.pageListTaoTianInventorySnapShot(snapshotParam);
                List<TaoTianStockSnapshotDTO> stockLotIndexDTOList = pageVOResult.getData().getDataList();
                if (stockLotIndexDTOList.size() == 0) {
                    backWarning(ownerResult.getOwnerCode()+","+entityWarehouse.getWarehouseCode()+":库存快照为空!");
                    continue;
                }
                try (PrintWriter out = new PrintWriter(new FileWriter(filePath))) {
                    out.println("ownerCode,warehouseCode,itemCode,inventoryType,quantity,lockQuantity"); // 标题行
                    for (TaoTianStockSnapshotDTO inventory : stockLotIndexDTOList) {
                        out.println(buildSnapshot(outOwnerResult.getOutOwnerCode(),outOwnerResult.getOutWarehouseCode(), inventory));// 转换并打印每一行
                    }
                    listPageSnapshot(snapshotParam, out, outOwnerResult);
                }
                //获取淘天上传数据
                File newFile = new File(filePath);
                TaoTianUploadFileBO uploadFileBO = new TaoTianUploadFileBO();
                uploadFileBO.setOwnerCode(ownerResult.getOwnerCode());
                uploadFileBO.setResourceCode(entityWarehouse.getWarehouseCode());
                uploadFileBO.setFileName(fileName);
                uploadFileBO.setFilePath(filePath);
                uploadFileBO.setContentLength(newFile.length());
                uploadFileBO.setContentMd5(MD5V2Utils.getFileMD5Base64(newFile));
                uploadFileBO.setOrderType("INV_IN_WMS");
                getTTUploadInfo(uploadFileBO);
                //上传淘天
                updateFile(uploadFileBO, 0);
            } catch (BusinessException e) {
                backWarning(e.getMessage());
            } catch (Exception e) {
                log.error("TaoTianInventoryCallbackFacade.callBackInventorySnapshot======上传淘天库存快照异常=====ex:", e);
                backWarning("上传淘天库存快照异常");
            } finally {
                deleteFile(defDate);
            }
        }
    }

    private void listPageSnapshot(TaoTianStockSnapshotParam snapshotParam, PrintWriter out, OutOwnerResult outOwnerResult) {
        snapshotParam.setCurrentPage(snapshotParam.getCurrentPage() + 1);
        Result<PageVO<TaoTianStockSnapshotDTO>> pageVOResult = wsmGoodsBatchInventoryManager.pageListTaoTianInventorySnapShot(snapshotParam);
        List<TaoTianStockSnapshotDTO> dataList = pageVOResult.getData().getDataList();
        if (dataList.size() > 0) {
           for (TaoTianStockSnapshotDTO inventory : dataList) {
               out.println(buildSnapshot(outOwnerResult.getOutOwnerCode(),outOwnerResult.getOutWarehouseCode(), inventory));// 转换并打印每一行
           }
           listPageSnapshot(snapshotParam, out, outOwnerResult);
       }
    }

    private String buildSnapshot(String outOwnerCode, String outWarehouseCode, TaoTianStockSnapshotDTO inventory) {
        StringBuilder sb = new StringBuilder();
        sb.append(outOwnerCode).append(",");
        sb.append(outWarehouseCode).append(",");
        sb.append(StringUtils.isNotBlank(inventory.getItemCode()) ? inventory.getItemCode(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getInventoryType()) ? inventory.getInventoryType(): "").append(",");
        sb.append(Objects.nonNull(inventory.getQuantity()) ? inventory.getQuantity(): "").append(",");
        sb.append(Objects.nonNull(inventory.getLockQuantity()) ? inventory.getLockQuantity(): "");
        return sb.toString();
    }

    @Override
    public void callBackInventoryBatch(Date defDate, String ownerCode) {
        List<OwnerResult> ownerResultList = remoteLogicWarehouseFacade.listOwnerByCode(ownerCode);
        TaoTianStockLotSnapshotParam lotSnapshotParam = new TaoTianStockLotSnapshotParam();
        String dateTime = DateUtils.defaultFormat(defDate.getTime());
        for (OwnerResult ownerResult: ownerResultList) {
            try {
                EntityWarehouseResult entityWarehouse = remoteLogicWarehouseFacade.getEntityWarehouse(ownerResult.getEntityWarehouseCode());
                OutOwnerResult outOwnerResult = remoteLogicWarehouseFacade.getOutOwnerCodeMapping(ownerResult.getOwnerCode(), entityWarehouse.getWarehouseCode());

                String fileName = outOwnerResult.getOutOwnerCode() + "_" + outOwnerResult.getOutWarehouseCode() + "_" + DateUtils.defaultFormat(defDate.getTime()) + "batch.csv";
                String filePath = buildFilePath(defDate, fileName);

                lotSnapshotParam.setWarehouseCode(entityWarehouse.getWarehouseCode());
                lotSnapshotParam.setOwnerCode(ownerResult.getOwnerCode());
                lotSnapshotParam.setCurrentPage(1);
                lotSnapshotParam.setPageSize(2000);
                lotSnapshotParam.setSnapshotDate(dateTime);
                Result<PageVO<TaoTianStockLotSnapshotDTO>> pageVOResult = wsmGoodsBatchInventoryManager.pageListTaoTianBatchInventorySnapShot(lotSnapshotParam);
                List<TaoTianStockLotSnapshotDTO> stockLotIndexDTOList = pageVOResult.getData().getDataList();
                if (stockLotIndexDTOList.size() == 0) {
                    backWarning(ownerResult.getOwnerCode()+","+entityWarehouse.getWarehouseCode()+":批次库存为空!");
                    continue;
                }
                try (PrintWriter out = new PrintWriter(new FileWriter(filePath))) {
                    out.println("ownerCode,warehouseCode,itemCode,barCode,inventoryType,quantity,lockQuantity,batchCode,produceCode,productDate,expireDate,inboundTime,inventoryAge,rejectDate,lockupDate,isShelfLifeMgmt,shelfLifeDays"); // 标题行
                    for (TaoTianStockLotSnapshotDTO inventory : stockLotIndexDTOList) {
                        out.println(buildLostSnapshot(outOwnerResult.getOutOwnerCode(), outOwnerResult.getOutWarehouseCode(), inventory));// 转换并打印每一行
                    }
                    listPageLostSnapshot(lotSnapshotParam, out, outOwnerResult);
                }
                //获取淘天上传数据
                File newFile = new File(filePath);
                TaoTianUploadFileBO uploadFileBO = new TaoTianUploadFileBO();
                uploadFileBO.setOwnerCode(ownerResult.getOwnerCode());
                uploadFileBO.setResourceCode(entityWarehouse.getWarehouseCode());
                uploadFileBO.setFileName(fileName);
                uploadFileBO.setFilePath(filePath);
                uploadFileBO.setContentLength(newFile.length());
                uploadFileBO.setContentMd5(MD5V2Utils.getFileMD5Base64(newFile));
                uploadFileBO.setOrderType("INV_EXPIRY_IN_WMS");
                getTTUploadInfo(uploadFileBO);
                //上传淘天
                updateFile(uploadFileBO, 0);
            } catch (BusinessException e) {
                backWarning(e.getMessage());
            } catch (Exception e) {
                log.error("TaoTianInventoryCallbackFacade.callBackInventoryBatch======上传淘天库存快照批次效期异常=====ex:", e);
                backWarning("上传淘天库存快照批次效期异常");
            }
        }
    }

    private void listPageLostSnapshot(TaoTianStockLotSnapshotParam lotSnapshotParam, PrintWriter out, OutOwnerResult outOwnerResult) {
        lotSnapshotParam.setCurrentPage(lotSnapshotParam.getCurrentPage() + 1);
        Result<PageVO<TaoTianStockLotSnapshotDTO>> pageVOResult = wsmGoodsBatchInventoryManager.pageListTaoTianBatchInventorySnapShot(lotSnapshotParam);
        List<TaoTianStockLotSnapshotDTO> dataList = pageVOResult.getData().getDataList();
        if (dataList.size() > 0) {
            for (TaoTianStockLotSnapshotDTO inventory : dataList) {
                out.println(buildLostSnapshot(outOwnerResult.getOutOwnerCode(), outOwnerResult.getOutWarehouseCode(), inventory));// 转换并打印每一行
            }
            listPageLostSnapshot(lotSnapshotParam, out, outOwnerResult);
        }
    }

    private String buildLostSnapshot(String outOwnerCode, String outWarehouseCode, TaoTianStockLotSnapshotDTO inventory) {
        StringBuilder sb = new StringBuilder();
        sb.append(outOwnerCode).append(",");
        sb.append(outWarehouseCode).append(",");
        sb.append(StringUtils.isNotBlank(inventory.getItemCode()) ? inventory.getItemCode(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getBarCode()) ? inventory.getBarCode(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getInventoryType()) ? inventory.getInventoryType(): "").append(",");
        sb.append(Objects.nonNull(inventory.getQuantity()) ? inventory.getQuantity(): "").append(",");
        sb.append(Objects.nonNull(inventory.getLockQuantity()) ? inventory.getLockQuantity(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getBatchCode()) ? inventory.getBatchCode(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getProduceCode()) ? inventory.getProduceCode(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getProductDate()) ? inventory.getProductDate(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getExpireDate()) ? inventory.getExpireDate(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getInboundTime()) ? inventory.getInboundTime(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getInventoryAge()) ? inventory.getInventoryAge(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getRejectDate()) ? inventory.getRejectDate(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getLockupDate()) ? inventory.getLockupDate(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getIsShelfLifeMgmt()) ? inventory.getIsShelfLifeMgmt(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getShelfLifeDays()) ? inventory.getShelfLifeDays(): "");
        return sb.toString();
    }

    @Override
    public void callBackInventoryLogFlow(Date startTime, Date defDate, String ownerCode) {
        List<OwnerResult> ownerResultList = remoteLogicWarehouseFacade.listOwnerByCode(ownerCode);
        TaoTianStockLotFlowParam flowParam = new TaoTianStockLotFlowParam();
        for (OwnerResult ownerResult: ownerResultList) {
            try {
                EntityWarehouseResult entityWarehouse = remoteLogicWarehouseFacade.getEntityWarehouse(ownerResult.getEntityWarehouseCode());
                OutOwnerResult outOwnerResult = remoteLogicWarehouseFacade.getOutOwnerCodeMapping(ownerResult.getOwnerCode(), entityWarehouse.getWarehouseCode());

                String fileName = outOwnerResult.getOutOwnerCode() + "_" + outOwnerResult.getOutWarehouseCode() + "_" + DateUtils.defaultFormat(defDate.getTime()) + "flow.csv";
                String filePath = buildFilePath(defDate, fileName);

                flowParam.setWarehouseCode(entityWarehouse.getWarehouseCode());
                flowParam.setOwnerCode(ownerResult.getOwnerCode());
                flowParam.setCurrentPage(1);
                flowParam.setPageSize(2000);
                flowParam.setCreatedTimeStart(startTime.getTime());
                flowParam.setCreatedTimeEnd(defDate.getTime());
                Result<PageVO<TaoTianStockLotFlowDTO>> pageVOResult = wsmGoodsBatchInventoryManager.pageListTaoTianInventoryFlow(flowParam);
                List<TaoTianStockLotFlowDTO> stockLotIndexDTOList = pageVOResult.getData().getDataList();
                if (stockLotIndexDTOList.size() == 0) {
                    backWarning(ownerResult.getOwnerCode()+","+entityWarehouse.getWarehouseCode()+":库存流水为空!");
                    continue;
                }
                try (PrintWriter out = new PrintWriter(new FileWriter(filePath))) {
                    out.println("ownerCode,warehouseCode,itemCode,inventoryType,batchCode,orderCode,orderLineNo,changeQuantity,changeLockQuantity,operateTime,orderType"); // 标题行
                    for (TaoTianStockLotFlowDTO inventory : stockLotIndexDTOList) {
                        out.println(buildFlow(outOwnerResult.getOutOwnerCode(), outOwnerResult.getOutWarehouseCode(), inventory));// 转换并打印每一行
                    }
                    listPageInventoryFlow(flowParam, out, outOwnerResult);
                }
                //获取淘天上传数据
                File newFile = new File(filePath);
                TaoTianUploadFileBO uploadFileBO = new TaoTianUploadFileBO();
                uploadFileBO.setOwnerCode(ownerResult.getOwnerCode());
                uploadFileBO.setResourceCode(entityWarehouse.getWarehouseCode());
                uploadFileBO.setFileName(fileName);
                uploadFileBO.setFilePath(filePath);
                uploadFileBO.setContentLength(newFile.length());
                uploadFileBO.setContentMd5(MD5V2Utils.getFileMD5Base64(newFile));
                uploadFileBO.setOrderType("INV_LOG_IN_WMS");
                getTTUploadInfo(uploadFileBO);
                //上传淘天
                updateFile(uploadFileBO, 0);
            } catch (BusinessException e) {
                backWarning(e.getMessage());
            } catch (Exception e) {
                log.error("TaoTianInventoryCallbackFacade.callBackInventoryLogFlow======上传淘天库存流水异常=====ex:", e);
                backWarning("上传淘天库存流水异常");
            }
        }
    }

    private void listPageInventoryFlow(TaoTianStockLotFlowParam flowParam, PrintWriter out, OutOwnerResult outOwnerResult) {
        flowParam.setCurrentPage(flowParam.getCurrentPage() + 1);
        Result<PageVO<TaoTianStockLotFlowDTO>> pageVOResult = wsmGoodsBatchInventoryManager.pageListTaoTianInventoryFlow(flowParam);
        List<TaoTianStockLotFlowDTO> dataList = pageVOResult.getData().getDataList();
        if (dataList.size() > 0) {
            for (TaoTianStockLotFlowDTO inventory : dataList) {
                out.println(buildFlow(outOwnerResult.getOutOwnerCode(), outOwnerResult.getOutWarehouseCode(), inventory));// 转换并打印每一行
            }
            listPageInventoryFlow(flowParam, out, outOwnerResult);
        }
    }

    private String buildFlow(String outOwnerCode, String outWarehouseCode, TaoTianStockLotFlowDTO inventory) {
        StringBuilder sb = new StringBuilder();
        sb.append(outOwnerCode).append(",");
        sb.append(outWarehouseCode).append(",");
        sb.append(StringUtils.isNotBlank(inventory.getItemCode()) ? inventory.getItemCode(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getInventoryType()) ? inventory.getInventoryType(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getBatchCode()) ? inventory.getBatchCode(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getOrderCode()) ? inventory.getOrderCode(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getOrderLineNo()) ? inventory.getOrderLineNo(): "").append(",");
        sb.append(Objects.nonNull(inventory.getChangeQuantity()) ? inventory.getChangeQuantity(): "").append(",");
        sb.append(Objects.nonNull(inventory.getChangeLockQuantity()) ? inventory.getChangeLockQuantity(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getOperateTime()) ? inventory.getOperateTime(): "").append(",");
        sb.append(StringUtils.isNotBlank(inventory.getOrderType()) ? inventory.getOrderType(): "");
        return sb.toString();
    }

    /**
     * 获取上传淘天需要的信息
     *
     * @param uploadFileBO
     */
    private void getTTUploadInfo(TaoTianUploadFileBO uploadFileBO) {
        uploadFileBO.setRequestId(Md5Utils.md5("requestId" + uploadFileBO.getFileName()));
        uploadFileBO.setContentType("application/octet-stream");

        Map<String, Object> backMap = new HashMap<>();
        backMap.put("requestId", uploadFileBO.getRequestId());//调用方生成的请求ID, 用于唯一标识一次上传操作
        backMap.put("ownerCode", uploadFileBO.getOwnerCode());
        backMap.put("resourceCode", uploadFileBO.getResourceCode());//使用仓编码
        backMap.put("resourceType", "WAREHOUSE");//资源类型(WAREHOUSE=仓 、 DELIVERY=配)

        Map<String, Object> bizContextMap = new HashMap<>();
        bizContextMap.put("orderType", uploadFileBO.getOrderType());//文件关联的上下文作业单类型(orderCode有值时必填)
        bizContextMap.put("operateTime", DateUtils.defaultFormat(new Date().getTime()) + " 00:00:00");
        backMap.put("bizContext", bizContextMap);

        Map<String, Object> fileMetaMap = new HashMap<>();
        fileMetaMap.put("contentLength", uploadFileBO.getContentLength());//待上传文件字节数大小,单位Byte
        fileMetaMap.put("contentType", uploadFileBO.getContentType());//待上传文件的MIME类型
        fileMetaMap.put("contentMd5", uploadFileBO.getContentMd5());//待上传文件MD5摘要
        fileMetaMap.put("fileName", uploadFileBO.getFileName());
        backMap.put("fileMeta", fileMetaMap);
        backMap.put("tenantId", SimpleTenantHelper.getTenantId());

        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("bizData", JSON.toJSONString(backMap));
        requestMap.put("method", "taobao.file.upload.apply");

        String resp = postForm(requestMap, inventoryNacosConfig.getV3CallbackUrl());
        log.info("[TaoTianInventoryCallbackFacade-getTTUploadInfo]===============================ownerCode:{},奇门回传返回值:{}", uploadFileBO.getOrderCode(), resp);
        JSONObject jsonObject = JSON.parseObject(resp);
        Boolean success = jsonObject.getBoolean("success");
        JSONObject result = jsonObject.getJSONObject("result");
        if (!success) {
            throw new BusinessException(uploadFileBO.getOwnerCode() + ":查询淘天上传文件信息失败:" + result.getString("errorMessage"));
        }
        String signedUrl = result.getString("signedUrl");
        String token = result.getString("token");

        uploadFileBO.setSignedUrl(signedUrl);
        uploadFileBO.setToken(token);
    }

    /**
     * 上传文件到淘天
     */
    private void updateFile(TaoTianUploadFileBO uploadFileBO, int count) {
        try {
            if (count > 5) {//重试次数
                backWarning(uploadFileBO.getFileName() + ",上传淘天文件失败,已重试5次!");
                return;
            }
            String appKey = inventoryNacosConfig.getTaotianAppKeyMap().get("appkey_" + SimpleTenantHelper.getTenantId());
            if (StringUtils.isBlank(appKey)) {
                appKey = inventoryNacosConfig.getTaotianAppKey();
            }
            // 基于appKey生成的32位全大写MD5值
            String appKeyMd5 = BinaryUtil.encodeMD5(appKey.getBytes(StandardCharsets.UTF_8));

            CloseableHttpClient httpClient = null;
            CloseableHttpResponse response = null;
            try {
                HttpPut put = new HttpPut(uploadFileBO.getSignedUrl());
                HttpEntity entity = new FileEntity(new File(uploadFileBO.getFilePath()));
                put.setEntity(entity);

                // 参与签名验签的HttpHeaders
                put.addHeader("Content-MD5", uploadFileBO.getContentMd5());
                put.addHeader("Content-Type", uploadFileBO.getContentType());
                put.addHeader("x-oss-meta-requestId", uploadFileBO.getRequestId());
                put.addHeader("x-oss-meta-appKey", appKeyMd5);
                put.addHeader("x-oss-meta-token", uploadFileBO.getToken());

                httpClient = HttpClients.createDefault();
                response = httpClient.execute(put);
                log.info(uploadFileBO.getFileName() + ",上传淘天文件返回值:" + JSON.toJSONString(response));
                if(!Objects.equals(response.getStatusLine().getStatusCode(), 200)){
                    if (Objects.equals(response.getStatusLine().getStatusCode(), 400)) {
                        updateFile(uploadFileBO, ++count); //重试
                    } else {
                        backWarning(uploadFileBO.getFileName() + ",上传淘天文件失败!");
                    }
                }
            } finally {
                if (Objects.nonNull(response)) {
                    response.close();
                }
                if (Objects.nonNull(httpClient)) {
                    httpClient.close();
                }
            }
        } catch (Exception e) {
            log.error("上传淘天文件异常:", e);
            backWarning("上传淘天文件异常");
        }
    }

    /**
     * 发送消息到企业微信
     *
     * @param message
     */
    private void backWarning(String message){
        try {
            String msg = "{\"msgtype\": \"text\",\"text\": {\"content\": \"" + message + "\",\"mentioned_mobile_list\":" + Arrays.toString(inventoryNacosConfig.getMentionedMobile().split(",")) + "}}";
            HttpRequestUtils.postJson(msg, inventoryNacosConfig.getRobotUrl(), "淘天库存文件上传操作");
        } catch (Exception e) {
            log.error("[TaoTianInventoryCallbackFacade-backWarning]=========淘天库存文件上传操作=========message:{},ex:", message, e);
        }
    }

    private String buildFilePath(Date defDate, String fileName) {
        String filePath = "/nfs/" + DateUtils.defaultFormat(defDate.getTime());
        File dir = new File(filePath);
        if (!dir.exists()) {
            boolean result = dir.mkdirs();
            if (!result) {
                throw new BusinessException("目录创建失败: " + dir.getPath());
            }
        }
        filePath = filePath + "/" + fileName;

        File file = new File(filePath);
        if (file.exists()) {
            boolean deleted = file.delete();
            if (!deleted) {
                log.error("无法删除文件：" + filePath);
            }
        }
        return filePath;
    }

    /**
     * 删除30天前的文件
     *
     * @param defDate
     */
    private void deleteFile(Date defDate) {
        try {
            String filePath = "/nfs/" + DateUtils.defaultFormat(DateUtils.beforNumDays(defDate, 30));
            deleteDir(new File(filePath));
        } catch (Exception e) {
            log.error("deleteFile删除历史文件异常ex ", e);
        }
    }

    private boolean deleteDir(File dir) {
        if (dir.isDirectory()) {
            String[] children = dir.list();
            for (int i = 0; i < children.length; i++) {
                boolean success = deleteDir(new File(dir, children[i]));
                if (!success) {
                    return false;
                }
            }
        }
        return dir.delete();
    }


}
