package com.danding.business.server.ares.excel;

import cn.hutool.extra.spring.SpringUtil;
import com.danding.business.common.ares.enums.ExcelExportEnum;
import com.danding.business.common.ares.excel.AbstractExcelExportSingleSheetServiceV2;
import com.danding.business.common.ares.excel.ExcelExportHelper;
import com.danding.business.common.ares.excel.IExcelExportFacade;
import com.danding.business.common.ares.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 * Excel 导出服务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Slf4j
@DubboService(group = "${excel.export.dubbo.group}",timeout = 600000)
public class IExcelExportFacadeImpl<T extends com.danding.component.common.api.common.page.Page> implements IExcelExportFacade<T> {
    @Autowired
    private ExcelExportHelper excelExportHelper;

    @Override
    public void asyncExportTask(T queryParam, String uid, String funCode) {
        excelExportHelper.asyncExportTask(queryParam, uid, funCode);
    }

    @Override
    public void asyncPDFExportTask(T queryParam, String uid, String funCode) {

    }


}
