/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.danding.business.server.ares.inventory;

import com.alibaba.fastjson.JSONObject;
import com.danding.business.ares.inventory.api.GoodsBatchInventoryRPCServiceI;
import com.danding.business.ares.inventory.dto.GoodsBatchInventoryParam;
import com.danding.business.ares.inventory.dto.GoodsBatchInventoryQry;
import com.danding.business.client.ares.inventory.facade.IGoodsBatchInventoryRpcFacade;
import com.danding.business.client.ares.inventory.facade.IGoodsInventoryFacade;
import com.danding.business.client.ares.inventory.facade.IGoodsInventoryRpcFacade;
import com.danding.business.client.ares.inventory.message.InventoryProcessMessage;
import com.danding.business.client.ares.inventory.param.GoodsBatchInventoryRpcQuery;
import com.danding.business.client.ares.inventory.param.GoodsInventoryQueryParam;
import com.danding.business.client.ares.inventory.param.GoodsInventoryRpcAdd;
import com.danding.business.common.ares.enums.HeadEnum;
import com.danding.business.common.ares.utils.GenerateUtils;
import com.danding.business.server.ares.inventory.listener.handler.InventoryProcessor;
import com.danding.business.server.ares.inventory.param.WsmStockQueryParam;
import com.danding.business.server.ares.inventory.remote.wsm.RemoteStockOperationFacade;
import com.danding.business.server.ares.inventory.remote.wsm.RemoteStockQueryFacade;
import io.swagger.annotations.Api;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 功能描述: 启动之后的健康检查
 * 创建时间:  2020/8/24 10:02 上午
 *
 * <AUTHOR>
 */
@Controller
@Api(value = "启动之后的健康检查", tags = "启动之后的健康检查")
public class TestController {


    @GetMapping("/hello")
    @ResponseBody
    public String hello() {
        return "hello";
    }
    @DubboReference
    IGoodsInventoryFacade goodsInventoryFacade;

    @Resource
    RemoteStockQueryFacade remoteStockQueryFacade;
    @Resource
    IGoodsBatchInventoryRpcFacade goodsBatchInventoryRpcFacade;
    @Autowired
    private InventoryProcessor inventoryProcessor;
    @Resource
    GoodsBatchInventoryRPCServiceI goodsBatchInventoryRPCServiceI;
    @Autowired
    private RemoteStockOperationFacade remoteStockOperationFacade;
    @DubboReference
    private IGoodsInventoryRpcFacade goodsInventoryRpcFacade;
    /**
     * 测试入口
     */
    @PostMapping("/test3")
    @GetMapping ("/test3")
    @ResponseBody
    public Object test(@RequestBody JSONObject params){
        int type = params.getInteger("type");
        JSONObject jsonObject =params.getJSONObject("data");
        switch (type) {

            case 4:
                System.out.println(GenerateUtils.generateOrderNo(HeadEnum.C));
                WsmStockQueryParam queryParam= params.getObject("data",WsmStockQueryParam.class);;
                remoteStockQueryFacade.queryStockLotList(queryParam);
            case 6:
                GoodsInventoryQueryParam goodsInventoryQueryParam = new GoodsInventoryQueryParam();
                goodsInventoryQueryParam.setPageSize(20);
                goodsInventoryQueryParam.setCurrentPage(1);
                goodsInventoryQueryParam.setUserId(100246L);
            return goodsInventoryFacade.selectGroupByList(goodsInventoryQueryParam);
            case 7:

                return goodsInventoryFacade.getGoodsInventoryById(jsonObject.getString("id"));
            case 106:
                InventoryProcessMessage message = params.getObject("data",InventoryProcessMessage.class);
                inventoryProcessor.processInventory(message);
            case 107:
              return   goodsBatchInventoryRpcFacade.getGoodsBatchInventory(params.getObject("data", GoodsBatchInventoryRpcQuery.class));
            case 108:
                return goodsBatchInventoryRPCServiceI.listGoodsBatchInventory(params.getObject("data", GoodsBatchInventoryParam.class));
            case 109:
                return goodsBatchInventoryRPCServiceI.searchGoodsBatchInventory(params.getObject("data", GoodsBatchInventoryQry.class));
            case 200:
                return remoteStockOperationFacade.wmsInventoryUnLock(jsonObject.getString("warehouseCode"), jsonObject.getString("ownerCode"),  jsonObject.getString("globalNo"));
            case 201:
                return goodsInventoryRpcFacade.optGoodsInventory(jsonObject.getJSONArray("list").toJavaList(GoodsInventoryRpcAdd.class));
            }

        return "Ok";
    }

    @GetMapping ("/test4")
    public Object test(@RequestParam( name = "wc") String warehouseCode,@RequestParam( name = "oc") String ownerCode,@RequestParam( name = "no") String globalNo ){
        return Boolean.valueOf(remoteStockOperationFacade.wmsInventoryUnLock(warehouseCode,ownerCode, globalNo));
    }


}
