package com.danding.business.server.ares.inventory.param;

import com.danding.business.client.ares.inventory.param.PropertyGroupType;
import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class WsmStockQueryParam extends Page {


    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    @ApiModelProperty(value = "货主编码")
    private String cargoCode;
    private String skuCode;
    private List<String> skuCodeList;
    private String skuQuality;
    private Long expireTimeBegin;
    private Long expireTimeEnd;
    private String skuLotNo;
    private String externalSkuLotNo;
    private String externalSkuLotNoLike;
    private List<String> skuLotNoList;
    private PropertyGroupType group;
    /**
     *"销毁出库 销毁出库 true"
     */
    private Boolean isDestroy;

    /**
     * 入库关联单号
     */
    private String entryRecordNo;

    private Long productionTimeBegin;
    private Long productionTimeEnd;
    private Map<String, String> sortParamMap;
    /**
     * 目前支持: 货品名称, sku, 批次, 效期
     */
    private String likeQuery;

}
