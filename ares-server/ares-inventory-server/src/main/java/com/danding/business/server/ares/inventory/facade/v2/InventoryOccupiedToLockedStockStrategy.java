package com.danding.business.server.ares.inventory.facade.v2;

import com.danding.business.client.ares.inventory.param.InventoryOperationParam;
import com.danding.business.common.ares.context.AresContext;
import com.danding.business.common.ares.enums.inventory.InventoryBusinessType;
import com.danding.business.common.ares.enums.inventory.InventoryType;
import com.danding.business.common.ares.enums.inventory.InventoryUpdateType;
import com.danding.business.server.ares.inventory.BO.GoodsBatchInventoryBO;
import com.danding.business.server.ares.inventory.BO.GoodsBatchInventoryLogsBO;
import com.danding.business.server.ares.inventory.BO.GoodsInventoryBO;
import com.danding.business.server.ares.inventory.BO.GoodsInventoryLogsBO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.soul.client.common.exception.BusinessException;
import com.danding.soul.client.common.result.RpcResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.danding.business.core.ares.inventory.service.wrapper.GoodsInventoryWrapper.EMPTY_WAREHOUSE_CODE;

/**
 * 占用转锁定库存操作（减少占用、可用；增加已售、预售、已用、锁定）
 * 对应预售单转销售单的预定单关联销售单操作
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/26 10:12
 */
@Slf4j
@Component
@Deprecated
public class InventoryOccupiedToLockedStockStrategy extends InventoryStrategy {

    @Override
    protected List<InventoryBusinessType> setSupportBusinessTypeList() {
        List<InventoryBusinessType> supportBusinessTypeList = Lists.newArrayList();
        supportBusinessTypeList.add(InventoryBusinessType.YDD_XSD_RELATED);
        return supportBusinessTypeList;
    }

    @Override
    protected RpcResult dbHandle(List<InventoryOperationParam> paramList, boolean openHosting, boolean batchManageOpen) {
        if (openHosting) {
            // 开启托管则不需处理占用转锁定直接返回
            log.debug("[==============InventoryOccupiedToLockedStockStrategy.dbHandle==============] 不是【非托管而且强控成本】无需操作预售直接返回。");
            return RpcResult.success();
        }
        Assert.notEmpty(paramList, "待处理参数错误：库存操作参数列表不能为空！");
        // 一次操作只有一个业务类型：获取第一个即可
        InventoryBusinessType businessType = paramList.get(0).getRelatedBusinessType();
        return handleInventory(paramList, openHosting, batchManageOpen);
    }

    /**
     * 在途核增操作（增加可售、预售和在途库存）
     * 支持业务类型：采购审核通过
     *
     * @param paramList
     * @param openHosting
     * @param batchManageOpen
     * @return
     */
    private RpcResult handleInventory(List<InventoryOperationParam> paramList, boolean openHosting, boolean batchManageOpen) {
        // 一次操作只有一个业务类型+一个仓库+更新类型：获取第一个即可
        Long userId = paramList.get(0).getUserId();
        InventoryUpdateType updateType = paramList.get(0).getUpdateType();
        String logicWarehouseCode = paramList.get(0).getLogicWarehouseCode();
        List<String> logicWarehouseCodeList = Lists.newArrayList();
        logicWarehouseCodeList.add(logicWarehouseCode);
        logicWarehouseCodeList.add(EMPTY_WAREHOUSE_CODE);
        Set<String> goodsCodeList = paramList.stream().map(InventoryOperationParam::getGoodsCode).collect(Collectors.toSet());
        // 查询该货品库存信息是否存在，存在更新，否则新增
        List<GoodsInventoryBO> dbGoodsInventoryList = getGoodsInventoryList(logicWarehouseCodeList, new ArrayList<>(goodsCodeList), userId);
        Map<String, GoodsInventoryBO> inventoryBOMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(dbGoodsInventoryList)) {
            inventoryBOMap = dbGoodsInventoryList.stream().collect(Collectors.toMap(item -> item.getLogicWarehouseCode() + item.getGoodsCode(), o -> o, (k1, k2) -> k1));
        }
        List<GoodsInventoryBO> resultGoodsInventoryBOListForUpdate = Lists.newArrayList();
        List<GoodsInventoryLogsBO> resultGoodsInventoryLogsBOList = Lists.newArrayList();
        // 对paramList进行sku+正次品+是否超件 group by:注意GOURPBY会修改原paramListTemp的值，所以要先复制原list，不要用paramList直接操作
        List<InventoryOperationParam> paramListTemp = BeanUtils.copyProperties(paramList, InventoryOperationParam.class);
        Map<String, InventoryOperationParam> groupByGoodsCodeParam = paramListTemp.stream()
                .collect(Collectors.toMap(item -> item.getGoodsCode() + item.getInventoryType(), o -> o, (k1, k2) -> {
                    k1.setUpdateNum(k1.getUpdateNum() + k2.getUpdateNum());
                    k1.setPlanUpdateNum(k1.getPlanUpdateNum() + k2.getPlanUpdateNum());
                    if (null != k1.getBatchCode() && null != k2.getBatchCode()) {
                        k1.setBatchCode(k1.getBatchCode() + "," + k2.getBatchCode());
                    } else if (null != k2.getBatchCode()) {
                        k1.setBatchCode(k2.getBatchCode());
                    }
                    if (null != k1.getInternalBatchCode() && null != k2.getInternalBatchCode()) {
                        k1.setInternalBatchCode(k1.getInternalBatchCode() + "," + k2.getInternalBatchCode());
                    } else if (null != k2.getInternalBatchCode()) {
                        k1.setInternalBatchCode(k2.getInternalBatchCode());
                    }
                    return k1;
                }));
        if (!CollectionUtils.isEmpty(groupByGoodsCodeParam)) {
            Collection<InventoryOperationParam> groupByOperationParamList = groupByGoodsCodeParam.values();
            for (InventoryOperationParam inventoryParamBO : groupByOperationParamList) {
                // 查询该货品库存信息是否存在，存在更新，否则新增
                GoodsInventoryBO dbGoodsInventory = inventoryBOMap.get(inventoryParamBO.getLogicWarehouseCode() + inventoryParamBO.getGoodsCode());
                // 查询无仓库库存
                GoodsInventoryBO dbEmptyWarehouseGoodsInventory = inventoryBOMap.get(EMPTY_WAREHOUSE_CODE + inventoryParamBO.getGoodsCode());

                if (dbGoodsInventory == null || dbEmptyWarehouseGoodsInventory == null) {
                    // 占用转锁定的时候应该已经有在途和预售库存,也有实际库存
                    throw new BusinessException("该货品库存不存在！goodsCode=" + inventoryParamBO.getGoodsCode() + ";SKU=" + inventoryParamBO.getSku());
                } else {
                    if (InventoryType.QUALITY.equals(inventoryParamBO.getInventoryType())) {
                        // 更新
                        int updatedAvailableNum = dbGoodsInventory.getAvailableNum() - inventoryParamBO.getUpdateNum();
                        if (updatedAvailableNum < 0) {
                            throw new BusinessException(AresContext.INSUFFICIENT_INVENTORY, "该货品可用库存不足，无法锁定！goodsCode=" + inventoryParamBO.getGoodsCode() + ";SKU=" + inventoryParamBO.getSku());
                        }
                        // 可售更新
                        dbGoodsInventory.setForsaleNum(dbGoodsInventory.getForsaleNum() - inventoryParamBO.getUpdateNum());
                        dbGoodsInventory.setAvailableNum(updatedAvailableNum);
                    } else {
                        int updateUnavailableNum = dbGoodsInventory.getUnavailableNum() - inventoryParamBO.getUpdateNum();
                        if (updateUnavailableNum < 0) {
                            throw new BusinessException(AresContext.INSUFFICIENT_INVENTORY, "该货品次品库存不足，无法锁定！goodsCode=" + inventoryParamBO.getGoodsCode() + ";SKU=" + inventoryParamBO.getSku());
                        }
                        // 次品更新
                        dbGoodsInventory.setUnavailableNum(updateUnavailableNum);
                        dbGoodsInventory.setDefectiveNum(dbGoodsInventory.getDefectiveNum() - inventoryParamBO.getUpdateNum());
                    }
                    dbGoodsInventory.setSoldNum(dbGoodsInventory.getSoldNum() + inventoryParamBO.getUpdateNum());
                    dbGoodsInventory.setUsedNum(dbGoodsInventory.getUsedNum() + inventoryParamBO.getUpdateNum());
                    dbGoodsInventory.setLockedNum(dbGoodsInventory.getLockedNum() + inventoryParamBO.getUpdateNum());
                    // update 仓库 库存信息
                    if (!resultGoodsInventoryBOListForUpdate.contains(dbGoodsInventory)) {
                        // 正品和次品处理的是同一条货品库存记录，这里去重一下：同一个goodsCode的正品和次品处理只update一条记录
                        resultGoodsInventoryBOListForUpdate.add(dbGoodsInventory);
                    }
                    // update 空库 库存信息
                    dbEmptyWarehouseGoodsInventory.setForsaleNum(dbEmptyWarehouseGoodsInventory.getForsaleNum() + inventoryParamBO.getUpdateNum());
                    dbEmptyWarehouseGoodsInventory.setPresaleNum(dbEmptyWarehouseGoodsInventory.getPresaleNum() + inventoryParamBO.getUpdateNum());
                    dbEmptyWarehouseGoodsInventory.setOccupiedNum(dbEmptyWarehouseGoodsInventory.getOccupiedNum() - inventoryParamBO.getUpdateNum());
                    if (!resultGoodsInventoryBOListForUpdate.contains(dbEmptyWarehouseGoodsInventory)) {
                        // 空仓在途库存只有一条记录
                        resultGoodsInventoryBOListForUpdate.add(dbEmptyWarehouseGoodsInventory);
                    }
                }
                resultGoodsInventoryLogsBOList.add(generateGoodsInventoryLogsBO(dbGoodsInventory, inventoryParamBO));
            }
        }
        if (batchManageOpen) {
            // 开启效期管理的仓库执行
            RpcResult result = handleBatchInventory(paramList, openHosting);
            if (!Objects.equals(AresContext.ARES_SUCCESS, result.getCode())) {
                // 失败则退出,成功则继续执行
                return result;
            }
        }
        // 把批次库存调整到先执行，主要是db验证阶段该抛出的错误就会在db操作之前抛出，提搞分布式事务回滚效率
        boolean isSuccess = saveOrUpdateGoodsInventorys(resultGoodsInventoryBOListForUpdate, null);
        if (isSuccess) {
            // 必须执行完货品库存更新后才能生成正确的货品库存generateGroupbyLogs操作日志
            isSuccess = saveGoodsInventoryLogs(resultGoodsInventoryLogsBOList, generateGroupbyLogs(paramList, groupByGoodsCodeParam, updateType));
        }
        return RpcResult.isSuccess(isSuccess, "货品库存更新失败！");
    }

    /**
     * 批次库存+日志处理
     * 托管和非托管都支持
     *
     * @param paramList
     * @return
     */
    private RpcResult handleBatchInventory(List<InventoryOperationParam> paramList, boolean openHosting) {
        // String relatedBusinessNo = paramList.get(0).getRelatedBusinessNo();
        // log.debug("===============handleBatchInventory=========== start.业务单号：{}", relatedBusinessNo);
        InventoryBusinessType businessType = paramList.get(0).getRelatedBusinessType();
        if (InventoryBusinessType.XSD_CHECKED_GOODS.equals(businessType)
                || InventoryBusinessType.DBCK_CHECKED_GOODS.equals(businessType)) {
            // 销售单审核通过[货品]：仅锁定货品，这里无操作
            return RpcResult.success();
        } else {
            // log.debug("===============handleBatchInventory=========== query goodsBatchInventory data ...业务单号：{}", relatedBusinessNo);
            List<GoodsBatchInventoryBO> dbGoodsBatchInventoryList = getGoodsBatchInventoryList(paramList, null, false);
            Map<String, GoodsBatchInventoryBO> batchInventoryBOMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(dbGoodsBatchInventoryList)) {
                batchInventoryBOMap = dbGoodsBatchInventoryList.stream()
                        .collect(Collectors.toMap(item -> item.getGoodsCode() + item.getInternalBatchCode() + item.getInventoryType(), o -> o, (k1, k2) -> k1));
            }
            // log.debug("===============handleBatchInventory=========== query goodsBatchInventory data size:{}业务单号：{}", dbGoodsBatchInventoryList.size(), relatedBusinessNo);
            // 对paramList进行sku+正次品+批次号 group by
            List<InventoryOperationParam> paramListTemp = BeanUtils.copyProperties(paramList, InventoryOperationParam.class);
            Map<String, InventoryOperationParam> groupByGoodsCodeParam = paramListTemp.stream()
                    .collect(Collectors.toMap(item -> item.getGoodsCode() + item.getInternalBatchCode() + item.getInventoryType(), o -> o, (k1, k2) -> {
                        k1.setUpdateNum(k1.getUpdateNum() + k2.getUpdateNum());
                        k1.setPlanUpdateNum(k1.getPlanUpdateNum() + k2.getPlanUpdateNum());
                        return k1;
                    }));
            // log.debug("===============handleBatchInventory=========== generate goodsBatchInventory data and logs.业务单号：{}", relatedBusinessNo);
            List<GoodsBatchInventoryBO> goodsBatchInventoryBOListForUpdate = Lists.newArrayList();
            List<GoodsBatchInventoryLogsBO> goodsBatchInventoryLogsBOList = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(groupByGoodsCodeParam)) {
                Collection<InventoryOperationParam> groupByOperationParamList = groupByGoodsCodeParam.values();
                for (InventoryOperationParam inventoryParamBO : groupByOperationParamList) {
                    GoodsBatchInventoryBO dbGoodsBatchInventory = batchInventoryBOMap.get(inventoryParamBO.getGoodsCode() + inventoryParamBO.getInternalBatchCode() + inventoryParamBO.getInventoryType());
                    dbGoodsBatchInventory = generateBatchInventoryBO(dbGoodsBatchInventory, inventoryParamBO);
                    goodsBatchInventoryBOListForUpdate.add(dbGoodsBatchInventory);
                    goodsBatchInventoryLogsBOList.add(generateBatchLogs(dbGoodsBatchInventory, inventoryParamBO));
                }
            }
            // log.debug("===============handleBatchInventory=========== query update goodsBatchInventory and insert logs ...业务单号：{}", relatedBusinessNo);
            boolean isSuccess = saveOrUpdateGoodsBatchInventorys(goodsBatchInventoryBOListForUpdate, null);
            if (isSuccess) {
                isSuccess = saveGoodsBatchInventoryLogs(goodsBatchInventoryLogsBOList);
            }
            return RpcResult.isSuccess(isSuccess, "货品批次库存更新失败！");
        }
    }

    /**
     * 处理批次库存
     *
     * @param dbGoodsBatchInventory
     * @param inventoryParamBO
     * @return
     */
    private GoodsBatchInventoryBO generateBatchInventoryBO(GoodsBatchInventoryBO dbGoodsBatchInventory, InventoryOperationParam inventoryParamBO) {
        if (dbGoodsBatchInventory == null) {
            throw new BusinessException(AresContext.INSUFFICIENT_INVENTORY, "该货品批次库存不存在(请检查批次号是否正确)！goodsCode=" + inventoryParamBO.getGoodsCode() + ";SKU=" + inventoryParamBO.getSku() + ";internalBatchCode=" + inventoryParamBO.getInternalBatchCode());
        } else {
            if (!inventoryParamBO.getInventoryType().equals(dbGoodsBatchInventory.getInventoryType())) {
                throw new BusinessException("库存类型参数错误（与该批次数据库库存类型不一致）！goodsCode=" + inventoryParamBO.getGoodsCode() + ";SKU=" + inventoryParamBO.getSku() + ";internalBatchCode=" + inventoryParamBO.getInternalBatchCode());
            }
            // 有效期的货品-过期的批次库存-直接报错不允许出
//            if (GoodsBatchManagement.YES.equals(dbGoodsBatchInventory.getBatchManagement())) {
//                Date expiredDate = dbGoodsBatchInventory.getExpireDate();
//                if (expiredDate.before(new Date())) {
//                    throw new BusinessException(AresContext.INSUFFICIENT_INVENTORY, "该货品批次库存已过期！goodsCode=" + inventoryParamBO.getGoodsCode() + ";SKU=" + inventoryParamBO.getSku() + ";internalBatchCode=" + inventoryParamBO.getInternalBatchCode());
//                }
//            }
            // 更新：增加锁定库存，减少可用库存
            int availableNumAfter = dbGoodsBatchInventory.getAvailableNum() - inventoryParamBO.getUpdateNum();
            if (availableNumAfter < 0) {
                throw new BusinessException(AresContext.INSUFFICIENT_INVENTORY, "该货品批次库存不足！goodsCode=" + inventoryParamBO.getGoodsCode() + ";SKU=" + inventoryParamBO.getSku() + ";internalBatchCode=" + inventoryParamBO.getInternalBatchCode());
            }
            dbGoodsBatchInventory.setLockedNum(dbGoodsBatchInventory.getLockedNum() + inventoryParamBO.getUpdateNum());
            dbGoodsBatchInventory.setAvailableNum(availableNumAfter);
            return dbGoodsBatchInventory;
        }
    }
}
