package com.danding.business.server.ares.inventory.facade.v2.strategy;

import com.danding.business.client.ares.inventory.facade.IDimensionInventoryFacade;
import com.danding.business.client.ares.inventory.param.DimensionInventoryAddParam;
import com.danding.business.client.ares.inventory.param.DimensionInventoryQueryParam;
import com.danding.business.client.ares.inventory.param.InventoryOperationParam;
import com.danding.business.client.ares.inventory.result.DimensionInventoryResult;
import com.danding.business.client.ares.logicwarehouse.result.LogicWarehouseResult;
import com.danding.business.common.ares.context.AresContext;
import com.danding.business.common.ares.enums.inventory.InventoryBusinessType;
import com.danding.business.common.ares.enums.inventory.InventoryType;
import com.danding.business.server.ares.inventory.BO.DimensionInventoryLogsBO;
import com.danding.business.server.ares.inventory.facade.v2.InventoryStrategy;
import com.danding.business.server.ares.inventory.manager.DimensionInventoryLogManager;
import com.danding.business.server.ares.inventory.remote.RemoteLogicWarehouseFacade;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.soul.client.common.exception.BusinessException;
import com.danding.soul.client.common.result.RpcResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 圈货库存 出库核扣策略
 * 圈货- 占用-
 * C单出库圈货
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/1 9:46
 */
@Slf4j
@Component
public class InventoryCircleOutStockStrategyV3 extends InventoryStrategy {

    @Autowired
    private IDimensionInventoryFacade dimensionInventoryFacade;
    @Autowired
    private RemoteLogicWarehouseFacade remoteLogicWarehouseFacade;
    @Autowired
    private DimensionInventoryLogManager dimensionInventoryLogManager;
    @Override
    protected List<InventoryBusinessType> setSupportBusinessTypeList() {
        List<InventoryBusinessType> supportBusinessTypeList = Lists.newArrayList();

        supportBusinessTypeList.add(InventoryBusinessType.RETURN_FINISHED);
        supportBusinessTypeList.add(InventoryBusinessType.CORNERING_RETURN_FINISHED);
        supportBusinessTypeList.add(InventoryBusinessType.CIRCLE_ORDER_OUT_FINISHED);

        return supportBusinessTypeList;
    }

    @Override
    protected RpcResult dbHandle(List<InventoryOperationParam> paramList, boolean openHosting, boolean batchManageOpen) {
        Assert.notEmpty(paramList, "待处理参数错误：库存操作参数列表不能为空！");
        return handleInventory(paramList, openHosting, batchManageOpen);
    }

    /**
     * 处理库存和日志
     *
     * @param paramList
     * @return
     */
    private RpcResult handleInventory(List<InventoryOperationParam> paramList, boolean openHosting, boolean batchManageOpen) {
        // String relatedBusinessNo = paramList.get(0).getRelatedBusinessNo();
        // log.debug("===============handleInventory======= start.业务单号：{}", relatedBusinessNo);
        // 一次操作只有一个业务类型+一个仓库+更新类型：获取第一个即可
        InventoryBusinessType businessType = paramList.get(0).getRelatedBusinessType();
        Long userId = paramList.get(0).getUserId();


        String logicWarehouseCode = paramList.get(0).getLogicWarehouseCode();
        Set<String> goodsCodeList = paramList.stream().map(InventoryOperationParam::getGoodsCode).collect(Collectors.toSet());
        List<String> circleDimensionNoList = paramList.stream().map(InventoryOperationParam::getCircleDimensionNo).collect(Collectors.toList());
        DimensionInventoryQueryParam dimensionInventoryQueryParam = new DimensionInventoryQueryParam();
        dimensionInventoryQueryParam.setUserId(userId);
        dimensionInventoryQueryParam.setCircleDimensionNoList(circleDimensionNoList);
        dimensionInventoryQueryParam.setGoodsCodeList(goodsCodeList.stream().collect(Collectors.toList()));
        dimensionInventoryQueryParam.setLogicWarehouseCode(logicWarehouseCode);
        List<DimensionInventoryResult> dimensionInventoryResultList = dimensionInventoryFacade.listByQueryParam(dimensionInventoryQueryParam);


        Map<String, DimensionInventoryResult> inventoryBOMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(dimensionInventoryResultList)) {
            inventoryBOMap = dimensionInventoryResultList.stream().collect(Collectors.toMap(m -> m.getLogicWarehouseCode() + "_" + m.getCircleDimensionNo() + "_" + m.getGoodsCode(), o -> o, (k1, k2) -> k1));
        }
        List<DimensionInventoryAddParam> resultGoodsInventoryBOListForUpdate = Lists.newArrayList();
        List<DimensionInventoryLogsBO> dimensionInventoryLogsBOList = Lists.newArrayList();
        // 对paramList进行sku+正次品 group by:注意GOURPBY会修改原paramListTemp的值，所以要先复制原list，不要用paramList直接操作
        List<InventoryOperationParam> paramListTemp = BeanUtils.copyProperties(paramList, InventoryOperationParam.class);
        Map<String, InventoryOperationParam> groupByGoodsCodeParam = paramListTemp.stream()
                .collect(Collectors.toMap(item -> item.getGoodsCode() + item.getCircleDimensionNo() + item.getInventoryType(), o -> o, (k1, k2) -> {
                    k1.setUpdateNum(k1.getUpdateNum() + k2.getUpdateNum());
                    k1.setPlanUpdateNum(k1.getPlanUpdateNum() + k2.getPlanUpdateNum());
                    return k1;
                }));
        // log.debug("===============handleInventory======= generate update data and logs ==== groupByOperationParamList size={}.业务单号：{}", groupByGoodsCodeParam.size(), relatedBusinessNo);
        if (!CollectionUtils.isEmpty(groupByGoodsCodeParam)) {
            Collection<InventoryOperationParam> groupByOperationParamList = groupByGoodsCodeParam.values();
            for (InventoryOperationParam inventoryParamBO : groupByOperationParamList) {
                DimensionInventoryResult dimensionInventoryResult = inventoryBOMap.get(inventoryParamBO.getLogicWarehouseCode() + "_" + inventoryParamBO.getCircleDimensionNo() + "_" + inventoryParamBO.getGoodsCode());
                DimensionInventoryAddParam dbGoodsInventory = generateDimensionInventoryAddParam(dimensionInventoryResult, inventoryParamBO);
                if (!resultGoodsInventoryBOListForUpdate.contains(dbGoodsInventory)) {
                    resultGoodsInventoryBOListForUpdate.add(dbGoodsInventory);
                }
                dimensionInventoryLogsBOList.add(generateDimensionInventoryLogsBO(dbGoodsInventory, inventoryParamBO));

            }
        }
        boolean isSuccess = dimensionInventoryFacade.updateListById(resultGoodsInventoryBOListForUpdate);
        if (isSuccess){
            isSuccess= dimensionInventoryLogManager.addList(dimensionInventoryLogsBOList);
        }
        return RpcResult.isSuccess(isSuccess, "货品圈货更新失败！");
    }


    /**
     * 处理库存
     *
     * @param dbDimensionInventory
     * @param inventoryParamBO
     * @return
     */
    private DimensionInventoryAddParam generateDimensionInventoryAddParam(DimensionInventoryResult dbDimensionInventory, InventoryOperationParam inventoryParamBO) {
        if (dbDimensionInventory == null) {
            throw new BusinessException(AresContext.INSUFFICIENT_INVENTORY, "该货品圈货库存不存在！goodsCode=" + inventoryParamBO.getGoodsCode() + ";logicWarehouseCode=" + inventoryParamBO.getLogicWarehouseCode() + ";circleDimensionNo=" + inventoryParamBO.getCircleDimensionNo());
        } else {
            //正品处理
            if (inventoryParamBO.getInventoryType().equals(InventoryType.QUALITY)) {
                int occupiedNumAfter = dbDimensionInventory.getOccupiedNum() - inventoryParamBO.getUpdateNum();
                if (occupiedNumAfter < 0) {
                    throw new BusinessException(AresContext.INSUFFICIENT_INVENTORY, "该货品正品圈货占用库存不足！goodsCode=" + inventoryParamBO.getGoodsCode() + ";logicWarehouseCode=" + inventoryParamBO.getLogicWarehouseCode() + ";CircleDimensionNo=" + inventoryParamBO.getCircleDimensionNo());
                }
                dbDimensionInventory.setOccupiedNum(occupiedNumAfter);
                int availableNumAfter = dbDimensionInventory.getCorneredNum() - inventoryParamBO.getUpdateNum();
                if (availableNumAfter < 0) {
                    throw new BusinessException(AresContext.INSUFFICIENT_INVENTORY, "该货品正品圈货可用库存不足！goodsCode=" + inventoryParamBO.getGoodsCode() + ";logicWarehouseCode=" + inventoryParamBO.getLogicWarehouseCode() + ";CircleDimensionNo=" + inventoryParamBO.getCircleDimensionNo());
                }
                dbDimensionInventory.setCorneredNum(availableNumAfter);
            }
            if (inventoryParamBO.getInventoryType().equals(InventoryType.DEFECTIVE)) {
                int occupiedNumAfter = dbDimensionInventory.getDefectiveOccupiedNum() - inventoryParamBO.getUpdateNum();
                if (occupiedNumAfter < 0) {
                    throw new BusinessException(AresContext.INSUFFICIENT_INVENTORY, "该货品次品圈货占用库存不足！goodsCode=" + inventoryParamBO.getGoodsCode() + ";logicWarehouseCode=" + inventoryParamBO.getLogicWarehouseCode() + ";CircleDimensionNo=" + inventoryParamBO.getCircleDimensionNo());
                }
                dbDimensionInventory.setDefectiveOccupiedNum(occupiedNumAfter);
                int availableNumAfter =dbDimensionInventory.getDefectiveCorneredNum() - inventoryParamBO.getUpdateNum();
                if (availableNumAfter < 0) {
                    throw new BusinessException(AresContext.INSUFFICIENT_INVENTORY, "该货品次品圈货可用库存不足！goodsCode=" + inventoryParamBO.getGoodsCode() + ";logicWarehouseCode=" + inventoryParamBO.getLogicWarehouseCode() + ";CircleDimensionNo=" + inventoryParamBO.getCircleDimensionNo());
                }
                dbDimensionInventory.setDefectiveCorneredNum(availableNumAfter);
            }
            return BeanUtils.copyProperties(dbDimensionInventory, DimensionInventoryAddParam.class);
        }


    }

}
