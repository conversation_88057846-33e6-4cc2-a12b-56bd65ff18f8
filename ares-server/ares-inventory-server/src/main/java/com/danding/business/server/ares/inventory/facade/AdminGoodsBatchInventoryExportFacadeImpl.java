package com.danding.business.server.ares.inventory.facade;

import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.goods.result.GoodsResult;
import com.danding.business.client.ares.inventory.facade.IGoodsBatchInventoryFacade;
import com.danding.business.client.ares.inventory.param.GoodsBatchInventoryQueryParam;
import com.danding.business.client.ares.inventory.result.GoodsBatchInventoryResult;
import com.danding.business.common.ares.excel.AbstractExcelExportSingleSheetServiceV2;
import com.danding.business.server.ares.inventory.excel.AdminGoodsBatchInventoryExportExcel;
import com.danding.business.server.ares.inventory.remote.RemoteGoodsFacade;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <p>
 * 货品批次库存(batch+sku+logic_warehouse) 服务实现类
 * </p>
 *
 * <AUTHOR> Xu
 * @since 2020-11-22
 */
@Component("adminGoodsBatchInventoryExcelService")
@Slf4j
public class AdminGoodsBatchInventoryExportFacadeImpl extends AbstractExcelExportSingleSheetServiceV2<AdminGoodsBatchInventoryExportExcel, GoodsBatchInventoryQueryParam>  {

    @Autowired
    private IGoodsBatchInventoryFacade goodsBatchInventoryFacade;
    @Autowired
    private RemoteGoodsFacade remoteGoodsFacade;


    @Override
    public void before(GoodsBatchInventoryQueryParam searchParam) {

    }

    @Override
    public List<AdminGoodsBatchInventoryExportExcel> getDataList(GoodsBatchInventoryQueryParam searchParam) {
        log.info("[export excel op:getDataList param={}]", JSON.toJSONString(searchParam));

        ListVO<GoodsBatchInventoryResult> pageList = goodsBatchInventoryFacade.pageListGoodsBatchInventory(searchParam);

        // 设置总页数
        this.setTotalPage(pageList.getPage().getTotalPage());
        List<GoodsBatchInventoryResult> list = pageList.getDataList();
        if (!CollectionUtils.isEmpty(list)) {
            Map<String, GoodsResult> goodsMap = remoteGoodsFacade.findGoodsMap(list.stream().map(GoodsBatchInventoryResult::getGoodsCode)
                    .distinct().collect(Collectors.toList()));

            return list.stream().map(goodsInventoryResult -> {
                AdminGoodsBatchInventoryExportExcel exportExcel = BeanUtils.copyProperties(goodsInventoryResult, AdminGoodsBatchInventoryExportExcel.class);
                if (goodsInventoryResult.getInstockTime() != null) {
                    exportExcel.setInstockDate(new Date(goodsInventoryResult.getInstockTime()));
                }
                exportExcel.setAvailableNum(goodsInventoryResult.getInstockNum()); //导出改为在库库存
                exportExcel.setGoodsLayering(parseGoodsLayering(goodsMap.get(goodsInventoryResult.getGoodsCode())));

                return exportExcel;
            }).collect(Collectors.toList());
        } else {
            return null;
        }
    }

    private String parseGoodsLayering(GoodsResult goodsResult) {
        if (goodsResult == null || goodsResult.getGoodsLayering() == null) {
            return StringUtils.EMPTY;
        }

        return goodsResult.getGoodsLayering().getDes();
    }
}
