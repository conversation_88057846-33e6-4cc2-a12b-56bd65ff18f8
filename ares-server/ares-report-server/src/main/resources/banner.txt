${AnsiColor.BLUE}                     .8.          8 888888888o.   8 8888888888     d888888o.             `8.`888b                 ,8' 8 8888888888   8 888888888o
${AnsiColor.BLUE}                    .888.         8 8888    `88.  8 8888         .`8888:' `88.            `8.`888b               ,8'  8 8888         8 8888    `88.
${AnsiColor.BLUE}                   :88888.        8 8888     `88  8 8888         8.`8888.   Y8             `8.`888b             ,8'   8 8888         8 8888     `88
${AnsiColor.BLUE}                  . `88888.       8 8888     ,88  8 8888         `8.`8888.                  `8.`888b     .b    ,8'    8 8888         8 8888     ,88
${AnsiColor.BLUE}                 .8. `88888.      8 8888.   ,88'  8 888888888888  `8.`8888.                  `8.`888b    88b  ,8'     8 888888888888 8 8888.   ,88'
${AnsiColor.BLUE}                .8`8. `88888.     8 888888888P'   8 8888           `8.`8888.                  `8.`888b .`888b,8'      8 8888         8 8888888888
${AnsiColor.BLUE}               .8' `8. `88888.    8 8888`8b       8 8888            `8.`8888.                  `8.`888b8.`8888'       8 8888         8 8888    `88.
${AnsiColor.BLUE}              .8'   `8. `88888.   8 8888 `8b.     8 8888        8b   `8.`8888.                  `8.`888`8.`88'        8 8888         8 8888      88
${AnsiColor.BLUE}             .888888888. `88888.  8 8888   `8b.   8 8888        `8b.  ;8.`8888                   `8.`8' `8,`'         8 8888         8 8888    ,88'
${AnsiColor.BLUE}            .8'       `8. `88888. 8 8888     `88. 8 888888888888 `Y8888P ,88P'                    `8.`   `8'          8 888888888888 8 888888888P

${AnsiColor.BLUE}:: Ares :: ${dubbo.application.name}:${AnsiColor.RED}${spring.profiles.active}${AnsiColor.BLUE} :: Running SpringBoot ${spring-boot.version} :: ${AnsiColor.BRIGHT_BLACK}
