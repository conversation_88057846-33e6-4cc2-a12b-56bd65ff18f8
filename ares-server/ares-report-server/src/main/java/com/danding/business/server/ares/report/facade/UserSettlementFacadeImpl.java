package com.danding.business.server.ares.report.facade;

import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.report.facade.IUserSettlementFacade;
import com.danding.business.client.ares.report.param.UserSettlementEditParam;
import com.danding.business.client.ares.report.param.UserSettlementParam;
import com.danding.business.client.ares.report.param.UserSettlementQueryParam;
import com.danding.business.client.ares.report.result.UserSettlementResult;
import com.danding.business.common.ares.BO.report.UserBillsBO;
import com.danding.business.common.ares.BO.report.UserSettlementBO;
import com.danding.business.common.ares.config.CurrencyConfig;
import com.danding.business.common.ares.excel.AbstractExcelExportServiceV2;
import com.danding.business.common.ares.excel.TwoListsResult;
import com.danding.business.server.ares.report.excel.UserSettlementBillsExportExcel;
import com.danding.business.server.ares.report.excel.UserSettlementExportExcel;
import com.danding.business.server.ares.report.manager.UserBillsManager;
import com.danding.business.server.ares.report.manager.UserSettlementManager;
import com.danding.component.common.api.common.file.FileDto;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.shardingsphere.transaction.annotation.ShardingTransactionType;
import org.apache.shardingsphere.transaction.core.TransactionType;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Valid;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户结算单 服务实现类
 * </p>
 *
 * <AUTHOR> Xu
 * @since 2020-11-17
 */
@Slf4j
@DubboService
@Component("userSettlementExcelService")
public class UserSettlementFacadeImpl extends AbstractExcelExportServiceV2<UserSettlementExportExcel, UserSettlementBillsExportExcel, UserSettlementQueryParam> implements IUserSettlementFacade {
    @Autowired
    private UserSettlementManager userSettlementManager;
    @Autowired
    private UserBillsManager userBillsManager;
    @Autowired
    private CurrencyConfig billConfig;

    @Override
    public UserSettlementResult saveSettlement(@Valid UserSettlementParam userSettlementParam) {
        UserSettlementBO userSettlementBO = BeanUtils.copyProperties(userSettlementParam, UserSettlementBO.class);
        return BeanUtils.copyProperties(userSettlementManager.saveSettlement(userSettlementBO), UserSettlementResult.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @ShardingTransactionType(value = TransactionType.BASE)
    public boolean submitAudit(String settlementNo, Long userId, Long realUserId) {
        return userSettlementManager.submitAudit(settlementNo, userId, realUserId);
    }

    @Override
    public boolean editSettlement(@Valid UserSettlementEditParam userSettlementEditParam) {
        return userSettlementManager.editSettlement(userSettlementEditParam);
    }

    @Override
    public ListVO<UserSettlementBO> settlementPageList(UserSettlementQueryParam userSettlementQuery) {
        ListVO<UserSettlementBO> pageList = userSettlementManager.pageList(userSettlementQuery);
        if (!CollectionUtils.isEmpty(pageList.getDataList())) {
            pageList.getDataList().stream()
                    .peek(userSettlementBO -> userSettlementBO.setBillCurrencyName(billConfig.getNameByCode(userSettlementBO.getBillCurrency())))
                    .collect(Collectors.toList());
        }
        return pageList;
    }

    @Override
    public List<UserSettlementBO> getListByBillNo(String billNo, Long userId) {
        List<UserSettlementBO> userSettlementBOList = userSettlementManager.listByBillNo(billNo, userId);
        if (!CollectionUtils.isEmpty(userSettlementBOList)) {
            userSettlementBOList.stream()
                    .peek(userSettlementBO -> userSettlementBO.setBillCurrencyName(billConfig.getNameByCode(userSettlementBO.getBillCurrency())))
                    .collect(Collectors.toList());
        }
        return userSettlementBOList;
    }

    @Override
    public UserSettlementResult getDetailById(Long id) {
        UserSettlementBO userSettlement = userSettlementManager.getById(id);
        UserSettlementResult userSettlementResult = BeanUtils.copyProperties(userSettlement, UserSettlementResult.class);
        userSettlementResult.setBillCurrencyName(billConfig.getNameByCode(userSettlementResult.getBillCurrency()));
        // 获取结算单对应的账单列表
        List<UserBillsBO> billsBOList = userBillsManager.listBySettlementNo(userSettlement.getSettlementNo(), userSettlement.getUserId());
        if (!CollectionUtils.isEmpty(billsBOList)) {
            billsBOList.stream()
                    .peek(userSettlementBO -> userSettlementBO.setBillCurrencyName(billConfig.getNameByCode(userSettlementBO.getBillCurrency())))
                    .collect(Collectors.toList());
        }
        userSettlementResult.setBillsList(billsBOList);
        return userSettlementResult;
    }

    @Override
    public UserSettlementResult getDetailByNo(String settlementNo, Long userId) {
        UserSettlementBO userSettlement = userSettlementManager.getDetailByNo(settlementNo, userId);
        UserSettlementResult userSettlementResult = BeanUtils.copyProperties(userSettlement, UserSettlementResult.class);
        userSettlementResult.setBillCurrencyName(billConfig.getNameByCode(userSettlementResult.getBillCurrency()));
        // 获取结算单对应的账单列表
        List<UserBillsBO> billsBOList = userBillsManager.listBySettlementNo(userSettlement.getSettlementNo(), userSettlement.getUserId());
        if (!CollectionUtils.isEmpty(billsBOList)) {
            billsBOList.stream()
                    .peek(userSettlementBO -> userSettlementBO.setBillCurrencyName(billConfig.getNameByCode(userSettlementBO.getBillCurrency())))
                    .collect(Collectors.toList());
        }
        userSettlementResult.setBillsList(billsBOList);
        return userSettlementResult;
    }
    @Override
    public void before(UserSettlementQueryParam searchParam) {

    }

    @Override
    public TwoListsResult<UserSettlementExportExcel, UserSettlementBillsExportExcel> getDataList(UserSettlementQueryParam userSettlementQuery) {
        ListVO<UserSettlementBO> settlementPageList = settlementPageList(userSettlementQuery);
        // 设置总页数
        this.setTotalPage(settlementPageList.getPage().getTotalPage());
        List<UserSettlementBO> list = settlementPageList.getDataList();
        log.debug("list size={}", list.size());
        if (!CollectionUtils.isEmpty(list)) {
            TwoListsResult<UserSettlementExportExcel, UserSettlementBillsExportExcel> twoListsResult = new TwoListsResult<>();
            // 生成sheet1对应的数据列表:结算单列表
            List<UserSettlementExportExcel> firstList = Lists.newArrayList();
            for (UserSettlementBO userSettlementBO : list) {
                log.debug("userSettlementBO.getSettlementNo()={}", userSettlementBO.getSettlementNo());
                UserSettlementExportExcel settlementExportExcel = new UserSettlementExportExcel();
                settlementExportExcel.setSettlementNo(userSettlementBO.getSettlementNo());
                settlementExportExcel.setSettlementStatusName(userSettlementBO.getSettlementStatus().getDes());
                settlementExportExcel.setBillCurrencyName(billConfig.getNameByCode(userSettlementBO.getBillCurrency()));
                settlementExportExcel.setCurrencyRate(userSettlementBO.getCurrencyRate());
                settlementExportExcel.setSettlementTypeName(userSettlementBO.getSettlementType().getDes());
                settlementExportExcel.setSettlementWithName(userSettlementBO.getSettlementWithName());
                settlementExportExcel.setSettlementAccount(userSettlementBO.getSettlementAccount());
                settlementExportExcel.setSettlementMethodName(userSettlementBO.getSettlementMethodName());
                settlementExportExcel.setSettlementAmount(userSettlementBO.getSettlementAmount());
                settlementExportExcel.setSettlementFee(userSettlementBO.getSettlementFee());
                if (!CollectionUtils.isEmpty(userSettlementBO.getAnnex())) {
                    // 直接用userSettlementBO.getAnnex().get(0).getKey()会报对象cast错误？？？
                    String json = JSON.toJSONString(userSettlementBO.getAnnex());
                    List<FileDto> dto = JSON.parseArray(json, FileDto.class);
                    settlementExportExcel.setFileUrl(dto.get(0).getKey());
                }
                settlementExportExcel.setCreateByName(userSettlementBO.getCreateByName());
                settlementExportExcel.setCreateTime(new DateTime(userSettlementBO.getCreateTime()).toDate());
                settlementExportExcel.setRemark(userSettlementBO.getRemark());
                log.debug("add a item={}", settlementExportExcel.getSettlementNo());
                firstList.add(settlementExportExcel);
            }
            twoListsResult.setFirstList(firstList);
            log.debug("firstList size={}", firstList.size());
            // 生成sheet2对应的数据列表：结算账单明细
            List<UserSettlementBillsExportExcel> secondList = Lists.newArrayList();
            Set<String> settlementNoSet = list.stream().map(UserSettlementBO::getSettlementNo).collect(Collectors.toSet());
            List<UserBillsBO> billsBOList = userBillsManager.listBySettlementNoSet(settlementNoSet, userSettlementQuery.getUserId());
            log.debug("billsBOList size={}", billsBOList.size());
            if (!CollectionUtils.isEmpty(billsBOList)) {
                for (UserBillsBO userBillsBO : billsBOList) {
                    UserSettlementBillsExportExcel billsExportExcel = new UserSettlementBillsExportExcel();
                    billsExportExcel.setSettlementNo(userBillsBO.getSettlementNo());
                    billsExportExcel.setBillNo(userBillsBO.getBillNo());
                    billsExportExcel.setBillTypeName(userBillsBO.getBillType().getDes());
                    billsExportExcel.setBillStatusName(userBillsBO.getBillStatus().getDes());
                    billsExportExcel.setBillCurrencyName(billConfig.getNameByCode(userBillsBO.getBillCurrency()));
                    billsExportExcel.setBillBusinessTypeName(userBillsBO.getBillBusinessType().getDes());
                    billsExportExcel.setRelatedBillNo(userBillsBO.getRelatedBillNo());
                    if (userBillsBO.getRelatedCreateTime() != null) {
                        billsExportExcel.setRelatedCreateTime(new DateTime(userBillsBO.getRelatedCreateTime()).toDate());
                    }
                    billsExportExcel.setCreateTime(new DateTime(userBillsBO.getCreateTime()).toDate());
                    billsExportExcel.setBillAmount(userBillsBO.getBillAmount());
                    billsExportExcel.setBillFinishedAmount(userBillsBO.getBillFinishedAmount());
                    billsExportExcel.setUnFinishedAmount(userBillsBO.getBillAmount().subtract(userBillsBO.getBillFinishedAmount()));
                    billsExportExcel.setCurrentSettleAmount(userBillsBO.getCurrentSettleAmount());
                    billsExportExcel.setRemark(userBillsBO.getRemark());
                    secondList.add(billsExportExcel);
                }
            }
            twoListsResult.setSecondList(secondList);
            log.debug("secondList size={}", secondList.size());
            return twoListsResult;
        } else {
            return null;
        }
    }
}
