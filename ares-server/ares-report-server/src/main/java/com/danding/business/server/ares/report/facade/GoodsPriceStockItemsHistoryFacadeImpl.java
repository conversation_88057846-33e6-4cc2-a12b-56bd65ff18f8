package com.danding.business.server.ares.report.facade;

import com.danding.business.client.ares.report.facade.IGoodsPriceStockItemsHistoryFacade;
import com.danding.business.client.ares.report.param.GoodsPriceStockItemsHistoryAddParam;
import com.danding.business.client.ares.report.param.GoodsPriceStockItemsHistoryQueryParam;
import com.danding.business.client.ares.report.result.GoodsPriceStockItemsHistoryResult;
import com.danding.business.core.ares.report.search.GoodsPriceStockItemsHistorySearch;
import com.danding.business.server.ares.report.BO.GoodsPriceStockItemsHistoryBO;
import com.danding.business.server.ares.report.manager.GoodsPriceStockItemsHistoryManager;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.List;


/**
 * <p>
 * 价盘子表历史更新记录 服务实现类
 * </p>
 *
 * <AUTHOR> Xu
 * @since 2020-12-22
 */
@DubboService
public class GoodsPriceStockItemsHistoryFacadeImpl implements IGoodsPriceStockItemsHistoryFacade {

    @Autowired
    private GoodsPriceStockItemsHistoryManager goodsPriceStockItemsHistoryManager;

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    @Override
    public GoodsPriceStockItemsHistoryResult getById(Serializable id) {
        GoodsPriceStockItemsHistoryBO goodsPriceStockItemsHistoryBO = goodsPriceStockItemsHistoryManager.getById(id);
        return BeanUtils.copyProperties(goodsPriceStockItemsHistoryBO, GoodsPriceStockItemsHistoryResult.class);
    }

    /**
     * 条件查询单个
     *
     * @param goodsPriceStockItemsHistoryQueryParam
     * @return
     */
    @Override
    public GoodsPriceStockItemsHistoryResult getByQueryParam(GoodsPriceStockItemsHistoryQueryParam goodsPriceStockItemsHistoryQueryParam) {
        GoodsPriceStockItemsHistoryBO goodsPriceStockItemsHistoryBO = goodsPriceStockItemsHistoryManager.getBySearch(BeanUtils.copyProperties(goodsPriceStockItemsHistoryQueryParam, GoodsPriceStockItemsHistorySearch.class));
        return BeanUtils.copyProperties(goodsPriceStockItemsHistoryBO, GoodsPriceStockItemsHistoryResult.class);
    }

    /**
     * 条件查询list
     *
     * @param goodsPriceStockItemsHistoryQueryParam
     * @return
     */
    @Override
    public List<GoodsPriceStockItemsHistoryResult> listByQueryParam(GoodsPriceStockItemsHistoryQueryParam goodsPriceStockItemsHistoryQueryParam) {
        List<GoodsPriceStockItemsHistoryBO> goodsPriceStockItemsHistoryBOList = goodsPriceStockItemsHistoryManager.listBySearch(BeanUtils.copyProperties(goodsPriceStockItemsHistoryQueryParam, GoodsPriceStockItemsHistorySearch.class));
        return BeanUtils.copyProperties(goodsPriceStockItemsHistoryBOList, GoodsPriceStockItemsHistoryResult.class);
    }

    /**
     * 条件分页查询
     *
     * @param goodsPriceStockItemsHistoryQueryParam
     * @return
     */
    @Override
    public ListVO<GoodsPriceStockItemsHistoryResult> pageListByQueryParam(GoodsPriceStockItemsHistoryQueryParam goodsPriceStockItemsHistoryQueryParam) {
        ListVO<GoodsPriceStockItemsHistoryBO> goodsPriceStockItemsHistoryBOListVO = goodsPriceStockItemsHistoryManager.pageListBySearch(BeanUtils.copyProperties(goodsPriceStockItemsHistoryQueryParam, GoodsPriceStockItemsHistorySearch.class));
        return ListVO.build(goodsPriceStockItemsHistoryBOListVO.getPage(), BeanUtils.copyProperties(goodsPriceStockItemsHistoryBOListVO.getDataList(), GoodsPriceStockItemsHistoryResult.class));
    }

    /**
     * 插入
     *
     * @param goodsPriceStockItemsHistoryAddParam
     * @return
     */
    @Override
    public boolean add(GoodsPriceStockItemsHistoryAddParam goodsPriceStockItemsHistoryAddParam) {
        return goodsPriceStockItemsHistoryManager.add(BeanUtils.copyProperties(goodsPriceStockItemsHistoryAddParam, GoodsPriceStockItemsHistoryBO.class));
    }

    /**
     * 批量插入
     *
     * @param goodsPriceStockItemsHistoryAddParamList
     * @return
     */
    @Override
    public boolean addList(List<GoodsPriceStockItemsHistoryAddParam> goodsPriceStockItemsHistoryAddParamList) {
        return goodsPriceStockItemsHistoryManager.addList(BeanUtils.copyProperties(goodsPriceStockItemsHistoryAddParamList, GoodsPriceStockItemsHistoryBO.class));
    }

    /**
     * 根据主键id修改
     *
     * @param goodsPriceStockItemsHistoryAddParam
     * @return
     */
    @Override
    public boolean updateById(GoodsPriceStockItemsHistoryAddParam goodsPriceStockItemsHistoryAddParam) {
        return goodsPriceStockItemsHistoryManager.updateById(BeanUtils.copyProperties(goodsPriceStockItemsHistoryAddParam, GoodsPriceStockItemsHistoryBO.class));
    }

    /**
     * 根据主键id批量修改
     *
     * @param goodsPriceStockItemsHistoryAddParamList
     * @return
     */
    @Override
    public boolean updateListById(List<GoodsPriceStockItemsHistoryAddParam> goodsPriceStockItemsHistoryAddParamList) {
        return goodsPriceStockItemsHistoryManager.updateListById(BeanUtils.copyProperties(goodsPriceStockItemsHistoryAddParamList, GoodsPriceStockItemsHistoryBO.class));
    }

    /**
     * 根据条件修改
     *
     * @param goodsPriceStockItemsHistoryQueryParam
     * @param goodsPriceStockItemsHistoryAddParam
     * @return
     */
    @Override
    public boolean updateListByQueryParam(GoodsPriceStockItemsHistoryQueryParam goodsPriceStockItemsHistoryQueryParam, GoodsPriceStockItemsHistoryAddParam goodsPriceStockItemsHistoryAddParam) {
        return goodsPriceStockItemsHistoryManager.updateListBySearch(BeanUtils.copyProperties(goodsPriceStockItemsHistoryQueryParam, GoodsPriceStockItemsHistorySearch.class), BeanUtils.copyProperties(goodsPriceStockItemsHistoryAddParam, GoodsPriceStockItemsHistoryBO.class));
    }

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    @Override
    public boolean removeById(Serializable id) {
        return goodsPriceStockItemsHistoryManager.removeById(id);
    }

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    @Override
    public boolean removeByIds(List<Long> idList) {
        return goodsPriceStockItemsHistoryManager.removeByIds(idList);
    }

    /**
     * 根据条件删除
     *
     * @param goodsPriceStockItemsHistoryQueryParam
     * @return
     */
    @Override
    public boolean removeByQueryParam(GoodsPriceStockItemsHistoryQueryParam goodsPriceStockItemsHistoryQueryParam) {
        return goodsPriceStockItemsHistoryManager.removeBySearch(BeanUtils.copyProperties(goodsPriceStockItemsHistoryQueryParam, GoodsPriceStockItemsHistorySearch.class));
    }

}
