<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>business-parent</artifactId>
        <groupId>com.danding</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>${revision}</version>
    <artifactId>ares-server</artifactId>
    <packaging>pom</packaging>
    <properties>
        <revision>3.1.19-SNAPSHOT</revision>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.plugin.version>3.8.1</maven.plugin.version>
        <java.version>1.8</java.version>
        <cds.order.version>3.1.0-RELEASE</cds.order.version>
        <cds.item.version>3.1.0-RELEASE</cds.item.version>
        <cds.company.version>3.1.0-RELEASE</cds.company.version>
        <cds.out.api.version>3.1.2-RELEASE</cds.out.api.version>
        <wms.platform.rpc.version>3.2.9-RELEASE</wms.platform.rpc.version>
        <wms.exchange.version>3.2.2-RELEASE</wms.exchange.version>
        <oms.order.version>1.2.1-RELEASE</oms.order.version>
        <park-common.version>1.2.0-SNAPSHOT</park-common.version>
        <scf.risk.version>1.1.0-RELEASE</scf.risk.version>
        <oms.explorer.version>1.2.1-RELEASE</oms.explorer.version>
        <uc.user.rpc.version>1.1-RELEASE</uc.user.rpc.version>
        <liteflow.version>2.11.4.2</liteflow.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <elasticsearch.data.version>2.6.1</elasticsearch.data.version>
        <component.version>2.0-SNAPSHOT</component.version>
        <springboot.version>2.2.5.RELEASE</springboot.version>
        <micrometer.version>1.3.5</micrometer.version>
        <rpc-common.version>2.0-dubbo30-SNAPSHOT</rpc-common.version>
        <zxing.version>3.4.1</zxing.version>
        <pdfbox.version>2.0.27</pdfbox.version>
    </properties>
    <modules>
        <module>ares-trade-server</module>
        <module>ares-config-server</module>
        <module>ares-configuration-server</module>
        <module>ares-flow-server</module>
        <module>ares-goods-server</module>
        <module>ares-order-server</module>
        <module>ares-report-server</module>
        <module>ares-inventory-server</module>
        <module>ares-job-server</module>
        <module>ares-risk-server</module>
    </modules>

    <dependencies>
        <!--        <dependency>-->
        <!--            <groupId>com.danding</groupId>-->
        <!--            <artifactId>uc-component-dubbo</artifactId>-->
        <!--            <version>1.0.1-SNAPSHOT</version>-->
        <!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.alibaba.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>-->
<!--            <version>${springboot.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>api-common</artifactId>
            <version>2.0-SNAPSHOT</version>

        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>utils-common</artifactId>
            <version>2.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>${transmittable-thread-local.version}</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>park-client</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>park-client-autoconfigure</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>dubbo-seata-component</artifactId>
            <version>${component.version}</version>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>cache-component</artifactId>
            <version>${component.version}</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>dt-component-rocketmq</artifactId>
            <version>1.0.9-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>log-component</artifactId>
            <version>${component.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
            <version>2.6.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
            <version>2.2.5.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>actuator-component</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <!-- 第三方项目包版本统一在这里设置，不允许在自己的pom中指定第三方包版本（除非特殊场景） -->
        <dependencies>
            <!--监控-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>${micrometer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding.component</groupId>
                <artifactId>actuator-component</artifactId>
                <version>${component.version}</version>
            </dependency>

            <!--监控 end-->
            <dependency>
                <groupId>com.danding.component</groupId>
                <artifactId>cache-component</artifactId>
                <version>${component.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-order-api</artifactId>
                <version>${cds.order.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-item-api</artifactId>
                <version>${cds.item.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <artifactId>cds-company-api</artifactId>
                <groupId>com.danding</groupId>
                <version>${cds.company.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>cds-out-rpc</artifactId>
                <version>${cds.out.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dt</groupId>
                <artifactId>wms-exchange-client</artifactId>
                <version>${wms.exchange.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.baomidou</groupId>
                        <artifactId>mybatis-plus-extension</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.cloud</groupId>
                        <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.dt</groupId>
                <artifactId>wms-platform-rpc-client</artifactId>
                <version>${wms.platform.rpc.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.baomidou</groupId>
                        <artifactId>mybatis-plus-extension</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.cloud</groupId>
                        <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>oms-order-rpc-client</artifactId>
                <version>${oms.order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>scf-risk-rpc-client</artifactId>
                <version>${scf.risk.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>oms-explorer-rpc-client</artifactId>
                <version>${oms.explorer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dt</groupId>
                <artifactId>wms-elasticsearch-rpc-client</artifactId>
                <version>1.0.7-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.dt</groupId>
                <artifactId>tms-rpc-waybill</artifactId>
                <version>2.1.5-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>uc-user-rpc-client</artifactId>
                <version>${uc.user.rpc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.danding</groupId>
                <artifactId>uc-auth-rpc-client</artifactId>
                <version>${uc.user.rpc.version}</version>

            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
                <version>${elasticsearch.data.version}</version>
            </dependency>
            <dependency>
                <artifactId>bms-rpc-contract-client</artifactId>
                <groupId>com.danding</groupId>
                <version>1.1.5-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>
        </dependencies>


    </dependencyManagement>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>1.5.0</version>
                    <executions>
                        <execution>
                            <id>flatten</id>
                            <phase>process-resources</phase>
                            <goals>
                                <goal>flatten</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>flatten.clean</id>
                            <phase>clean</phase>
                            <goals>
                                <goal>clean</goal>
                            </goals>
                        </execution>
                    </executions>
                    <inherited>true</inherited>
                    <configuration>
                        <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                        <updatePomFile>true</updatePomFile>
                        <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.2.5.RELEASE</version>
                    <configuration>
                        <fork>true</fork>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <tasks>
                                    <!--suppress UnresolvedMavenProperty -->
                                    <copy overwrite="true"
                                          tofile="${session.executionRootDirectory}/target/${project.artifactId}.jar"
                                          file="${project.build.directory}/${project.artifactId}.jar"/>
                                </tasks>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <!--版本统一修改器  命令  mvn versions:set -DnewVersion=1.0-SNAPSHOT -->
            <plugin>
                <!-- https://mvnrepository.com/artifact/org.codehaus.mojo/versions-maven-plugin -->
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.7</version>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>danding</id>
            <name>danding</name>
            <url>http://mvn.yang800.cn/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
