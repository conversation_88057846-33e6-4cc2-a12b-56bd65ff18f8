package com.danding.business.server.ares.goods.manager.check.rule;

import com.danding.business.server.ares.goods.BO.GoodsBO;
import com.danding.business.server.ares.goods.BO.GoodsFlowContext;
import com.danding.soul.client.common.exception.BusinessException;
import com.yomahub.liteflow.core.NodeComponent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * 条码与sku 去除首未空格
 */
@Component("skuTrimRule")
public class SkuTrimRuleNodeComponent extends NodeComponent {

    @Override
    public void process() throws Exception {
        GoodsFlowContext flowContext =this.getContextBean(GoodsFlowContext.class);
        GoodsBO goodsBO =flowContext.getGoodsBO();
        String  sku = goodsBO.getSku();
        goodsBO.setSku(StringUtils.trim(sku));
        String  barcode = goodsBO.getBarcode();
        goodsBO.setBarcode(StringUtils.trim(barcode));
    }
}
