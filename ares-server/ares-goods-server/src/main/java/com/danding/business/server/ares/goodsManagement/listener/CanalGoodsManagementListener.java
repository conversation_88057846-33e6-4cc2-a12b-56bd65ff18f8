package com.danding.business.server.ares.goodsManagement.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.alibaba.spring.util.ObjectUtils;
import com.danding.business.client.ares.entitywarehouse.facade.IEntityWarehouseFacade;
import com.danding.business.client.ares.entitywarehouse.result.EntityWarehouseResult;
import com.danding.business.client.ares.goodsManagement.facade.IEsGoodsManagementFacade;
import com.danding.business.client.ares.goodsManagement.message.GoodsManagementSmallSampleMessage;
import com.danding.business.client.ares.goodsManagement.result.NativeGoodsManagementResult;
import com.danding.business.client.ares.logicwarehouse.facade.ILogicWarehouseFacade;
import com.danding.business.client.ares.logicwarehouse.param.LogicWarehouseQueryParam;
import com.danding.business.client.ares.logicwarehouse.result.LogicWarehouseResult;
import com.danding.business.client.ares.order.message.ItemSkuMessage;
import com.danding.business.client.ares.order.message.OrderMessage;
import com.danding.business.client.ares.owner.facade.IOwnerFacade;
import com.danding.business.client.ares.owner.result.OwnerResult;
import com.danding.business.client.ares.record.facade.IGoodsRecordFacade;
import com.danding.business.client.ares.record.param.GoodsRecordQueryParam;
import com.danding.business.client.ares.record.result.GoodsRecordResult;
import com.danding.business.client.rpc.goods.center.GoodsTagHelper;
import com.danding.business.client.rpc.goods.center.constant.Tag1Constant;
import com.danding.business.common.ares.enums.goods.RecordQueryType;
import com.danding.business.common.ares.enums.goods.RecordStatus;
import com.danding.business.common.ares.enums.goods.RecordType;
import com.danding.business.common.ares.enums.goods.SkuTagEnum;
import com.danding.business.common.ares.enums.order.MessageType;
import com.danding.business.core.ares.record.search.GoodsRecordSearch;
import com.danding.business.server.ares.goods.BO.GoodsBO;
import com.danding.business.server.ares.goods.manager.GoodsManager;
import com.danding.business.server.ares.goodsManagement.BO.GoodsManagementBO;
import com.danding.business.server.ares.goodsManagement.manager.GoodsManagementManager;
import com.danding.business.server.ares.record.BO.GoodsRecordBO;
import com.danding.business.server.ares.record.facade.ProxyConfig;
import com.danding.business.server.ares.record.manager.CentralGoodsRecordManager;
import com.danding.business.server.ares.record.manager.GoodsRecordManager;
import com.danding.component.canal.mq.AbstractCanalMQService;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.rocketmq.message.SpringMessage;
import com.danding.component.rocketmq.producer.SpringRocketMQProducer;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.soul.client.common.exception.BusinessException;
import com.google.common.collect.Maps;

import cn.hutool.core.lang.UUID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * canal MQ监听,按照id顺序消费，保证后面的最新记录
 * 货品备案的更新。
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 15:35
 */

@Service
@Slf4j
@RocketMQMessageListener(topic = "${rocketmq.topic.canal.goods.management}", consumerGroup = "ares_canal_goods_management_consumer", consumeMode = ConsumeMode.ORDERLY)
public class CanalGoodsManagementListener extends AbstractCanalMQService<NativeGoodsManagementResult> implements RocketMQListener<FlatMessage> {

    @DubboReference
    private IGoodsRecordFacade goodsRecordFacade;
    @Autowired
    private GoodsManager goodsManager;
    @Autowired
    private GoodsManagementManager goodsManagementManager;
    @DubboReference
    private IOwnerFacade ownerFacade;
    @DubboReference
    private ILogicWarehouseFacade logicWarehouseFacade;
    @Autowired
    private CentralGoodsRecordManager centralGoodsRecordManager;
    @Autowired
    private ProxyConfig proxyConfig;
    @Autowired
    private SpringRocketMQProducer springRocketMQProducer;

    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }

    @Override
    protected void insert(NativeGoodsManagementResult nativeGoodsManagementResult) {
        log.info("[CanalGoodsManagementListener-insert]================{}", JSON.toJSONString(nativeGoodsManagementResult));
        updateMaterialCode(nativeGoodsManagementResult);

        sendSmallSampleTagChangeMsg(nativeGoodsManagementResult);
    }

    private void updateMaterialCode(NativeGoodsManagementResult nativeGoodsManagementResult) {

        try {
            GoodsRecordQueryParam search = new GoodsRecordQueryParam();
            search.setGoodsCode(nativeGoodsManagementResult.getGoodsCode());

            OwnerResult owner = ownerFacade.getByCode(nativeGoodsManagementResult.getOwnerCode());
            if (Objects.nonNull(owner)) {
                LogicWarehouseQueryParam logicWarehouseQueryParam = new LogicWarehouseQueryParam();
                logicWarehouseQueryParam.setOwnerCode(owner.getOwnerCode());
                List<LogicWarehouseResult> logicWarehouseResults = logicWarehouseFacade.queryBySearch(logicWarehouseQueryParam);
                if (!CollectionUtils.isEmpty(logicWarehouseResults)) {
                    // 过滤口岸为空的逻辑仓
                    List<LogicWarehouseResult> logicWarehouseList = logicWarehouseResults.stream().filter(logicWarehouseResult -> !StringUtils.isEmpty(logicWarehouseResult.getPort())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(logicWarehouseList)) {
                        log.error("erp同步ccs料号失败, 查询逻辑仓口岸均为空, 查询结果: {}", JSON.toJSONString(logicWarehouseResults));
                        return;
                    }
                    if (logicWarehouseList.size() > 1) {
                        log.warn("erp同步ccs料号, 查询逻辑仓数量大于1: ownerCode: {}, 查询结果: {}", owner.getOwnerCode(), JSON.toJSONString(logicWarehouseList));
                    } else {
                        search.setQueryType2(RecordQueryType.FILED_LOGIC_WAREHOUSE.getValue());
                        search.setQueryInfo2(logicWarehouseList.get(0).getPort());
                    }
                } else {
                    log.error("erp同步ccs料号失败, 查询逻辑仓为空: ownerCode: {}", owner.getOwnerCode());
                    return;
                }
            } else {
                log.error("erp同步ccs料号失败, 查询货主失败: ownerCode: {}", nativeGoodsManagementResult.getOwnerCode());
                return;
            }
            List<GoodsRecordResult> list = goodsRecordFacade.listByQueryParam(search);
            GoodsRecordBO goodsRecordBO;
            if (list.size() == 1) {
                goodsRecordBO = BeanUtils.copyProperties(list.get(0), GoodsRecordBO.class);
            } else {
                goodsRecordBO = BeanUtils.copyProperties(list.stream().filter(m -> m.getRecordType() == null || m.getRecordType().equals(RecordType.NEW)).findAny().orElse(null), GoodsRecordBO.class);
            }
            if (goodsRecordBO != null) {
                if (proxyConfig.isSwitchCcsCenter()) {
                    goodsRecordBO.setCcsRecordId(goodsRecordBO.getId());
                    if (goodsRecordBO.getCcsRecordId() == null) {
                        log.info("updateMaterialCode is goodsRecordBO.getCcsRecordId() is null", JSON.toJSONString(goodsRecordBO));
                        return;
                    }
                    centralGoodsRecordManager.updateMaterialCode(goodsRecordBO, false);
                } else {
                    goodsManagementManager.updateMaterialCode(goodsRecordBO);
                }
            }
        } catch (Exception ex) {
            log.error("CanalGoodsManagementListener failure. GoodsCode:" + nativeGoodsManagementResult.getGoodsCode(), ex);
            throw new BusinessException(ex);
        }
    }

    @Override
    protected void update(NativeGoodsManagementResult before, NativeGoodsManagementResult after) {
        log.info("[CanalGoodsManagementListener-update after]================{}", JSON.toJSONString(after));
        if (StringUtils.isBlank(after.getMaterialCode()) && Objects.equals(before.getMaterialCode(), after.getMaterialCode())) {
            updateMaterialCode(after);
        }
        if (StringUtils.isBlank(after.getMaterialCode()) && !Objects.equals(before.getMaterialCode(), after.getMaterialCode())) {
            updateMaterialCode(after);
        }

        sendSmallSampleTagChangeMsg(before, after);
    }

    private void sendSmallSampleTagChangeMsg(NativeGoodsManagementResult nativeGoodsManagementResult) {
        if (GoodsTagHelper.isOpenTag(nativeGoodsManagementResult.getTag1(), Tag1Constant.IS_TAOTIAN)) {
            Integer skuTag = nativeGoodsManagementResult.getSkuTag();
            if (skuTag == null) {
                return;
            }
            Set<SkuTagEnum> skuTagEnums = SkuTagEnum.NumToEnum(skuTag);
            //
            String operateTag;
            if (skuTagEnums.contains(SkuTagEnum.SMALL_SAMPLE_SKU)) {
                operateTag = "add";
            } else {
                return;
            }

            GoodsManagementSmallSampleMessage goodsMessage = new GoodsManagementSmallSampleMessage();
            goodsMessage.setMessageId(UUID.fastUUID().toString().replaceAll("-", ""));
            goodsMessage.setWarehouseCode(nativeGoodsManagementResult.getWarehouseCode());
            goodsMessage.setOwnerCode(nativeGoodsManagementResult.getOwnerCode());
            goodsMessage.setSku(nativeGoodsManagementResult.getSku());
            goodsMessage.setMessageTime(System.currentTimeMillis());
            goodsMessage.setOperateTag(operateTag);

            Map keysMap = Maps.newHashMap();
            keysMap.put("KEYS", nativeGoodsManagementResult.getSku());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message = SpringMessage.createMessage(goodsMessage, messageHeaders);
            springRocketMQProducer.syncSend("ares-goods-management-small-sample-tag-change-topic", message);
        }
    }

    private void sendSmallSampleTagChangeMsg(NativeGoodsManagementResult before, NativeGoodsManagementResult after) {
        if (GoodsTagHelper.isOpenTag(after.getTag1(), Tag1Constant.IS_TAOTIAN)) {
            Integer beforeSkuTag = before.getSkuTag();
            Integer afterSkuTag = after.getSkuTag();
            if (beforeSkuTag == null || afterSkuTag == null) {
                return;
            }
            Set<SkuTagEnum> beforeSkuTagEnums = SkuTagEnum.NumToEnum(beforeSkuTag);
            Set<SkuTagEnum> afterSkuTagEnums = SkuTagEnum.NumToEnum(afterSkuTag);
            //
            String operateTag;
            if (!beforeSkuTagEnums.contains(SkuTagEnum.SMALL_SAMPLE_SKU) && afterSkuTagEnums.contains(SkuTagEnum.SMALL_SAMPLE_SKU)) {
                operateTag = "add";
            } else {
                return;
            }

            GoodsManagementSmallSampleMessage goodsMessage = new GoodsManagementSmallSampleMessage();
            goodsMessage.setMessageId(UUID.fastUUID().toString().replaceAll("-", ""));
            goodsMessage.setWarehouseCode(after.getWarehouseCode());
            goodsMessage.setOwnerCode(after.getOwnerCode());
            goodsMessage.setSku(after.getSku());
            goodsMessage.setMessageTime(System.currentTimeMillis());
            goodsMessage.setOperateTag(operateTag);

            Map keysMap = Maps.newHashMap();
            keysMap.put("KEYS", after.getSku());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message = SpringMessage.createMessage(goodsMessage, messageHeaders);
            springRocketMQProducer.syncSend("ares-goods-management-small-sample-tag-change-topic", message);
        }
    }

    @Override
    protected void delete(NativeGoodsManagementResult outOrderResult) {

    }


}
