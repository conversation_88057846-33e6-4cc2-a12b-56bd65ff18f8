package com.danding.business.server.ares.goodsManagement.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.business.client.ares.entitywarehouse.result.EntityWarehouseResult;
import com.danding.business.client.ares.goods.param.GoodsOperationLogParam;
import com.danding.business.client.ares.goodsManagement.result.NativeGoodsManagementResult;
import com.danding.business.client.ares.logicwarehouse.facade.ILogicWarehouseFacade;
import com.danding.business.client.ares.logicwarehouse.param.LogicWarehouseQueryParam;
import com.danding.business.client.ares.logicwarehouse.result.LogicWarehouseResult;
import com.danding.business.client.ares.owner.facade.IOwnerFacade;
import com.danding.business.client.ares.owner.result.OwnerResult;
import com.danding.business.common.ares.context.AresContext;
import com.danding.business.common.ares.context.Tag1Constant;
import com.danding.business.common.ares.enums.common.TradeType;
import com.danding.business.common.ares.enums.goods.GoodsBatchManagement;
import com.danding.business.common.ares.enums.goods.GoodsManagementFieldEnum;
import com.danding.business.common.ares.enums.goods.GoodsStatus;
import com.danding.business.common.ares.enums.goods.SkuTagEnum;
import com.danding.business.common.ares.enums.operationLogs.OperationType;
import com.danding.business.common.ares.utils.ConfigTagHelper;
import com.danding.business.core.ares.goodsManagement.entity.GoodsManagement;
import com.danding.business.core.ares.goodsManagement.search.GoodsManagementSearch;
import com.danding.business.core.ares.goodsManagement.service.IGoodsManagementService;
import com.danding.business.core.ares.record.search.GoodsRecordLogicWarehouseSearch;
import com.danding.business.server.ares.config.GoodsNacosConfig;
import com.danding.business.server.ares.goods.BO.GoodsBO;
import com.danding.business.server.ares.goods.BO.GoodsFlowContext;
import com.danding.business.server.ares.goods.manager.GoodsManager;
import com.danding.business.server.ares.goods.remote.RemoteConfigFacade;
import com.danding.business.server.ares.goods.remote.RemoteGoodsFacade;
import com.danding.business.server.ares.goodsManagement.BO.GoodsManagementBO;
import com.danding.business.server.ares.goodsManagement.manager.helper.GoodsManagementManagerHelper;
import com.danding.business.server.ares.record.BO.GoodsRecordBO;
import com.danding.business.server.ares.record.BO.GoodsRecordLogicWarehouseBO;
import com.danding.business.server.ares.record.manager.GoodsRecordLogicWarehouseManager;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.soul.client.common.exception.BusinessException;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 货品管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-06
 */
@Component
@Slf4j
public class GoodsManagementManager {

    @Autowired
    private IGoodsManagementService goodsManagementService;
    @Autowired
    private GoodsManagementManagerHelper goodsManagementManagerHelper;
    @Autowired
    private GoodsRecordLogicWarehouseManager goodsRecordLogicWarehouseManager;
    @DubboReference
    private ILogicWarehouseFacade logicWarehouseFacade;
    @Autowired
    private RemoteGoodsFacade remoteGoodsFacade;
    @Autowired
    private GoodsManager goodsManager;

    @Autowired
    private RemoteConfigFacade remoteConfigFacade;

    @DubboReference
    IOwnerFacade ownerFacade;

    @Autowired
    private GoodsNacosConfig goodsNacosConfig;


    public Set<String> getReturnWarehouseCode() {
        return goodsNacosConfig.getReturnWarehouseMap().keySet();
    }


    public boolean getForcedBatchManagement() {
        return goodsNacosConfig.isForcedBatchManagement();
    }

    /**
     * id查询单个
     *
     * @param id
     * @return
     */
    public GoodsManagementBO getById(Serializable id) {
        return toGoodsManagementBO(goodsManagementService.selectById(id));
    }

    /**
     * 条件查询单个
     *
     * @param goodsManagementSearch
     * @return
     */
    public GoodsManagementBO getBySearch(GoodsManagementSearch goodsManagementSearch) {
        GoodsManagement goodsManagement = goodsManagementService.selectBySearch(goodsManagementSearch);
        return toGoodsManagementBO(goodsManagement);
    }

    /**
     * 列表查询
     *
     * @param goodsManagementSearch
     * @return
     */
    public List<GoodsManagementBO> listBySearch(GoodsManagementSearch goodsManagementSearch) {
        List<GoodsManagement> goodsManagementList = goodsManagementService.selectListBySearch(goodsManagementSearch);
        return goodsManagementList.stream().map(m -> toGoodsManagementBO(m)).collect(Collectors.toList());
    }

    public List<GoodsManagementBO> selectListCustomBySearch(GoodsManagementSearch goodsManagementSearch) {
        List<GoodsManagement> goodsManagementList = goodsManagementService.selectListCustomBySearch(goodsManagementSearch);
        return goodsManagementList.stream().map(m -> toGoodsManagementBO(m)).collect(Collectors.toList());
    }

    @PageSelect
    public ListVO<GoodsManagementBO> pageListCustomBySearch(GoodsManagementSearch goodsManagementSearch) {
        ListVO<GoodsManagementBO> goodsManagementBOListVO = new ListVO<>();
        List<GoodsManagement> goodsManagementList = goodsManagementService.selectListCustomBySearch(goodsManagementSearch);
        return ListVO.build(goodsManagementBOListVO.getPage(), goodsManagementList.stream().map(m -> toGoodsManagementBO(m)).collect(Collectors.toList()));
    }


    /**
     * 分页查询
     *
     * @param goodsManagementSearch
     * @return
     */
    @PageSelect
    public ListVO<GoodsManagementBO> pageListBySearch(GoodsManagementSearch goodsManagementSearch) {
        ListVO<GoodsManagementBO> goodsManagementBOListVO = new ListVO<>();
        List<GoodsManagement> goodsManagementList = goodsManagementService.selectListBySearch(goodsManagementSearch);
        return ListVO.build(goodsManagementBOListVO.getPage(), goodsManagementList.stream().map(m -> toGoodsManagementBO(m)).collect(Collectors.toList()));
    }

    /**
     * 功能描述:  插入
     */
    public boolean add(GoodsManagementBO goodsManagementBO) {
        return goodsManagementService.insert(toGoodsManagement(goodsManagementBO));
    }

    /**
     * 功能描述:  批量插入
     */
    public boolean addList(List<GoodsManagementBO> goodsManagementBOList) {
        List<GoodsManagement> goodsManagementList = goodsManagementBOList.stream().map(m -> toGoodsManagement(m)).collect(Collectors.toList());
        return goodsManagementService.insertList(goodsManagementList);
    }

    private GoodsManagement toGoodsManagement(GoodsManagementBO goodsManagementBO) {
        GoodsManagement goodsManagement = BeanUtils.copyProperties(goodsManagementBO, GoodsManagement.class);
        goodsManagement.setUpdateTime(System.currentTimeMillis());//强制更新updateTime
        goodsManagement.setFeature(JSON.toJSONString(goodsManagementBO.getFeature()));
        return goodsManagement;
    }

    private GoodsManagementBO toGoodsManagementBO(GoodsManagement goodsManagement) {
        if (goodsManagement == null) {
            return null;
        }
        GoodsManagementBO goodsManagementBO = BeanUtils.copyProperties(goodsManagement, GoodsManagementBO.class);
        goodsManagementBO.setFeature(JSON.parseObject(goodsManagement.getFeature(), Map.class));
        //默认值设置，数据库无默认值
        goodsManagementBO.setSource(goodsManagementBO.getSource() == null ? "" : goodsManagementBO.getSource());
        goodsManagementBO.setBatchManagement(goodsManagementBO.getBatchManagement() == null ? GoodsBatchManagement.YES : goodsManagementBO.getBatchManagement());
        goodsManagementBO.setStatus(goodsManagementBO.getStatus() == null ? GoodsStatus.ENABLE : goodsManagementBO.getStatus());

        //非效期，默认效期数据
        if ("WMS".equalsIgnoreCase(goodsManagementBO.getFromSource())) {
            if (GoodsBatchManagement.NO.getValue().equals(goodsManagementBO.getBatchManagement().getValue())) {
                goodsManagementBO.setWarningDate(0);
                goodsManagementBO.setNoCollectDate(0);
                goodsManagementBO.setNoSellDate(0);
                goodsManagementBO.setShelfLife(0);
            }
        } else {
            //非效期，非WMS来源使用强效期规则
            /*
            if (GoodsBatchManagement.NO.getValue().equals(goodsManagementBO.getBatchManagement().getValue())) {
                if (getForcedBatchManagement()) {
                    goodsManagementBO.setBatchManagement(GoodsBatchManagement.YES);
                    goodsManagementBO.setWarningDate(1);
                    goodsManagementBO.setNoCollectDate(1);
                    goodsManagementBO.setNoSellDate(1);
                    goodsManagementBO.setShelfLife(36500);
                } else {
                    goodsManagementBO.setWarningDate(0);
                    goodsManagementBO.setNoCollectDate(0);
                    goodsManagementBO.setNoSellDate(0);
                    goodsManagementBO.setShelfLife(0);
                }
            }*/
        }
        return goodsManagementBO;
    }

    /**
     * 功能描述:  根据主键id修改
     */
    public boolean updateById(GoodsManagementBO goodsManagementBO) {
        return goodsManagementService.updateById(toGoodsManagement(goodsManagementBO));
    }

    public boolean updateExternalCodeById(Long id, String externalCode) {
        return goodsManagementService.updateExternalCodeById(id, externalCode);
    }

    public boolean updateExternalSkuById(Long id, String externalSku) {
        return goodsManagementService.updateExternalSkuById(id, externalSku);
    }

    /**
     * 功能描述:  根据主键id批量修改
     */
    public boolean updateListById(List<GoodsManagementBO> goodsManagementBOList) {
        List<GoodsManagement> goodsManagementList = goodsManagementBOList.stream().map(m -> toGoodsManagement(m)).collect(Collectors.toList());
        return goodsManagementService.updateListById(goodsManagementList);
    }

    /**
     * 功能描述:  根据条件修改
     */
    public boolean updateListBySearch(GoodsManagementSearch goodsManagementSearch, GoodsManagementBO goodsManagementBO) {
        return goodsManagementService.updateListBySearch(goodsManagementSearch, toGoodsManagement(goodsManagementBO));
    }

    /**
     * 功能描述:  根据主键id删除
     */
    public boolean removeById(Serializable id) {
        return goodsManagementService.deleteById(id);
    }

    /**
     * 功能描述:  根据主键id批量删除
     */
    public boolean removeByIds(List<Long> idList) {
        return goodsManagementService.deleteByIds(idList);
    }

    /**
     * 功能描述:  根据条件删除
     */
    public boolean removeBySearch(GoodsManagementSearch goodsManagementSearch) {
        return goodsManagementService.deleteBySearch(goodsManagementSearch);
    }

    /**
     * 是否校验禁售、禁收、预警天数
     *
     * @param userId
     * @return
     */
    public boolean goodsBatchCheckIgnoreUserId(Long userId) {
        if (StringUtils.isNotBlank(goodsNacosConfig.getBatchCheckIgnoreUserId())) {
            Map<String, String> checkUserIdMap = Arrays.stream(goodsNacosConfig.getBatchCheckIgnoreUserId().split(",")).collect(Collectors.toMap(Object::toString, Object::toString));
            if (StringUtils.isNotBlank(checkUserIdMap.get(String.valueOf(userId)))) {
                log.info("跳过校验禁售、禁收、预警天数 {}", userId);
                return true;
            }
        }
        return false;
    }

    public GoodsManagementBO queryGoodsBySku(String sku, String ownerCode) {
        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setSku(sku);
        goodsManagementSearch.setOwnerCode(ownerCode);
        return getBySearch(goodsManagementSearch);
    }

    public GoodsManagementBO queryGoodsByGoodsCode(Long userId, String goodsCode, String ownerCode) {
        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setUserId(userId);
        goodsManagementSearch.setGoodsCode(goodsCode);
        goodsManagementSearch.setOwnerCode(ownerCode);
        return getBySearch(goodsManagementSearch);
    }

    public List<GoodsManagementBO> queryGoodsListByGoodsCode(Long userId, String goodsCode) {
        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setUserId(userId);
        goodsManagementSearch.setGoodsCode(goodsCode);
        return listBySearch(goodsManagementSearch);
    }


    public GoodsManagementBO queryGoodsBySku(String sku, String ownerCode, String warehouseCode) {
        return queryGoodsBySku(sku, ownerCode, warehouseCode, null);
    }

    public GoodsManagementBO queryGoodsBySku(String sku, String ownerCode, String warehouseCode, GoodsStatus goodsStatus) {
        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setSku(sku);
        goodsManagementSearch.setOwnerCode(ownerCode);
        goodsManagementSearch.setWarehouseCode(warehouseCode);
        goodsManagementSearch.setStatus(goodsStatus);
        return getBySearch(goodsManagementSearch);
    }

    public GoodsManagementBO queryGoodsBySkuNoDeleted(String sku, String ownerCode, String warehouseCode) {
        return toGoodsManagementBO(goodsManagementService.selectBySku(sku, ownerCode, warehouseCode));
    }

    public List<GoodsManagementBO> queryGoodsListBySkuNoDeleted(String sku, String ownerCode) {
        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setSku(sku);
        goodsManagementSearch.setOwnerCode(ownerCode);
        return listBySearch(goodsManagementSearch);
    }

    public List<GoodsManagementBO> queryGoodsListBySku(List<String> skuList, List<String> ownerCodeList) {
        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setSkus(skuList);
        goodsManagementSearch.setOwnerCodeList(ownerCodeList);
        return listBySearch(goodsManagementSearch);
    }

    public GoodsManagementBO queryGoodsByBarcode(String barcode, String ownerCode) {
        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setBarcode(barcode);
        goodsManagementSearch.setOwnerCode(ownerCode);
        return getBySearch(goodsManagementSearch);
    }

    public boolean updateMaterialCode(GoodsRecordBO goodsRecordBO) {

        Long userId = goodsRecordBO.getUserId();
        Long goodsRecordId = goodsRecordBO.getId();
        String goodsCode = goodsRecordBO.getGoodsCode();
        String materialCode = goodsRecordBO.getMaterialCode();
        if (StringUtils.isBlank(materialCode)) {
            log.info("updateMaterialCode materialCode is null");
            return false;
        }
        if (Objects.isNull(goodsCode)) {
            log.info("updateMaterialCode goodsCode is null");
            return false;
        }
        if (Objects.isNull(goodsRecordId)) {
            log.info("updateMaterialCode goodsRecordId is null");
            return false;
        }
        if (Objects.isNull(userId)) {
            log.info("updateMaterialCode userId is null");
            return false;
        }

        GoodsRecordLogicWarehouseSearch goodsRecordLogicWarehouseSearch = new GoodsRecordLogicWarehouseSearch();
        goodsRecordLogicWarehouseSearch.setGoodsRecordId(goodsRecordId);
        List<GoodsRecordLogicWarehouseBO> goodsRecordLogicWarehouseBOList = goodsRecordLogicWarehouseManager.listBySearch(goodsRecordLogicWarehouseSearch);

        LogicWarehouseQueryParam logicWarehouseQueryParam = new LogicWarehouseQueryParam();
        logicWarehouseQueryParam.setLogicWarehouseCodes(StringUtils.join(goodsRecordLogicWarehouseBOList.stream().map(GoodsRecordLogicWarehouseBO::getLogicWarehouseCode).collect(Collectors.toList()).toArray(), ","));
        List<LogicWarehouseResult> logicWarehouseBOList = logicWarehouseFacade.listLogicWarehouseByParam(logicWarehouseQueryParam);

        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setGoodsCode(goodsCode);
        goodsManagementSearch.setOwnerCodeList(logicWarehouseBOList.stream().map(LogicWarehouseResult::getOwnerCode).collect(Collectors.toList()));
        goodsManagementSearch.setUserId(userId);
        /**
         List<GoodsManagementBO> updateList = this.listBySearch(goodsManagementSearch);
         updateList.stream().forEach(goodsManagementBO->goodsManagementBO.setMaterialCode(materialCode));
         **/
        if (CollectionUtils.isEmpty(goodsManagementSearch.getOwnerCodeList())) {
            log.info("updateMaterialCode getOwnerCodeList is empty");
            return false;
        }

        return goodsManagementService.updateMaterialCodeByOwnerCodeAndGoodsCode(goodsManagementSearch, materialCode);
        //   return updateListById(updateList);


    }

    //任务同步空料号
    public boolean syncMaterialCode() {
        return goodsManagementService.updateMaterialCodeBySql();
    }

    /**
     * 只返回 商品名称，商品条码，商品sku，货主名称，货主编码
     *
     * @param barcode
     * @param returnWarehouseCode
     * @return
     */
    public List<GoodsManagementBO> listRpcGoodsByReturnWarehouseCode(String barcode, String returnWarehouseCode) {
        List<EntityWarehouseResult> entityWarehouseResultList = remoteConfigFacade.getOwnerList(returnWarehouseCode);
        if (CollectionUtils.isEmpty(entityWarehouseResultList)) {
            return new ArrayList<>();
        }
        Map<String, String> entityWarehouseResultMap = entityWarehouseResultList.stream().collect(Collectors.toMap(k -> k.getOwnerCode(), v -> v.getSystemName(), (k1, k2) -> k1));
        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        //限定返回字段
        goodsManagementSearch.setAppointField(Arrays.asList(GoodsManagementFieldEnum.SKU.name(), GoodsManagementFieldEnum.GOODS_NAME.name(), GoodsManagementFieldEnum.OWNER_CODE.name(), GoodsManagementFieldEnum.BARCODE.name(), GoodsManagementFieldEnum.OPEN_PERIOD_VALIDITY.name(), GoodsManagementFieldEnum.SHELF_LIFE.name()));
        goodsManagementSearch.setBarcode(barcode);
        goodsManagementSearch.setOwnerCodeList(entityWarehouseResultList.stream().map(EntityWarehouseResult::getOwnerCode).collect(Collectors.toSet()).stream().collect(Collectors.toList()));
        goodsManagementSearch.setWarehouseCodeList(entityWarehouseResultList.stream().map(EntityWarehouseResult::getWarehouseCode).collect(Collectors.toSet()).stream().collect(Collectors.toList()));
        List<GoodsManagementBO> list = listBySearch(goodsManagementSearch);
        list.stream().forEach(m -> m.setOwnerName(entityWarehouseResultMap.get(m.getOwnerCode())));
        return list;
    }

    private static List<String> authList = Arrays.asList("wms_auth_length", "wms_auth_width", "wms_auth_height", "wms_auth_weight", "wms_auth_carton_height", "wms_auth_carton_width", "wms_auth_carton_length", "wms_auth_carton_weight", "wms_auth_carton_pcs");


    public static boolean isAuthValue(GoodsBO bo) {
        if (bo != null && bo.getFeature() != null) {
            JSONObject feature = bo.getFeature();
            return isAuthValue(feature);
        } else {
            log.info("isAuthValue bo is null or feature is null");
        }
        return false;

    }

    private static boolean isAuthValue(JSONObject feature) {
        log.info("isAuthValue bo feature {}", feature.toJSONString());
        for (String field : authList) {
            if (feature.getDoubleValue(field) == 0) {
                log.info("isAuthValue false field={}", field);
                return false;
            }
        }
        return true;
    }

    public static void cleanFeatureAuthValue(GoodsManagementBO bo) {
        for (String field : authList) {
            bo.getFeature().remove(field);
        }
    }
    public static void cleanFeatureAuthValue(JSONObject jsonObject) {
        for (String field : authList) {
            jsonObject.remove(field);
        }
    }
    public static boolean allowedUpdate(GoodsManagementBO bo) {
        Integer skuTag = bo.getSkuTag();
        Set<SkuTagEnum> skuTagEnumSet = SkuTagEnum.NumToEnum(skuTag);
        if (bo.getFeature() != null) {
            JSONObject feature = new JSONObject(bo.getFeature());
            return isAuthValue(feature) && (skuTagEnumSet.contains(SkuTagEnum.WAIT_AUTH) || skuTagEnumSet.contains(SkuTagEnum.WAIT_MEASURE_RETRY)
                    || skuTagEnumSet.contains(SkuTagEnum.WAIT_MEASURE) || skuTagEnumSet.contains(SkuTagEnum.AUTH_SUCCESS));
        } else {
            return false;
        }

    }

    public static boolean allowedUpdate(GoodsBO bo, Integer skuTag) {
        Set<SkuTagEnum> skuTagEnumSet = SkuTagEnum.NumToEnum(skuTag);
        if (bo.getFeature() != null) {
            JSONObject feature = new JSONObject(bo.getFeature());
            return isAuthValue(feature) && (skuTagEnumSet.contains(SkuTagEnum.WAIT_AUTH) || skuTagEnumSet.contains(SkuTagEnum.WAIT_MEASURE_RETRY)
                    || skuTagEnumSet.contains(SkuTagEnum.WAIT_MEASURE) || skuTagEnumSet.contains(SkuTagEnum.AUTH_SUCCESS));
        } else {
            return false;
        }

    }


    public void syncReturnOwnerGoods(String ownerCode, List<String> skuList) {
        log.info("syncReturnOwnerGoods ownerCode={}, skuList={}", ownerCode, skuList);
        if (Objects.isNull(ownerCode) || Objects.isNull(skuList)) {
            log.info("[货品同步逆向货主] 参数异常: ownerCode={}, skuList={}", ownerCode, skuList);
            return;
        }
        OwnerResult ownerResult = ownerFacade.getByCode(ownerCode);
        if (ownerResult == null) {
            log.info("[货品同步逆向货主] 货主编码查询为空: {}", ownerCode);
            return;
        }
        // 只处理代塔货主
        if (!Objects.equals(ownerResult.getSystemCode(), AresContext.SYSTEM_DT)) {
            log.info("[货品同步逆向货主] 非代塔货主: {}", ownerCode);
            return;
        }
        // 淘天标货主不处理
        boolean openTag = ConfigTagHelper.isOpenTag(ownerResult.getTag1(), Tag1Constant.IS_TAOTIAN);
        if (openTag) {
            log.info("[货品同步逆向货主] 淘天货主不处理: {}", ownerCode);
            return;
        }
        // 查询逆向货主
        OwnerResult returnOwnerRes = ownerFacade.getReturnOwnerByCode(ownerCode);
        if (Objects.isNull(returnOwnerRes)) {
            log.info("[货品同步逆向货主] 逆向货主查询为空: {}", ownerCode);
            return;
        }
        // 查询逻辑仓
        List<LogicWarehouseResult> logicWarehouseResults = logicWarehouseFacade.listLogicWareHouseByOwner(ownerCode);
        if (CollectionUtils.isEmpty(logicWarehouseResults)) {
            log.info("[货品同步逆向货主] 逻辑仓查询为空: {}", ownerCode);
            return;
        }
        LogicWarehouseResult warehouseResult = logicWarehouseResults.iterator().next();
        // 仅对保税生效
        if (!Objects.equals(warehouseResult.getTradeType(), TradeType.BONDED)) {
            log.info("[货品同步逆向货主] 非保税逻辑仓不处理: {}, ownerCode={}", warehouseResult.getLogicWarehouseCode(), ownerCode);
            return;
        }

        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setOwnerCode(ownerCode);
        goodsManagementSearch.setSkus(skuList);
        List<GoodsManagement> goodsManagements = goodsManagementService.selectListBySearch(goodsManagementSearch);
        if (CollectionUtils.isEmpty(goodsManagements)) {
            return;
        }
        String returnOwnerCode = returnOwnerRes.getOwnerCode();
        GoodsManagementSearch returnSearch = new GoodsManagementSearch();
        returnSearch.setSkus(skuList);
        returnSearch.setOwnerCode(returnOwnerCode);
        List<GoodsManagement> returnGoodsManagement = goodsManagementService.selectListBySearch(returnSearch);

        // 逆向货主货品
        Map<String, GoodsManagement> returnGooodsMap = returnGoodsManagement.stream()
                .collect(Collectors.toMap(GoodsManagement::getGoodsCode,
                        Function.identity(), (k1, k2) -> k1));

        // 正向货主货品
        for (GoodsManagement goodsManagement : goodsManagements) {
            // 只处理旧品
            if (!Objects.equals(goodsManagement.getIsNewRecord(), -1)) {
                log.info("[货品同步逆向货主] 非旧品不处理: skuCode={}, ownerCode={}", goodsManagement.getSku(), ownerCode);
                continue;
            }
            // todo 查询逆向货主的货品是否存在 存在更新，不存在新增
            GoodsManagement returnGoods = returnGooodsMap.get(goodsManagement.getGoodsCode());
            if (Objects.isNull(returnGoods)) {
                GoodsManagement copied = BeanUtils.copyProperties(goodsManagement, GoodsManagement.class);
                copied.setId(null);
                copied.setIsNewRecord(-1);
                goodsManagementService.insert(goodsManagement);
            } else {
                String beforeString = JSON.toJSONString(returnGoods);
                // 更新同步
                returnGoods.setGoodsName();
                returnGoods.setOwnerCode();
                returnGoods.setStatus();
                returnGoods.setBarcode();
                returnGoods.setType();
                returnGoods.setBrandCode();
                returnGoods.setBrandName();
                returnGoods.setRemark();
                returnGoods.setBatchManagement();
                returnGoods.setOpenPeriodValidity();
                returnGoods.setShelfLife();
                returnGoods.setNoSellDate();
                returnGoods.setNoCollectDate();
                returnGoods.setWarningDate();
                returnGoods.setSource();
                returnGoods.setPledgeStatus();
                returnGoods.setExternalCode();
                returnGoods.setExternalSku();
                returnGoods.setLength();
                returnGoods.setWidth();
                returnGoods.setHeight();
                returnGoods.setVolume();
                returnGoods.setGrossWeight();
                returnGoods.setNetWeight();
                returnGoods.setIsNewRecord();
                returnGoods.setLotRuleCode();
                returnGoods.setAllocationRuleCode();
                returnGoods.setTurnoverRuleCode();
                returnGoods.setSkuWrap();
                returnGoods.setWrapQty();
                returnGoods.setStandardLength();
                returnGoods.setStandardWidth();
                returnGoods.setStandardHeight();
                returnGoods.setStandardVolume();
                returnGoods.setBracketGauge();
                returnGoods.setCartonPcs();
                returnGoods.setColour();
                returnGoods.setColourCode();
                returnGoods.setStyle();
                returnGoods.setSkuSize();
                returnGoods.setSizeCode();
                
                returnGoods.setMaterialCode();

                returnGoods.setFeature();
                returnGoods.setWarehouseCode();
                returnGoods.setSkuTag();
                returnGoods.setIsPre();

                returnGoods.setMaterialAddWeight();
                returnGoods.setWmsMaterialCode();
                returnGoods.setItemType();

                goodsManagementService.updateById(returnGoods);

                GoodsOperationLogParam logParam = new GoodsOperationLogParam(SimpleUserHelper.getRealUserId(), OperationType.OT_GOODS_MANAGEMENT_EDIT,
                        "同步逆向货主货品");
                remoteGoodsFacade.addOperationLog(logParam, goodsManagement.getGoodsCode(),
                        beforeString, JSON.toJSONString(goodsManagement));
            }
        }
    }
}
