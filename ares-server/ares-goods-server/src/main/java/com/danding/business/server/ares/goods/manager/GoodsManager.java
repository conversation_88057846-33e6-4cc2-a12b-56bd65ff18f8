package com.danding.business.server.ares.goods.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.business.client.ares.goods.param.GoodsOperationLogParam;
import com.danding.business.common.ares.enums.goods.GoodsStatus;
import com.danding.business.common.ares.enums.operationLogs.OperationType;
import com.danding.business.common.ares.enums.operationLogs.UserTypeEnum;
import com.danding.business.common.ares.utils.Assert;
import com.danding.business.core.ares.goods.entity.Goods;
import com.danding.business.core.ares.goods.search.GoodsSearch;
import com.danding.business.core.ares.goods.service.IGoodsService;
import com.danding.business.server.ares.goods.BO.GoodsBO;
import com.danding.business.server.ares.goods.remote.RemoteGoodsFacade;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

import static com.danding.logistics.business.common.flow.ProBook.goods;

/**
 * <p>
 * 货品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@Slf4j
@Component
public class GoodsManager {

    @Autowired
    private IGoodsService goodsService;
    @Autowired
    private RemoteGoodsFacade remoteGoodsFacade;

    public boolean addGoods(GoodsBO bo) {
        Goods goods = BeanUtils.copyProperties(bo, Goods.class);
        toFeatureString(bo, goods);
        return goodsService.insert(goods);
    }

    private void toFeatureString(GoodsBO bo, Goods goods) {
        if (bo != null && goods != null && bo.getFeature() != null) {
            goods.setFeature(bo.getFeature().toJSONString());
        }
    }

    public boolean addGoods4Qm(GoodsBO bo) {
        Goods goods = BeanUtils.copyProperties(bo, Goods.class);
        toFeatureString(bo, goods);
        GoodsBO goodsBySku = this.getByUserIdAndSku(bo.getUserId(), bo.getSku());
        if (goodsBySku == null) {
            remoteGoodsFacade.syncGoodsForDT(bo);
            goodsService.insert(goods);

            return remoteGoodsFacade.addOperationLog(new GoodsOperationLogParam("奇门", UserTypeEnum.OPEN_API,
                            OperationType.OT_GOODS_ADD_BY_API, "下发新增"), bo.getGoodsCode(), JSON.toJSONString(goodsBySku),
                    JSON.toJSONString(bo));
        } else {
            // 已存在情况下goodsCode不变
            goods.setId(goodsBySku.getId());
            goods.setGoodsCode(goodsBySku.getGoodsCode());
            bo.setId(goodsBySku.getId());
            bo.setGoodsCode(goodsBySku.getGoodsCode());
            remoteGoodsFacade.syncGoodsForDT(bo);
            goodsService.updateById(goods);

            return remoteGoodsFacade.addOperationLog(new GoodsOperationLogParam("奇门", UserTypeEnum.OPEN_API,
                            OperationType.OT_GOODS_SYSTEM_CALLBACK, "回传更新"), bo.getGoodsCode(), JSON.toJSONString(goodsBySku),
                    JSON.toJSONString(bo));
        }
    }

    public GoodsBO getByUserIdAndSku(Long userId, String sku) {
        GoodsSearch search = new GoodsSearch();
        search.setUserId(userId);
        search.setSku(sku);
        return this.getGoodsByParam(search);
    }

    public GoodsBO getByUserIdAndGoodsCode(Long userId, String goodsCode) {
        GoodsSearch search = new GoodsSearch();
        search.setUserId(userId);
        search.setGoodsCode(goodsCode);
        return this.getGoodsByParam(search);
    }

    public GoodsBO getByUserIdAndGoodsCodeAndSku(Long userId, String goodsCode, String sku) {
        GoodsSearch search = new GoodsSearch();
        search.setUserId(userId);
        search.setGoodsCode(goodsCode);
        search.setSku(sku);
        return this.getGoodsByParam(search);
    }

    public boolean updateGoodsById(GoodsBO bo) {
        log.info("updateGoodsById----" + JSON.toJSONString(bo));
        Goods goods = BeanUtils.copyProperties(bo, Goods.class);
        toFeatureString(bo, goods);
        return goodsService.updateById(goods);
    }

    public List<GoodsBO> listGoodsByParam(GoodsSearch search) {
        search.setIsPre("1");
        List<Goods> goods = goodsService.selectGoodsListByParam(search);
        if (!CollectionUtils.isEmpty(goods)) {
            return BeanUtils.copyProperties(goods, GoodsBO.class);
        }
        return new ArrayList<>();
    }

    @PageSelect
    public ListVO<GoodsBO> pageListGoodsByParam(GoodsSearch search) {
        search.setIsPre("1");
        List<Goods> goods = goodsService.selectGoodsListByParam(search);
        ListVO<GoodsBO> listVO = new ListVO<>();
        List<GoodsBO> bos = BeanUtils.copyProperties(goods, GoodsBO.class);
        listVO.setDataList(bos);
        return listVO;
    }

    public GoodsBO getGoodsByParam(GoodsSearch search) {
        Goods goods = goodsService.getGoodsByParam(search);
        GoodsBO goodsBO = BeanUtils.copyProperties(goods, GoodsBO.class);
        if (goods != null) {
            if (goods.getFeature() != null && goods.getFeature().trim().startsWith("{")) {
                goodsBO.setFeature(JSON.parseObject(goods.getFeature()));
            } else {
                goodsBO.setFeature(new JSONObject());
            }
        }
        return goodsBO;
    }

    public GoodsBO getGoodsByNoDeleted(String goodsCode) {
        return BeanUtils.copyProperties(goodsService.getGoodsByNoDeleted(goodsCode), GoodsBO.class);
    }

    public boolean addGoodsList(List<GoodsBO> goodsBOS) {
        return goodsService.insertList(BeanUtils.copyProperties(goodsBOS, Goods.class));
    }

    public boolean forbiddenGoods(Long id) {
        GoodsBO goodsBO = getById(id);
        Assert.isTrue(goodsBO != null, "货品不存在：" + id);
        goodsBO.setUpdateTime(System.currentTimeMillis());
        goodsBO.setUpdateBy(SimpleUserHelper.getRealUserId());
        goodsBO.setStatus(GoodsStatus.FORBIDDEN);
        if (goodsService.updateById(BeanUtils.copyProperties(goodsBO, Goods.class))) {
            return remoteGoodsFacade.syncGoodsForDT(goodsBO);
        } else {
            return false;
        }
    }

    public GoodsBO getById(Long id) {
        Goods goods = goodsService.getById(id);
        if (goods != null) {
            GoodsBO goodsBO = BeanUtils.copyProperties(goods, GoodsBO.class);
            if (goods.getFeature() != null && goods.getFeature().trim().startsWith("{")) {
                goodsBO.setFeature(JSON.parseObject(goods.getFeature()));
            } else {
                goodsBO.setFeature(new JSONObject());
            }
            return goodsBO;
        }
        return null;
    }

    public GoodsBO getBySku(String sku) {
        return BeanUtils.copyProperties(goodsService.getBySku(sku), GoodsBO.class);
    }

    public boolean deleteById(GoodsBO goodsBO) {
        String newGoodsCode = "D-" + goodsBO.getGoodsCode();
        goodsBO.setGoodsCode(newGoodsCode);
        goodsBO.setSku(newGoodsCode);
        goodsBO.setDeleted(2);
        return goodsService.removeById(BeanUtils.copyProperties(goodsBO, Goods.class));
    }

    public int count() {
        return goodsService.count();
    }

    public GoodsBO getGoodsByCargoCode(Long userId, String cargoCode) {
        GoodsSearch search = new GoodsSearch();
        search.setUserId(userId);
        search.setCargoCode(cargoCode);
        return this.getGoodsByParam(search);
    }

    public GoodsBO goodsCarryDayReport(Long userId) {
        return BeanUtils.copyProperties(goodsService.goodsCarryDayReport(userId), GoodsBO.class);
    }

    public List<GoodsBO> getCargoCodeRepeatList() {
        return BeanUtils.copyProperties(goodsService.getCargoCodeRepeatList(), GoodsBO.class);
    }

    private int getGoodsCount(GoodsSearch goodsSearch) {
        return goodsService.getGoodsCount(goodsSearch);
    }
}
