package com.danding.business.server.ares.goodsManagement.es;

import com.danding.business.server.ares.config.GoodsNacosConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 * 索引名称配置 按月创建索引
 */
@Component
@Slf4j
public class IndexNameConfig {

    @Autowired
    private GoodsNacosConfig goodsNacosConfig;

    public long getEsScrollTimeout(){
        return goodsNacosConfig.getEsScrollTimeout();
    }


    /**
     * 每月索引文件名后缀日期格式
     */
    public static final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");



    public String getGoodsManagementIndexName() {
        return goodsNacosConfig.getGoodsManagementIndexName();
    }




}
