package com.danding.business.server.ares.goods.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.danding.business.client.ares.Issue.facade.IGoodsIssueFacade;
import com.danding.business.client.ares.Issue.param.GoodsIssueAddParam;
import com.danding.business.client.ares.entitywarehouse.facade.IEntityWarehouseFacade;
import com.danding.business.client.ares.entitywarehouse.result.EntityWarehouseResult;
import com.danding.business.client.ares.goods.facade.IGoodsFacade;
import com.danding.business.client.ares.goods.message.GoodsSyncMessage;
import com.danding.business.client.ares.goods.param.*;
import com.danding.business.client.ares.goods.result.GoodsCarryDayReportResult;
import com.danding.business.client.ares.goods.result.GoodsLogResult;
import com.danding.business.client.ares.goods.result.GoodsResult;
import com.danding.business.client.ares.goodsManagement.facade.IGoodsManagementFacade;
import com.danding.business.client.ares.goodsManagement.param.GoodsManagementAddParam;
import com.danding.business.client.ares.goodsManagement.param.GoodsManagementQueryParam;
import com.danding.business.client.ares.goodsManagement.result.GoodsManagementResult;
import com.danding.business.client.ares.inventory.facade.IGoodsBatchInventoryFacade;
import com.danding.business.client.ares.inventory.facade.IGoodsInventoryReportFacade;
import com.danding.business.client.ares.inventory.facade.IInventoryOperationFacade;
import com.danding.business.client.ares.inventory.param.GoodsBatchInventoryQueryParam;
import com.danding.business.client.ares.inventory.param.InventoryGoodsInfoUpdateParam;
import com.danding.business.client.ares.logicwarehouse.facade.ILogicWarehouseFacade;
import com.danding.business.client.ares.logicwarehouse.result.LogicWarehouseResult;
import com.danding.business.client.ares.operationLogs.facade.IOperationLogsFacade;
import com.danding.business.client.ares.operationLogs.param.OperationLogsAddParam;
import com.danding.business.client.ares.operationLogs.param.OperationLogsQueryParam;
import com.danding.business.client.ares.operationLogs.result.OperationLogsResult;
import com.danding.business.client.ares.order.result.InOrderTotalNumResult;
import com.danding.business.client.ares.owner.result.OwnerResult;
import com.danding.business.client.ares.record.facade.IGoodsRecordFacade;
import com.danding.business.client.ares.record.param.GoodsRecordQueryParam;
import com.danding.business.client.ares.record.result.GoodsRecordResult;
import com.danding.business.client.ares.snapshot.facade.IGoodsInventoryDailySnapshotFacade;
import com.danding.business.client.ares.snapshot.param.GoodsInventoryDailySnapshotQueryParam;
import com.danding.business.client.ares.snapshot.result.GoodsInventoryDailySnapshotResult;
import com.danding.business.client.rpc.goods.center.GoodsTagHelper;
import com.danding.business.client.rpc.goods.center.constant.Tag1Constant;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.business.common.ares.annotation.ParamLog;
import com.danding.business.common.ares.context.AresContext;
import com.danding.business.common.ares.enums.common.ActionType;
import com.danding.business.common.ares.enums.common.TradeType;
import com.danding.business.common.ares.enums.goods.*;
import com.danding.business.common.ares.enums.inventory.InventoryStatus;
import com.danding.business.common.ares.enums.operationLogs.OperationType;
import com.danding.business.common.ares.enums.operationLogs.UserTypeEnum;
import com.danding.business.common.ares.excel.AbstractExcelExportSingleSheetServiceV2;
import com.danding.business.common.ares.utils.*;
import com.danding.business.core.ares.goods.search.GoodsSearch;
import com.danding.business.core.ares.goodsManagement.search.GoodsManagementSearch;
import com.danding.business.server.ares.brand.BO.BrandBO;
import com.danding.business.server.ares.brand.manager.BrandManager;
import com.danding.business.server.ares.config.GoodsNacosConfig;
import com.danding.business.server.ares.goods.BO.GoodsBO;
import com.danding.business.server.ares.goods.BO.GoodsFlowContext;
import com.danding.business.server.ares.goods.center.opt.GoodsCenterDependentManager;
import com.danding.business.server.ares.goods.center.opt.OptContextParam;
import com.danding.business.server.ares.goods.constant.GoodsPeriodOfValidityConstant;
import com.danding.business.server.ares.goods.excel.GoodsExportExcel;
import com.danding.business.server.ares.goods.manager.ContactManager;
import com.danding.business.server.ares.goods.manager.GoodsManager;
import com.danding.business.server.ares.goods.manager.config.GoodsConfiguration;
import com.danding.business.server.ares.goods.manager.convert.GoodsLogConvertor;
import com.danding.business.server.ares.goods.remote.RemoteConfigFacade;
import com.danding.business.server.ares.goods.remote.RemoteGoodsFacade;
import com.danding.business.server.ares.goods.remote.RemoteUserFacade;
import com.danding.business.server.ares.goodsManagement.BO.GoodsManagementBO;
import com.danding.business.server.ares.goodsManagement.manager.GoodsManagementManager;
import com.danding.business.server.ares.record.facade.ProxyConfig;
import com.danding.business.server.ares.record.manager.GoodsRecordManager;
import com.danding.cds.out.api.GoodsRecordRpc;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.req.DeleteGoodsRecordReqVO;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.rocketmq.message.SpringMessage;
import com.danding.component.rocketmq.producer.SpringRocketMQProducer;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.soul.client.common.exception.BusinessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.shardingsphere.transaction.annotation.ShardingTransactionType;
import org.apache.shardingsphere.transaction.core.TransactionType;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.danding.business.common.ares.context.AresContext.*;
import static com.danding.business.common.ares.utils.HttpRequestUtils.postJson;

/**
 * <p>
 * 货品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@Slf4j
@DubboService
@Component("goodsExcelService")
public class GoodsFacadeImpl extends AbstractExcelExportSingleSheetServiceV2<GoodsExportExcel, GoodsQueryParam> implements IGoodsFacade {

    @Autowired
    private GoodsManager goodsManager;
    @Autowired
    @Lazy
    private GoodsRecordManager goodsRecordManager;
    @DubboReference
    private IGoodsRecordFacade goodsRecordFacade;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ILogicWarehouseFacade logicWarehouseFacade;
    @Autowired
    private GoodsManagementManager goodsManagementManager;
    @Autowired
    private BrandManager brandManager;
    @Autowired
    private RemoteGoodsFacade remoteGoodsFacade;
    @Autowired
    private RemoteConfigFacade remoteConfigFacade;
    @Autowired
    private GoodsLogConvertor goodsLogConvertor;
    @DubboReference
    private IGoodsManagementFacade goodsManagementFacade;
    @DubboReference
    private IGoodsInventoryReportFacade goodsInventoryReportFacade;
    @DubboReference
    private IGoodsInventoryDailySnapshotFacade goodsInventoryDailySnapshotFacade;
    @DubboReference
    private IGoodsBatchInventoryFacade goodsBatchInventoryFacade;
    @DubboReference
    private IInventoryOperationFacade inventoryOperationFacade;
    @DubboReference
    private IGoodsIssueFacade goodsIssueFacade;
    @DubboReference
    private IOperationLogsFacade operationLogsFacade;

    @DubboReference
    private GoodsRecordRpc goodsRecordRpc;
    @Autowired
    private RemoteUserFacade remoteUserFacade;
    @Autowired
    private GoodsCenterDependentManager goodsCenterDependentManager;

    @Autowired
    private SpringRocketMQProducer springRocketMQProducer;

    @Autowired
    private ProxyConfig proxyConfig;

    @Autowired
    private GoodsConfiguration goodsConfiguration;

    @Resource
    private FlowExecutor flowExecutor;
    @Autowired
    private ContactManager contactManager;

    @Autowired
    private GoodsNacosConfig goodsNacosConfig;

    @DubboReference
    private IEntityWarehouseFacade entityWarehouseFacade;

    private static final ScheduledExecutorService executorService = Executors.newScheduledThreadPool(20);


    @Override
    @ShardingTransactionType(value = TransactionType.BASE)
    @Transactional(rollbackFor = Exception.class)
    public boolean create(GoodsAddParam addParam) {
        AddOptions options = new AddOptions();
        options.setDistributeWms(true);
        return create(addParam, null, options);
    }

    @Override
    @ShardingTransactionType(value = TransactionType.BASE)
    @Transactional(rollbackFor = Exception.class)
    public boolean create(GoodsCenterAddParam addParam) {
        AddOptions options = new AddOptions();
        options.setDistributeWms(false);
        options.setSyncData(addParam.isSyncData());
        validAndEnrichGoodsCenterParam(addParam);
        GoodsAddParam goodsAddParam = BeanUtils.copyProperties(addParam, GoodsAddParam.class);
        goodsAddParam.setGoodsCode(goodsRecordManager.generateGoodsCode(addParam.getUserId()));
        GoodsManagementAddParam managementAddParam = BeanUtils.copyProperties(addParam, GoodsManagementAddParam.class);
        managementAddParam.setOperationLogParam(null);
        goodsAddParam.setSource(addParam.getOriginAppName());
        managementAddParam.setSource(addParam.getOriginAppName());
        return create(goodsAddParam, managementAddParam, options);
    }

    /*
    货品中心第一版，货主编码ownerCode与实体仓编码warehouseCode编码不能为空，且对应关系需要正确
     */
    private boolean validAndEnrichGoodsCenterParam(GoodsCenterAddParam addParam) {
        Assert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(addParam.getOwnerCode()), "货主编码不能为空" + addParam.getOwnerCode());
        Assert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(addParam.getWarehouseCode()), "实体仓编码不能空" + addParam.getWarehouseCode());
        OwnerResult ownerResult = remoteGoodsFacade.getOwnerByCode(addParam.getOwnerCode());
        Assert.isTrue(ownerResult != null, "货主编码有误:" + addParam.getOwnerCode());
        EntityWarehouseResult entityWarehouse = remoteConfigFacade.getEntityWarehouseFacade(ownerResult.getEntityWarehouseCode());
        Assert.isTrue(entityWarehouse != null, "货主绑定的实体仓编码有误,绑定的实体仓代码为:" + ownerResult.getEntityWarehouseCode());
        if (!entityWarehouse.getWarehouseCode().toLowerCase().equals(addParam.getWarehouseCode().toLowerCase())) {
            throw new BusinessException("传入的实体仓编码与货主绑定的实体仓编码不一致");
        }
        //补充userId,userId,必须是货主说在的UserId
        addParam.setUserId(ownerResult.getUserId());

        if (addParam.getCreateBy() == null) {
            String createByName = Optional.ofNullable(addParam.getFeature()).map(m -> (String) m.get("createdBy")).orElse(null);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(createByName)) {
                Long userId = remoteUserFacade.getUserIdByUserName(createByName);
                addParam.setCreateBy(userId);
                if (addParam.getOperationLogParam().getOperatorUserId() == null) {
                    addParam.getOperationLogParam().setOperatorUserId(userId);
                }
            }

        }


        return true;
    }

    /**
     * 使用liteflow支持货品业务开发
     * 文档地址 https://liteflow.cc/pages/5816c5/
     * 引流流程定义在
     * apollo 的liteflowChainConfig namespace中
     *
     * @param chainId
     * @param addParam
     * @param managementAddParam
     * @param options
     * @return
     */
    private boolean createCoreChain(String chainId, GoodsAddParam addParam, GoodsManagementAddParam managementAddParam, AddOptions options) {
        GoodsBO bo = BeanUtils.copyProperties(addParam, GoodsBO.class);
        if (addParam.getFeature() != null) {
            bo.setFeature(new JSONObject(addParam.getFeature()));
        }
        GoodsFlowContext goodsFlowContext = new GoodsFlowContext();
        goodsFlowContext.setGoodsBO(bo);
        goodsFlowContext.setGoodsAddParam(addParam);
        goodsFlowContext.setAddOptions(options);
        goodsFlowContext.setGoodsManagementAddParam(managementAddParam);
        LiteflowResponse response = flowExecutor.execute2Resp(chainId, null, goodsFlowContext);
        if (!response.isSuccess()) {
            throw new BusinessException(response.getMessage());
        }
        return true;
    }

    public boolean create(GoodsAddParam addParam, GoodsManagementAddParam managementAddParam, AddOptions options) {
        if (goodsConfiguration.isOpenRefactor()) {
            return createCoreChain("goodsCreateCoreChain", addParam, managementAddParam, options);
        }

        // 1 校验保质期属性是否符合要求
        GoodsBO bo = BeanUtils.copyProperties(addParam, GoodsBO.class);
        //sku，条码檢查，目前规则一致
        skuFormingRule(bo);
        calculateVolume(bo);
        validShelfLife(bo);
        // 校验条形码是否重复
        validRepeat(addParam.getUserId(), addParam.getBarcode());
        //sku是否重复
        validRepeatSku(addParam.getUserId(), addParam.getSku());
        // 2 设置默认启用状态和相关属性
        bo.setStatus(GoodsStatus.ENABLE);
        this.setBrandName(bo);
        // 3 过滤sku用户白名单
        checkUserFilter(bo);
        // 4 新增货品
        log.info("[GoodsFacadeImpl-create]===============新增货品=============:{}", JSON.toJSONString(bo));
        if (options.isSyncData()) {
            bo.setCreateTime(managementAddParam.getCreateTime());
            bo.setUpdateTime(managementAddParam.getUpdateTime());
        }
        boolean b = goodsManager.addGoods(bo);
        if (b) {
            //走分发逻辑
            if (options.isDistributeWms()) {
                //5.0 同步当前货品货品管理
                if (StringUtils.isNotBlank(addParam.getOwnerCode())) {
                    goodsManagementFacade.syncGoodsManagementAndWms(bo.getUserId(), bo.getGoodsCode(), addParam.getOwnerCode());
                }
                // 5 同步所有货品管理
                goodsManagementFacade.asyncGoodsManagementAndWms(bo.getUserId(), bo.getGoodsCode());
            } else {
                //不下发wms直接保存
                managementAddParam.setGoodsCode(bo.getGoodsCode());
                managementAddParam.setBrandName(bo.getBrandName());
                goodsManagementFacade.add(managementAddParam, false, true);
                //业务日志只有在未同步状态下记录
                if (!options.isSyncData()) {
                    String logRequestMessage = Optional.ofNullable(addParam.getOperationLogParam()).map(GoodsOperationLogParam::getLogRequestBody).orElse(JSON.toJSONString(bo));
                    remoteGoodsFacade.addOperationLog(addParam.getOperationLogParam(), managementAddParam.getOwnerCode() + "_" + managementAddParam.getSku(),
                            "{}", logRequestMessage);
                }
            }
        }
        // 6 当前用户是否被代塔T绑定,如果绑定需要同步

        remoteGoodsFacade.syncGoodsForDT(bo);
        //发送mq,通知oms
        sendMessageToOMS(bo);

        String logRequestMessage = Optional.ofNullable(addParam.getOperationLogParam()).map(GoodsOperationLogParam::getLogRequestBody).orElse(JSON.toJSONString(bo));
        //操作日志
        if (!options.isSyncData()) {
            remoteGoodsFacade.addOperationLog(addParam.getOperationLogParam(), addParam.getGoodsCode(),
                    "{}", logRequestMessage);
        }


        return b;
    }


    /**
     * 计算体积
     *
     * @param bo
     */
    private void calculateVolume(GoodsBO bo) {
        if (bo == null || bo.getLength() == null || bo.getWidth() == null || bo.getHeight() == null) {
            return;
        }
        if (bo.getVolume() != null) {
            return;
        }

        bo.setVolume(bo.getLength() * bo.getWidth() * bo.getHeight());
    }

    /**
     * 校验：同一个用户下，sku不能重复
     *
     * @param userId
     * @param sku
     */
    private void validRepeatSku(Long userId, String sku) {
        GoodsQueryParam param = new GoodsQueryParam();
        param.setUserId(userId);
        param.setSku(sku);
        List<GoodsResult> goodsResults = listGoodsByParam(param);
        if (CollectionUtil.isNotEmpty(goodsResults)) {
            throw new BusinessException("重复的SKU，无法新增。");
        }
    }

    private void validRepeat(Long userId, String barcode) {
        GoodsQueryParam param = new GoodsQueryParam();
        param.setUserId(userId);
        param.setBarcode(barcode);
        List<GoodsResult> goodsResults = listGoodsByParam(param);
        if (CollectionUtil.isNotEmpty(goodsResults)) {
            throw new BusinessException("重复的条形码，无法新增。");
        }
    }

    //sku与条码规则一致
    private void skuFormingRule(GoodsBO bo) {
        LiteflowResponse response = flowExecutor.execute2Resp("skuFormingChain", null, bo);
        if (!response.isSuccess()) {
            throw new BusinessException("sku:" + bo.getSku() + "条码：" + bo.getBarcode() + "异常。sku/条形码仅支持英文大小写、数字、英文下划线、英文中划线、中间空格，请检查！");
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean edit(GoodsAddParam addParam) {
        if (goodsConfiguration.isOpenRefactor()) {
            return edit2024("goodsUpdateCoreChain", addParam);
        }
        GoodsBO goodsBO = goodsManager.getById(addParam.getId());
        if (goodsBO == null) {
            throw new BusinessException("查询不到货品信息:" + addParam.getId());
        }
        String beforeString = JSON.toJSONString(goodsBO);

        String barcode = goodsBO.getBarcode() == null ? "" : goodsBO.getBarcode();
        if ((!barcode.equals(addParam.getBarcode()))) {
            // 条形码修改的情况下需要判断条形码是否已存在
            validRepeat(addParam.getUserId(), addParam.getBarcode());
        }

        if (goodsBO.getItemType() != null && !goodsBO.getItemType().equals(addParam.getItemType()) &&
                !SYSTEM_ERP.equals(goodsBO.getSource()) && !SYSTEM_QM.equals(addParam.getSource())) {
            throw new BusinessException("商品类型不允许修改");
        }
        boolean goodsNameSameFlg = Objects.equals(goodsBO.getGoodsName(), addParam.getGoodsName());
        boolean barcodeSameFlg = Objects.equals(goodsBO.getBarcode(), addParam.getBarcode());
        boolean brandCodeSameFlg = Objects.equals(goodsBO.getBrandCode(), addParam.getBrandCode());
        boolean tradeTypeSameFlg = Objects.equals(goodsBO.getType(), addParam.getType());
        boolean itemTypeFlg = Objects.equals(goodsBO.getItemType(), addParam.getItemType());
        GoodsBO bo = BeanUtils.copyProperties(addParam, GoodsBO.class);
        bo.setStatus(GoodsStatus.ENABLE);
        this.setBrandName(bo);
        calculateVolume(bo, goodsBO);
        log.info("[GoodsFacadeImpl-edit]===============编辑货品=============:{}", JSON.toJSONString(bo));
        if (!goodsManager.updateGoodsById(bo)) {
            throw new BusinessException("编辑货品失败，请稍后再试");
        }
        if (!goodsNameSameFlg || !barcodeSameFlg) {
            //货品备案修改货品名称、条形码

            goodsRecordFacade.updateGoodsNameAndBarcode(bo.getUserId(), goodsBO.getGoodsCode(), bo.getGoodsName(), bo.getBarcode());
        }

        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setGoodsCode(goodsBO.getGoodsCode());
        goodsManagementSearch.setUserId(goodsBO.getUserId());
        List<GoodsManagementBO> goodsManagementBOS = goodsManagementManager.listBySearch(goodsManagementSearch);
        goodsManagementBOS.stream().forEach(goodsManagementBO -> {
            if (!goodsNameSameFlg || !barcodeSameFlg || !tradeTypeSameFlg || !itemTypeFlg) {
                //货品效期管理修改货品名称、条形码、贸易类型
                goodsManagementBO.setGoodsName(bo.getGoodsName());
                goodsManagementBO.setBarcode(bo.getBarcode());
                goodsManagementBO.setType(bo.getType());
                goodsManagementBO.setItemType(bo.getItemType());
            }
            goodsManagementBO.setStatus(GoodsStatus.ENABLE);
            if (!goodsManagementManager.updateById(goodsManagementBO)) {
                throw new BusinessException("编辑货品失败，请稍后再试。");
            }
        });
        if (!goodsNameSameFlg || !barcodeSameFlg || !brandCodeSameFlg || !tradeTypeSameFlg) {
            //库存修改货品名称、条形码、品牌、贸易类型
            InventoryGoodsInfoUpdateParam inventoryGoodsInfoUpdateParam = BeanUtils.copyProperties(bo, InventoryGoodsInfoUpdateParam.class);
            inventoryOperationFacade.updateInventoryGoodsInfo(inventoryGoodsInfoUpdateParam);
        }

        // 添加操作日志
        remoteGoodsFacade.addOperationLog(addParam.getOperationLogParam(), addParam.getGoodsCode(),
                beforeString, JSON.toJSONString(bo));

        remoteGoodsFacade.syncGoodsForDT(bo);
        return true;
    }

    private boolean edit2024(String chainId, GoodsAddParam addParam) {
        GoodsBO goodsBO = goodsManager.getById(addParam.getId());
        if (goodsBO == null) {
            throw new BusinessException("查询不到货品信息:" + addParam.getId());
        }
        GoodsBO bo = BeanUtils.copyProperties(addParam, GoodsBO.class);
        GoodsFlowContext goodsFlowContext = new GoodsFlowContext();
        goodsFlowContext.setBeforeGoodBO(goodsBO);
        goodsFlowContext.setGoodsBO(bo);
        goodsFlowContext.setGoodsAddParam(addParam);
        if (addParam.getFeature() == null) {
            addParam.setFeature(new HashMap<>());
        }
        LiteflowResponse response = flowExecutor.execute2Resp(chainId, null, goodsFlowContext);
        if (!response.isSuccess()) {
            throw new BusinessException(response.getMessage());
        }
        return true;
    }

    /**
     * 计算体积
     *
     * @param updateBo
     * @param originBo
     */
    private void calculateVolume(GoodsBO updateBo, GoodsBO originBo) {
        if (updateBo == null || updateBo.getVolume() != null) {
            return;
        }

        Double length = updateBo.getLength() != null ? updateBo.getLength() : originBo.getLength();
        Double width = updateBo.getWidth() != null ? updateBo.getWidth() : originBo.getWidth();
        Double height = updateBo.getHeight() != null ? updateBo.getHeight() : originBo.getHeight();
        if (length == null || width == null || height == null) {
            return;
        }

        updateBo.setVolume(length * width * height);
    }

    private void checkUserFilter(GoodsBO bo) {
        // 如果sku为空默认取goodsCode的值
        if (StringUtils.isBlank(bo.getSku())) {
            bo.setSku(bo.getGoodsCode());
        }
        // 新增判断用户是否属于白名单,白名单用户使用sku作为goodsCode
        List<Long> userFilterList = this.goodsConfiguration.getUserFilter();
        log.info("[GoodsFacadeImpl-checkUserFilter]=======（白名单用户使用sku作为货品Id）======={}", JSON.toJSONString(userFilterList));
        if (CollectionUtils.isNotEmpty(userFilterList)) {
            if (userFilterList.contains(bo.getUserId())) {
                // 该用户属于白名单用户 sku赋值给goodsCode
                bo.setGoodsCode(bo.getSku());
            }
        }
    }

    @Override
    public boolean edit4Qm(GoodsAddParam addParam) throws Exception {
        if (goodsConfiguration.isOpenRefactor()) {
            return edit4Qm2024(addParam);
        }
        // 基础信息校验(货主+云仓)
        OwnerResult ownerResult = remoteGoodsFacade.getOwnerByCode(addParam.getOwnerCode());
        Assert.isTrue(ownerResult != null, "货主编码有误:" + addParam.getOwnerCode());
        LogicWarehouseResult logicWarehouseResult = remoteGoodsFacade.getlogicWarehouseByCOde(addParam.getLogicWarehouseCode());
        Assert.isTrue(logicWarehouseResult != null, "云仓编码有误:" + addParam.getOwnerCode());
        // 校验保质期属性
        GoodsBO bo = BeanUtils.copyProperties(addParam, GoodsBO.class);
        bo.setUserId(ownerResult.getUserId());
        validShelfLife(bo);

        //设置默认的效期信息
        setDefaultPeriodOfValidity(bo);

        // 货品信息维护
        bo.setUserName(ownerResult.getUserName());
        bo.setOriginUserId(ownerResult.getUserId());
        bo.setOriginUserName(ownerResult.getUserName());
        bo.setType(TradeType.BONDED.equals(logicWarehouseResult.getTradeType()) ? GoodsType.BONDED : GoodsType.DUTY_PAID);
        bo.setRetailPrice(BigDecimal.ZERO);
        bo.setSource(AresContext.SYSTEM_QM);
        // 过滤白名单用户
        checkUserFilter(bo);
        // 校验货品属性
        GoodsBO bo1 = goodsManager.getByUserIdAndSku(bo.getUserId(), bo.getSku());
        Boolean isNewGoods = Boolean.FALSE;
        if (bo1 != null) {
            bo.setGoodsCode(bo1.getGoodsCode());
            //【sku】【货品名称】【货品保质期属性不允许编辑】不可编辑
            Assert.isTrue(bo1.getGoodsName().equals(bo.getGoodsName()), "货品名称不允许编辑");
            Assert.isTrue(bo1.getType().equals(bo.getType()), "货品类型不允许编辑");
            // 使用老品的启用状态
            bo.setStatus(bo1.getStatus());
            if (!(StringUtils.isBlank(bo1.getBarcode()) ? "" : bo1.getBarcode()).equals(StringUtils.isBlank(bo.getBarcode()) ? "" : bo.getBarcode())) {
                // 判断是否允许编辑条形码
                validateBarcode(bo);
            }
        } else {
            bo.setGoodsCode(goodsRecordManager.generateGoodsCode(ownerResult.getUserId()));
            // 新品
            checkEnable(bo);
            isNewGoods = Boolean.TRUE;//新货品
        }
        this.setBrandName(bo);
        // 货品管理维护
        GoodsManagementQueryParam queryParam = new GoodsManagementQueryParam();
        queryParam.setUserId(bo.getUserId());
        queryParam.setGoodsCode(bo.getGoodsCode());
        queryParam.setOwnerCode(addParam.getOwnerCode());
        GoodsManagementResult goodsManagementResult = goodsManagementFacade.getDetailByQueryParam(queryParam);
        log.info("[GoodsFacadeImpl-edit4Qm]=============奇门新增货品============:{}", JSON.toJSONString(bo));
        boolean b = goodsManager.addGoods4Qm(bo);
        if (b) {
            if (goodsManagementResult == null) {
                // 新增货品管理
                try {
                    GoodsManagementAddParam goodsManagementAddParam = BeanUtils.copyProperties(bo, GoodsManagementAddParam.class);
                    goodsManagementAddParam.setId(null);
                    goodsManagementAddParam.setOwnerCode(addParam.getOwnerCode());
                    b = goodsManagementFacade.add(goodsManagementAddParam);
                } catch (Exception e) {
                    log.error("[GoodsFacadeImpl-edit4Qm]=============货品管理新增异常============:{}", JSON.toJSONString(bo), e);
                }
            }

            //下发wms
            remoteGoodsFacade.asyncIssueGoods(addParam.getOwnerCode(), bo.getGoodsCode());
        }
        //发送mq,通知oms
        if (isNewGoods) {
            sendMessageToOMS(bo);
        }
        return b;
    }

    @ParamLog(target = "edit4Qm2024", needResult = true)
    private boolean edit4Qm2024(GoodsAddParam addParam) throws Exception {
        GoodsBO bo = null;
        RLock userBarcodeLock = null;
        RLock userSkuLock = null;
        try {
            qmParamCheckChain(addParam);

            bo = BeanUtils.copyProperties(addParam, GoodsBO.class);
            if (addParam.getFeature() != null) {
                bo.setFeature(new JSONObject(addParam.getFeature()));
            }

            OwnerResult ownerResult = remoteGoodsFacade.getOwnerByCode(addParam.getOwnerCode());
            Assert.isTrue(ownerResult != null, "货主编码有误:" + addParam.getOwnerCode());
            LogicWarehouseResult logicWarehouseResult = null;
            if (AresContext.SYSTEM_DOUYIN_MARKET.equals(addParam.getSource())) {
                // 抖音超市云仓一对一
                List<LogicWarehouseResult> logicWarehouseResults = logicWarehouseFacade.listLogicWareHouseByOwner(addParam.getOwnerCode());
                if (CollectionUtils.isNotEmpty(logicWarehouseResults)) {
                    logicWarehouseResult = logicWarehouseResults.iterator().next();
                    addParam.setLogicWarehouseCode(logicWarehouseResult.getLogicWarehouseCode());
                }
            } else {
                logicWarehouseResult = remoteGoodsFacade.getlogicWarehouseByCOde(addParam.getLogicWarehouseCode());
            }
            Assert.isTrue(logicWarehouseResult != null, "云仓编码有误:" + addParam.getOwnerCode());

            // 条码 sku 加锁
            userBarcodeLock = redissonClient.getLock("dt_erp_qm_goods_barcode_lock:" + ownerResult.getUserId() + ":" + bo.getBarcode());
            boolean barcodeTryLock = userBarcodeLock.tryLock(1, 10, TimeUnit.SECONDS);
            if (!barcodeTryLock) {
                log.warn("淘天下发商品, 获取barcode锁失败 sku: {} , barcode: {}, ownerCode: {}", addParam.getSku(), addParam.getBarcode(), addParam.getOwnerCode());
                throw new BusinessException("获取锁失败");
            }
            userSkuLock = redissonClient.getLock("dt_erp_qm_goods_sku_lock:" + ownerResult.getUserId() + ":" + bo.getSku());
            boolean skuTryLock = userSkuLock.tryLock(1, 10, TimeUnit.SECONDS);
            if (!skuTryLock) {
                log.warn("淘天下发商品, 获取sku锁失败 sku: {}, barcode: {}, ownerCode:{}", addParam.getSku(), addParam.getBarcode(), addParam.getOwnerCode());
                throw new BusinessException("获取锁失败");
            }

            bo.setUserId(ownerResult.getUserId());
            // 货品信息维护
            bo.setUserName(ownerResult.getUserName());
            bo.setOriginUserId(ownerResult.getUserId());
            bo.setOriginUserName(ownerResult.getUserName());
            if (bo.isJINDGoods()){
            }else {
                bo.setType(TradeType.BONDED.equals(logicWarehouseResult.getTradeType()) ? GoodsType.BONDED : GoodsType.DUTY_PAID);
            }
            bo.setRetailPrice(BigDecimal.ZERO);
            addParam.setUserId(bo.getUserId());
            addParam.setOriginUserId(bo.getOriginUserId());
            addParam.setOriginUserName(bo.getOriginUserName());
            addParam.setType(bo.getType());
            addParam.setUserName(bo.getUserName());
            addParam.setRetailPrice(bo.getRetailPrice());

            if (AresContext.SYSTEM_DOUYIN_MARKET.equals(bo.getSource())) {
                // 抖音超市固定开启批次管理
                bo.setBatchManagement(GoodsBatchManagement.YES);
                bo.setTag1(GoodsTagHelper.openTag(bo.getTag1(), com.danding.business.common.ares.context.Tag1Constant.IS_DYDM));
                addParam.setTag1(bo.getTag1());
                addParam.setBatchManagement(GoodsBatchManagement.YES);
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(addParam.getSource())) {
                bo.setSource(AresContext.SYSTEM_QM);
                addParam.setSource(AresContext.SYSTEM_QM);
            }
            if (AresContext.SYSTEM_TAG_TAOTIAN.equals(bo.getSource())) {
                bo.setTag1(GoodsTagHelper.openTag(bo.getTag1(), Tag1Constant.IS_TAOTIAN));
                bo.setRetailPrice(BigDecimal.ONE);
                bo.setCargoCode(bo.getSku());
                if (ItemType.ZC.equals(addParam.getItemType())) {
                    addParam.setType(GoodsType.BONDED);
                    bo.setType(GoodsType.BONDED);
                }
                if (ItemType.ZP.equals(addParam.getItemType())) {
                    addParam.setType(GoodsType.DUTY_PAID);
                    bo.setType(GoodsType.DUTY_PAID);
                }
                addParam.setCargoCode(bo.getSku());
                addParam.setTag1(bo.getTag1());
                addParam.setRetailPrice(bo.getRetailPrice());
                /**
                 * 淘天的净重与毛重取值特殊，二值相反
                 */
              // addParam.setNetWeight(addParam.getGrossWeight());
               Optional.ofNullable(addParam.getFeature().get("wms_auth_weight")).ifPresent(m -> addParam.setGrossWeight(Convert.stringToDouble((String) m)));

                bo.setGrossWeight(addParam.getGrossWeight());
                bo.setNetWeight(addParam.getNetWeight());

            }

            String sku = org.apache.commons.lang3.StringUtils.trim(bo.getSku());
            GoodsBO bo1 = getGoodsBO(addParam, bo, sku);

            Assert.isTrue(bo.getUserId() != null, "缺少用户id");
            if (bo1 == null) {

                AddOptions addOptions = new AddOptions().setDistributeByOwner(true);
                addParam.setOperationLogParam(new GoodsOperationLogParam("奇门", UserTypeEnum.OPEN_API,
                        OperationType.OT_GOODS_ADD_BY_API, "下发新增"));
                createCoreChain("goodsQMCreateChain", addParam, null, addOptions);
            } else {
                GoodsFlowContext goodsFlowContext = new GoodsFlowContext();
                goodsFlowContext.setBeforeGoodBO(bo1);
                bo.setGoodsCode(bo1.getGoodsCode());
                bo.setId(bo1.getId());
                if (AresContext.SYSTEM_TAG_TAOTIAN.equals(bo.getSource())) {
                    bo.setTag1(GoodsTagHelper.openTag(bo.getTag1(), Tag1Constant.IS_TAOTIAN));
                }
                if (AresContext.SYSTEM_DOUYIN_MARKET.equals(bo.getSource())) {
                    // 如果抖超目前的是效期品，则不能更新为非效期品
                    if (Objects.equals(bo1.getOpenPeriodValidity(), GoodsOpenPeriodValidity.YES)) {
                        bo.setOpenPeriodValidity(GoodsOpenPeriodValidity.YES);
                        addParam.setOpenPeriodValidity(GoodsOpenPeriodValidity.YES);
                    }
                }
                goodsFlowContext.setGoodsBO(bo);
                goodsFlowContext.setGoodsAddParam(addParam);
                addParam.setOperationLogParam(new GoodsOperationLogParam("奇门", UserTypeEnum.OPEN_API,
                        OperationType.OT_GOODS_SYSTEM_CALLBACK, "回传更新"));
                LiteflowResponse response = flowExecutor.execute2Resp("goodsQMUpdateCoreChain", null, goodsFlowContext);
                if (!response.isSuccess()) {
                    throw new BusinessException(response.getMessage());
                }
            }
        } catch (Exception e) {
            if (bo != null) {
                if (AresContext.SYSTEM_TAG_TAOTIAN.equals(bo.getSource())) {
                    contactManager.contact(goodsNacosConfig.getCreateGoodsErrCode(), String.valueOf(bo.getSku()), "下发商品失败sku=" + bo.getSku() + "；失败原因：" + e.getMessage());
                    if (e instanceof BusinessException) {
                        boolean needSend = false;
                        for (String msgWileKey : goodsNacosConfig.getCreateGoodsBizErrMsgWhiteList()) {
                            if (StringUtils.isNotBlank(e.getMessage())) {
                                if (e.getMessage().contains(msgWileKey)) {
                                    needSend = true;
                                    break;
                                }
                            }
                        }
                        if (needSend) {
                            contactManager.contact(goodsNacosConfig.getCreateGoodsBizErrCode(), String.valueOf(bo.getSku()), "下发商品失败sku=" + bo.getSku() + "；失败原因：" + e.getMessage());
                        }
                    }

                } else if (AresContext.SYSTEM_DOUYIN_MARKET.equals(bo.getSource())) {
                    contactManager.contact(goodsNacosConfig.getCreateDYDMGoodsErrCode(), String.valueOf(bo.getSku()), "抖超商品下发商品失败sku=" + bo.getSku() + "；失败原因：" + e.getMessage());
                }
            }
            throw e;
        } finally {
            if (userSkuLock != null) {
                userSkuLock.unlock();
            }
            if (userBarcodeLock != null) {
                userBarcodeLock.unlock();
            }
        }
        return true;
    }

    private GoodsBO getGoodsBO(GoodsAddParam addParam, GoodsBO bo, String sku) {
        GoodsBO bo1 = null;
        if (AresContext.SYSTEM_DOUYIN_MARKET.equals(bo.getSource())) {
            GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
            goodsManagementSearch.setOwnerCode(addParam.getOwnerCode());
            goodsManagementSearch.setUserId(bo.getUserId());
            goodsManagementSearch.setExternalCode(addParam.getExternalCode());
            GoodsManagementBO managementBO = goodsManagementManager.getBySearch(goodsManagementSearch);
            if (managementBO != null) {
                if (!Objects.equals(managementBO.getSku(), addParam.getSku())) {
                    log.error("不允许一个抖音超市外部货品id对应多个sku, externalCode: {}, sku: {}, managementBO.getSku(): {}, ownerCode: {}", addParam.getExternalCode(), addParam.getSku(), managementBO.getSku(), addParam.getOwnerCode());
                    throw new BusinessException("不允许一个抖音超市外部货品id对应多个sku");
                }
                bo1 = goodsManager.getByUserIdAndSku(bo.getUserId(), managementBO.getSku());
            }
            // 抖超预警天数如未下发，则等于禁售天数 + 2
            if (Objects.equals(GoodsOpenPeriodValidity.YES, addParam.getOpenPeriodValidity()) &&
                    Objects.nonNull(addParam.getNoSellDate())) {
                addParam.setWarningDate(addParam.getNoSellDate() + 2);
                bo.setWarningDate(addParam.getWarningDate());
            }
        } else {
            bo1 = goodsManager.getByUserIdAndSku(bo.getUserId(), sku);
        }
        return bo1;
    }

    private boolean qmParamCheckChain(GoodsAddParam addParam) {
        GoodsFlowContext goodsFlowContext = new GoodsFlowContext();
        goodsFlowContext.setGoodsAddParam(addParam);
        LiteflowResponse response = flowExecutor.execute2Resp("goodsQMParamsCheckChain", null, goodsFlowContext);
        if (!response.isSuccess()) {
            throw new BusinessException(response.getMessage());
        }
        return true;
    }

    /**
     * 设置效期相关信息
     *
     * @param bo
     */
    private void setDefaultPeriodOfValidity(GoodsBO bo) {
        //默认都要开启批次
        bo.setBatchManagement(GoodsBatchManagement.YES);

        if (bo.getOpenPeriodValidity() == null || GoodsOpenPeriodValidity.NO == bo.getOpenPeriodValidity()) {
            bo.setOpenPeriodValidity(GoodsOpenPeriodValidity.NO);

            //非效期品，取值均为默认值
            bo.setWarningDate(GoodsPeriodOfValidityConstant.DEFAULT_WARNING_DATE);
            bo.setNoSellDate(GoodsPeriodOfValidityConstant.DEFAULT_NO_SELL_DATE);
            bo.setNoCollectDate(GoodsPeriodOfValidityConstant.DEFAULT_NO_COLLECT_DATE);
            bo.setShelfLife(GoodsPeriodOfValidityConstant.DEFAULT_SHELF_LIFE);
        } else if (GoodsOpenPeriodValidity.YES == bo.getOpenPeriodValidity()) {
            //效期品，若没有保质期字段推下来，则取默认；其它3个字段均取默认值
            if (bo.getShelfLife() == null || bo.getShelfLife() <= 0) {
                bo.setShelfLife(GoodsPeriodOfValidityConstant.DEFAULT_SHELF_LIFE);
            }

            boolean ignore = goodsManagementManager.goodsBatchCheckIgnoreUserId(bo.getUserId());
            if (ignore || (bo.getNoSellDate() == null || bo.getNoSellDate() == 0) ||
                    (bo.getNoCollectDate() == null || bo.getNoCollectDate() == 0) ||
                    (bo.getWarningDate() == null || bo.getWarningDate() == 0) ||
                    !(bo.getNoSellDate() <= bo.getWarningDate() &&
                            bo.getWarningDate() <= bo.getNoCollectDate() &&
                            bo.getNoCollectDate() <= bo.getShelfLife())
            ) {
                bo.setWarningDate(GoodsPeriodOfValidityConstant.DEFAULT_WARNING_DATE);
                bo.setNoSellDate(GoodsPeriodOfValidityConstant.DEFAULT_NO_SELL_DATE);
                bo.setNoCollectDate(GoodsPeriodOfValidityConstant.DEFAULT_NO_COLLECT_DATE);
            }
        }
    }


    private void validShelfLife(GoodsBO bo) {
        GoodsBatchManagement batchManagement = bo.getBatchManagement();
        if (batchManagement == GoodsBatchManagement.YES) {
            if (bo.getShelfLife() == null || bo.getShelfLife() == 0) {
                throw new BusinessException("效期商品的保质期天数不能为空");
            }
            //是否校验禁售、禁收、预警天数
            if (goodsManagementManager.goodsBatchCheckIgnoreUserId(bo.getUserId())) {
                return;
            }
            if (bo.getNoSellDate() == null || bo.getNoSellDate() == 0) {
                throw new BusinessException("效期商品的禁售天数不能为空");
            }
            if (bo.getNoCollectDate() == null || bo.getNoCollectDate() == 0) {
                throw new BusinessException("效期商品的禁收天数不能为空");
            }
            if (bo.getWarningDate() == null || bo.getWarningDate() == 0) {
                throw new BusinessException("效期商品的预警天数不能为空");
            }
            if (!(bo.getNoSellDate() <= bo.getWarningDate() &&
                    bo.getWarningDate() <= bo.getNoCollectDate() &&
                    bo.getNoCollectDate() <= bo.getShelfLife())) {
                throw new BusinessException("保质期相关信息设置请符合：【禁售天数】<=【预警天数】<=【禁收天数】<=【保质期天数】");
            }
        } else {
            if ("WMS".equalsIgnoreCase(bo.getFromSource())) {
                bo.setWarningDate(0);
                bo.setNoCollectDate(0);
                bo.setNoSellDate(0);
                bo.setShelfLife(0);
            } else {
                if (goodsManagementManager.getForcedBatchManagement()) {
                    bo.setBatchManagement(GoodsBatchManagement.YES);
                    bo.setWarningDate(1);
                    bo.setNoCollectDate(1);
                    bo.setNoSellDate(1);
                    bo.setShelfLife(36500);
                } else {
                    bo.setWarningDate(null);
                    bo.setNoCollectDate(null);
                    bo.setNoSellDate(null);
                    bo.setShelfLife(null);
                }
            }
        }
    }

    @Override
    public boolean updatePledgeStatus(List<GoodsAddParam> goodsAddParam) {
        List<String> skus = goodsAddParam.stream().map(GoodsAddParam::getSku).collect(Collectors.toList());
        Map<String, PledgeStatus> collect = goodsAddParam.stream().collect(Collectors.toMap(GoodsAddParam::getSku, GoodsAddParam::getPledgeStatus));
        GoodsQueryParam param = new GoodsQueryParam();
        param.setUserId(goodsAddParam.get(0).getUserId());
        param.setSkus(skus);
        List<GoodsResult> goodsResults = listGoodsByParam(param);
        for (GoodsResult goodsResult : goodsResults) {
            goodsResult.setPledgeStatus(collect.get(goodsResult.getSku()));
            goodsManager.updateGoodsById(BeanUtils.copyProperties(goodsResult, GoodsBO.class));
        }
        return true;
    }

    private void checkEnable(GoodsBO bo) {
        // 默认启用
        bo.setStatus(GoodsStatus.ENABLE);
        // 新增判断用户是否属于白名单
        List<Long> enableList = this.goodsConfiguration.getEnableUser();
        ;
        log.info("=======货品是否启用（用户白名单）======={}", JSON.toJSONString(enableList));
        if (CollectionUtils.isNotEmpty(enableList)) {
            if (enableList.contains(bo.getUserId())) {
                // 该用户属于白名单用户, 禁用
                bo.setStatus(GoodsStatus.FORBIDDEN);
            }
        }
    }

    /**
     * 设置品牌名称
     *
     * @param bo
     */
    private void setBrandName(GoodsBO bo) {
        if (StringUtils.isNotBlank(bo.getBrandCode()) && StringUtils.isBlank(bo.getBrandName())) {
            BrandBO brandBO = brandManager.getByBrandCode(bo.getUserId(), bo.getBrandCode());
            if (brandBO != null) {
                bo.setBrandName(brandBO.getBrandName());
            }
        }
    }

    /**
     * 校验是否允许修改条形码
     *
     * @param bo
     */
    private void validateBarcode(GoodsBO bo) {
        if (!remoteGoodsFacade.validateGoodsIssue(bo)) {
            throw new BusinessException("该货品已成功下发过，不允许修改条形码:" + bo.getGoodsCode());
        }
    }

    @Override
    public boolean update(GoodsAddParam addParam) {
        GoodsBO bo = BeanUtils.copyProperties(addParam, GoodsBO.class);
        return goodsManager.updateGoodsById(bo);
    }

    @Override
    public List<GoodsResult> listGoodsByParam(GoodsQueryParam queryParam) {
        GoodsSearch search = BeanUtils.copyProperties(queryParam, GoodsSearch.class);
        List<GoodsBO> goodsBOS = goodsManager.listGoodsByParam(search);
        if (CollectionUtils.isNotEmpty(goodsBOS)) {
            return BeanUtils.copyProperties(goodsBOS, GoodsResult.class);
        }
        return new ArrayList<>();
    }

    @Override
    public GoodsResult getGoodsByParam(Long userId, String goodsCode) {
        return getGoodsByParam(userId, goodsCode, null);
    }

    @Override
    public GoodsResult getGoodsByParam(Long userId, String goodsCode, String sku) {
        return getGoodsByParam(userId, goodsCode, sku, null);
    }

    @Override
    public GoodsResult getGoodsByParam(Long userId, String goodsCode, String sku, GoodsStatus goodsStatus) {
        GoodsSearch search = new GoodsSearch();
        search.setUserId(userId);
        search.setGoodsCode(goodsCode);
        search.setSku(sku);
        search.setStatus(goodsStatus);
        GoodsBO goodsBO = goodsManager.getGoodsByParam(search);
        GoodsResult result = BeanUtils.copyProperties(goodsBO, GoodsResult.class);
        if (result != null) {
            result.setFeature(goodsBO.getFeature() != null ? goodsBO.getFeature().getInnerMap() : null);
        }
        return result;
    }

    @Override
    public ListVO<GoodsResult> pageListGoodsByParam(GoodsQueryParam queryParam) {
        GoodsSearch search = BeanUtils.copyProperties(queryParam, GoodsSearch.class);
        ListVO<GoodsResult> goodsResultListVO = new ListVO<>();
        ListVO<GoodsBO> listVO = goodsManager.pageListGoodsByParam(search);
        List<GoodsResult> results = BeanUtils.copyProperties(listVO.getDataList(), GoodsResult.class);
        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        for (GoodsResult result : results) {
            if (StringUtils.isNotBlank(result.getBrandCode())) {
                BrandBO brandBO = brandManager.getByBrandCode(result.getUserId(), result.getBrandCode());
                if (brandBO != null) {
                    result.setBrandName(brandBO.getBrandName());
                    result.setBrandNameEn(brandBO.getBrandNameEn());
                }
            }
            goodsManagementSearch.setGoodsCode(result.getGoodsCode());
            goodsManagementSearch.setUserId(result.getUserId());
            List<GoodsManagementBO> goodsManagementBOList = goodsManagementManager.listBySearch(goodsManagementSearch);
            Set<String> collect = goodsManagementBOList.stream().filter(m -> StringUtils.isNotBlank(m.getExternalCode())).map(m -> m.getExternalCode()).collect(Collectors.toSet());
            Set<String> collectSku = goodsManagementBOList.stream().filter(m -> StringUtils.isNotBlank(m.getExternalSku())).map(m -> m.getExternalSku()).collect(Collectors.toSet());
            result.setExternalCodeSet(collect);
            result.setExternalSkuSet(collectSku);
        }
        goodsResultListVO.setDataList(results);
        goodsResultListVO.setPage(listVO.getPage());
        return goodsResultListVO;
    }

    @Override
    public ListVO<GoodsResult> pageListGoodsByParamV2(GoodsQueryParam queryParam) {
        GoodsSearch search = BeanUtils.copyProperties(queryParam, GoodsSearch.class);
        ListVO<GoodsResult> goodsResultListVO = new ListVO<>();
        ListVO<GoodsBO> listVO = goodsManager.pageListGoodsByParam(search);
        List<GoodsResult> results = BeanUtils.copyProperties(listVO.getDataList(), GoodsResult.class);
        goodsResultListVO.setDataList(results);
        goodsResultListVO.setPage(listVO.getPage());
        return goodsResultListVO;
    }

    @Override
    public Pair<Boolean, List<String>> outExecuteGoods(List<String> goodsCodes) {
        return null;
    }

    /**
     * 批量插入
     *
     * @param goodsAddParamList
     * @return
     */
    @Override
    public boolean addGoodsList(List<GoodsAddParam> goodsAddParamList) {
        return goodsManager.addGoodsList(BeanUtils.copyProperties(goodsAddParamList, GoodsBO.class));
    }


    @Override
    public void asyncImportTask(String urlAddr, String uid) {

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean forbiddenGoods(Long id) {
        try {
            boolean ret = goodsManager.forbiddenGoods(id);
            GoodsBO goodsBO = goodsManager.getById(id);
            this.goodsManagementFacade.forbiddenGoodsByGoodsCode(goodsBO.getGoodsCode());
            return ret;
        } catch (Exception e) {
            throw new BusinessException("商品禁用失败");
        }
    }

    @Override
    public GoodsResult getByUserIdAndSku(Long userId, String sku) {
        return BeanUtils.copyProperties(goodsManager.getByUserIdAndSku(userId, sku), GoodsResult.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableList(List<String> ids) {
        for (String id : ids) {
            try {
                GoodsBO goodsBO = goodsManager.getById(Long.valueOf(id));
                goodsBO.setStatus(GoodsStatus.ENABLE);
                goodsBO.setUpdateTime(System.currentTimeMillis());
                goodsBO.setUpdateBy(SimpleUserHelper.getRealUserId());
                goodsManager.updateGoodsById(goodsBO);
                remoteGoodsFacade.syncGoodsForDT(goodsBO);
                goodsManagementFacade.enableByGoodsCode(goodsBO.getGoodsCode());
            } catch (Exception e) {
                log.error("===========货品启用异常========={}", id, e);
                throw new BusinessException("商品启用失败");
            }
        }
        return true;
    }

    @Override
    public boolean deleteById(String id, Long operatorId) {
        log.info("[GoodsFacadeImpl-deleteById]=========货品删除========id:{},operatorId:{}", id, operatorId);
        GoodsBO bo = goodsManager.getById(Long.valueOf(id));
        if (bo == null) {
            throw new BusinessException("货品" + id + "不存在");
        }
        GoodsBO goodsBO = BeanUtils.copyProperties(bo, GoodsBO.class);

        // 1 判断货品是否存在库存
        String goodsCode = goodsBO.getGoodsCode();
        Pair<Boolean, String> canModifyFlag = remoteGoodsFacade.getCanModifyFlag(goodsBO.getGoodsCode(), goodsBO.getUserId(), null, 2);
        if (!canModifyFlag.getLeft()) {
            throw new BusinessException(canModifyFlag.getRight());
        }
        OptContextParam optContextParam = new OptContextParam();
        optContextParam.setGoodsCode(goodsBO.getGoodsCode());
        optContextParam.setUserId(goodsBO.getUserId());
        //依赖系统是否可以删除
        Pair<Boolean, String> canDeleteFlag = goodsCenterDependentManager.check(optContextParam);
        if (!canDeleteFlag.getLeft()) {
            throw new BusinessException(canDeleteFlag.getRight());
        }
        //0 依赖系统删除
        goodsCenterDependentManager.delete(optContextParam);
        //1.1删除ccs 报备
        deleteInCcs(bo.getUserId(), goodsCode);
        // 2 删除备案
        String newGoodsCode = "D-" + goodsCode;
        remoteGoodsFacade.deleteGoodsRecord(bo.getUserId(), goodsCode, newGoodsCode);
        // 3 删除货品管理
        GoodsManagementQueryParam param = new GoodsManagementQueryParam();
        param.setGoodsCode(goodsCode);
        goodsManagementFacade.removeByQueryParam(param);
        // 4 删除货品
        goodsBO.setUpdateBy(operatorId);
        //重置version
        GoodsBO tmp = goodsManager.getById(Long.valueOf(id));
        goodsBO.setVersion(tmp.getVersion());
        if (goodsManager.deleteById(goodsBO)) {
            remoteGoodsFacade.addOperationLog(new GoodsOperationLogParam(operatorId,
                            OperationType.OT_GOODS_DELETE, "来自业务删除"), goodsCode, "{}",
                    "{}");
            // 5 删除代塔T货品
            return remoteGoodsFacade.deleteGoodsForDT(bo);

        } else {
            return false;
        }

    }

    @Override
    public int count() {
        return goodsManager.count();
    }

    @Override
    public boolean syncGoodsForDaitaT(Long TuserId, String TuserName, Long userId) {
        try {
            Long tenantId = SimpleTenantHelper.getTenantId();
            // 1.开启异步线程进行货品同步
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    SimpleTenantHelper.setTenantId(tenantId);
                    GoodsSyncMessage goodsSyncMessage = new GoodsSyncMessage();
                    goodsSyncMessage.setCode(1);
                    goodsSyncMessage.setTuserId(TuserId);
                    goodsSyncMessage.setUserId(userId);
                    // 2.查询所有货品
                    GoodsSearch param = new GoodsSearch();
                    param.setUserId(userId);
                    List<GoodsBO> goodsBOS = new ArrayList<>(goodsManager.listGoodsByParam(param));
                    if (CollectionUtils.isNotEmpty(goodsBOS)) {
                        // 3.过滤未同步货品
                        for (GoodsBO goodsBO : goodsBOS) {
                            GoodsSearch search = new GoodsSearch();
                            search.setGoodsCode(MyStringUtils.getDaitaTGoodsCode(goodsBO.getGoodsCode()));
                            GoodsBO originalBO = goodsManager.getGoodsByParam(search);
                            if (originalBO == null) {
                                try {
                                    // 4.进行同步
                                    goodsBO.setGoodsCode(MyStringUtils.getDaitaTGoodsCode(goodsBO.getGoodsCode()));
                                    goodsBO.setSku(MyStringUtils.getDaitaTSku(goodsBO.getSku(), goodsBO.getUserId()));
                                    goodsBO.setId(null);
                                    goodsBO.setOriginUserId(goodsBO.getUserId());
                                    goodsBO.setOriginUserName(goodsBO.getUserName());
                                    goodsBO.setType(GoodsType.DUTY_PAID);
                                    goodsBO.setUserId(TuserId);
                                    goodsBO.setUserName(TuserName);
                                    goodsBO.setCreateTime(null);
                                    goodsBO.setUpdateTime(null);
                                    goodsBO.setSource("综保圈货");
                                    goodsManager.addGoods(goodsBO);
                                } catch (Exception e) {
                                    goodsSyncMessage.setCode(0);
                                    goodsSyncMessage.getFailList().put(goodsBO.getGoodsCode(), e.getMessage());
                                    log.error("[GoodsFacadeImpl-syncGoods]=========代塔T用户货品同步失败========={}", goodsBO.getGoodsCode(), e);
                                }
                            }
                        }
                    }
                    // 5.发送mq消息
                    sendMessage(goodsSyncMessage);
                }
            });
            return true;
        } catch (Exception e) {
            log.error("[GoodsFacadeImpl-syncGoods]=========代塔T同步货品失败=========", e);
        }
        return false;
    }

    private boolean sendMessage(GoodsSyncMessage goodsSyncMessage) {
        Map keysMap = Maps.newHashMap();
        // 配置消息KEYS会显示在RocketMQ的Key字段
        String format = DateFormatUtils.format(new Date(System.currentTimeMillis()), "yyyy-MM-dd hh:mm:ss");
        keysMap.put("KEYS", goodsSyncMessage.getTuserId() + "-" + format);
        MessageHeaders messageHeaders = new MessageHeaders(keysMap);
        SpringMessage message = SpringMessage.createMessage(goodsSyncMessage, messageHeaders);
        SendResult sendResult = doSyncSend(5000L, null, message);
        if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
            log.warn("代塔T同步货品MQ发送失败1次：单号：{} 正在重试...", goodsSyncMessage.getTuserId() + "-" + format);
            sendResult = doSyncSend(5000L, null, message);
            if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
                log.error("======代塔T同步货品MQ发送失败2次，需要手工处理========processMessage={}", JSON.toJSONString(message));
                return false;
            } else {
                return true;
            }
        } else {
            return true;
        }
    }

    private SendResult doSyncSend(Long timeout, Integer delayLevel, SpringMessage message) {
        String goodsSyncTopic = goodsNacosConfig.getGoodsSyncTopic();
        SendResult sendResult;
        if (Objects.nonNull(delayLevel)) {
            sendResult = springRocketMQProducer.syncSend(goodsSyncTopic, message, timeout, delayLevel);
        } else {
            sendResult = springRocketMQProducer.syncSend(goodsSyncTopic, message, timeout);
        }
        return sendResult;
    }

    @Override
    public boolean listEditValuation(List<String> ids, IsValuation isValuation, Long operatorUserId) {
        boolean flag = true;
        List<OperationLogsAddParam> logList = new ArrayList<>();

        UserRpcResult userRpcResult = remoteGoodsFacade.findUser(operatorUserId);
        for (String id : ids) {
            try {
                GoodsBO goodsBO = goodsManager.getById(Long.valueOf(id));
                if (goodsBO != null) {
                    String beforeJson = JSON.toJSONString(goodsBO);

                    goodsBO.setIsValuation(isValuation);
                    String afterJson = JSON.toJSONString(goodsBO);

                    goodsManager.updateGoodsById(goodsBO);

                    logList.add(convertLog(operatorUserId, userRpcResult, OperationType.OT_GOODS_EDIT, "批量修改估值属性",
                            goodsBO.getGoodsCode(), beforeJson, afterJson));
                }
            } catch (Exception e) {
                log.error("[GoodsFacadeImpl-listEditValuation]=============货品修改估值属性异常============:{}", id, e);
                flag = false;
            }
        }

        remoteGoodsFacade.addOperationLogs(logList);

        return flag;
    }

    private OperationLogsAddParam convertLog(Long userId, UserRpcResult userRpcResult, OperationType operationType, String reason,
                                             String goodsCode, String beforeJson, String afterJson) {
        OperationLogsAddParam addParam = new OperationLogsAddParam();

        addParam.setBusinessNo(goodsCode);
        addParam.setOperationMessage(beforeJson);
        addParam.setOperationType(operationType);
        addParam.setReason(reason);
        addParam.setRequestMessage(afterJson);

        addParam.setUserId(userId);
        if (userRpcResult != null) {
            addParam.setUserName(userRpcResult.getUserName());
            addParam.setNickName(userRpcResult.getNickName());
            addParam.setEmail(userRpcResult.getEmail());
            addParam.setMobile(userRpcResult.getMobile());
            addParam.setUserType(UserTypeEnum.parse(userRpcResult.getType()));
        }

        return addParam;
    }

    @Override
    public boolean addCargoCodeForByte(Long userId, List<GoodsAddParam> list) {
        List<OperationLogsAddParam> logList = new ArrayList<>();

        for (GoodsAddParam goodsAddParam : list) {
            GoodsBO goods = goodsManager.getByUserIdAndGoodsCode(userId, goodsAddParam.getGoodsCode());
            String beforeJson = JSON.toJSONString(goods);

            goods.setCargoCode(goodsAddParam.getCargoCode());
            String afterJson = JSON.toJSONString(goods);

            goodsManager.updateGoodsById(goods);
            logList.add(convertLog(goods.getGoodsCode(), beforeJson, afterJson));
        }

        //操作日志
        remoteGoodsFacade.addOperationLogs(logList);

        return true;
    }

    private OperationLogsAddParam convertLog(String goodsCode, String beforeJson, String afterJson) {
        OperationLogsAddParam addParam = new OperationLogsAddParam();

        addParam.setUserId(0L);
        addParam.setUserName("openAPI");
        addParam.setUserType(UserTypeEnum.OPEN_API);
        addParam.setOperationType(OperationType.OT_GOODS_SYSTEM_UPDATE);

        addParam.setBusinessNo(goodsCode);
        addParam.setReason("字节下发入库单接口，填入货品基础信息的外部货品ID");
        addParam.setOperationMessage(beforeJson);
        addParam.setRequestMessage(afterJson);

        return addParam;
    }

    @Override
    public void before(GoodsQueryParam searchParam) {

    }

    @Override
    public List<GoodsExportExcel> getDataList(GoodsQueryParam searchParam) {
        ListVO<GoodsResult> goodsResultListVO = pageListGoodsByParam(searchParam);
        // 设置总页数
        this.setTotalPage(goodsResultListVO.getPage().getTotalPage());
        List<GoodsResult> list = goodsResultListVO.getDataList();
        if (CollectionUtils.isNotEmpty(list)) {
            // 生成sheet1对应的数据列表:结算单列表
            Date startSnapshotTime = MinusDaysUtils.getMinusStartDate(new Date(), 30);
            Date endSnapshotTime = MinusDaysUtils.getMinusEndDate(new Date());
            list.forEach(result -> {
                BigDecimal carryDay = goodsInventoryReportFacade.getByCache(null, result.getUserId(), result.getGoodsCode(),
                        startSnapshotTime, endSnapshotTime);
                result.setCarryDay(carryDay);
            });
            List<GoodsExportExcel> firstList = Lists.newArrayList();
            for (GoodsResult goodsResult : list) {
                GoodsExportExcel goodsExportExcel = BeanUtils.copyProperties(goodsResult, GoodsExportExcel.class);
                if (GoodsBatchManagement.YES.equals(goodsResult.getBatchManagement())) {
                    goodsExportExcel.setBatchManagementName("启用");
                } else {
                    goodsExportExcel.setBatchManagementName("不启用");
                }
                if (GoodsOpenPeriodValidity.YES.equals(goodsResult.getOpenPeriodValidity())) {
                    goodsExportExcel.setOpenPeriodValidityName("是");
                } else {
                    goodsExportExcel.setOpenPeriodValidityName("否");
                }
                FastDateFormat format = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");
                goodsExportExcel.setCreateTimeFormat(format.format(goodsResult.getCreateTime()));
                goodsExportExcel.setUpdateTimeFormat(format.format(goodsResult.getUpdateTime()));
                firstList.add(goodsExportExcel);
            }
            return firstList;
        } else {
            return null;
        }
    }

    @Override
    public int syncGoodsManagementForOwner(Long userId, String ownerCode, Long operatorUserId) {
        try {
            List<GoodsManagementAddParam> addParams = findAddList(userId, ownerCode);
            if (CollectionUtils.isEmpty(addParams)) {
                return 0;
            }
            Long tenantId = SimpleTenantHelper.getTenantId();
            executorService.execute(() -> {
                try {
                    SimpleTenantHelper.setTenantId(tenantId);
                    // 批量插入
                    goodsManagementFacade.addList(addParams, false);

                    // 下发到wms
                    sendToWms(addParams, operatorUserId);

                    // 操作日志
                    addSyncLogs(null, ownerCode, operatorUserId, addParams);
                } catch (Exception e) {
                    log.error("[GoodsFacadeImpl-syncGoodsManagementForOwner]=========同步货品管理异常,货主:{}=========", ownerCode, e);
                    String content = "同步货品管理异常：货主=" + ownerCode + ", 异常信息=" + e.getMessage();
                    String msg = "{\"msgtype\": \"text\",\"text\": {\"content\": \"" + content + "\",\"mentioned_mobile_list\":" + Arrays.toString(goodsNacosConfig.getSyncMentionedMobile().split(",")) + "}}";
                    HttpRequestUtils.postJson(msg, goodsNacosConfig.getSyncRobotUrl(), "同步货品管理异常!");
                }
            });

            return addParams.size();
        } catch (Exception e) {
            log.error("[GoodsFacadeImpl-syncGoodsManagementForOwner]=========异步线程执行异常=========", e);
            return 0;
        }
    }

    /**
     * 查询并构造待同步的货主货品对象
     *
     * @param userId
     * @param ownerCode
     * @return
     */
    private List<GoodsManagementAddParam> findAddList(Long userId, String ownerCode) {
        // 查询
        GoodsQueryParam param = new GoodsQueryParam();
        param.setUserId(userId);
        List<GoodsResult> goodsResults = listGoodsByParam(param);
        if (CollectionUtils.isEmpty(goodsResults)) {
            return Collections.emptyList();
        }

        // 构造
        List<GoodsManagementAddParam> addParams = BeanUtils.copyProperties(goodsResults, GoodsManagementAddParam.class);
        addParams.forEach(addParam -> {
            addParam.setId(null);
            addParam.setOwnerCode(ownerCode);
            addParam.setStatus(GoodsStatus.ENABLE);
        });

        // 过滤已存在的货品管理
        List<String> goodsCodes = goodsResults.stream().map(GoodsResult::getGoodsCode).collect(Collectors.toList());
        GoodsManagementQueryParam queryParam = new GoodsManagementQueryParam();
        queryParam.setOwnerCode(ownerCode);
        queryParam.setUserId(userId);
        queryParam.setGoodsCodes(goodsCodes);
        List<GoodsManagementResult> goodsManagementResults = goodsManagementFacade.listByQueryParam(queryParam);
        if (CollectionUtils.isNotEmpty(goodsManagementResults)) {
            List<String> exists = goodsManagementResults.stream().map(GoodsManagementResult::getGoodsCode).collect(Collectors.toList());
            addParams.removeIf(a -> exists.contains(a.getGoodsCode()));
        }

        return addParams;
    }

    @Override
    public int syncGoodsManagementForOwner(String sourceOwnerCode, String targetOwnerCode, Long operatorUserId) {
        if (StringUtils.isBlank(targetOwnerCode)) {
            return 0;
        }
        OwnerResult ownerResult = remoteGoodsFacade.getOwnerByCode(targetOwnerCode);
        if (ownerResult == null) {
            throw new BusinessException(String.format("货主信息查询失败targetOwnerCode=%s", targetOwnerCode));
        }
        EntityWarehouseResult entityWarehouseResult = remoteConfigFacade.getEntityWarehouseFacade(ownerResult.getEntityWarehouseCode());
        if (com.danding.business.common.ares.enums.common.SendStatus.READY_SEND.getCode().equals(ownerResult.getSendStatus())
                && Objects.equals(SYSTEM_DT, Optional.ofNullable(entityWarehouseResult).map(EntityWarehouseResult::getSystemCode).orElse(null))) {
            throw new BusinessException(String.format("货主信息未下发targetOwnerCode=%s", targetOwnerCode));
        }
        if (StringUtils.isBlank(sourceOwnerCode)) {
            return syncGoodsManagementForOwner(ownerResult.getUserId(), targetOwnerCode, operatorUserId);
        } else {
            return doSyncGoodsManagement(sourceOwnerCode, targetOwnerCode, operatorUserId);
        }
    }

    private int doSyncGoodsManagement(String sourceOwnerCode, String targetOwnerCode, Long operatorUserId) {
        try {
            // 1、 查询源货主下所有货品，并构造待同步添加的列表
            List<GoodsManagementAddParam> addParams = findAddList(sourceOwnerCode, targetOwnerCode);
            if (CollectionUtils.isEmpty(addParams)) {
                return 0;
            }

            // 2、 过滤已存在的货品管理
            filterExisted(addParams, targetOwnerCode);
            if (CollectionUtils.isEmpty(addParams)) {
                return 0;
            }
            OwnerResult sourceOwner = remoteGoodsFacade.getOwnerByCode(sourceOwnerCode);
            OwnerResult targetOwner = remoteGoodsFacade.getOwnerByCode(targetOwnerCode);

            EntityWarehouseResult sourceEntityWarehouse = entityWarehouseFacade.getDetailByCode(sourceOwner.getEntityWarehouseCode());
            EntityWarehouseResult targetEntityWarehouse = entityWarehouseFacade.getDetailByCode(targetOwner.getEntityWarehouseCode());

            TradeType sourceEntityWarehouseTradeType = sourceEntityWarehouse.getTradeType();
            TradeType targetEntityWarehouseTradeType = targetEntityWarehouse.getTradeType();

            boolean syncRecordWarehouse = TradeType.BONDED.equals(sourceEntityWarehouseTradeType) && TradeType.BONDED.equals(targetEntityWarehouseTradeType);

            Long tenantId = SimpleTenantHelper.getTenantId();
            executorService.execute(() -> {
                try {
                    SimpleTenantHelper.setTenantId(tenantId);
                    // 3、 批量插入
                    goodsManagementFacade.addList(addParams, syncRecordWarehouse);

                    // 4、下发到wms
                    sendToWms(addParams, operatorUserId);

                    // 5、操作日志
                    addSyncLogs(sourceOwnerCode, targetOwnerCode, operatorUserId, addParams);
                } catch (Exception e) {
                    log.error("[GoodsFacadeImpl-syncGoodsManagementForOwner]=========同步货品管理异常, 源货主={}, 目标货主={} ",
                            sourceOwnerCode, targetOwnerCode, e);
                    String content = "同步货品管理异常：源货主=" + sourceOwnerCode + ", 目标货主=" + targetOwnerCode + ", 异常信息=" + e.getMessage();
                    String msg = "{\"msgtype\": \"text\",\"text\": {\"content\": \"" + content + "\",\"mentioned_mobile_list\":" + Arrays.toString(goodsNacosConfig.getSyncMentionedMobile().split(",")) + "}}";
                    HttpRequestUtils.postJson(msg, goodsNacosConfig.getSyncRobotUrl(), "同步货品管理异常!");
                }
            });

            return addParams.size();
        } catch (Exception e) {
            log.error("[GoodsFacadeImpl-syncGoodsManagementForOwner]=========异步线程执行异常=========", e);
            return 0;
        }
    }

    /**
     * 添加货主同步日志
     *
     * @param sourceOwnerCode
     * @param targetOwnerCode
     * @param operatorUserId
     * @param addParams
     */
    private void addSyncLogs(String sourceOwnerCode, String targetOwnerCode, Long operatorUserId,
                             List<GoodsManagementAddParam> addParams) {
        String reason = buildReason(sourceOwnerCode, targetOwnerCode);

        addParams.forEach(addParam -> remoteGoodsFacade.addOperationLog(
                new GoodsOperationLogParam(operatorUserId, OperationType.OT_GOODS_OWNER_SYNC, reason.toString()),
                addParam.getGoodsCode(), "{}", JSON.toJSONString(addParam)));
    }

    private String buildReason(String sourceOwnerCode, String targetOwnerCode) {
        StringBuilder reason = new StringBuilder("同步");

        String sourceOwnerName = "货品默认";
        if (StringUtils.isNotBlank(sourceOwnerCode)) {
            sourceOwnerName = remoteGoodsFacade.getOwnerByCode(sourceOwnerCode).getOwnerName();
            reason.append("货主[").append(sourceOwnerName).append("]");
        } else {
            reason.append(sourceOwnerName);
        }

        String targetOwnerName = remoteGoodsFacade.getOwnerByCode(targetOwnerCode).getOwnerName();
        reason.append("的货品信息到货主[").append(targetOwnerName).append("]");

        return reason.toString();
    }

    /**
     * 下发到wms
     *
     * @param addParams
     */
    private void sendToWms(List<GoodsManagementAddParam> addParams, Long operatorUserId) {
        if (CollectionUtils.isEmpty(addParams)) {
            return;
        }

        OwnerResult owner = remoteGoodsFacade.getOwnerByCode(addParams.get(0).getOwnerCode());
        UserRpcResult operator = remoteGoodsFacade.findUser(operatorUserId);

        //模型转换
        List<GoodsIssueAddParam> list = addParams.stream().map(addParam -> {
            GoodsIssueAddParam issueParam = new GoodsIssueAddParam();
            issueParam.setGoodsCode(addParam.getGoodsCode());
            issueParam.setOwnerCode(addParam.getOwnerCode());
            issueParam.setUserId(owner.getUserId());
            issueParam.setEntityWarehouseCode(owner.getEntityWarehouseCode());
            issueParam.setActionType(ActionType.MODIFY);
            issueParam.setOperator(operatorUserId);
            issueParam.setUserName(operator != null ? operator.getUserName() : StringUtils.EMPTY);

            return issueParam;
        }).collect(Collectors.toList());

        //下发
        goodsIssueFacade.issueGoodsList(list);
    }

    /**
     * 查询并构造待同步添加的列表
     *
     * @param sourceOwnerCode
     * @param targetOwnerCode
     * @return
     */
    private List<GoodsManagementAddParam> findAddList(String sourceOwnerCode, String targetOwnerCode) {
        GoodsManagementQueryParam queryParam = new GoodsManagementQueryParam();
        queryParam.setOwnerCode(sourceOwnerCode);
        List<GoodsManagementResult> goodsManagements = goodsManagementFacade.listByQueryParam(queryParam);
        if (CollectionUtils.isEmpty(goodsManagements)) {
            return Collections.emptyList();
        }

        List<GoodsManagementAddParam> addParams = BeanUtils.copyProperties(goodsManagements, GoodsManagementAddParam.class);
        addParams.forEach(addParam -> {
            addParam.setId(null);
            addParam.setOwnerCode(targetOwnerCode);
            addParam.setStatus(GoodsStatus.ENABLE);
        });

        return addParams;
    }

    /**
     * 过滤已存在的货品管理
     *
     * @param addParams
     * @param targetOwnerCode
     */
    private void filterExisted(List<GoodsManagementAddParam> addParams, String targetOwnerCode) {
        GoodsManagementQueryParam queryParam = new GoodsManagementQueryParam();
        queryParam.setOwnerCode(targetOwnerCode);
        queryParam.setGoodsCodes(addParams.stream().map(GoodsManagementAddParam::getGoodsCode).collect(Collectors.toList()));
        List<GoodsManagementResult> goodsManagementResults = goodsManagementFacade.listByQueryParam(queryParam);
        if (CollectionUtils.isEmpty(goodsManagementResults)) {
            return;
        }

        List<String> exists = goodsManagementResults.stream().map(GoodsManagementResult::getGoodsCode).collect(Collectors.toList());
        addParams.removeIf(a -> exists.contains(a.getGoodsCode()));
    }

    @Override
    public void goodsReportCalc(Long userId, String goodsCode, Date startTime, Date endTime) {
        GoodsSearch goodsSearch = new GoodsSearch();
        goodsSearch.setUserId(userId);
        goodsSearch.setGoodsCode(goodsCode);
        List<GoodsBO> goodsBOS = goodsManager.listGoodsByParam(goodsSearch);
        goodsBOS.forEach(goodsBO -> {
            try {
                GoodsInventoryDailySnapshotQueryParam snapshotQueryParam = new GoodsInventoryDailySnapshotQueryParam();
                snapshotQueryParam.setUserId(goodsBO.getUserId());
                snapshotQueryParam.setGoodsCode(goodsBO.getGoodsCode());
                snapshotQueryParam.setSnapshotDateStart(startTime);
                snapshotQueryParam.setSnapshotDateEnd(endTime);
                GoodsInventoryDailySnapshotResult snapshotResult = goodsInventoryDailySnapshotFacade.getBySnapshotDate(snapshotQueryParam);
                if (ObjectUtil.isNotNull(snapshotResult) && snapshotResult.getAvailableNum() != null) {
                    //近30天商品累计正品库存数量
                    goodsBO.setQualityNum(snapshotResult.getAvailableNum().longValue());
                } else {
                    goodsBO.setQualityNum(0L);
                }

                if (ObjectUtil.isNotNull(snapshotResult) && snapshotResult.getInstockNum() != null) {
                    //近30天商品累计库存数量
                    goodsBO.setInstockNum(snapshotResult.getInstockNum().longValue());
                } else {
                    goodsBO.setInstockNum(0L);
                }

                InOrderTotalNumResult inOrderTotalNumResult = remoteGoodsFacade.getActualOutOrderResultV2(goodsBO.getUserId(), goodsBO.getGoodsCode(),
                        null, startTime.getTime(), endTime.getTime(), "C");
                if (ObjectUtil.isNotNull(inOrderTotalNumResult) && ObjectUtil.isNotNull(inOrderTotalNumResult.getNum())) {
                    //近30天商品C单出库数量
                    goodsBO.setToCNum(inOrderTotalNumResult.getNum().longValue());
                } else {
                    goodsBO.setToCNum(0L);
                }

                inOrderTotalNumResult = remoteGoodsFacade.getActualOutOrderResultV2(goodsBO.getUserId(), goodsBO.getGoodsCode(),
                        null, startTime.getTime(), endTime.getTime(), "B");
                if (ObjectUtil.isNotNull(inOrderTotalNumResult) && ObjectUtil.isNotNull(inOrderTotalNumResult.getNum())) {
                    //近30天商品B单出库数量
                    goodsBO.setToBNum(inOrderTotalNumResult.getNum().longValue());
                } else {
                    goodsBO.setToBNum(0L);
                }

                //近30天商品出库数量
                goodsBO.setTotalNum(goodsBO.getToBNum() + goodsBO.getToCNum());
            } catch (Exception e) {
                log.error("goodsReportCalc error", e);
            }
        });

        //计算货品分层
        calculateGoodsLayering(goodsBOS.stream().collect(Collectors.groupingBy(GoodsBO::getUserId)));

        //更新
        goodsBOS.forEach(goodsBO -> goodsManager.updateGoodsById(goodsBO));
    }

    /**
     * 计算货品分层
     *
     * @param sourceMap
     */
    private void calculateGoodsLayering(Map<Long, List<GoodsBO>> sourceMap) {
        if (CollectionUtils.isEmpty(sourceMap)) {
            return;
        }

        sourceMap.forEach((userId, goodsList) -> {
            if (CollectionUtils.isEmpty(goodsList)) {
                return;
            }

            List<Long> accumulation = new ArrayList<>(goodsList.size());

            //0、当前用户所有货品的”近30天商品出库数量“的总和
            BigDecimal totalNum = new BigDecimal(goodsList.stream().mapToLong(GoodsBO::getTotalNum).sum());

            //1、按”近30天商品出库数量“从大到小排序
            goodsList.sort(Comparator.comparing(GoodsBO::getTotalNum).reversed());

            //2、按规则计算并设置”货品分层“
            for (int goodIndex = 0, accumulateIndex = -1; goodIndex < goodsList.size(); goodIndex++, accumulateIndex++) {
                //当前排名之前的所有货品的出库量累计
                long accumulate = accumulateIndex < 0 ? 0L : accumulation.get(accumulateIndex);

                //计算、设置
                goodsList.get(goodIndex).setGoodsLayering(getGoodsLayering(goodsList.get(goodIndex), accumulate, totalNum));

                //更新累计
                accumulation.add(accumulateIndex + 1, accumulate + goodsList.get(goodIndex).getTotalNum());
            }
        });
    }

    private GoodsLayering getGoodsLayering(GoodsBO goodsBO, Long accumulate, BigDecimal totalNum) {
        if (goodsBO.getTotalNum() == null || goodsBO.getTotalNum() <= 0) {
            //近30天无销售出库
            if (goodsBO.getInstockNum() == null || goodsBO.getInstockNum() <= 0) {
                //近30天无库存
                return GoodsLayering.OTHER;
            } else {
                //近30天有库存
                return GoodsLayering.NO_SALE;
            }
        } else {
            //近30天有销售出库
            return GoodsLayering.getByPercent(new BigDecimal(accumulate).divide(totalNum, 6, RoundingMode.HALF_UP));
        }
    }

    @Override
    public GoodsCarryDayReportResult goodsCarryDayReport(Long userId) {
        GoodsResult goodsResult = BeanUtils.copyProperties(goodsManager.goodsCarryDayReport(userId), GoodsResult.class);
        GoodsCarryDayReportResult goodsCarryDayReportResult = new GoodsCarryDayReportResult();
        if (Objects.nonNull(goodsResult)) {
            goodsCarryDayReportResult = BeanUtils.copyProperties(goodsResult, GoodsCarryDayReportResult.class);
            try {
                //动销率 近30天商品累计销售数量/近30天商品累计正品库存数量
                if (goodsCarryDayReportResult.getQualityNum() != 0) {
                    double sellingRate = BigDecimalUtils.mul(BigDecimalUtils.div(goodsCarryDayReportResult.getTotalNum(), goodsCarryDayReportResult.getQualityNum()), 100D);
                    goodsCarryDayReportResult.setSellingRate(new BigDecimal(String.valueOf(sellingRate)).setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros());
                }
                //周转天数 近30天日均正品库存数据/近30天日均销售出库数
                if (goodsCarryDayReportResult.getTotalNum() != 0) {
                    double carryDay = BigDecimalUtils.div(String.valueOf(goodsCarryDayReportResult.getQualityNum()), String.valueOf(goodsCarryDayReportResult.getTotalNum()));
                    goodsCarryDayReportResult.setCarryDay(new BigDecimal(String.valueOf(carryDay)).setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros());
                }
                //近30天日均商品累计正品库存数量
                double perQualityNum = BigDecimalUtils.div(String.valueOf(goodsCarryDayReportResult.getQualityNum()), "30");
                goodsCarryDayReportResult.setPerQualityNum(new BigDecimal(String.valueOf(perQualityNum)).setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros());
                //近30天日均商品出库数量
                double perTotalNum = BigDecimalUtils.div(String.valueOf(goodsCarryDayReportResult.getTotalNum()), "30");
                goodsCarryDayReportResult.setPerTotalNum(new BigDecimal(String.valueOf(perTotalNum)).setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros());
                //库存预警个数
                GoodsBatchInventoryQueryParam goodsBatchInventoryQueryParam = new GoodsBatchInventoryQueryParam();
                goodsBatchInventoryQueryParam.setUserId(userId);
                goodsBatchInventoryQueryParam.setInventoryStatus(InventoryStatus.ALARM);
                goodsCarryDayReportResult.setAlarmNum(goodsInventoryReportFacade.alarmGoodsBatchInventoryCount(goodsBatchInventoryQueryParam));
            } catch (Exception e) {
                log.error("goodsCarryDayReport error", e);
            }
        }
        return goodsCarryDayReportResult;
    }

    @Override
    public void goodCargoCodeRepeatWarning() {
        List<GoodsBO> cargoCodeRepeatList = goodsManager.getCargoCodeRepeatList();
        cargoCodeRepeatList = cargoCodeRepeatList.stream().filter(cargoCodeRepeat -> !StringUtils.isBlank(cargoCodeRepeat.getCargoCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cargoCodeRepeatList)) {
            return;
        }
        StringBuilder sb = new StringBuilder();
        sb.append("以下外部货品ID对应了多个内部货品ID，请与商家及时核对并修正\n");
        for (int i = 0; i < cargoCodeRepeatList.size(); i++) {
            sb.append(cargoCodeRepeatList.get(i).getCargoCode());
            if (i != cargoCodeRepeatList.size() - 1) {
                sb.append("\n");
            }
        }
        WarningUtils.backWarning(sb.toString(), goodsNacosConfig.getRobotUrl(), goodsNacosConfig.getMentionedMobile());
    }

    @Override
    public Map<String, GoodsResult> findGoodsMap(List<String> goodsCodes) {
        if (CollectionUtils.isEmpty(goodsCodes)) {
            return Collections.emptyMap();
        }

        GoodsQueryParam queryParam = new GoodsQueryParam();
        queryParam.setGoodsCodes(goodsCodes);
        List<GoodsResult> list = listGoodsByParam(queryParam);
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(GoodsResult::getGoodsCode, o -> o, (k1, k2) -> k1));
    }

    @Override
    public ListVO<GoodsLogResult> findLogs(GoodsLogQueryParam queryForm) {
        OperationLogsQueryParam param = new OperationLogsQueryParam();
        param.setBusinessNo(queryForm.getGoodsCode());
        param.setCurrentPage(queryForm.getCurrentPage());
        param.setPageSize(queryForm.getPageSize());
        ListVO<OperationLogsResult> result = operationLogsFacade.pageListByQueryParam(param);

        return ListVO.build(result.getPage(), goodsLogConvertor.convert(result.getDataList()));
    }

    @Override
    public void addOperationLog(GoodsOperationLogParam logParam, String goodsCode, String beforeJson, String afterJson) {
        remoteGoodsFacade.addOperationLog(logParam, goodsCode, beforeJson, afterJson);
    }

    /**
     * 发送新增mq
     *
     * @param goodsBO
     */
    private void sendMessageToOMS(GoodsBO goodsBO) {
        try {
            Map keysMap = Maps.newHashMap();
            keysMap.put("KEYS", goodsBO.getSku());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message = SpringMessage.createMessage(JSON.toJSONString(goodsBO), messageHeaders);
            springRocketMQProducer.syncSend(this.goodsConfiguration.getCreateGoodsTopic(), message, 5000L, 1);
        } catch (Exception e) {
            log.error("[GoodsFacadeImpl-sendMessageToOMS]==============发送新增消息异常===============userId:{},goodsCode:{},ex:{}", goodsBO.getUserId(), goodsBO.getSku(), e);
        }
    }

    private void deleteInCcs(Long userId, String goodsCodes) {
        if (!proxyConfig.isSwitchCcsCenter()) {
            GoodsRecordQueryParam queryParam = new GoodsRecordQueryParam();
            queryParam.setGoodsCode(goodsCodes);
            queryParam.setUserId(userId);
            //  queryParam.setRecordStatus(RecordStatus.SUCCESS);
            List<GoodsRecordResult> recordResults = goodsRecordFacade.listByQueryParam(queryParam);
            recordResults.stream().forEach(goodsRecord -> {
                DeleteGoodsRecordReqVO reqVO = new DeleteGoodsRecordReqVO();
                reqVO.setRecordType(goodsRecord.getRecordType().name().toUpperCase());
                // reqVO.setRecordType(RecordType.OLD.name().toUpperCase());
                reqVO.setSkuId(goodsRecord.getSku());
                reqVO.setTenantId(goodsRecord.getUserId().toString());
                reqVO.setCustomsBookNo(goodsRecord.getAccountCode());
                reqVO.setProductId(goodsRecord.getMaterialCode());
                log.info("deleteInCcs goodsRecordRpc.deleteGoodsRecord param:{}", JSON.toJSONString(reqVO));
                RpcResult<String> ccsResult = goodsRecordRpc.deleteGoodsRecord(reqVO);
                log.info("deleteInCcs goodsRecordRpc.deleteGoodsRecord result:{}", JSON.toJSONString(ccsResult));
                if (ccsResult == null) {
                    log.info("ccs删除接品返回为空" + reqVO.getSkuId());
                }
            });
        }
    }

    public void pushGoodsToDSTP(List<String> goodsList, String ownerCode) {
        if (CollectionUtil.isEmpty(goodsList)) {
            throw new BusinessException("货品ID不能为空!");
        }
        if (StringUtils.isBlank(ownerCode)) {
            throw new BusinessException("货主编码不能为空!");
        }
        Map<String, Object> pushMap = new HashMap<>();
        pushMap.put("enterpriseCode", SOURCE_CODE_DSTP);
        pushMap.put("enterpriseName", "金华代塔供应链管理有限公司");
        for (String goodsCode : goodsList) {
            try {
                List<Object> skuBatchList = new ArrayList<>();
                Map<String, Object> goodsMap = new HashMap<>();
                GoodsBO goodsBO = goodsManager.getByUserIdAndGoodsCode(null, goodsCode);
                goodsMap.put("entGoodsNo", goodsBO.getSku());
                goodsMap.put("entGoodsName", goodsBO.getGoodsName());
                skuBatchList.add(goodsMap);
                pushMap.put("goodsList", skuBatchList);

                String request = postJson(pushMap, goodsNacosConfig.getCallBackUrlDSTP() + "/open/logistics/goods", SYSTEM_DSTP
                        , DstpSignUtils.buildRequestHeaders(goodsNacosConfig.getDstpAppKey(), goodsNacosConfig.getDstpAppSecret()));
                log.info("[GoodsFacadeImpl-pushGoodsToDSTP]================推送dstp货品信息===============DSTP回传返回值:" + request);
                JSONObject jsonObject = JSON.parseObject(request);
                String results = jsonObject.getString("flag");
                if (!Objects.equals("success", results)) {
                    String notes = jsonObject.getString("message");
                    log.info("[GoodsFacadeImpl-pushGoodsToDSTP]=================推送dstp货品信息失败:{},参数:{}", notes, JSON.toJSONString(pushMap));
                }
            } catch (Exception e) {
                log.error("[GoodsFacadeImpl-pushGoodsToDSTP]=================推送dstp货品信息异常,goodsCode:{},ex:", goodsCode, e);
            }
        }
    }

    @Override
    public boolean goodsAddTag(GoodsAddParam addParam) {
        if (StringUtils.isBlank(addParam.getGoodsCode())) {
            throw new BusinessException("货品编码不能为空!");
        }
        GoodsBO goodsBO = goodsManager.getByUserIdAndGoodsCode(null, addParam.getGoodsCode());
        if (Objects.isNull(goodsBO)) {
            throw new BusinessException("货品不存在!");
        }
        String beforeJson = JSON.toJSONString(goodsBO);

        Long tag1 = goodsBO.getTag1();
        if (Objects.nonNull(addParam.getGoodsTag())) {
            goodsBO.setTag1(ConfigTagHelper.openTag(tag1, com.danding.business.common.ares.context.Tag1Constant.SPECIALTY_GOODS));
        } else {
            goodsBO.setTag1(ConfigTagHelper.closeTag(tag1, com.danding.business.common.ares.context.Tag1Constant.SPECIALTY_GOODS));
        }
        boolean updateGoodsById = goodsManager.updateGoodsById(goodsBO);
        if (updateGoodsById) {
            remoteGoodsFacade.addOperationLog(new GoodsOperationLogParam(SimpleUserHelper.getRealUserId(), OperationType.OT_GOODS_TAG, "货品打标操作"),
                    addParam.getGoodsCode(), beforeJson, JSON.toJSONString(goodsBO));
        }
        return updateGoodsById;
    }

    @Override
    public List<GoodsResult> listGoodsBySku(Long userId, List<String> skuList) {
        if (Objects.isNull(userId)) {
            throw new BusinessException("用户ID为空!");
        }
        if (CollectionUtil.isEmpty(skuList)) {
            return new ArrayList<>();
        }
        GoodsSearch search = new GoodsSearch();
        search.setUserId(userId);
        search.setSkus(skuList);
        List<GoodsBO> goodsBOList = goodsManager.listGoodsByParam(search);
        return BeanUtils.copyProperties(goodsBOList, GoodsResult.class);
    }

}
