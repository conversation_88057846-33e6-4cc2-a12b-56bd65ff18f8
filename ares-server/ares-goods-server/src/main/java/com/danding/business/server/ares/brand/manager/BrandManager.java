package com.danding.business.server.ares.brand.manager;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.danding.business.core.ares.brand.entity.Brand;
import com.danding.business.core.ares.brand.search.BrandSearch;
import com.danding.business.core.ares.brand.service.IBrandService;
import com.danding.business.core.ares.goods.search.GoodsSearch;
import com.danding.business.server.ares.brand.BO.BrandBO;
import com.danding.business.server.ares.goods.manager.helper.GoodsManagerHelper;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.soul.client.common.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 品牌表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@Component
public class BrandManager {

    @Autowired
    private IBrandService brandService;

    @Autowired
    private GoodsManagerHelper goodsManagerHelper;

    public boolean addBrand(BrandBO bo) {
        Brand brand = BeanUtils.copyProperties(bo, Brand.class);
        if (brand.getId() == null) {
            // 新增品牌判断品牌编码是否重复
            if (!checkBrandName(bo.getBrandName())) {
                throw new BusinessException("品牌名称已存在");
            }
            return brandService.insert(brand);
        } else {
            BrandBO oldBrand = this.getBrandById(brand.getId());
            if (!oldBrand.getBrandName().equals(bo.getBrandName())) {
                if (!checkBrandName(bo.getBrandName())) {
                    throw new BusinessException("品牌名称已存在");
                }
            }
            GoodsSearch search = new GoodsSearch();
            search.setBrandCode(brand.getBrandCode());
            if (goodsManagerHelper.check(search)) {
                throw new BusinessException("该品牌下有货品，不允许编辑");
            }
            return brandService.updateById(brand);
        }
    }

    public BrandBO getBrandById(Long id) {
        return BeanUtils.copyProperties(brandService.getBrandById(id), BrandBO.class);
    }

    /**
     * 检查品牌名称是否重复
     *
     * @param brandName
     * @return
     */
    private boolean checkBrandName(String brandName) {
        return this.getBrandByUserIdAndBrandName(SimpleUserHelper.getUserId(), brandName) == null;
    }

    public Brand getBrandByUserIdAndBrandName(Long userId, String brandName) {
        return brandService.getBrandByUserIdAndBrandName(userId, brandName);
    }

    public List<BrandBO> listBrandByParam(BrandSearch search) {
        List<Brand> brands = brandService.selectBrandListByParam(search);
        if (CollectionUtils.isNotEmpty(brands)) {
            return BeanUtils.copyProperties(brands, BrandBO.class);
        }
        return null;
    }

    public boolean deleteById(String id) {
        BrandBO brandBO = getBrandById(Long.valueOf(id));
        GoodsSearch search = new GoodsSearch();
        search.setBrandCode(brandBO.getBrandCode());
        if (goodsManagerHelper.check(search)) {
            throw new BusinessException("该品牌下有授权商品，不允许删除");
        }
        return brandService.deleteById(id);
    }

    @PageSelect
    public ListVO<BrandBO> pageListBrandByParam(BrandSearch search) {
        List<Brand> brands = brandService.selectBrandListByParam(search);
        ListVO<BrandBO> listVO = new ListVO<>();
        List<BrandBO> bos = BeanUtils.copyProperties(brands, BrandBO.class);
        // 装入授权数量
        if (CollectionUtils.isNotEmpty(bos)) {
            GoodsSearch goodsSearch = new GoodsSearch();
            for (BrandBO bo : bos) {
                goodsSearch.setBrandCode(bo.getBrandCode());
                int size = goodsManagerHelper.checkSize(goodsSearch);
                bo.setAuthorizationNum(size);
            }
        }
        listVO.setDataList(bos);
        return listVO;
    }

    public BrandBO getByBrandCode(String brandCode) {
        return BeanUtils.copyProperties(brandService.getByBrandCode(brandCode), BrandBO.class);
    }

    public BrandBO getByBrandCode(Long userId, String brandCode) {
        return BeanUtils.copyProperties(brandService.getByBrandCode(userId, brandCode), BrandBO.class);
    }
}
