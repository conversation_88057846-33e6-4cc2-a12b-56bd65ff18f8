package com.danding.business.server.ares.goodsManagement.listener;

import javax.annotation.Resource;

import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import com.alibaba.otter.canal.protocol.FlatMessage;
import com.danding.business.client.ares.goodsManagement.result.NativeGoodsManagementResult;
import com.danding.business.server.ares.goodsManagement.manager.GoodsManagementManager;
import com.danding.component.canal.mq.AbstractCanalMQService;
import com.google.common.collect.Lists;
import com.google.gson.Gson;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: yousx
 * @Date: 2025/05/23
 * @Description: 公共仓同步退货货主商品
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketmq.topic.canal.goods.management}", consumerGroup = "ares_canal_goods_management_return_sync_consumer", consumeMode = ConsumeMode.ORDERLY)
public class CanalGoodsManagementReturnSync extends AbstractCanalMQService<NativeGoodsManagementResult> implements RocketMQListener<FlatMessage> {


    @Resource
    private GoodsManagementManager goodsManagementManager;

    @Override
    protected void insert(NativeGoodsManagementResult nativeGoodsManagementResult) {
        goodsManagementManager.syncReturnOwnerGoods(nativeGoodsManagementResult.getOwnerCode(), Lists.newArrayList(nativeGoodsManagementResult.getSku()), true, true);
    }

    @Override
    protected void update(NativeGoodsManagementResult before, NativeGoodsManagementResult after) {
        goodsManagementManager.syncReturnOwnerGoods(after.getOwnerCode(), Lists.newArrayList(after.getSku()), true, true);
    }

    @Override
    protected void delete(NativeGoodsManagementResult nativeGoodsManagementResult) {

    }

    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }
}
