package com.danding.business.server.ares.goodsManagement.listener;

import java.util.Objects;
import javax.annotation.Resource;

import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import com.alibaba.otter.canal.protocol.FlatMessage;
import com.danding.business.client.ares.goodsManagement.result.NativeGoodsManagementResult;
import com.danding.business.server.ares.goodsManagement.manager.GoodsManagementManager;
import com.danding.component.canal.mq.AbstractCanalMQService;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: yousx
 * @Date: 2025/05/23
 * @Description: 公共仓同步退货货主商品
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketmq.topic.canal.goods.management}", consumerGroup = "ares_canal_goods_management_return_sync_consumer", consumeMode = ConsumeMode.ORDERLY)
public class CanalGoodsManagementReturnSync extends AbstractCanalMQService<NativeGoodsManagementResult> implements RocketMQListener<FlatMessage> {


    @Resource
    private GoodsManagementManager goodsManagementManager;

    @Override
    protected void insert(NativeGoodsManagementResult nativeGoodsManagementResult) {
        goodsManagementManager.syncReturnOwnerGoods(nativeGoodsManagementResult.getOwnerCode(), Lists.newArrayList(nativeGoodsManagementResult.getSku()), true, true);
    }

    @Override
    protected void update(NativeGoodsManagementResult before, NativeGoodsManagementResult after) {
        // 比较before和after的所有相关字段，如果完全一样则不执行同步
        if (isFieldsEqual(before, after)) {
            log.info("[货品同步逆向货主] 字段无变化，跳过同步: sku={}, ownerCode={}", after.getSku(), after.getOwnerCode());
            return;
        }

        goodsManagementManager.syncReturnOwnerGoods(after.getOwnerCode(), Lists.newArrayList(after.getSku()), true, true);
    }

    /**
     * 比较before和after对象的所有相关字段是否完全一样
     */
    private boolean isFieldsEqual(NativeGoodsManagementResult before, NativeGoodsManagementResult after) {
        if (before == null || after == null || before.getGoodsCode() == null || after.getGoodsCode() == null) {
            return true;
        }

        return Objects.equals(before.getOpenPeriodValidity(), after.getOpenPeriodValidity()) &&
               Objects.equals(before.getShelfLife(), after.getShelfLife()) &&
               Objects.equals(before.getNoSellDate(), after.getNoSellDate()) &&
               Objects.equals(before.getNoCollectDate(), after.getNoCollectDate()) &&
               Objects.equals(before.getWarningDate(), after.getWarningDate()) &&
               Objects.equals(before.getLength(), after.getLength()) &&
               Objects.equals(before.getWidth(), after.getWidth()) &&
               Objects.equals(before.getHeight(), after.getHeight()) &&
               Objects.equals(before.getVolume(), after.getVolume()) &&
               Objects.equals(before.getGrossWeight(), after.getGrossWeight()) &&
               Objects.equals(before.getNetWeight(), after.getNetWeight()) &&
               Objects.equals(before.getIsNewRecord(), after.getIsNewRecord()) &&
               Objects.equals(before.getStandardLength(), after.getStandardLength()) &&
               Objects.equals(before.getStandardWidth(), after.getStandardWidth()) &&
               Objects.equals(before.getStandardHeight(), after.getStandardHeight()) &&
               Objects.equals(before.getStandardVolume(), after.getStandardVolume()) &&
               Objects.equals(before.getBracketGauge(), after.getBracketGauge()) &&
               Objects.equals(before.getCartonPcs(), after.getCartonPcs());
    }

    @Override
    protected void delete(NativeGoodsManagementResult nativeGoodsManagementResult) {

    }

    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }
}
