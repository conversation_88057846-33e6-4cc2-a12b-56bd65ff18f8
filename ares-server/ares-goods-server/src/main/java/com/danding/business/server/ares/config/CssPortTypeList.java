package com.danding.business.server.ares.config;

import com.danding.business.common.ares.enums.common.PortType;
import com.danding.business.common.ares.utils.RedisUtils;
import com.danding.cds.company.out.api.CustomsRpc;
import com.danding.cds.company.out.bean.vo.CustomsDistrictVO;
import com.danding.component.common.utils.EnumUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.List;

@Slf4j
@Component
public class CssPortTypeList {

    private static final String REDIS_PORT_TYPE_LIST_KEY = "ERP:REDIS:PORTTYPE:LIST";
    private static final String REDIS_PORT_TYPE_LIST_KEY_ITEM = "portTypeList";
    @DubboReference
    private CustomsRpc customsRpc;
    @Autowired
    private RedisUtils redisUtils;

    @PostConstruct
    public void init() {
        get();
    }

    /**
     * 获取ccs口岸列表
     *
     * @return
     */
    private List<EnumUtils> get() {
        try {
            List<CustomsDistrictVO> allCustoms = customsRpc.getAllCustoms();
            List<EnumUtils> portTypeList = EnumUtils.build(allCustoms, "code", "desc");
            redisUtils.hset(REDIS_PORT_TYPE_LIST_KEY, REDIS_PORT_TYPE_LIST_KEY_ITEM, portTypeList, 60 * 60);
            return portTypeList;
        } catch (Exception e) {
            log.error("cssPortTypeList#init 获取ccs口岸失败", e);
            return EnumUtils.buildCode(PortType.class);
        }
    }

    /**
     * 获取ccs口岸列表
     *
     * @return
     */
    public List<EnumUtils> getPortTypeList() {
        List<EnumUtils> portTypeList = (List<EnumUtils>) redisUtils.hget(REDIS_PORT_TYPE_LIST_KEY, REDIS_PORT_TYPE_LIST_KEY_ITEM);
        if (!CollectionUtils.isEmpty(portTypeList)) {
            return portTypeList;
        }
        return get();
    }

    /**
     * 根据口岸code获取口岸名称
     *
     * @param code
     * @return
     */
    public String getPortTypeNameByCode(String code) {
        List<EnumUtils> portTypeList = getPortTypeList();
        for (EnumUtils enumUtils : portTypeList) {
            if (enumUtils.getId().equals(code)) {
                return (String) enumUtils.getName();
            }
        }
        return null;
    }

    /**
     * 根据口岸名称获取口岸code
     *
     * @param name
     * @return
     */
    public String getPortTypeCodeByName(String name) {
        List<EnumUtils> portTypeList = getPortTypeList();
        for (EnumUtils enumUtils : portTypeList) {
            if (enumUtils.getName().equals(name)) {
                return (String) enumUtils.getId();
            }
        }
        return null;
    }
}
