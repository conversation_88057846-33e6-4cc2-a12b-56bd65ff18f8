package com.danding.business.server.ares.goods.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.danding.business.client.rpc.goods.facade.IGoodsManagementRpcFacade;
import com.danding.business.client.rpc.goods.result.GoodsManagementRpcResult;
import com.danding.business.core.ares.goodsManagement.search.GoodsManagementSearch;
import com.danding.business.server.ares.goods.manager.GoodsManager;
import com.danding.business.server.ares.goodsManagement.BO.GoodsManagementBO;
import com.danding.business.server.ares.goodsManagement.manager.GoodsManagementManager;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class GoodsManagementRpcFacadeImpl implements IGoodsManagementRpcFacade {

    @Autowired
    private GoodsManagementManager goodsManagementManager;
    @Autowired
    private GoodsManager goodsManager;

    @Override
    public List<GoodsManagementRpcResult> queryRpcGoodsBy(String sku, String ownerCode) {
        List<String> skuList = null;
        List<String> ownerCodeList = null;
        if (StringUtils.isNotBlank(sku)) {
            skuList = Arrays.asList(sku);
        }
        if (StringUtils.isNotBlank(ownerCode)) {
            ownerCodeList = Arrays.asList(ownerCode);
        }
        return listRpcGoodsBy(skuList, ownerCodeList);
    }

    @Override
    public List<GoodsManagementRpcResult> listRpcGoodsBy(List<String> skuList, List<String> ownerCodeList) {
        if(CollectionUtil.isEmpty(ownerCodeList)) {
            throw new BusinessException("货主不能为空!");
        }
        GoodsManagementSearch search = new GoodsManagementSearch();
        search.setSkus(skuList);
        search.setOwnerCodeList(ownerCodeList);
        List<GoodsManagementBO> managementBOList = goodsManagementManager.listBySearch(search);
        List<String> collect = managementBOList.stream().map(goodsManagementBO -> goodsManagementBO.getGoodsCode()).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) {
            throw new BusinessException("货品查询不存在!");
        }

        List<GoodsManagementRpcResult> resultList = new ArrayList<>();
        for(GoodsManagementBO goodsManagementBO : managementBOList) {
            GoodsManagementRpcResult rpcResult = new GoodsManagementRpcResult();
            rpcResult.setOwnerCode(goodsManagementBO.getOwnerCode());
            rpcResult.setGoodsName(goodsManagementBO.getGoodsName());
            rpcResult.setGoodsCode(goodsManagementBO.getGoodsCode());
            rpcResult.setSku(goodsManagementBO.getSku());
            rpcResult.setType(goodsManagementBO.getType());
            rpcResult.setExternalCode(goodsManagementBO.getExternalCode());
            rpcResult.setExternalSku(goodsManagementBO.getExternalSku());
            resultList.add(rpcResult);
        }
        return resultList;
    }

    @Override
    public void syncReturnOwnerGoods(String ownerCode, List<String> skuList) {
        goodsManagementManager.syncReturnOwnerGoods(ownerCode, skuList, true, true);
    }
}
