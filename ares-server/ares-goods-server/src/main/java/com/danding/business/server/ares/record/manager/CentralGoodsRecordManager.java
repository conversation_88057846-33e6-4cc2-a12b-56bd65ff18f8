package com.danding.business.server.ares.record.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.danding.business.client.ares.entitywarehouse.facade.IEntityWarehouseFacade;
import com.danding.business.client.ares.entitywarehouse.param.EntityWarehouseQueryParam;
import com.danding.business.client.ares.entitywarehouse.result.EntityWarehouseResult;
import com.danding.business.client.ares.goods.facade.IGoodsFacade;
import com.danding.business.client.ares.goods.message.GoodsRecordInfoSyncMessage;
import com.danding.business.client.ares.goods.param.GoodsAddParam;
import com.danding.business.client.ares.goods.param.GoodsOperationLogParam;
import com.danding.business.client.ares.goodsManagement.facade.IGoodsManagementFacade;
import com.danding.business.client.ares.goodsManagement.param.GoodsManagementAddParam;
import com.danding.business.client.ares.goodsManagement.param.GoodsManagementQueryParam;
import com.danding.business.client.ares.goodsManagement.result.GoodsManagementResult;
import com.danding.business.client.ares.logicwarehouse.facade.ILogicWarehouseFacade;
import com.danding.business.client.ares.logicwarehouse.param.LogicWarehouseQueryParam;
import com.danding.business.client.ares.logicwarehouse.result.LogicWarehouseResult;
import com.danding.business.client.ares.owner.facade.IOwnerFacade;
import com.danding.business.client.ares.owner.result.OwnerResult;
import com.danding.business.client.rpc.goods.center.GoodsTagHelper;
import com.danding.business.client.rpc.goods.center.constant.Tag1Constant;
import com.danding.business.client.rpc.goods.exception.GoodsRecordExistBusinessException;
import com.danding.business.client.rpc.goods.result.GoodsRecordCssV1RpcResult;
import com.danding.business.client.rpc.goods.result.GoodsRecordRpcResult;
import com.danding.business.client.rpc.user.facade.IUserRpcFacade;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.business.common.ares.annotation.ParamLog;
import com.danding.business.common.ares.context.AresContext;
import com.danding.business.common.ares.context.AresRedisContext;
import com.danding.business.common.ares.enums.common.OpenStatus;
import com.danding.business.common.ares.enums.common.TradeType;
import com.danding.business.common.ares.enums.goods.*;
import com.danding.business.common.ares.enums.operationLogs.OperationType;
import com.danding.business.common.ares.enums.operationLogs.UserTypeEnum;
import com.danding.business.common.ares.utils.Assert;
import com.danding.business.common.ares.utils.RedisUtils;
import com.danding.business.core.ares.goods.search.GoodsSearch;
import com.danding.business.core.ares.goodsManagement.search.GoodsManagementSearch;
import com.danding.business.core.ares.goodsManagement.service.IGoodsManagementService;
import com.danding.business.core.ares.record.search.GoodsRecordLogicWarehouseSearch;
import com.danding.business.core.ares.record.search.GoodsRecordPortSearch;
import com.danding.business.core.ares.record.search.GoodsRecordSearch;
import com.danding.business.server.ares.config.CssPortTypeList;
import com.danding.business.server.ares.config.GoodsNacosConfig;
import com.danding.business.server.ares.goods.BO.GoodsBO;
import com.danding.business.server.ares.goods.manager.ContactManager;
import com.danding.business.server.ares.goods.manager.GoodsManager;
import com.danding.business.server.ares.record.BO.*;
import com.danding.business.server.ares.record.facade.ProxyConfig;
import com.danding.business.server.ares.record.manager.helper.CentralGoodsRecordManagerHelper;
import com.danding.business.server.ares.record.remote.RemoteCentralRecordFacade;
import com.danding.cds.item.api.dto.GoodsRecordAuditSubmitDTO;
import com.danding.cds.out.api.CustomsBaseDataRpc;
import com.danding.cds.out.api.CustomsBookItemRpc;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.req.*;
import com.danding.cds.out.bean.vo.res.*;
import com.danding.cds.out.common.constant.ResultCodeCons;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.common.response.PageResult;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.rocketmq.message.SpringMessage;
import com.danding.component.rocketmq.producer.SpringRocketMQProducer;
import com.danding.soul.client.common.exception.BusinessException;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.redisson.api.RedissonClient;

import static com.danding.business.common.ares.context.AresContext.*;

/**
 * <p>
 * 备案中心管理
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Component
@Slf4j
public class CentralGoodsRecordManager {


    @Autowired
    private CentralGoodsRecordManagerHelper centralGoodsRecordManagerHelper;
    @Autowired
    private RemoteCentralRecordFacade remoteCentralRecordFacade;

    @Autowired
    private IGoodsManagementService goodsManagementService;

    @DubboReference
    private ILogicWarehouseFacade logicWarehouseFacade;
    @DubboReference
    private IEntityWarehouseFacade entityWarehouseFacade;
    @Autowired
    private IGoodsFacade goodsFacade;
    @DubboReference
    IOwnerFacade ownerFacade;
    @DubboReference
    CustomsBaseDataRpc customsBaseDataRpc;

    @DubboReference
    private CustomsBookItemRpc customsBookItemRpc;

    @Autowired
    private IGoodsManagementFacade goodsManagementFacade;

    @Autowired
    private ContactManager contactManager;
    @Autowired
    private GoodsManager goodsManager;

    @Autowired
    private SpringRocketMQProducer springRocketMQProducer;

    @DubboReference
    private IUserRpcFacade userRpcFacade;

    @Autowired
    private GoodsRecordLogicWarehouseManager goodsRecordLogicWarehouseManager;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private CssPortTypeList cssPortTypeList;
    @Autowired
    private ProxyConfig proxyConfig;

    private static final Integer USER_LENGTH = 7;
    private static final String FILL = "0";

    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyMMdd");

    @Autowired
    private GoodsNacosConfig goodsNacosConfig;

    @Autowired
    private RedissonClient redissonClient;

    public ListVO<GoodsRecordBO> pageListGoodsRecordByParam(GoodsRecordSearch search) {
        CentralGoodsRecordSearchReqVo reqVo = centralGoodsRecordManagerHelper.toCentralGoodsRecordSearchReqVo(search);
        RpcResult<com.danding.logistics.api.common.response.ListVO<CentralGoodsRecordPagingResVO>> result = remoteCentralRecordFacade.paging(reqVo);
        buildResult(result, true);
        ListVO<GoodsRecordBO> listVO = new ListVO<>();
        listVO.setPage(BeanUtils.copyProperties(result.getData().getPage(), PageResult.class));
        listVO.setDataList(centralGoodsRecordManagerHelper.toPagingResVOListGoodsRecordBO(result.getData().getDataList()));
        return listVO;
    }

    public GoodsRecordBO getGoodsRecordByParam(GoodsRecordSearch search) {
        List<GoodsRecordBO> list = listGoodsRecordByParamThrows(search);
        return list.stream().findAny().orElse(null);
    }

    public List<GoodsRecordBO> listGoodsRecordByParam(GoodsRecordSearch search) {
        CentralGoodsRecordSearchReqVo reqVo = centralGoodsRecordManagerHelper.toCentralGoodsRecordSearchReqVo(search);
        search.setPageSize(10000);
        search.setCurrentPage(1);
        RpcResult<List<CentralGoodsRecordPagingResVO>> result = remoteCentralRecordFacade.search(reqVo);
        return centralGoodsRecordManagerHelper.toPagingResVOListGoodsRecordBO(buildListResult(result));
    }

    public List<GoodsRecordBO> listGoodsRecordByParamThrows(GoodsRecordSearch search) throws BusinessException {
        CentralGoodsRecordSearchReqVo reqVo = centralGoodsRecordManagerHelper.toCentralGoodsRecordSearchReqVo(search);
        search.setPageSize(10000);
        search.setCurrentPage(1);
        RpcResult<List<CentralGoodsRecordPagingResVO>> result = remoteCentralRecordFacade.search(reqVo);
        return centralGoodsRecordManagerHelper.toPagingResVOListGoodsRecordBO(buildListResultThrows(result));
    }

    public GoodsRecordReportBO getStatusCount(Long userId) {

        GoodsRecordSearch search = new GoodsRecordSearch();
        search.setUserId(userId);

        CentralGoodsRecordSearchReqVo reqVo = centralGoodsRecordManagerHelper.toCentralGoodsRecordSearchReqVo(search);
        GoodsRecordReportBO goodsRecordReportBO = new GoodsRecordReportBO();

        RpcResult<CentralRecordStatusCountResVO> result = remoteCentralRecordFacade.getStatusCount(reqVo);
        if (ResultCodeCons.SUCCESSFUL == result.getCode()) {
            CentralRecordStatusCountResVO resVo = result.getData();
            goodsRecordReportBO.setApprovalCount(resVo.getWaitExamineCount() == null ? 0 : resVo.getWaitExamineCount());
            goodsRecordReportBO.setRejectedCount(resVo.getRejectCount() == null ? 0 : resVo.getRejectCount());
            goodsRecordReportBO.setCreatedCount(resVo.getWaitCommitCount() == null ? 0 : resVo.getWaitCommitCount());
            goodsRecordReportBO.setSuccessCount(resVo.getFinishCount() == null ? 0 : resVo.getFinishCount());
        }
        return goodsRecordReportBO;
    }


    public Long saveOrUpdate(GoodsRecordBO bo) {
        String lockKey = RECORD_UPDATE_LOCK_KEY + bo.getGoodsCode();
        RLock rLock = redissonClient.getLock(lockKey);
        try {
            if (Objects.isNull(rLock)) {
                log.error("======CentralGoodsRecordManager.saveOrUpdate=========== 无法获取redis锁! return.", JSON.toJSONString(bo));
                throw new BusinessException("获取锁失败，稍后重试");
            }
            rLock.lock(10, TimeUnit.MINUTES);
            Pair<List<EntityWarehouseResult>, List<LogicWarehouseResult>> entityAndLogicListPair = getRealEntityAndLogicList(bo.getUserId(), bo.getPortCode(), bo.getWaitDeclaredPort());
            generateMaterialCode(bo);
            GoodsRecordSubmitReqVO reqVO = centralGoodsRecordManagerHelper.buildGoodsRecordSubmitReqVO(bo, entityAndLogicListPair.getLeft());
            RpcResult<Long> result = remoteCentralRecordFacade.saveGoodsRecord(reqVO);
            if (result.getData() != null) {
                bo.setCcsRecordId(result.getData());
                buildRelation(bo, entityAndLogicListPair.getRight(), entityAndLogicListPair.getLeft());
            }
            buildResult(result);
            sleep();
            return result.getData();
        } finally {
            if (Objects.nonNull(rLock)) {
                log.info("======InventoryProcessListener.onMessage=========== release redis lock. lockKey={}", rLock.getName());
                rLock.unlock();
            }
        }
    }

    public void generateMaterialCode(GoodsRecordBO bo) {
        if (StringUtils.isBlank(bo.getMaterialCode())) {
            //1、检查ccs 备案是否已在
            GoodsRecordBO goodsRecordBO = this.getGoodsRecord(bo.getUserId(), bo.getGoodsCode());
            Assert.isTrue(goodsRecordBO == null, "货品备案已存在，不能创建");
            //小于18的货品id是老的货品id
            if (bo.getGoodsCode().length() < 18) {
                // 老货品ID的需要生成料号(使用新的goodsCode生成逻辑)
                //二次检查料号，ccs规则维度有无备案
                GoodsRecordBO ccsGoodsRecordBO = getGoodsRecordCcs(bo.getUserId(), bo.getGoodsCode());
                if (ccsGoodsRecordBO == null) {
                    String materialCode = this.generateGoodsCode(bo.getUserId());
                    bo.setMaterialCode(materialCode);
                } else {
                    bo.setMaterialCode(ccsGoodsRecordBO.getMaterialCode());
                }
            } else {
                bo.setMaterialCode(bo.getGoodsCode());
            }
        }
    }


    public int saveOrUpdate(List<GoodsRecordBO> list) {
        AtomicInteger i = new AtomicInteger();
        list.stream().forEach(m -> {
            saveOrUpdate(m);
            i.getAndIncrement();
        });
        return i.get();

    }

    public boolean syncEntityWarehouse(List<Long> recordIdIds, String port, String warehouseCode) {
        CentralRecordSyncWarehouseSubmitReqVO submitReqVO = new CentralRecordSyncWarehouseSubmitReqVO();
        submitReqVO.setRecordId(recordIdIds);
        submitReqVO.setCustomsCode(port);
        submitReqVO.setWmsWarehouseList(Arrays.asList(warehouseCode));
        RpcResult<String> result = remoteCentralRecordFacade.syncCentralEntityWarehouse(submitReqVO);
        buildResult(result);
        return true;
    }

    public void buildRelation(GoodsRecordBO bo, String port) {
        log.info("buildRelation bo={},port={}", JSON.toJSONString(bo), port);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(port)) {
            Pair<List<EntityWarehouseResult>, List<LogicWarehouseResult>> entityAndLogicListPair = getRealEntityAndLogicList(bo.getUserId(), port, this.cssPortTypeList.getPortTypeNameByCode(port));
            buildPortRelation(bo.getUserId(), bo.getCcsRecordId(), port, entityAndLogicListPair.getRight(), entityAndLogicListPair.getLeft());
        } else {
            List<GoodsRecordPortBO> portBOList = this.getGoodsRecordPortList(bo.getCcsRecordId());
            portBOList.stream().forEach(m -> {
                        Pair<List<EntityWarehouseResult>, List<LogicWarehouseResult>> entityAndLogicListPair = getRealEntityAndLogicList(bo.getUserId(), m.getPort(), this.cssPortTypeList.getPortTypeNameByCode(m.getPort()));
                        buildPortRelation(bo.getUserId(), bo.getCcsRecordId(), m.getPort(), entityAndLogicListPair.getRight(), entityAndLogicListPair.getLeft());
                    }
            );
        }
    }

    public void buildRelation(GoodsRecordBO bo, List<LogicWarehouseResult> logicWarehouseResults, List<EntityWarehouseResult> entityWarehouseResults) {
        buildPortRelation(bo.getUserId(), bo.getCcsRecordId(), bo.getPortCode(), logicWarehouseResults, entityWarehouseResults);
    }

    public void buildSyncPortRelation(GoodsRecordBO bo, List<LogicWarehouseResult> logicWarehouseResults, List<EntityWarehouseResult> entityWarehouseResults) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(bo.getWaitDeclaredPort())) {
            buildPortRelation(bo.getUserId(), bo.getId(), this.cssPortTypeList.getPortTypeCodeByName(bo.getWaitDeclaredPort()), logicWarehouseResults, entityWarehouseResults);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(bo.getAuditPort())) {
            buildPortRelation(bo.getUserId(), bo.getId(), this.cssPortTypeList.getPortTypeCodeByName(bo.getAuditPort()), logicWarehouseResults, entityWarehouseResults);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(bo.getDeclaredPort())) {
            buildPortRelation(bo.getUserId(), bo.getId(), this.cssPortTypeList.getPortTypeCodeByName(bo.getDeclaredPort()), logicWarehouseResults, entityWarehouseResults);
        }


    }

    public void buildPortRelation(Long userId, Long ccsRecordId, String port, List<LogicWarehouseResult> logicWarehouseResults, List<EntityWarehouseResult> entityWarehouseResults) {

        //1、先删除仓库
        Assert.isTrue(userId != null, "userId 不能为空", ccsRecordId);
        Assert.isTrue(ccsRecordId != null, "ccsRecordId 不能为空", ccsRecordId);
        Assert.isTrue(port != null, "ccsRecordId 不能为空", port);
        GoodsRecordLogicWarehouseSearch goodsRecordLogicWarehouseSearch = new GoodsRecordLogicWarehouseSearch();
        goodsRecordLogicWarehouseSearch.setUserId(userId);
        goodsRecordLogicWarehouseSearch.setGoodsCcsRecordId(ccsRecordId);
        goodsRecordLogicWarehouseSearch.setPort(port);
        goodsRecordLogicWarehouseManager.removeBySearch(goodsRecordLogicWarehouseSearch);

        //货品备案云仓关系
        Map<String, EntityWarehouseResult> entityWarehouseResultMap = entityWarehouseResults.stream().collect(Collectors.toMap(EntityWarehouseResult::getEntityWarehouseCode, o -> o, (oldValue, newValue) -> oldValue));
        logicWarehouseResults.stream().forEach(logicWarehouseResult -> {
            GoodsRecordLogicWarehouseBO goodsRecordLogicWarehouseBO = new GoodsRecordLogicWarehouseBO();
            goodsRecordLogicWarehouseBO.setGoodsRecordId(-1L);
            goodsRecordLogicWarehouseBO.setCcsRecordId(ccsRecordId);
            goodsRecordLogicWarehouseBO.setLogicWarehouseCode(logicWarehouseResult.getLogicWarehouseCode());
            goodsRecordLogicWarehouseBO.setLogicWarehouseName(logicWarehouseResult.getLogicWarehouseName());
            EntityWarehouseResult entityWarehouseResult = entityWarehouseResultMap.get(logicWarehouseResult.getEntityWarehouseCode());
            if (Objects.isNull(entityWarehouseResult)) {
                throw new BusinessException("该云仓" + logicWarehouseResult.getLogicWarehouseCode() + "对应的实体仓不存在或者已经被删除");
            }
            goodsRecordLogicWarehouseBO.setEntityWarehouseCode(entityWarehouseResult.getEntityWarehouseCode());
            goodsRecordLogicWarehouseBO.setEntityWarehouseName(entityWarehouseResult.getEntityWarehouseName());
            goodsRecordLogicWarehouseBO.setWarehouseCode(entityWarehouseResult.getWarehouseCode());
            goodsRecordLogicWarehouseBO.setPort(port);
            goodsRecordLogicWarehouseBO.setPortName(cssPortTypeList.getPortTypeNameByCode(port));
            goodsRecordLogicWarehouseBO.setUserId(userId);
            if (!goodsRecordLogicWarehouseManager.add(goodsRecordLogicWarehouseBO)) {
                throw new BusinessException("新增备案失败");
            }
        });
    }


    //
    public boolean submitAuditV2(Long id, String waitDeclaredPort) {
        if (StringUtils.isBlank(waitDeclaredPort)) {
            List<GoodsRecordPortBO> list = getGoodsRecordPortList(id);
            list = list.stream().filter(m -> Objects.equals(m.getErpCommitStatus(), 1)).collect(Collectors.toList());
            list.stream().forEach(m -> {
                RpcResult<Long> result = remoteCentralRecordFacade.auditGoodsRecord(id, m.getPort());
                buildResult(result);
            });
        } else {
            RpcResult<Long> result = remoteCentralRecordFacade.auditGoodsRecord(id, this.cssPortTypeList.getPortTypeCodeByName(waitDeclaredPort));
            buildResult(result);
        }
        return true;
    }

    public Pair<List<EntityWarehouseResult>, List<LogicWarehouseResult>> getRealEntityAndLogicList(Long userId, String portCode, String portName) {
        //根据当前用户下所有云仓
        LogicWarehouseQueryParam logicWarehouseQueryParam = new LogicWarehouseQueryParam();
        logicWarehouseQueryParam.setUserId(userId);
        logicWarehouseQueryParam.setTradeType(TradeType.BONDED);
        logicWarehouseQueryParam.setOpenStatus(OpenStatus.OPEN);
        List<LogicWarehouseResult> logicWarehouseResults = logicWarehouseFacade.listLogicWarehouseByParam(logicWarehouseQueryParam);
        if (CollectionUtils.isEmpty(logicWarehouseResults)) {
            throw new BusinessException("当前用户口岸【" + portName + "】下没有实体仓或者云仓");
        }
        Set<String> entityWarehouseCodeSet = logicWarehouseResults.stream().map(LogicWarehouseResult::getEntityWarehouseCode).collect(Collectors.toSet());

        //根据口岸获取当前用户下所有实体仓
        EntityWarehouseQueryParam entityWarehouseQueryParam = new EntityWarehouseQueryParam();
        entityWarehouseQueryParam.setEntityWarehouseCodeSet(entityWarehouseCodeSet);
        entityWarehouseQueryParam.setPort(portCode);
        entityWarehouseQueryParam.setTradeType(TradeType.BONDED);
//        entityWarehouseQueryParam.setSystemCode(AresContext.SYSTEM_DT);
        List<EntityWarehouseResult> entityWarehouseResults = entityWarehouseFacade.listEntityByParam(entityWarehouseQueryParam);

        if (CollectionUtils.isEmpty(entityWarehouseResults)) {
            throw new BusinessException("当前用户口岸【" + portName + "】下没有实体仓或者云仓");
        }
        //这里才是真正的云仓列表
        Set<String> curEntityWarehouseCodeSet = entityWarehouseResults.stream().map(EntityWarehouseResult::getEntityWarehouseCode).collect(Collectors.toSet());
        logicWarehouseQueryParam = new LogicWarehouseQueryParam();
        logicWarehouseQueryParam.setUserId(userId);
        logicWarehouseQueryParam.setTradeType(TradeType.BONDED);
        logicWarehouseQueryParam.setOpenStatus(OpenStatus.OPEN);
        logicWarehouseQueryParam.setEntityWarehouseCodeSet(curEntityWarehouseCodeSet);
        logicWarehouseResults = logicWarehouseFacade.listLogicWarehouseByParam(logicWarehouseQueryParam);
        if (CollectionUtils.isEmpty(logicWarehouseResults)) {
            throw new BusinessException("当前用户口岸【" + portName + "】下没有实体仓或者云仓");
        }

        //这里才是真正的实体仓列表
        Set<String> realEntityWarehouseCodeSet = logicWarehouseResults.stream().map(LogicWarehouseResult::getEntityWarehouseCode).collect(Collectors.toSet());
        entityWarehouseQueryParam = new EntityWarehouseQueryParam();
        entityWarehouseQueryParam.setEntityWarehouseCodeSet(realEntityWarehouseCodeSet);
        entityWarehouseQueryParam.setPort(portCode);
        entityWarehouseQueryParam.setTradeType(TradeType.BONDED);
//        entityWarehouseQueryParam.setSystemCode(AresContext.SYSTEM_DT);
        entityWarehouseResults = entityWarehouseFacade.listEntityByParam(entityWarehouseQueryParam);
        if (CollectionUtils.isEmpty(entityWarehouseResults)) {
            throw new BusinessException("当前用户口岸【" + portName + "】下没有实体仓或者云仓");
        }

        return Pair.of(entityWarehouseResults, logicWarehouseResults);
    }


    public boolean buildResult(RpcResult result, boolean isPageList) {
        if (isPageList) {
            if (ResultCodeCons.SUCCESSFUL == result.getCode()) {
                return true;
            } else {
                throw new BusinessException(result.getMessage());
            }
        }
        if (ResultCodeCons.SUCCESSFUL == result.getCode()) {
            return true;
        } else {
            throw new BusinessException(result.getMessage());
        }
    }

    public boolean buildResult(RpcResult result) {
        if (ResultCodeCons.SUCCESSFUL == result.getCode()) {
            return true;
        } else {
            throw new BusinessException(result.getMessage());
        }
    }

    public List buildListResult(RpcResult<List<CentralGoodsRecordPagingResVO>> result) {
        if (ResultCodeCons.SUCCESSFUL == result.getCode()) {
            return result.getData();
        }
        return new ArrayList<>();
    }

    public List buildListResultThrows(RpcResult<List<CentralGoodsRecordPagingResVO>> result) throws BusinessException {
        if (ResultCodeCons.SUCCESSFUL == result.getCode()) {
            return result.getData();
        }
        throw new BusinessException(result.getMessage());
    }

    public List<Long> getByGoodsCode(Long userId, String goodsCode) {
        CentralGoodsRecordSearchReqVo reqVo = new CentralGoodsRecordSearchReqVo();
        if (org.apache.commons.lang3.StringUtils.isBlank(goodsCode)) {
            throw new BusinessException("参数异常goodsCode为空！");
        }
        if (userId == null) {
            throw new BusinessException("参数异常userId为空！");
        }
        reqVo.setGoodsCodeList(Arrays.asList(goodsCode));
        reqVo.setUserId(userId);
        RpcResult<List<CentralGoodsRecordPagingResVO>> result = remoteCentralRecordFacade.search(reqVo);
        List<Long> ids = result.getData().stream().map(CentralGoodsRecordPagingResVO::getId).collect(Collectors.toList());
        return ids;

    }

    public boolean deleteByIds(List<Long> ids) {
        RpcResult<String> result = remoteCentralRecordFacade.delete(ids);
        buildResult(result);
        return true;

    }

    /**
     * 用于详情
     *
     * @param goodsCode
     * @param recordId
     * @param port
     * @return
     */
    public GoodsRecordBO getGoodsRecord(Long userId, String goodsCode, Long recordId, String port) {
        RpcResult<CentralGoodsRecordDetailResVo> result = getCentralGoodsRecordDetailResVo(userId, goodsCode, recordId, port);
        return centralGoodsRecordManagerHelper.toGoodsRecordBO(Optional.ofNullable(result).map(RpcResult::getData).map(CentralGoodsRecordDetailResVo::getBaseInfoVO).orElse(null));
    }

    /**
     * 返回ccs规则的备案
     *
     * @param goodsCode
     * @param
     * @return
     */
    public GoodsRecordBO getGoodsRecordCcs(Long userId, String goodsCode) {
        CentralGoodsRecordSearchReqVo centralGoodsRecordSearchReqVo = new CentralGoodsRecordSearchReqVo();
        centralGoodsRecordSearchReqVo.setUserId(userId);
        centralGoodsRecordSearchReqVo.setGoodsCodeList(Arrays.asList(goodsCode));
        centralGoodsRecordSearchReqVo.setPageSize(1);
        centralGoodsRecordSearchReqVo.setCurrentPage(1);
        centralGoodsRecordSearchReqVo.setFilterErpDeleted(false);
        RpcResult<List<CentralGoodsRecordPagingResVO>> result = remoteCentralRecordFacade.searchByDb(centralGoodsRecordSearchReqVo);
        List<GoodsRecordBO> list = centralGoodsRecordManagerHelper.toPagingResVOListGoodsRecordBO(buildListResultThrows(result));
        return list.stream().findAny().orElse(null);
    }

    public RpcResult<CentralGoodsRecordDetailResVo> getCentralGoodsRecordDetailResVo(Long userId, String goodsCode, Long recordId, String port) {
        CentralGoodsRecordDetailReqVo reqVo = new CentralGoodsRecordDetailReqVo();
        reqVo.setGoodsCode(goodsCode);
        reqVo.setId(recordId);
        reqVo.setCustomsCode(port);
        reqVo.setUserId(userId);
        RpcResult<CentralGoodsRecordDetailResVo> result = remoteCentralRecordFacade.detail(reqVo);
        buildResult(result);
        return result;
    }

    public GoodsRecordPortBO getGoodsRecordPort(Long recordId, String port) {
        List<GoodsRecordPortBO> list = getGoodsRecordPortList(recordId);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(port)) {
            list = list.stream().filter(m -> Objects.equals(port, m.getPort())).collect(Collectors.toList());
        }
        return list.stream().findFirst().orElse(null);
    }

    public List<GoodsRecordPortBO> getGoodsRecordPortList(Long recordId) {
        RpcResult<List<CentralGoodsRecordCustomsInfoResVO>> result = remoteCentralRecordFacade.getCustomsListById(recordId);
        List<GoodsRecordPortBO> list = centralGoodsRecordManagerHelper.toGoodsRecordPortBOList(result.getData());
        list.stream().forEach(m -> m.setGoodsRecordId(recordId));
        return list;
    }

    public List<GoodsRecordPortBO> getGoodsRecordPortListByUserId(Long userId) {
        RpcResult<List<CentralGoodsRecordCustomsInfoResVO>> result = remoteCentralRecordFacade.getCustomsListByUserId(String.valueOf(userId));
        List<GoodsRecordPortBO> list = centralGoodsRecordManagerHelper.toGoodsRecordPortBOList(result.getData());
        return list;
    }


    public List<GoodsRecordPortBO> getGoodsRecordPortList(Long userId, String goodsCode) {
        RpcResult<CentralGoodsRecordDetailResVo> result = getCentralGoodsRecordDetailResVo(userId, goodsCode, null, null);
        return centralGoodsRecordManagerHelper.toGoodsRecordPortBOList(result.getData());
    }

    public GoodsRecordBO getGoodsRecord(String goodsCode, Long recordId, String port) {
        return getGoodsRecord(null, goodsCode, recordId, port);
    }

    public GoodsRecordBO getGoodsRecord(Long userId, String goodsCode, String port) {
        return getGoodsRecord(userId, goodsCode, null, port);
    }

    public GoodsRecordBO getGoodsRecord(Long userId, String goodsCode) {
        return getGoodsRecord(userId, goodsCode, null, null);
    }

    public GoodsRecordBO getGoodsRecord(Long id) {
        return getGoodsRecord(null, null, id, null);
    }

    public String generateGoodsCode(Long userId) {
        StringBuilder value = new StringBuilder(String.valueOf(userId));
        int length = value.length();
        if (length < USER_LENGTH) {
            for (int i = 0; i < USER_LENGTH - length; i++) {
                value.insert(0, FILL);
            }
        } else {
            value = new StringBuilder(value.substring(length - USER_LENGTH, length));
        }
        String date = SDF.format(System.currentTimeMillis());
        String key = AresRedisContext.MATERIAL_CODE + userId + ":" + date;
        long redisResult = redisUtils.incr(key, 1);
        redisUtils.expire(key, 1l, TimeUnit.DAYS);
        int redisResultLength = String.valueOf(redisResult).length();
        String end = String.format("%0" + (redisResultLength <= 5 ? 5 : redisResultLength) + "d", redisResult);
        return value + date + end;
    }


    public GoodsRecordNewEditBO getByGoodsCodeAndPortV2(Long userId, String goodsCode, String portName) {
        GoodsRecordNewEditBO goodsRecordNewEditBO = new GoodsRecordNewEditBO();
        goodsRecordNewEditBO.setFlag(1);
        // 首先查询recordPort表，不存在再查goodsRecord表
        if (org.apache.commons.lang3.StringUtils.isBlank(portName)) {
            throw new BusinessException("口岸不能为空");
        }
        String port = cssPortTypeList.getPortTypeCodeByName(portName);
        GoodsRecordPortSearch goodsRecordPortSearch = new GoodsRecordPortSearch();
        goodsRecordPortSearch.setUserId(userId);
        goodsRecordPortSearch.setGoodsCode(goodsCode);
        List<GoodsRecordPortBO> goodsRecordPortList = getGoodsRecordPortList(userId, goodsCode);
        if (CollectionUtils.isEmpty(goodsRecordPortList)) {
            // 口岸有多条备案信息的时候取第一条即可
            GoodsRecordBO goodsRecord = getGoodsRecord(userId, goodsCode, port);
            if (goodsRecord == null) {
                return goodsRecordNewEditBO;
            }
            goodsRecordNewEditBO.setFlag(0);
            goodsRecordNewEditBO.setMessage("当前货品提交的口岸已存在，不能提交此备案");
            return goodsRecordNewEditBO;
        } else {
            //存在已创建、已驳回有记录，则无法发起备案
            Optional<GoodsRecordPortBO> any = goodsRecordPortList.stream().filter(goodsRecordPort -> {
                if (RecordStatus.CREATED.equals(goodsRecordPort.getRecordStatus()) || RecordStatus.REJECTED.equals(goodsRecordPort.getRecordStatus())) {
                    return true;
                }
                return false;
            }).findAny();

            if (any.isPresent()) {
                goodsRecordNewEditBO.setFlag(0);
                goodsRecordNewEditBO.setMessage("当前货品存在待申报口岸，不允许发起备案，请去货品备案页面编辑此备案");
                return goodsRecordNewEditBO;
            }

            //审核中，已申报 只要存在当前口岸，则无法发起备案
            Optional<GoodsRecordPortBO> any2 = goodsRecordPortList.stream().filter(goodsRecordPort -> {
                if (portName.equals(goodsRecordPort.getPortName())) {
                    return true;
                }
                return false;
            }).findAny();

            if (any2.isPresent()) {
                goodsRecordNewEditBO.setFlag(0);
                goodsRecordNewEditBO.setMessage("当前货品提交的口岸存在审核中、已申报口岸，不能提交此备案");
                return goodsRecordNewEditBO;
            }


            goodsRecordNewEditBO.setFlag(2);
            goodsRecordNewEditBO.setGoodsRecordId(goodsRecordPortList.get(0).getGoodsRecordId());
            return goodsRecordNewEditBO;
        }
    }

    public GoodsRecordBO getBySkuAndWarehouseCode(String ownerCode, String sku, String warehouseCode) {
        OwnerResult ownerResult = ownerFacade.getByCode(ownerCode);
        Assert.isTrue(ownerResult != null, "货主编码有误:" + ownerCode);
        EntityWarehouseQueryParam queryParam = new EntityWarehouseQueryParam();
        //  queryParam.setUserId(ownerResult.getUserId());
        queryParam.setWarehouseCode(warehouseCode);
        List<EntityWarehouseResult> entityWarehouseResultList = entityWarehouseFacade.listEntityWarehouseByQuery(queryParam);
        Assert.isTrue(org.apache.commons.collections4.CollectionUtils.isNotEmpty(entityWarehouseResultList), "仓库编码有误:" + warehouseCode);

        CentralGoodsRecordSearchReqVo reqVo = new CentralGoodsRecordSearchReqVo();
        reqVo.setSkuList(Arrays.asList(sku));
        reqVo.setPageSize(1);
        reqVo.setCurrentPage(1);
        RpcResult<List<CentralGoodsRecordPagingResVO>> result = remoteCentralRecordFacade.search(reqVo);
        List<GoodsRecordBO> list = centralGoodsRecordManagerHelper.toPagingResVOListGoodsRecordBO(buildListResult(result));
        return list.stream().findFirst().orElse(null);
    }

    @ParamLog(target = "自动货品备案", needResult = true)
    public List<GoodsRecordBO> checkAndReportGoodsRecord(List<GoodsRecordCssV1RpcResult> roodsRecordList, RecordSource source) {
        List<GoodsRecordBO> retList = new ArrayList<>();
        log.info("checkAndReportGoodsRecord param={}", JSON.toJSONString(roodsRecordList));
        for (GoodsRecordCssV1RpcResult goodsRecordRpcResult : roodsRecordList) {
            // oms说item_id赋值到了 goodsCode ，这个goodsCode 复制下 externalMaterialCode 以便后续使用
            String externalMaterialCode = goodsRecordRpcResult.getExternalMaterialCode();
            if (StringUtils.isBlank(externalMaterialCode)) {
                goodsRecordRpcResult.setExternalMaterialCode(goodsRecordRpcResult.getGoodsCode());
            }
            GoodsRecordBO goodsRecordBO;
            try {
                goodsRecordBO = checkAndReportGoodsRecord(goodsRecordRpcResult, source);
                retList.add(goodsRecordBO);
            } catch (Throwable e) {
                goodsRecordBO = BeanUtils.copyProperties(goodsRecordRpcResult, GoodsRecordBO.class);
                goodsRecordBO.setSyncMessage(e.getMessage());
                log.error("checkAndReportGoodsRecord error barcode:" + goodsRecordBO.getBarcode() + "sku:" + goodsRecordBO.getSku() + ",logicWarehouseCode:" + goodsRecordBO.getLogicWarehouseCode(), e);
                if (throwsException(source)) {
                    throw e;
                }
            }

        }
        log.info("checkAndReportGoodsRecord result={}", JSON.toJSONString(retList));
        return retList;
    }

    @Transactional(rollbackFor = Exception.class, noRollbackFor = GoodsRecordExistBusinessException.class)
    public GoodsRecordBO checkAndReportGoodsRecord(GoodsRecordCssV1RpcResult goodsRecord, RecordSource source) {

        List<Integer> passRecordStatus = Arrays.asList(RecordStatus.SUCCESS.getValue(), RecordStatus.REJECTED.getValue(), RecordStatus.APPROVAL.getValue());
        LogicWarehouseResult logicWarehouseResult = logicWarehouseFacade.getDetailByCode(goodsRecord.getUserId(), goodsRecord.getLogicWarehouseCode());

        //口岸数据补充
        if (org.apache.commons.lang3.StringUtils.isBlank(goodsRecord.getPort())) {
            goodsRecord.setPort(logicWarehouseResult.getPort());
        }
        //1.获取商品
        GoodsBO bo = getGoods(goodsRecord);
        GoodsRecordBO goodsRecordBO = null;
        if (bo != null) {
            goodsRecordBO = getGoodsRecord(bo.getUserId(), bo.getGoodsCode());
            log.info("checkAndReportGoodsRecord getGoodsRecord by userId={},goodCode={},result:{}", bo.getUserId(), bo.getGoodsCode(), JSON.toJSONString(goodsRecordBO));
        } else {
            GoodsRecordSearch queryParam = new GoodsRecordSearch();
            queryParam.setUserId(goodsRecord.getUserId());
            //条码
            queryParam.setQueryType(RecordQueryType.BARCODE.getValue());
            queryParam.setQueryInfo(goodsRecord.getBarcode());
            Assert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(goodsRecord.getBarcode()), "条码不能为空");
            Assert.isTrue(Objects.nonNull(goodsRecord.getUserId()), "用户id不能为空");
            goodsRecordBO = getGoodsRecordByParam(queryParam);
            log.info("checkAndReportGoodsRecord getGoodsRecord queryParam{},result:{}", JSON.toJSONString(queryParam), JSON.toJSONString(goodsRecordBO));
        }

        boolean needUpdate = false;
        //有备案
        if (goodsRecordBO != null) {
            //旧备案直接返回
            //if (RecordType.OLD.getValue().equals(goodsRecordBO.getRecordType().getValue())) {
            //    return importMode(goodsRecordBO, source);
            //}
            goodsRecordBO = getGoodsRecord(goodsRecordBO.getId());
            enrichPort(goodsRecordBO, goodsRecord, source);
            goodsRecord.setMaterialCode(goodsRecordBO.getMaterialCode());
            GoodsRecordBO portBO = this.getGoodsRecord(goodsRecordBO.getUserId(), goodsRecordBO.getGoodsCode(), goodsRecord.getPort());
            if (portBO != null && passRecordStatus.contains(portBO.getRecordStatus().getValue())) {
                return importMode(goodsRecordBO, source);
            }

            needUpdate = true;
        }
        //无备案记录,先检查goods
        //淘天与字节来源，云仓上有外部货主就为淘天来源,后期可能要用货主上的标记判断
        String itemSource = org.apache.commons.lang3.StringUtils.isNotBlank(logicWarehouseResult.getOutOwnerCode()) ? SYSTEM_TAG_TAOTIAN : SYSTEM_DYZJ;
        checkAndCreateGoods(goodsRecord, logicWarehouseResult.getOwnerCode(), logicWarehouseResult.getAccountCode(), source, itemSource);
        if (goodsRecordBO == null || needUpdate) {
            Long id = null;
            RecordType recordType = RecordType.NEW;
            if (goodsRecordBO != null) {
                id = goodsRecordBO.getId();
                recordType = goodsRecordBO.getRecordType();
            }
            goodsRecordBO = BeanUtils.copyProperties(goodsRecord, GoodsRecordBO.class);
            goodsRecordBO.setId(id);
            goodsRecordBO.setRecordType(recordType);
            goodsRecordBO.setPort("");
        }
        enrichPort(goodsRecordBO, goodsRecord, source);
        enrichRecord(goodsRecordBO, goodsRecord);
        Long id = saveOrUpdate(goodsRecordBO);
        return getGoodsRecord(id);

    }

    private void sleep() {
        //延时2秒
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 导入模式业务走异常
     */
    private GoodsRecordBO importMode(GoodsRecordBO goodsRecordBO, RecordSource recordSource) {
        if (RecordSource.CCS.getValue().equals(recordSource.getValue())) {
            throw new GoodsRecordExistBusinessException("备案已存在");
        }
        return goodsRecordBO;
    }

    private void enrichPort(GoodsRecordBO goodsRecordBO, GoodsRecordCssV1RpcResult goodsRecord, RecordSource recordSource) {
        String portName = cssPortTypeList.getPortTypeNameByCode(goodsRecord.getPort());
        if (goodsRecordBO.getPortCode() == null) {
            goodsRecordBO.setPortCode(goodsRecord.getPort());
        }
        goodsRecordBO.setWaitDeclaredPort(portName);
        goodsRecordBO.setAutoFlag(recordSource.getValue());
    }

    //补充转换
    private void enrichRecord(GoodsRecordBO goodsRecordBO, GoodsRecordCssV1RpcResult goodsRecord) {
        try {
            //单位转换
            if (org.apache.commons.lang3.StringUtils.isNotBlank(goodsRecordBO.getFirstUnit())) {
                CustomsUomResVo customsUomResVo = customsBaseDataRpc.findUomByName(goodsRecordBO.getFirstUnit());
                goodsRecordBO.setFirstUnit(customsUomResVo.getCode());
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(goodsRecordBO.getDeclaredUnit())) {
                CustomsUomResVo customsUomResVo = customsBaseDataRpc.findUomByName(goodsRecordBO.getDeclaredUnit());
                if (customsUomResVo != null) {
                    goodsRecordBO.setDeclaredUnit(customsUomResVo.getCode());
                }
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(goodsRecordBO.getSecondUnit())) {
                CustomsUomResVo customsUomResVo = customsBaseDataRpc.findUomByName(goodsRecordBO.getSecondUnit());
                if (customsUomResVo != null) {
                    goodsRecordBO.setSecondUnit(customsUomResVo.getCode());
                }
            }
        } catch (Throwable e) {
            log.info("findUomByName error", e);
            throw e;
        }

        //补全法1
        if (org.apache.commons.lang3.StringUtils.isBlank(goodsRecordBO.getFirstUnit()) && org.apache.commons.lang3.StringUtils.isNotBlank(goodsRecord.getHsCode())) {
            try {
                CustomsHsResVo customsHsResVo = customsBaseDataRpc.findHsByCode(goodsRecordBO.getHsCode());
                if (customsHsResVo != null) {
                    goodsRecordBO.setFirstUnit(customsHsResVo.getFirstLegalUnit());
                }
            } catch (Throwable e) {
                log.error("customsBaseDataRpc.findHsByCode HsCode=" + goodsRecord.getHsCode(), e);
            }
        }
        if (goodsRecordBO.getSecondQuantity() != null && goodsRecordBO.getSecondQuantity() <= 0) {
            goodsRecordBO.setSecondQuantity(null);
            goodsRecordBO.setSecondUnit(null);
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(goodsRecord.getSecondUnit())) {
            goodsRecordBO.setSecondQuantity(null);
        }


    }

    private GoodsBO getGoods(GoodsRecordCssV1RpcResult goodsRecord) {
        GoodsSearch goodsSearch = new GoodsSearch();
        goodsSearch.setBarcode(goodsRecord.getBarcode());
        goodsSearch.setUserId(goodsRecord.getUserId());
        log.info("checkAndCreateGoods 1", JSON.toJSONString(goodsSearch));
        GoodsBO goodsBO = goodsManager.getGoodsByParam(goodsSearch);
        return goodsBO;
    }

    private boolean checkAndCreateGoods(GoodsRecordCssV1RpcResult goodsRecord, String ownerCode, String accountCode, RecordSource source, String itemSource) {
        GoodsSearch goodsSearch = new GoodsSearch();
        goodsSearch.setBarcode(goodsRecord.getBarcode());
        goodsSearch.setUserId(goodsRecord.getUserId());
        log.info("checkAndCreateGoods", JSON.toJSONString(goodsSearch));
        GoodsBO goodsBO = goodsManager.getGoodsByParam(goodsSearch);
        UserRpcResult userRpcResult = userRpcFacade.getById(goodsRecord.getUserId());
        if (goodsBO == null) {
            log.info("checkAndCreateGoods is null{}", JSON.toJSONString(goodsSearch));
            GoodsAddParam addParam = new GoodsAddParam();
            addParam.setGoodsCode(generateGoodsCode(goodsRecord.getUserId()));
            addParam.setUserId(goodsRecord.getUserId());

            addParam.setUserName(userRpcResult.getUserName());
            addParam.setOriginUserName(userRpcResult.getUserName());
            goodsRecord.setUserName(userRpcResult.getUserName());
            addParam.setOriginUserId(goodsRecord.getUserId());
            addParam.setType(GoodsType.BONDED);
            addParam.setSku(goodsRecord.getSku());
            addParam.setBarcode(goodsRecord.getBarcode());
            addParam.setGrossWeight(goodsRecord.getGrossWeight());
            addParam.setNetWeight(goodsRecord.getNetWeight());
            addParam.setModel(goodsRecord.getModel());
            addParam.setBatchManagement(GoodsBatchManagement.YES);
            addParam.setWarningDate(1);
            addParam.setShelfLife(36500);
            addParam.setNoSellDate(1);
            addParam.setNoCollectDate(1);
            addParam.setOpenPeriodValidity(GoodsOpenPeriodValidity.YES);

            if (AresContext.SYSTEM_TAG_TAOTIAN.equals(itemSource)){
                addParam.setTag1( GoodsTagHelper.openTag(addParam.getTag1(), Tag1Constant.IS_TAOTIAN));
            }
            addParam.setSource(itemSource);
            /*
                则按照新口岸获取用户下的实体仓（业务上唯一），取【外部货品ID+账册】查询清关系统的账册库存接口，接口【海关备案料号+账册】返回商品名称，ERP填入货品名称。（查询到多条数据时，清关系统取金二序号最大值返回商品名称）
             */
            String goodName = goodsRecord.getGoodsName();
            BigDecimal declaredUnitPrice = goodsRecord.getDeclaredUnitPrice();
            try {
                com.danding.cds.out.bean.RpcResult<CustomsBookItemInfoResVo> result = customsBookItemRpc.getCustomsBookItemInfoByProIdAndBookNo(goodsRecord.getExternalMaterialCode(), accountCode);
                String tempGoodName = Optional.ofNullable(result).map(temp -> temp.getData()).map(CustomsBookItemInfoResVo::getGoodsName).orElse(null);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(tempGoodName)) {
                    goodName = tempGoodName;
                }
                if (declaredUnitPrice == null || declaredUnitPrice.compareTo(BigDecimal.ZERO) == 0) {
                    declaredUnitPrice = Optional.ofNullable(result).map(temp -> temp.getData()).map(CustomsBookItemInfoResVo::getDeclarePrice).orElse(null);
                }
            } catch (Throwable e) {
                log.error("checkAndCreateGoods getGoodsNameByProIdAndBookNo error", e);
            }
            addParam.setGoodsName(goodName);//
            goodsRecord.setGoodsName(goodName);
            goodsRecord.setDeclaredUnitPrice(declaredUnitPrice);//获取单价
            addParam.setRetailPrice(goodsRecord.getDeclaredUnitPrice());//申报价格
            goodsRecord.setGoodsCode(addParam.getGoodsCode());
            GoodsOperationLogParam operationLogParam = new GoodsOperationLogParam();
            operationLogParam.setOperationType(OperationType.OT_GOODS_SYSTEM_NEW);
            operationLogParam.setOperatorType(UserTypeEnum.SYSTEM);
            operationLogParam.setOperatorUserId(-99L);
            operationLogParam.setOperatorUserName("系统");
            operationLogParam.setReason(source.getDes() + "自动创建");
            addParam.setOperationLogParam(operationLogParam);//日志
            addParam.setOwnerCode(ownerCode);
            try {
                boolean ret = goodsFacade.create(addParam);
                //contactManager.contact(createGoodsErrCode, "0", "创建货品GoodsCode=" + addParam.getGoodsCode());
            } catch (BusinessException e) {
                contactManager.contact(goodsNacosConfig.getCreateRecordGoodsErrCode(), "1", "创建货品失败：" + e.getMessage());
                log.error("业务错误", e);
                throw e;
            } catch (Throwable e) {
                contactManager.contact(goodsNacosConfig.getCreateRecordGoodsErrCode(), "2", "创建货品失败:系统内部异常");
                log.error("系统内部错误", e);
                throw e;
            }
        } else {
            goodsRecord.setGoodsCode(goodsBO.getGoodsCode());
            goodsRecord.setUserName(userRpcResult.getUserName());
            BigDecimal declaredUnitPrice = goodsRecord.getDeclaredUnitPrice();
            if (declaredUnitPrice == null || declaredUnitPrice.compareTo(BigDecimal.ZERO) == 0) {
                goodsRecord.setDeclaredUnitPrice(goodsBO.getRetailPrice());
            }


        }
        GoodsManagementQueryParam queryParam = new GoodsManagementQueryParam();
        queryParam.setUserId(goodsRecord.getUserId());
        queryParam.setBarcode(goodsRecord.getBarcode());
        queryParam.setOwnerCode(ownerCode);
        GoodsManagementResult oldResult = goodsManagementFacade.getByQueryParam(queryParam);
        String oldExternalCode = null;
        String oldExternalSku = null;
        String externalCode = null;
        if (oldResult != null) {
            oldExternalCode = oldResult.getExternalCode();
            oldExternalSku = oldResult.getExternalSku();
        }
        if (Objects.equals(itemSource, SYSTEM_TAG_TAOTIAN)) {
            externalCode = goodsRecord.getExternalCode();
        }
        if (Objects.equals(itemSource, SYSTEM_DYZJ)) {
            if (StringUtils.isNotBlank(goodsRecord.getExternalCode())) {
                externalCode = goodsRecord.getExternalCode();
            }
        }
        //更新外部ID
        if (Objects.equals(itemSource, SYSTEM_DYZJ)) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(externalCode) && !Objects.equals(externalCode, oldExternalCode)) {
                updateExternalCode(ownerCode, goodsRecord.getGoodsCode(), externalCode, null);
            }
        }
        //更新菜鸟外部ID
        if (org.apache.commons.lang3.StringUtils.isNotBlank(goodsRecord.getExternalSku()) && !Objects.equals(goodsRecord.getExternalSku(), oldExternalSku)) {
            updateExternalCode(ownerCode, goodsRecord.getGoodsCode(), null, goodsRecord.getExternalSku());
        }

        return true;

    }

    private void updateExternalCode(String ownerCode, String goodCode, String externalCode, String externalSku) {
        GoodsManagementAddParam addParam = new GoodsManagementAddParam();
        addParam.setOwnerCode(ownerCode);
        addParam.setGoodsCode(goodCode);
        addParam.setExternalCode(externalCode);
        addParam.setExternalSku(externalSku);
        log.info("checkAndCreateGoods goodsManagementFacade.updateExternalCode{}", JSON.toJSONString(addParam));
        goodsManagementFacade.updateExternalCode(addParam);
        log.info("checkAndCreateGoods goodsManagementFacade.updateExternalCode{} 成功！", JSON.toJSONString(addParam));
    }

    private boolean throwsException(RecordSource source) {
        return RecordSource.CCS.getValue().equals(source.getValue());
    }

    public Long callbackFromCCS(GoodsRecordAuditSubmitDTO editParam) {
        //审核驳回 删除 已创建
        if (editParam.getOpinion() == 0) {
            GoodsRecordLogicWarehouseSearch goodsRecordLogicWarehouseSearch = new GoodsRecordLogicWarehouseSearch();
            goodsRecordLogicWarehouseSearch.setUserId(Long.valueOf(editParam.getTenantId()));
            goodsRecordLogicWarehouseSearch.setGoodsRecordId(editParam.getId());
            goodsRecordLogicWarehouseSearch.setPort(editParam.getCustomsCode());
            goodsRecordLogicWarehouseManager.removeBySearch(goodsRecordLogicWarehouseSearch);
        }
        return editParam.getId();
    }

    public boolean sendMessage(Long goodsRecordId) {
        GoodsRecordInfoSyncMessage goodsRecordInfoSyncMessage = new GoodsRecordInfoSyncMessage();
        goodsRecordInfoSyncMessage.setGoodsRecordId(goodsRecordId);
        return sendMessage(goodsRecordInfoSyncMessage);
    }

    public boolean sendMessage(Long goodsRecordId, Integer opinion, RecordType recordType) {
        GoodsRecordInfoSyncMessage goodsRecordInfoSyncMessage = new GoodsRecordInfoSyncMessage();
        goodsRecordInfoSyncMessage.setGoodsRecordId(goodsRecordId);
        goodsRecordInfoSyncMessage.setOpinion(opinion);
        goodsRecordInfoSyncMessage.setRecordType(recordType);
        return sendMessage(goodsRecordInfoSyncMessage);
    }

    public boolean updateImageInfo(GoodsRecordBO goodsRecordBO) {

        CentralUpdateImgInfoReqVo centralUpdateImgInfoReqVo = new CentralUpdateImgInfoReqVo();

        // 货品图片-正面
        if (StringUtils.isNotBlank(goodsRecordBO.getFrontJson())) {
            JSONObject jsonObject = JSON.parseObject(goodsRecordBO.getFrontJson());
            if (Objects.nonNull(jsonObject)) {
                centralUpdateImgInfoReqVo.setFrontImage(jsonObject.getString("url"));
            }
        }
        // 货品图片-侧面
        if (StringUtils.isNotBlank(goodsRecordBO.getSideJson())) {
            JSONObject jsonObject = JSON.parseObject(goodsRecordBO.getSideJson());
            if (Objects.nonNull(jsonObject)) {
                centralUpdateImgInfoReqVo.setSideImage(jsonObject.getString("url"));
            }
        }
        // 货品图片-背面
        if (StringUtils.isNotBlank(goodsRecordBO.getBackJson())) {
            JSONObject jsonObject = JSON.parseObject(goodsRecordBO.getBackJson());
            if (Objects.nonNull(jsonObject)) {
                centralUpdateImgInfoReqVo.setBackImage(jsonObject.getString("url"));
            }
        }
        // 货品图片-标签
        if (StringUtils.isNotBlank(goodsRecordBO.getLabelJson())) {
            JSONObject jsonObject = JSON.parseObject(goodsRecordBO.getLabelJson());
            if (Objects.nonNull(jsonObject)) {
                centralUpdateImgInfoReqVo.setLabelImage(jsonObject.getString("url"));
            }
        }

        centralUpdateImgInfoReqVo.setRecordId(goodsRecordBO.getId());
        RpcResult<String> result = remoteCentralRecordFacade.updateCentralImgInfo(Arrays.asList(centralUpdateImgInfoReqVo));
        return buildResult(result);

    }

    public boolean sendMessage(GoodsRecordInfoSyncMessage goodsRecordInfoSyncMessage) {
        try {
            Map keysMap = Maps.newHashMap();
            // 配置消息KEYS会显示在RocketMQ的Key字段
            String format = DateFormatUtils.format(new Date(System.currentTimeMillis()), "yyyy-MM-dd hh:mm:ss");
            keysMap.put("KEYS", goodsRecordInfoSyncMessage.getGoodsRecordId() + "-" + format);
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);

            SpringMessage message = SpringMessage.createMessage(goodsRecordInfoSyncMessage, messageHeaders);

            SendResult sendResult = doSyncSend(5000L, 1, message);
            if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
                log.warn("同步货品MQ发送失败1次：单号：{} 正在重试...", goodsRecordInfoSyncMessage.getGoodsRecordId() + "-" + format);
                sendResult = doSyncSend(5000L, 1, message);
                if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
                    log.error("======同步货品MQ发送失败2次，需要手工处理========processMessage={}", JSON.toJSONString(message));
                    return false;
                } else {
                    return true;
                }
            } else {
                return true;
            }
        } catch (Exception ex) {
            log.error("sendMessage error ", ex);
        }
        return false;
    }

    public boolean updateMaterialCode(GoodsRecordBO goodsRecordBO) {

        Long userId = goodsRecordBO.getUserId();
        Long goodsRecordId = goodsRecordBO.getCcsRecordId();
        if (goodsRecordId == null) {
            log.info("updateMaterialCode goodsRecordBO goodsRecordId null" + JSONObject.toJSONString(goodsRecordBO));
            return false;
        }
        if (!Objects.equals(RecordStatus.SUCCESS.getValue(), goodsRecordBO.getRecordStatus().getValue())) {
            log.info("updateMaterialCode goodsRecordBO  RecordStatus not is  SUCCESS" + JSONObject.toJSONString(goodsRecordBO));
            return false;

        }
        String goodsCode = goodsRecordBO.getGoodsCode();
        String materialCode = goodsRecordBO.getMaterialCode();

        GoodsRecordLogicWarehouseSearch goodsRecordLogicWarehouseSearch = new GoodsRecordLogicWarehouseSearch();
        goodsRecordLogicWarehouseSearch.setGoodsCcsRecordId(goodsRecordId);
        List<GoodsRecordLogicWarehouseBO> goodsRecordLogicWarehouseBOList = goodsRecordLogicWarehouseManager.listBySearch(goodsRecordLogicWarehouseSearch);

        LogicWarehouseQueryParam logicWarehouseQueryParam = new LogicWarehouseQueryParam();

        logicWarehouseQueryParam.setLogicWarehouseCodes(org.apache.commons.lang3.StringUtils.join(goodsRecordLogicWarehouseBOList.stream().map(GoodsRecordLogicWarehouseBO::getLogicWarehouseCode).collect(Collectors.toList()).toArray(), ","));
        List<LogicWarehouseResult> logicWarehouseBOList = logicWarehouseFacade.listLogicWarehouseByParam(logicWarehouseQueryParam);

        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setGoodsCode(goodsCode);
        goodsManagementSearch.setOwnerCodeList(logicWarehouseBOList.stream().map(LogicWarehouseResult::getOwnerCode).collect(Collectors.toList()));
        goodsManagementSearch.setUserId(userId);
        /**
         List<GoodsManagementBO> updateList = this.listBySearch(goodsManagementSearch);
         updateList.stream().forEach(goodsManagementBO->goodsManagementBO.setMaterialCode(materialCode));
         **/
        return goodsManagementService.updateMaterialCodeByOwnerCodeAndGoodsCode(goodsManagementSearch, materialCode);
        //   return updateListById(updateList);


    }


    public boolean updateMaterialCode(GoodsRecordBO goodsRecordBO, Boolean checkRecordStatus) {

        Long userId = goodsRecordBO.getUserId();
        Long goodsRecordId = goodsRecordBO.getCcsRecordId();
        if (goodsRecordId == null) {
            log.info("updateMaterialCode goodsRecordBO goodsRecordId null" + JSONObject.toJSONString(goodsRecordBO));
            return false;
        }
        if (Boolean.TRUE.equals(checkRecordStatus) && !Objects.equals(RecordStatus.SUCCESS.getValue(), goodsRecordBO.getRecordStatus().getValue())) {
            log.info("updateMaterialCode goodsRecordBO  RecordStatus not is  SUCCESS" + JSONObject.toJSONString(goodsRecordBO));
            return false;
        }
        String goodsCode = goodsRecordBO.getGoodsCode();
        String materialCode = goodsRecordBO.getMaterialCode();

        GoodsRecordLogicWarehouseSearch goodsRecordLogicWarehouseSearch = new GoodsRecordLogicWarehouseSearch();
        goodsRecordLogicWarehouseSearch.setGoodsCcsRecordId(goodsRecordId);
        List<GoodsRecordLogicWarehouseBO> goodsRecordLogicWarehouseBOList = goodsRecordLogicWarehouseManager.listBySearch(goodsRecordLogicWarehouseSearch);

        LogicWarehouseQueryParam logicWarehouseQueryParam = new LogicWarehouseQueryParam();

        logicWarehouseQueryParam.setLogicWarehouseCodes(org.apache.commons.lang3.StringUtils.join(goodsRecordLogicWarehouseBOList.stream().map(GoodsRecordLogicWarehouseBO::getLogicWarehouseCode).collect(Collectors.toList()).toArray(), ","));
        List<LogicWarehouseResult> logicWarehouseBOList = logicWarehouseFacade.listLogicWarehouseByParam(logicWarehouseQueryParam);

        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setGoodsCode(goodsCode);
        goodsManagementSearch.setOwnerCodeList(logicWarehouseBOList.stream().map(LogicWarehouseResult::getOwnerCode).collect(Collectors.toList()));
        goodsManagementSearch.setUserId(userId);
        /**
         List<GoodsManagementBO> updateList = this.listBySearch(goodsManagementSearch);
         updateList.stream().forEach(goodsManagementBO->goodsManagementBO.setMaterialCode(materialCode));
         **/
        return goodsManagementService.updateMaterialCodeByOwnerCodeAndGoodsCode(goodsManagementSearch, materialCode);
        //   return updateListById(updateList);


    }

    public void updateMaterialCodeByOwnerCodeAndGoodsCode(GoodsManagementSearch goodsManagementSearch, String materialCode) {
        goodsManagementService.updateMaterialCodeByOwnerCodeAndGoodsCode(goodsManagementSearch, materialCode);
    }

    private SendResult doSyncSend(Long timeout, Integer delayLevel, SpringMessage message) {
        SendResult sendResult;
        if (Objects.nonNull(delayLevel)) {
            sendResult = springRocketMQProducer.syncSend(goodsNacosConfig.getGoodsRecordInfoSyncTopic() + proxyConfig.getCcsTag(), message, timeout, delayLevel);
        } else {
            sendResult = springRocketMQProducer.syncSend(goodsNacosConfig.getGoodsRecordInfoSyncTopic() + proxyConfig.getCcsTag(), message, timeout);
        }
        return sendResult;
    }

    public boolean updateGoodsNameAndBarcode(Long userId, String goodsCode, String goodName, String barcode) {
        GoodsRecordBO goodsRecordBO = this.getGoodsRecord(userId, goodsCode);
        if (Objects.isNull(goodsRecordBO)) {
            log.info("货品备案未找到:" + goodsCode + "无需修改");
            return false;
        }
        List<CentralUpdateNameAndBarCodeReqVo> list = new ArrayList<>();
        CentralUpdateNameAndBarCodeReqVo reqVo = new CentralUpdateNameAndBarCodeReqVo();
        reqVo.setRecordId(goodsRecordBO.getId());
        reqVo.setGoodsRecordName(goodName);
        reqVo.setBarCode(barcode);
        list.add(reqVo);
        RpcResult<String> result = remoteCentralRecordFacade.updateCentralNameAndBarCode(list);
        return buildResult(result);
    }

    public String getPortTypeCodeByName(String codeName) {
        return cssPortTypeList.getPortTypeCodeByName(codeName);
    }


}


