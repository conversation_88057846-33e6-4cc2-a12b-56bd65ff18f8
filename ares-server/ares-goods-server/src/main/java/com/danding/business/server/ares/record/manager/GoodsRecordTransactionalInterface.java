package com.danding.business.server.ares.record.manager;

import com.danding.business.client.rpc.goods.result.GoodsRecordRpcResult;
import com.danding.business.common.ares.enums.goods.RecordSource;
import com.danding.business.server.ares.record.BO.GoodsRecordBO;

import java.util.List;

public interface GoodsRecordTransactionalInterface {

    public GoodsRecordBO checkAndReportGoodsRecord(GoodsRecordRpcResult goodsRecord, RecordSource source);

    public List<GoodsRecordRpcResult> importGoodsRecord(GoodsRecordRpcResult goodsRecordRpcResult, RecordSource source);
}
