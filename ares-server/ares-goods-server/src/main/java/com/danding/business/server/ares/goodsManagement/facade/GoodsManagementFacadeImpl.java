package com.danding.business.server.ares.goodsManagement.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.Issue.facade.IGoodsIssueFacade;
import com.danding.business.client.ares.Issue.param.GoodsIssueAddParam;
import com.danding.business.client.ares.brand.facade.IBrandFacade;
import com.danding.business.client.ares.entitywarehouse.result.EntityWarehouseResult;
import com.danding.business.client.ares.goods.facade.IGoodsFacade;
import com.danding.business.client.ares.goods.param.GoodsOperationLogParam;
import com.danding.business.client.ares.goods.param.GoodsQueryParam;
import com.danding.business.client.ares.goods.result.GoodsResult;
import com.danding.business.client.ares.goodsManagement.facade.IGoodsManagementFacade;
import com.danding.business.client.ares.goodsManagement.param.GoodsManagementAddParam;
import com.danding.business.client.ares.goodsManagement.param.GoodsManagementQueryParam;
import com.danding.business.client.ares.goodsManagement.param.GoodsManagementWmsUpdParam;
import com.danding.business.client.ares.goodsManagement.param.PeriodValidityUpdateParam;
import com.danding.business.client.ares.goodsManagement.result.GoodsManagementResult;
import com.danding.business.client.ares.operationLogs.param.OperationLogsAddParam;
import com.danding.business.client.ares.owner.facade.IOwnerFacade;
import com.danding.business.client.ares.owner.param.OwnerQueryParam;
import com.danding.business.client.ares.owner.result.OwnerIdNameResult;
import com.danding.business.client.ares.owner.result.OwnerResult;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.business.common.ares.context.AresContext;
import com.danding.business.common.ares.context.Tag1Constant;
import com.danding.business.common.ares.enums.common.ActionType;
import com.danding.business.common.ares.enums.common.TradeType;
import com.danding.business.common.ares.enums.goods.GoodsBatchManagement;
import com.danding.business.common.ares.enums.goods.GoodsOpenPeriodValidity;
import com.danding.business.common.ares.enums.goods.GoodsStatus;
import com.danding.business.common.ares.enums.goods.PledgeStatus;
import com.danding.business.common.ares.enums.goods.SkuTagEnum;
import com.danding.business.common.ares.enums.operationLogs.OperationType;
import com.danding.business.common.ares.enums.operationLogs.UserTypeEnum;
import com.danding.business.common.ares.excel.AbstractExcelExportSingleSheetServiceV2;
import com.danding.business.common.ares.utils.Assert;
import com.danding.business.common.ares.utils.ConfigTagHelper;
import com.danding.business.common.ares.utils.MyStringUtils;
import com.danding.business.common.ares.utils.SourceUtils;
import com.danding.business.core.ares.goodsManagement.search.GoodsManagementSearch;
import com.danding.business.core.ares.record.search.GoodsRecordSearch;
import com.danding.business.server.ares.goods.BO.GoodsBO;
import com.danding.business.server.ares.goods.manager.GoodsManager;
import com.danding.business.server.ares.goods.remote.RemoteConfigFacade;
import com.danding.business.server.ares.goods.remote.RemoteGoodsFacade;
import com.danding.business.server.ares.goods.remote.RemoteUserFacade;
import com.danding.business.server.ares.goodsManagement.BO.GoodsManagementBO;
import com.danding.business.server.ares.goodsManagement.excel.GoodsManagementExportExcel;
import com.danding.business.server.ares.goodsManagement.manager.GoodsManagementManager;
import com.danding.business.server.ares.record.BO.GoodsRecordBO;
import com.danding.business.server.ares.record.manager.CentralGoodsRecordManager;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.soul.client.common.exception.BusinessException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 货品管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-06
 */
@DubboService
@Slf4j
@Component("goodsManagementExcelService")
public class GoodsManagementFacadeImpl extends AbstractExcelExportSingleSheetServiceV2<GoodsManagementExportExcel, GoodsManagementQueryParam> implements IGoodsManagementFacade {

    @Autowired
    private GoodsManagementManager goodsManagementManager;
    @Autowired
    private IOwnerFacade ownerFacade;
    @Autowired
    private RemoteGoodsFacade remoteGoodsFacade;
    @Autowired
    private IGoodsFacade goodsFacade;
    @Autowired
    private IBrandFacade brandFacade;
    @Autowired
    private IGoodsIssueFacade goodsIssueFacade;
    @Autowired
    private GoodsManager goodsManager;

    @Autowired
    private RemoteConfigFacade remoteConfigFacade;

    @Autowired
    private RemoteUserFacade remoteUserFacade;

    @Autowired
    private CentralGoodsRecordManager centralGoodsRecordManager;


    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    @Override
    public GoodsManagementResult getById(Serializable id) {
        GoodsManagementBO goodsManagementBO = goodsManagementManager.getById(id);
        return BeanUtils.copyProperties(goodsManagementBO, GoodsManagementResult.class);
    }

    /**
     * 条件查询单个
     *
     * @param goodsManagementQueryParam
     * @return
     */
    @Override
    public GoodsManagementResult getDetailByQueryParam(GoodsManagementQueryParam goodsManagementQueryParam) {
        log.info("[GoodsManagementFacadeImpl-getByQueryParam]=======查询货品管理信息=======:{}", JSON.toJSONString(goodsManagementQueryParam));
        GoodsManagementBO goodsManagementBO = goodsManagementManager.getBySearch(BeanUtils.copyProperties(goodsManagementQueryParam, GoodsManagementSearch.class));
        //淘天的商品直接返回，效期。
        if (Objects.nonNull(goodsManagementBO) && goodsManagementBO.isTaoTianGoods()) {
            return BeanUtils.copyProperties(goodsManagementBO, GoodsManagementResult.class);
        }
        GoodsResult goodsByParam;
        if (goodsManagementBO == null) {
            if (goodsManagementQueryParam.getFlag() == 1) {
                if (StringUtils.isNotEmpty(goodsManagementQueryParam.getGoodsCode())) {
                    goodsByParam = goodsFacade.getGoodsByParam(goodsManagementQueryParam.getUserId(), goodsManagementQueryParam.getGoodsCode());
                } else if (StringUtils.isNotEmpty(goodsManagementQueryParam.getSku())) {
                    goodsByParam = goodsFacade.getGoodsByParam(goodsManagementQueryParam.getUserId(), null, goodsManagementQueryParam.getSku());
                } else {
                    throw new BusinessException("查询单条货品详情时货品ID和Sku不能全为空！");
                }
                return BeanUtils.copyProperties(goodsByParam, GoodsManagementResult.class);
            } else {
                return null;
            }
        } else {
            goodsByParam = goodsFacade.getGoodsByParam(goodsManagementBO.getUserId(), goodsManagementBO.getGoodsCode());
        }
        GoodsManagementResult goodsManagementResult = BeanUtils.copyProperties(goodsByParam, GoodsManagementResult.class);
        goodsManagementResult.setShelfLife(goodsManagementBO.getShelfLife());
        goodsManagementResult.setNoCollectDate(goodsManagementBO.getNoCollectDate());
        goodsManagementResult.setNoSellDate(goodsManagementBO.getNoSellDate());
        goodsManagementResult.setWarningDate(goodsManagementBO.getWarningDate());
        goodsManagementResult.setBatchManagement(goodsManagementBO.getBatchManagement());
        goodsManagementResult.setOwnerCode(goodsManagementBO.getOwnerCode());
        goodsManagementResult.setPledgeStatus(goodsManagementBO.getPledgeStatus());
        goodsManagementResult.setExternalCode(goodsManagementBO.getExternalCode());
        //长宽高体积，净重，毛重以wms回传为准
        if (Objects.nonNull(goodsManagementBO.getLength())) {
            goodsManagementResult.setLength(goodsManagementBO.getLength());
        }
        if (Objects.nonNull(goodsManagementBO.getWidth())) {
            goodsManagementResult.setWidth(goodsManagementBO.getWidth());
        }
        if (Objects.nonNull(goodsManagementBO.getHeight())) {
            goodsManagementResult.setHeight(goodsManagementBO.getHeight());
        }
        if (Objects.nonNull(goodsManagementBO.getVolume())) {
            goodsManagementResult.setVolume(goodsManagementBO.getVolume());
        }
        if (Objects.nonNull(goodsManagementBO.getGrossWeight())) {
            goodsManagementResult.setGrossWeight(goodsManagementBO.getGrossWeight());
        }
        if (Objects.nonNull(goodsManagementBO.getNetWeight())) {
            goodsManagementResult.setNetWeight(goodsManagementBO.getNetWeight());
        }
        try {
            goodsManagementResult.setBrandName(brandFacade.getByBrandCode(goodsManagementResult.getBrandCode()).getBrandName());
        } catch (Exception e) {
            //log.error("[GoodsManagementFacadeImpl-getByQueryParam]========品牌查询异常=======:{}", goodsManagementResult.getBrandName(), e);
        }
        return goodsManagementResult;
    }

    @Override
    public List<GoodsManagementResult> getDetailListByQueryParamForT(List<String> goodsCodeList, String ownerCode, Long userId) {
        goodsCodeList.stream().peek(MyStringUtils::getGoodsCodeWithoutDaitaT).collect(Collectors.toList());
        return listGoodsManagementByQuery(null, goodsCodeList, ownerCode, 1);
//        return listByQueryParam(userId, goodsCodeList, ownerCode, 1);
    }

    @Override
    public GoodsManagementResult getDetailByQueryParamForT(String goodsCode, String ownerCode, Long userId) {
        goodsCode = MyStringUtils.getGoodsCodeWithoutDaitaT(goodsCode);
        return getDetailByQueryParam(goodsCode, ownerCode, userId);
    }

    @Override
    public GoodsManagementResult getDetailByQueryParam(String goodsCode, String ownerCode, Long userId) {
        return this.getDetailByQueryParam(goodsCode, null, ownerCode, userId);
    }

    @Override
    public GoodsManagementResult getDetailByQueryParam(String goodsCode, String sku, String ownerCode, Long userId) {
        if (AresContext.VIRTUAL_OWNER.equals(ownerCode)) {
            // 虚拟仓设置默认值
            GoodsResult goodsResult = goodsFacade.getGoodsByParam(userId, goodsCode, sku);
            if (Objects.isNull(goodsResult)) {
                log.error("货品信息不存在 userId:" + userId + ",goodsCode:" + goodsCode + ",sku:" + sku);
                return null;
            }
            GoodsManagementResult goodsManagementResult = BeanUtils.copyProperties(goodsResult, GoodsManagementResult.class);
            goodsManagementResult.setBatchManagement(GoodsBatchManagement.NO);
            goodsManagementResult.setPledgeStatus(PledgeStatus.NO);
            goodsManagementResult.setOwnerCode(AresContext.VIRTUAL_OWNER);
            return goodsManagementResult;
        }
        GoodsManagementQueryParam queryParam = new GoodsManagementQueryParam();
        queryParam.setOwnerCode(ownerCode);
        queryParam.setGoodsCode(goodsCode);
        queryParam.setUserId(userId);
        queryParam.setSku(sku);
        return this.getDetailByQueryParam(queryParam);
    }

    @Override
    public GoodsManagementResult getDetailByQueryParam(String sku, String ownerCode, String warehouseCode) {
        GoodsManagementQueryParam queryParam = new GoodsManagementQueryParam();
        queryParam.setOwnerCode(ownerCode);
        queryParam.setSku(sku);
        queryParam.setWarehouseCode(warehouseCode);
        return this.getDetailByQueryParam(queryParam);
    }

    @Override
    public GoodsManagementResult getByQueryParam(GoodsManagementQueryParam queryParam) {
        GoodsManagementBO goodsManagementBO = goodsManagementManager.getBySearch(BeanUtils.copyProperties(queryParam, GoodsManagementSearch.class));
        return BeanUtils.copyProperties(goodsManagementBO, GoodsManagementResult.class);
    }

    /**
     * 条件查询list
     *
     * @param goodsManagementQueryParam
     * @return
     */
    @Override
    public List<GoodsManagementResult> listByQueryParam(GoodsManagementQueryParam goodsManagementQueryParam) {
        List<GoodsManagementBO> goodsManagementBOList = goodsManagementManager.listBySearch(BeanUtils.copyProperties(goodsManagementQueryParam, GoodsManagementSearch.class));
        return BeanUtils.copyProperties(goodsManagementBOList, GoodsManagementResult.class);
    }

    @Override
    public List<GoodsManagementResult> listByQueryParam(Long userId, List<String> list, String ownerCode, Integer type) {
        // 1 -批量查询货品信息
        GoodsQueryParam goodsParam = new GoodsQueryParam();
        if (Objects.equals(1, type)) {
            goodsParam.setGoodsCodes(list);
        } else if (Objects.equals(2, type)) {
            goodsParam.setSkus(list);
        } else if (Objects.equals(3, type)) {
            // 如果为 3 ,说明是字节入库单查询
            goodsParam.setCargoCodeList(list);
        } else {
            throw new BusinessException("货品查询type传递有误");
        }
        goodsParam.setUserId(userId);
        log.info("[GoodsManagementFacadeImpl-listByQueryParam]=======批量查询货品管理信息=======:{}", JSON.toJSONString(goodsParam));
        List<GoodsResult> goodsResults = goodsFacade.listGoodsByParam(goodsParam);
        if (StringUtils.isNotBlank(ownerCode)) {
            //如果是虚拟货主编码，则直接返回不开启批次管理
            if (AresContext.VIRTUAL_OWNER.equals(ownerCode)) {
                List<GoodsManagementResult> goodsManagementResultList = BeanUtils.copyProperties(goodsResults, GoodsManagementResult.class);
                goodsManagementResultList.stream().peek(goodsManagementResult -> {
                    goodsManagementResult.setBatchManagement(GoodsBatchManagement.NO);
                    goodsManagementResult.setOwnerCode(AresContext.VIRTUAL_OWNER);
                    goodsManagementResult.setPledgeStatus(PledgeStatus.NO);
                }).collect(Collectors.toList());
                return goodsManagementResultList;
            }
            // 2 -批量查询货品管理
            GoodsManagementQueryParam param = new GoodsManagementQueryParam();
            param.setOwnerCode(ownerCode);
            if (Objects.equals(1, type)) {
                param.setGoodsCodes(list);
            } else if (Objects.equals(2, type)) {
                param.setSkus(list);
            } else if (Objects.equals(3, type)) {
                List<String> goodsList = goodsResults.stream().map(GoodsResult::getGoodsCode).collect(Collectors.toList());
                param.setGoodsCodes(goodsList);
            }
            param.setUserId(userId);
            List<GoodsManagementResult> goodsManagementResults = this.listByQueryParam(param);
            // 3 -组装map集合
            Map<String, GoodsResult> goodsResultMap = goodsResults.stream().collect(Collectors.toMap(GoodsResult::getGoodsCode, Function.identity()));
            // 3.1 -封装信息
            List<GoodsManagementResult> finalGoodsManagementResults = new ArrayList<>();
            for (GoodsManagementResult result : goodsManagementResults) {
                GoodsResult goodsResult = goodsResultMap.get(result.getGoodsCode());
                GoodsManagementResult goodsManagementResult = BeanUtils.copyProperties(goodsResult, GoodsManagementResult.class);
                goodsManagementResult.setShelfLife(result.getShelfLife());
                goodsManagementResult.setNoCollectDate(result.getNoCollectDate());
                goodsManagementResult.setNoSellDate(result.getNoSellDate());
                goodsManagementResult.setWarningDate(result.getWarningDate());
                goodsManagementResult.setBatchManagement(result.getBatchManagement());
                goodsManagementResult.setOwnerCode(result.getOwnerCode());
                goodsManagementResult.setPledgeStatus(result.getPledgeStatus());
                finalGoodsManagementResults.add(goodsManagementResult);
            }
            return finalGoodsManagementResults;
        } else {
            // 不传货主只处理货品基本信息
            return BeanUtils.copyProperties(goodsResults, GoodsManagementResult.class);
        }
    }

    /**
     * 条件分页查询
     *
     * @param goodsManagementQueryParam
     * @return
     */
    @Override
    public ListVO<GoodsManagementResult> pageListByQueryParam(GoodsManagementQueryParam goodsManagementQueryParam) {
        ListVO<GoodsManagementBO> goodsManagementBOListVO = goodsManagementManager.pageListBySearch(BeanUtils.copyProperties(goodsManagementQueryParam, GoodsManagementSearch.class));
        List<GoodsManagementBO> dataList = goodsManagementBOListVO.getDataList();
        if (!CollectionUtils.isEmpty(dataList)) {
            for (GoodsManagementBO goodsManagementBO : dataList) {
                try {
                    GoodsResult goodsResult = goodsFacade.getGoodsByParam(goodsManagementBO.getUserId(), goodsManagementBO.getGoodsCode());
                    if (goodsResult != null) {
                        goodsManagementBO.setBarcode(goodsResult.getBarcode());
//                        goodsManagementBO.setExternalSku(goodsResult.getExternalSku());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return ListVO.build(goodsManagementBOListVO.getPage(), BeanUtils.copyProperties(dataList, GoodsManagementResult.class));
    }

    /**
     * 插入
     *
     * @param addParam
     * @return
     */
    @Override
    public boolean add(GoodsManagementAddParam addParam) {
        return add(addParam, false);
    }

    @Override
    public boolean add(GoodsManagementAddParam addParam, boolean updateIfPresent) {
        checkArgs(addParam);
        return syncAdd(addParam, updateIfPresent);
    }

    @Override
    public boolean add(GoodsManagementAddParam goodsManagementAddParam, boolean updateIfPresent, boolean addIssueRecord) {
        boolean ret = add(goodsManagementAddParam, updateIfPresent);
        if (addIssueRecord) {
            Long operatorUserId = SimpleUserHelper.getUserId();
            addIssueGoodsRecord(operatorUserId, goodsManagementAddParam.getOwnerCode(), goodsManagementAddParam.getGoodsCode());
        }
        return ret;
    }

    private boolean addIssueGoodsRecord(Long operatorUserId, String ownerCode, String goodSCode) {
        UserRpcResult userRpcResult = remoteUserFacade.getById(operatorUserId);
        GoodsIssueAddParam addParam = new GoodsIssueAddParam();
        addParam.setGoodsCode(goodSCode);
        addParam.setOwnerCode(ownerCode);
        addParam.setActionType(ActionType.ADD);
        addParam.setOperator(operatorUserId);
        addParam.setUserName(userRpcResult != null ? userRpcResult.getUserName() : StringUtils.EMPTY);
        OwnerResult ownerResult = remoteConfigFacade.getOwnerCode(ownerCode);
        addParam.setUserId(ownerResult.getUserId());
        addParam.setEntityWarehouseCode(ownerResult.getEntityWarehouseCode());
        return goodsIssueFacade.addIssueGoodsRecord(addParam);
    }


    @Override
    public boolean syncAdd(GoodsManagementAddParam addParam) {
        return syncAdd(addParam, false);
    }

    //@Override
    public boolean syncAdd(GoodsManagementAddParam addParam, boolean updateIfPresent) {
        try {
            //新货品默认开启效期开启后，若用户的【货品基础信息】未打开批次管理，货品首次创建至货主下时，默认打开效期。
            if (GoodsOpenPeriodValidity.NO.equals(addParam.getOpenPeriodValidity())) {
                if ("WMS".equalsIgnoreCase(addParam.getFromSource())) {
                    if (GoodsOpenPeriodValidity.NO.getValue().equals(addParam.getOpenPeriodValidity().getValue())) {
                        addParam.setWarningDate(0);
                        addParam.setNoCollectDate(0);
                        addParam.setNoSellDate(0);
                        addParam.setShelfLife(0);
                    }
                } else {
                    OwnerResult ownerResult = remoteGoodsFacade.getOwnerByCode(addParam.getOwnerCode());
                    if (Objects.isNull(ownerResult)) {
                        throw new BusinessException("当前货主不存在【" + addParam.getOwnerCode() + "】");
                    }
                    /*
                    //【效期品设置】开启批次管理， 默认效期值：保质期=730、禁售天数=60、禁收天数=120、预警天数=80
                    if (Objects.equals(1, ownerResult.getNewGoodsIsBatch())) {
                        addParam.setBatchManagement(GoodsBatchManagement.YES);
                        addParam.setWarningDate(80);
                        addParam.setNoCollectDate(120);
                        addParam.setNoSellDate(60);
                        addParam.setShelfLife(730);
                    }*/
                }
            }
            GoodsManagementBO bo = BeanUtils.copyProperties(addParam, GoodsManagementBO.class);

            if (bo.getFeature() != null) {
                bo.setSkuSize((String) bo.getFeature().get("size"));
                if (bo.isTaoTianGoods()) {
                    bo.setStandardWidth(Convert.toBigDecimal(bo.getFeature().get("wms_auth_carton_width")));
                    bo.setStandardHeight(Convert.toBigDecimal(bo.getFeature().get("wms_auth_carton_height")));
                    bo.setStandardLength(Convert.toBigDecimal(bo.getFeature().get("wms_auth_carton_length")));
                    bo.calculateStandardVolume();
                    bo.setCartonPcs(Convert.toInt(bo.getFeature().get("wms_auth_carton_pcs")));
                    // 新增效期品，小样商品标记处理
                    if (bo.getFeature().containsKey("isSmallSample")) {
                        Object o = bo.getFeature().get("isSmallSample");
                        if (Objects.nonNull(o) && "true".equals(String.valueOf(o))) {
                            Integer skuTag = Optional.ofNullable(bo.getSkuTag()).orElse(0);
                            Set<SkuTagEnum> skuTagEnums = SkuTagEnum.NumToEnum(skuTag);
                            skuTagEnums.add(SkuTagEnum.SMALL_SAMPLE_SKU);
                            bo.setSkuTag(SkuTagEnum.enumToNum(Lists.newArrayList(skuTagEnums)));
                        }
                    }

                } else {
                    Object cartonPcs = bo.getFeature().get("carton_pcs");
                    if (Objects.nonNull(cartonPcs)) {
                        bo.setCartonPcs(Convert.toInt(cartonPcs));
                    }
                }
                BigDecimal cartonWeight = Convert.toBigDecimal(bo.getFeature().get("wms_auth_carton_weight"));
                Optional.ofNullable(cartonWeight).ifPresent(m -> {
                    bo.getFeature().put("cartonWeight", m);
                });
            }


            enrichWarehouseCode(bo);
            //退货仓，新增没测试数据设置为0
            if (bo.isTaoTianGoods() && goodsManagementManager.getReturnWarehouseCode().contains(bo.getWarehouseCode())) {
                bo.setHeight(0.0);
                bo.setWidth(0.0);
                bo.setLength(0.0);
                bo.setVolume(0.0);
                bo.setGrossWeight(0.0);
                bo.setStandardWidth(BigDecimal.ZERO);
                bo.setStandardHeight(BigDecimal.ZERO);
                bo.setStandardLength(BigDecimal.ZERO);
                bo.setStandardVolume(BigDecimal.ZERO);
                bo.getFeature().put("cartonWeight", 0);
                bo.setCartonPcs(0);
            }
            boolean added = goodsManagementManager.add(bo);
            if (added && addParam.getOperationLogParam() != null) {
                remoteGoodsFacade.addOperationLog(addParam.getOperationLogParam(), addParam.getGoodsCode(),
                        "{}", JSON.toJSONString(bo));
            }

            return added;
        } catch (DuplicateKeyException e) {
            if (updateIfPresent) {
                GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
                goodsManagementSearch.setUserId(addParam.getUserId());
                goodsManagementSearch.setGoodsCode(addParam.getGoodsCode());
                goodsManagementSearch.setOwnerCode(addParam.getOwnerCode());
                GoodsManagementBO goodsManagementBO = goodsManagementManager.getBySearch(goodsManagementSearch);
                if (Objects.isNull(goodsManagementBO)) {
                    throw new BusinessException("当前货品管理不存在 userId:" + addParam.getUserId() + ",goodsCode:" + addParam.getGoodsCode() + ",ownerCode:" + addParam.getOwnerCode());
                }
                GoodsManagementBO curGoodsManagementBO = BeanUtils.copyProperties(addParam, GoodsManagementBO.class);
                curGoodsManagementBO.setId(goodsManagementBO.getId());
                curGoodsManagementBO.setVersion(goodsManagementBO.getVersion());
                log.info("[GoodsManagementFacadeImpl-syncAdd]========货品效期更新==========={}", JSON.toJSONString(addParam));
                boolean result = goodsManagementManager.updateById(curGoodsManagementBO);
                if (!result) {
                    return result;
                }

                //操作日志
                if (addParam.getOperationLogParam() != null) {
                    remoteGoodsFacade.addOperationLog(addParam.getOperationLogParam(), goodsManagementBO.getGoodsCode(),
                            JSON.toJSONString(goodsManagementBO), JSON.toJSONString(curGoodsManagementBO));
                }

                return result;
            } else {
                log.error("[GoodsManagementFacadeImpl-add]========货品效期创建失败==========={}", JSON.toJSONString(addParam), e);
                throw new BusinessException("该货主下已经创建货品管理");
            }
        }
    }


    //设置货主配置的实体仓编码
    private void enrichWarehouseCode(GoodsManagementBO goodsManagementBO) {
        OwnerResult ownerResult = remoteGoodsFacade.getOwnerByCode(goodsManagementBO.getOwnerCode());
        Assert.isTrue(ownerResult != null, "货主编码有误:" + goodsManagementBO.getOwnerCode());
        EntityWarehouseResult entityWarehouse = remoteConfigFacade.getEntityWarehouseFacade(ownerResult.getEntityWarehouseCode());
        Assert.isTrue(entityWarehouse != null, "货主绑定的实体仓编码有误:" + ownerResult.getEntityWarehouseCode());
        goodsManagementBO.setWarehouseCode(entityWarehouse.getWarehouseCode());
    }

    //设置货主配置的实体仓编码,必须是同一货主的
    private void enrichWarehouseCode(List<GoodsManagementBO> goodsManagementBOList) {
        GoodsManagementBO goodsManagementBO = goodsManagementBOList.stream().findFirst().get();
        OwnerResult ownerResult = remoteGoodsFacade.getOwnerByCode(goodsManagementBO.getOwnerCode());
        Assert.isTrue(ownerResult != null, "货主编码有误:" + goodsManagementBO.getOwnerCode());
        EntityWarehouseResult entityWarehouse = remoteConfigFacade.getEntityWarehouseFacade(ownerResult.getEntityWarehouseCode());
        Assert.isTrue(entityWarehouse != null, "货主绑定的实体仓编码有误:" + ownerResult.getEntityWarehouseCode());
        goodsManagementBOList.stream().forEach(m -> m.setWarehouseCode(entityWarehouse.getWarehouseCode()));

    }

    private void checkArgs(GoodsManagementAddParam addParam) {
        GoodsOpenPeriodValidity openPeriodValidity = addParam.getOpenPeriodValidity();
        //非效期，默认效期数据
        if (openPeriodValidity == GoodsOpenPeriodValidity.YES) {
            if (ConfigTagHelper.isOpenTag(addParam.getTag1(), Tag1Constant.IS_TAOTIAN)) {
                return;
            }
            if (addParam.getShelfLife() == null) {
                throw new BusinessException("开启批次管理后保质期不能为空");
            }
            //是否校验禁售、禁收、预警天数
            if (goodsManagementManager.goodsBatchCheckIgnoreUserId(addParam.getUserId())) {
                return;
            }
            if (addParam.getNoSellDate() == null) {
                throw new BusinessException("开启批次管理后禁售天数不能为空");
            }
            if (addParam.getNoCollectDate() == null) {
                throw new BusinessException("开启批次管理后禁收天数不能为空");
            }
            if (addParam.getWarningDate() == null) {
                throw new BusinessException("开启批次管理后预警天数不能为空");
            }
            if (!(addParam.getNoSellDate() <= addParam.getWarningDate() &&
                    addParam.getWarningDate() <= addParam.getNoCollectDate() &&
                    addParam.getNoCollectDate() <= addParam.getShelfLife())) {
                throw new BusinessException("保质期相关信息设置请符合：【禁售天数】<=【预警天数】<=【禁收天数】<=【保质期天数】");
            }
        } else {
            if ("WMS".equalsIgnoreCase(addParam.getFromSource())) {
                addParam.setWarningDate(0);
                addParam.setNoCollectDate(0);
                addParam.setNoSellDate(0);
                addParam.setShelfLife(0);
            } else {
                /*
                if (goodsManagementManager.getForcedBatchManagement()) {
                    addParam.setBatchManagement(GoodsBatchManagement.YES);
                    addParam.setWarningDate(1);
                    addParam.setNoCollectDate(1);
                    addParam.setNoSellDate(1);
                    addParam.setShelfLife(36500);
                } else {
                    addParam.setWarningDate(null);
                    addParam.setNoCollectDate(null);
                    addParam.setNoSellDate(null);
                    addParam.setShelfLife(null);
                }*/
            }
        }
    }

    /**
     * 批量插入
     *
     * @param goodsManagementAddParamList
     * @param syncRecordWarehouse
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addList(List<GoodsManagementAddParam> goodsManagementAddParamList, boolean syncRecordWarehouse) {
        List<GoodsManagementBO> list = BeanUtils.copyProperties(goodsManagementAddParamList, GoodsManagementBO.class);
        enrichWarehouseCode(list);
        goodsManagementManager.addList(list);
        // 添加云仓关联关系
        if (syncRecordWarehouse) {
            try {
                for (GoodsManagementAddParam addParam : goodsManagementAddParamList) {
                    GoodsRecordSearch search = new GoodsRecordSearch();
                    search.setGoodsCode(addParam.getGoodsCode());
                    List<GoodsRecordBO> goodsRecordBOList = centralGoodsRecordManager.listGoodsRecordByParam(search);
                    for (GoodsRecordBO goodsRecordBO : goodsRecordBOList) {
                        centralGoodsRecordManager.buildRelation(goodsRecordBO, null);
                    }
                }
            } catch (Exception e) {
                log.error("货品同步云仓失败", e);
                throw new RuntimeException(e);
            }
        }
        return true;
    }

    /**
     * 根据主键id修改
     *
     * @param addParam
     * @return
     */
    @Override
    //@ShardingTransactionType(value = TransactionType.BASE)
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(GoodsManagementAddParam addParam) {
        return updateById(addParam, false, false);
    }

    /**
     * 根据主键id修改
     *
     * @param addParam
     * @return
     */
    @Override
//    @ShardingTransactionType(value = TransactionType.BASE)
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(GoodsManagementAddParam addParam, boolean jumpInOrderCheck, boolean checkInventory) {
        boolean updateOwnerCode = false;
        // 编辑时货品ID不能为空
        Assert.isTrue(addParam.getId() != null, "货品id有误:" + addParam.getId());
        // 校验是否修改批次管理
        GoodsManagementBO goodsManagementBO = goodsManagementManager.getById(addParam.getId());
        String beforeJson = JSON.toJSONString(goodsManagementBO);
        Assert.isTrue(goodsManagementBO != null, "货品id有误:" + addParam.getId());
        SourceUtils.checkEditSource(goodsManagementBO.getSource());
        // 保质期发生变更，则要校验
        if (!addParam.getShelfLife().equals(goodsManagementBO.getShelfLife())) {
            checkBeforeUpdate(goodsManagementBO, 1, jumpInOrderCheck, checkInventory);
        }
        //货主有变化检查
        if (!addParam.getOwnerCode().equals(goodsManagementBO.getOwnerCode())) {
            GoodsManagementBO goodsManagementBO1 = goodsManagementManager.queryGoodsByGoodsCode(goodsManagementBO.getUserId(), goodsManagementBO.getGoodsCode(), addParam.getOwnerCode());
            Assert.isTrue(goodsManagementBO1 == null, "货主下已该货品");
            updateOwnerCode = true;
            enrichWarehouseCode(goodsManagementBO);
        }

        // 校验批次属性
        checkArgs(addParam);
        GoodsManagementBO updateObject = BeanUtils.copyProperties(addParam, GoodsManagementBO.class);
        String afterJson = JSON.toJSONString(updateObject);
        updateObject.setFeature(goodsManagementBO.getFeature());
        if (goodsManagementManager.updateById(updateObject)) {
            // 添加操作日志
            remoteGoodsFacade.addOperationLog(addParam.getOperationLogParam(), addParam.getGoodsCode(),
                    beforeJson, afterJson);

            // 获取所有下发记录重新下发,修改货主，
            if (updateOwnerCode) {
                addIssueGoodsRecord(SimpleUserHelper.getUserId(), updateObject.getOwnerCode(), updateObject.getGoodsCode());
            }
            return remoteGoodsFacade.asyncIssueAgain(updateObject.getGoodsCode(), updateObject.getOwnerCode());
        }
        return false;
    }

    private void checkBeforeUpdate(GoodsManagementBO goodsManagementBO, int type, boolean jumpInOrderCheck, boolean checkInventory) {
        // 校验库存
        if (checkInventory) {
            Pair<Boolean, String> canModifyFlag = remoteGoodsFacade.getCanModifyFlagByInventory(goodsManagementBO.getGoodsCode(),
                    goodsManagementBO.getUserId(), goodsManagementBO.getOwnerCode(), type);
            if (!canModifyFlag.getLeft()) {
                throw new BusinessException(canModifyFlag.getRight());
            }
        }

        // 校验入库单
        if (!jumpInOrderCheck) {
            Pair<Boolean, String> canModifyFlag = remoteGoodsFacade.getCanModifyFlagByInOrder(goodsManagementBO.getGoodsCode(),
                    goodsManagementBO.getUserId(), goodsManagementBO.getOwnerCode(), type);
            if (!canModifyFlag.getLeft()) {
                throw new BusinessException(canModifyFlag.getRight());
            }
        }
    }


    /**
     * 根据主键id批量修改
     *
     * @param goodsManagementAddParamList
     * @return
     */
    @Override
    public boolean updateListById(List<GoodsManagementAddParam> goodsManagementAddParamList) {
        return goodsManagementManager.updateListById(BeanUtils.copyProperties(goodsManagementAddParamList, GoodsManagementBO.class));
    }

    /**
     * 根据条件修改
     *
     * @param goodsManagementQueryParam
     * @param goodsManagementAddParam
     * @return
     */
    @Override
    public boolean updateListByQueryParam(GoodsManagementQueryParam goodsManagementQueryParam, GoodsManagementAddParam goodsManagementAddParam) {
        return goodsManagementManager.updateListBySearch(BeanUtils.copyProperties(goodsManagementQueryParam, GoodsManagementSearch.class), BeanUtils.copyProperties(goodsManagementAddParam, GoodsManagementBO.class));
    }

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    @Override
    public boolean removeById(Serializable id) {
        return goodsManagementManager.removeById(id);
    }

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    @Override
    @Deprecated
    public boolean removeByIds(List<Long> idList) {
        return goodsManagementManager.removeByIds(idList);
    }

    /**
     * 根据条件删除
     *
     * @param goodsManagementQueryParam
     * @return
     */
    @Override
    public boolean removeByQueryParam(GoodsManagementQueryParam goodsManagementQueryParam) {
        GoodsManagementSearch searchParam = BeanUtils.copyProperties(goodsManagementQueryParam, GoodsManagementSearch.class);
        List<GoodsManagementBO> list = goodsManagementManager.listBySearch(searchParam);
        list.forEach(m -> {
            //货主下唯一，删除加上时间戳
            if (StringUtils.isNotEmpty(m.getExternalCode())) {
                m.setExternalCode("DEL-" + System.currentTimeMillis() + "-" + m.getExternalCode());
            }

        });
        boolean ret = goodsManagementManager.updateListById(list);
        if (ret) {
            return goodsManagementManager.removeBySearch(searchParam);
        }
        return false;
    }

    @Override
    public boolean enableList(List<String> ids) {
        boolean flag = true;
        for (String id : ids) {
            GoodsManagementBO goodsManagementBO = goodsManagementManager.getById(Long.valueOf(id));
            goodsManagementBO.setStatus(GoodsStatus.ENABLE);
            goodsManagementBO.setUpdateTime(System.currentTimeMillis());
            goodsManagementBO.setUpdateBy(SimpleUserHelper.getRealUserId());
            try {
                goodsManagementManager.updateById(goodsManagementBO);
            } catch (Exception e) {
                flag = false;
                log.error("[GoodsManagementFacadeImpl-enableList]===========货品启用异常========={}", goodsManagementBO.getGoodsCode(), e);
            }
        }
        return flag;
    }

    @Override
    public boolean enableByGoodsCode(String goodsCode) {
        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setGoodsCode(goodsCode);
        List<GoodsManagementBO> list = goodsManagementManager.listBySearch(goodsManagementSearch);
        list.stream().forEach(m -> {
            m.setStatus(GoodsStatus.ENABLE);
            m.setUpdateTime(System.currentTimeMillis());
            m.setUpdateBy(SimpleUserHelper.getRealUserId());
        });
        return goodsManagementManager.updateListById(list);

    }

    @Override
    public boolean forbiddenGoodsByGoodsCode(String goodsCode) {
        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setGoodsCode(goodsCode);
        List<GoodsManagementBO> list = goodsManagementManager.listBySearch(goodsManagementSearch);
        list.stream().forEach(m -> {
            m.setStatus(GoodsStatus.FORBIDDEN);
            m.setUpdateTime(System.currentTimeMillis());
            m.setUpdateBy(SimpleUserHelper.getRealUserId());

        });
        return goodsManagementManager.updateListById(list);

    }

    @Override
    public boolean forbiddenGoods(Long id) {
        GoodsManagementBO goodsManagementBO = goodsManagementManager.getById(Long.valueOf(id));
        goodsManagementBO.setStatus(GoodsStatus.FORBIDDEN);
        goodsManagementBO.setUpdateTime(System.currentTimeMillis());
        goodsManagementBO.setUpdateBy(SimpleUserHelper.getRealUserId());
        try {
            goodsManagementManager.updateById(goodsManagementBO);
        } catch (Exception e) {
            log.error("[GoodsManagementFacadeImpl-enableList]===========货品禁用异常========={}", goodsManagementBO.getGoodsCode(), e);
            return false;
        }
        return true;
    }

    @Override
    public void asyncImportTask(String urlAddr, String uid) {

    }

    @Override
    public boolean updatePledgeStatus(List<GoodsManagementAddParam> addParams) {
        List<String> skus = addParams.stream().map(GoodsManagementAddParam::getSku).collect(Collectors.toList());
        Map<String, PledgeStatus> collect = addParams.stream().collect(Collectors.toMap(GoodsManagementAddParam::getSku, GoodsManagementAddParam::getPledgeStatus));
        GoodsManagementQueryParam param = new GoodsManagementQueryParam();
        param.setUserId(addParams.get(0).getUserId());
        param.setOwnerCode(addParams.get(0).getOwnerCode());
        param.setSkus(skus);
        List<GoodsManagementResult> goodsManagementResults = listByQueryParam(param);
        for (GoodsManagementResult result : goodsManagementResults) {
            result.setPledgeStatus(collect.get(result.getSku()));
            goodsManagementManager.updateById(BeanUtils.copyProperties(result, GoodsManagementBO.class));
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updGoodsManagementInfoByWsm(GoodsManagementWmsUpdParam goodsManagementWmsUpdParam) {
        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setSku(goodsManagementWmsUpdParam.getSku());
        goodsManagementSearch.setOwnerCode(goodsManagementWmsUpdParam.getOwnerCode());
        GoodsManagementBO goodsManagementBO = goodsManagementManager.getBySearch(goodsManagementSearch);
        if (Objects.isNull(goodsManagementBO)) {
            throw new BusinessException("当前货品效期不存在 ownerCode:" + goodsManagementWmsUpdParam.getOwnerCode() + ",sku:" + goodsManagementWmsUpdParam.getSku());
        }
        goodsManagementBO.setLength(goodsManagementWmsUpdParam.getLength());
        goodsManagementBO.setWidth(goodsManagementWmsUpdParam.getWidth());
        goodsManagementBO.setHeight(goodsManagementWmsUpdParam.getHeight());
        goodsManagementBO.setVolume(goodsManagementWmsUpdParam.getVolume());
        goodsManagementBO.setGrossWeight(goodsManagementWmsUpdParam.getGrossWeight());
        goodsManagementBO.setNetWeight(goodsManagementWmsUpdParam.getNetWeight());
        if (!goodsManagementManager.updateById(goodsManagementBO)) {
            throw new BusinessException("货品效期更新失败，请稍后再试");
        }

        GoodsBO goodsBO = goodsManager.getByUserIdAndGoodsCode(goodsManagementBO.getUserId(), goodsManagementBO.getGoodsCode());
        if (Objects.isNull(goodsBO)) {
            throw new BusinessException("当前货品不存在 goodsCode:" + goodsManagementBO.getGoodsCode() + ",userId:" + goodsManagementBO.getUserId());
        }
        String goodsBOBefore = JSON.toJSONString(goodsBO);

        //货品基本信息的长宽高，体积，净重，毛重以wms最新传递的值为准，不管哪个货主
        goodsBO.setLength(goodsManagementWmsUpdParam.getLength());
        goodsBO.setWidth(goodsManagementWmsUpdParam.getWidth());
        goodsBO.setHeight(goodsManagementWmsUpdParam.getHeight());
        goodsBO.setVolume(goodsManagementWmsUpdParam.getVolume());
        goodsBO.setGrossWeight(goodsManagementWmsUpdParam.getGrossWeight());
        goodsBO.setNetWeight(goodsManagementWmsUpdParam.getNetWeight());
        goodsBO.setWmsUpdFlg(1);
        if (!goodsManager.updateGoodsById(goodsBO)) {
            throw new BusinessException("货品基础信息更新失败，请稍后再试");
        }

        //操作日志
        remoteGoodsFacade.addOperationLog(new GoodsOperationLogParam("WMS", UserTypeEnum.SYSTEM,
                        OperationType.OT_GOODS_SYSTEM_CALLBACK, "回传更新"), goodsBO.getGoodsCode(), goodsBOBefore,
                JSON.toJSONString(goodsBO));
        return true;
    }

    @Override
    public boolean updatePeriodValidity(Long operatorUserId, List<PeriodValidityUpdateParam> list) {
        if (CollectionUtils.isEmpty(list) || operatorUserId == null) {
            return true;
        }
        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        goodsManagementSearch.setIdList(list.stream().map(PeriodValidityUpdateParam::getId).collect(Collectors.toList()));
        List<GoodsManagementBO> goodsManagementBOList = goodsManagementManager.listBySearch(goodsManagementSearch);

        //更新货主货品
        List<GoodsManagementBO> updateList = BeanUtils.copyProperties(list, GoodsManagementBO.class);
        if (!goodsManagementManager.updateListById(updateList)) {
            return false;
        }

        //下发wms
        goodsIssueFacade.asyncIssueGoods(operatorUserId,
                goodsManagementBOList.stream().map(GoodsManagementBO::getOwnerCode).collect(Collectors.toList()),
                goodsManagementBOList.get(0).getGoodsCode());
        Long tenantId = SimpleTenantHelper.getTenantId();
        //操作日志
        CompletableFuture.runAsync(() -> {
            SimpleTenantHelper.setTenantId(tenantId);
            addOperationLogs(operatorUserId, goodsManagementBOList, updateList);
        });

        return true;
    }

    @Override
    public void syncMaterialCodeTask() {
        goodsManagementManager.syncMaterialCode();
    }

    /**
     * 添加操作日志
     *
     * @param operatorUserId
     * @param goodsManagementBOList
     * @param updateList
     */
    private void addOperationLogs(Long operatorUserId, List<GoodsManagementBO> goodsManagementBOList, List<GoodsManagementBO> updateList) {
        UserRpcResult userRpcResult = remoteGoodsFacade.findUser(operatorUserId);

        Map<Long, GoodsManagementBO> beforeMap = goodsManagementBOList.stream().collect(
                Collectors.toMap(GoodsManagementBO::getId, o -> o, (k1, k2) -> k1));

        //构造
        List<OperationLogsAddParam> logsAddParamList = updateList.stream().map(updateObject -> {
            GoodsManagementBO before = beforeMap.get(updateObject.getId());
            if (before == null) {
                return null;
            }

            return convert(operatorUserId, userRpcResult, before, updateObject);
        }).filter(Objects::nonNull).collect(Collectors.toList());

        //添加
        remoteGoodsFacade.addOperationLogs(logsAddParamList);
    }

    private OperationLogsAddParam convert(Long operatorUserId, UserRpcResult userRpcResult, GoodsManagementBO before, GoodsManagementBO after) {
        OperationLogsAddParam operationLogsAddParam = new OperationLogsAddParam();

        operationLogsAddParam.setUserId(operatorUserId);
        if (userRpcResult != null) {
            operationLogsAddParam.setUserName(userRpcResult.getUserName());
            operationLogsAddParam.setNickName(userRpcResult.getNickName());
            operationLogsAddParam.setEmail(userRpcResult.getEmail());
            operationLogsAddParam.setMobile(userRpcResult.getMobile());
            operationLogsAddParam.setUserType(UserTypeEnum.parse(userRpcResult.getType()));
        }

        operationLogsAddParam.setOperationType(OperationType.OT_GOODS_MANAGEMENT_EDIT);
        operationLogsAddParam.setBusinessNo(before.getGoodsCode());
        operationLogsAddParam.setReason("编辑货主货品效期信息");
        operationLogsAddParam.setOperationMessage(JSON.toJSONString(before));
        operationLogsAddParam.setRequestMessage(JSON.toJSONString(after));

        return operationLogsAddParam;
    }

    @Override
    public boolean asycnGoodsManagement(Long userId, String goodsCode) {
        return asycnGoodsManagement(userId, goodsCode, false, null);
    }

    @Override
    public boolean asyncGoodsManagementAndWms(Long userId, String goodsCode) {
        return asycnGoodsManagement(userId, goodsCode, false, true, null);
    }

    @Override
    public boolean syncGoodsManagementAndWms(Long userId, String goodsCode, String ownerCode, String externalCode) {
        log.info("[GoodsManagementFacadeImpl-syncGoodsManagementAndWms]同步开始 userId={}，goodsCode={},ownerCode={}", userId, goodsCode, ownerCode);
        Assert.isTrue(StringUtils.isNotBlank(ownerCode), "ownerCode 不能为空");
        // 2 获取用户下所有货主
        //  List<OwnerResult> owners = remoteGoodsFacade.getOwnersByUserId(userId);

        log.info("[GoodsManagementFacadeImpl-syncGoodsManagementAndWms]--1");
        // 3 获取货品信息
        GoodsResult goods = goodsFacade.getGoodsByParam(userId, goodsCode);
        goods.setId(null);
        // 4 组装货品管理信息 插入
        log.info("[GoodsManagementFacadeImpl-syncGoodsManagementAndWms]--2");
        GoodsManagementAddParam addParam = BeanUtils.copyProperties(goods, GoodsManagementAddParam.class);
        if (goods.getFeature() != null) {
            addParam.setFeature(goods.getFeature());
        }
        if (ConfigTagHelper.isOpenTag(goods.getTag1(), Tag1Constant.IS_TAOTIAN)) {
            addParam.setExternalCode(goods.getCargoCode());
        } else if (StringUtils.isNotBlank(externalCode)) {
            addParam.setExternalCode(externalCode);
        }
        addParam.setOwnerCode(ownerCode);
        // 5 只做新增  如果唯一主键冲突则跳过异常
        try {
            log.info("[GoodsManagementFacadeImpl-syncGoodsManagementAndWms -add]{}", JSON.toJSONString(addParam));
            add(addParam, false);
        } catch (Exception e) {
            log.error("[GoodsManagementFacadeImpl-syncGoodsManagementAndWms]=========货品管理同步失败=========ownerCode:" + addParam.getOwnerCode() + ",goodsCode:" + addParam.getGoodsCode(), e);
        }
        // 6 下发给wms
        try {
            goodsIssueFacade.asyncIssueGoods(userId, Arrays.asList(ownerCode), goodsCode);

        } catch (Exception e) {
            log.error("[GoodsManagementFacadeImpl-asycnGoodsManagement]=========下发给wms失败=========goodsCode:" + goodsCode, e);
        }
        return true;
    }

    @Override
    public boolean syncGoodsManagementAndWms(Long userId, String goodsCode, String ownerCode) {
        return this.syncGoodsManagementAndWms(userId, goodsCode, ownerCode, null);
    }

    @Override
    public boolean asycnGoodsManagement(Long userId, String goodsCode, boolean updateIfPresent, GoodsOperationLogParam logParam) {
        return asycnGoodsManagement(userId, goodsCode, updateIfPresent, false, logParam);
    }

    private boolean asycnGoodsManagement(Long userId, String goodsCode, boolean updateIfPresent, boolean forceSendWms,
                                         GoodsOperationLogParam logParam) {

        Long tenantId = SimpleTenantHelper.getTenantId();
        // 1 异步处理
        Executors.newSingleThreadExecutor().execute(new Runnable() {
            @Override
            public void run() {
                SimpleTenantHelper.setTenantId(tenantId);
                log.info("[GoodsManagementFacadeImpl-asycnGoodsManagement]同步开始 userId={}，goodsCode={}", userId, goodsCode);
                // 2 获取用户下所有货主
                List<OwnerResult> owners = remoteGoodsFacade.getOwnersByUserId(userId);
                log.info("[GoodsManagementFacadeImpl-asycnGoodsManagement] owners,{}", JSON.toJSONString(owners));
                log.info("[GoodsManagementFacadeImpl-asycnGoodsManagement] !CollectionUtils.isEmpty(owners)，{}", !CollectionUtils.isEmpty(owners));
                if (!CollectionUtils.isEmpty(owners)) {
                    log.info("[GoodsManagementFacadeImpl-asycnGoodsManagement]--1");
                    // 3 获取货品信息
                    GoodsResult goods = goodsFacade.getGoodsByParam(userId, goodsCode);
                    goods.setId(null);
                    // 4 组装货品管理信息 插入
                    for (OwnerResult owner : owners) {
                        log.info("[GoodsManagementFacadeImpl-asycnGoodsManagement]--2");
                        GoodsManagementAddParam addParam = BeanUtils.copyProperties(goods, GoodsManagementAddParam.class);
                        addParam.setOwnerCode(owner.getOwnerCode());
                        addParam.setOperationLogParam(logParam);
                        // addParam.setStatus(GoodsStatus.FORBIDDEN);//默认为禁用，wms修改为启用
                        // 5 只做新增  如果唯一主键冲突则跳过异常
                        try {
                            log.info("[GoodsManagementFacadeImpl-asycnGoodsManagement -add]{}", JSON.toJSONString(addParam));
                            add(addParam, updateIfPresent);
                        } catch (Exception e) {
                            log.error("[GoodsManagementFacadeImpl-asycnGoodsManagement]=========货品管理同步失败=========ownerCode:" + addParam.getOwnerCode() + ",goodsCode:" + addParam.getGoodsCode(), e);
                        }
                    }
                    // 6 下发给wms
                    try {
                        if (updateIfPresent) {
                            remoteGoodsFacade.issueAgain(goodsCode);
                        }

                        if (forceSendWms) {
                            goodsIssueFacade.asyncIssueGoods(userId, goodsCode);
                        }
                    } catch (Exception e) {
                        log.error("[GoodsManagementFacadeImpl-asycnGoodsManagement]=========下发给wms失败=========goodsCode:" + goodsCode, e);
                    }
                }
            }
        });
        return true;
    }

    @Override
    public void before(GoodsManagementQueryParam searchParam) {

    }

    @Override
    public List<GoodsManagementExportExcel> getDataList(GoodsManagementQueryParam searchParam) {
        ListVO<GoodsManagementResult> listVO = pageListByQueryParam(searchParam);
        // 设置总页数
        this.setTotalPage(listVO.getPage().getTotalPage());
        List<GoodsManagementResult> dataList = listVO.getDataList();
        if (!CollectionUtils.isEmpty(dataList)) {
            List<GoodsManagementExportExcel> firstList = Lists.newArrayList();
            for (GoodsManagementResult goodsManagementResult : dataList) {
                try {
                    GoodsManagementExportExcel goodsExportExcel = BeanUtils.copyProperties(goodsManagementResult, GoodsManagementExportExcel.class);
                    FastDateFormat format = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");
                    goodsExportExcel.setCreateTimeFormat(format.format(goodsManagementResult.getCreateTime()));
                    goodsExportExcel.setUpdateTimeFormat(format.format(goodsManagementResult.getUpdateTime()));
                    /*
                    if (GoodsBatchManagement.YES.equals(goodsManagementResult.getBatchManagement())) {
                        goodsExportExcel.setBatchManagementName("启用");
                    } else {
                        goodsExportExcel.setBatchManagementName("不启用");
                    }
                    if (GoodsOpenPeriodValidity.YES.equals(goodsManagementResult.getOpenPeriodValidity())) {
                        goodsExportExcel.setBatchManagementName("是");
                    } else {
                        goodsExportExcel.setBatchManagementName("否");
                    }
                    */
                    firstList.add(goodsExportExcel);
                } catch (Exception e) {
                    log.error("[GoodsManagementFacadeImpl-getDataList]===========货品导出数据异常========={}", JSON.toJSONString(goodsManagementResult), e);
                }
            }
            return firstList;
        } else {
            return null;
        }
    }

    @Override
    public List<GoodsManagementResult> listGoodsManagementByQuery(Long userId, List<String> list, String ownerCode, Integer type) {
        GoodsQueryParam goodsParam = new GoodsQueryParam();
        goodsParam.setUserId(userId);
        if (Objects.equals(1, type)) {
            goodsParam.setGoodsCodes(list);
        } else if (Objects.equals(2, type)) {
            goodsParam.setSkus(list);
        } else if (Objects.equals(3, type)) {
            // 外部货品id
        } else {
            throw new BusinessException("货品查询type传递有误");
        }
        // 不传货主 或 是虚拟货主  直接返回货品基本信息
        if (StringUtils.isBlank(ownerCode) || AresContext.VIRTUAL_OWNER.equals(ownerCode)) {
            List<GoodsManagementResult> managementResultList = BeanUtils.copyProperties(goodsFacade.listGoodsByParam(goodsParam), GoodsManagementResult.class);
            if (AresContext.VIRTUAL_OWNER.equals(ownerCode)) {
                managementResultList.stream().peek(goodsManagementResult -> {
                    goodsManagementResult.setBatchManagement(GoodsBatchManagement.NO);
                    goodsManagementResult.setOwnerCode(AresContext.VIRTUAL_OWNER);
                    goodsManagementResult.setPledgeStatus(PledgeStatus.NO);
                }).collect(Collectors.toList());
            }
            return managementResultList;
        } else {
            //查询批次信息
            GoodsManagementQueryParam param = new GoodsManagementQueryParam();
            param.setOwnerCode(ownerCode);
            param.setUserId(userId);
            if (Objects.equals(1, type)) {
                param.setGoodsCodes(list);
            } else if (Objects.equals(2, type)) {
                param.setSkus(list);
            } else if (Objects.equals(3, type)) {
                param.setExternalCodeList(list);
            }
            List<GoodsManagementResult> goodsManagementResults = this.listByQueryParam(param);
            if (CollectionUtil.isEmpty(goodsManagementResults)) {
                return Arrays.asList();
            }
            if (Objects.equals(3, type)) {
                List<String> goodsList = goodsManagementResults.stream().map(GoodsManagementResult::getGoodsCode).collect(Collectors.toList());
                goodsParam.setGoodsCodes(goodsList);
            }
            //查询货品基本信息
            List<GoodsResult> goodsResultList = goodsFacade.listGoodsByParam(goodsParam);
            Map<String, GoodsResult> goodsResultMap = goodsResultList.stream().collect(Collectors.toMap(GoodsResult::getGoodsCode, Function.identity()));

            List<GoodsManagementResult> finalGoodsManagementResults = new ArrayList<>();
            for (GoodsManagementResult result : goodsManagementResults) {
                GoodsResult goodsResult = goodsResultMap.get(result.getGoodsCode());
                GoodsManagementResult goodsManagementResult = BeanUtils.copyProperties(goodsResult, GoodsManagementResult.class);
                goodsManagementResult.setShelfLife(result.getShelfLife());
                goodsManagementResult.setNoCollectDate(result.getNoCollectDate());
                goodsManagementResult.setNoSellDate(result.getNoSellDate());
                goodsManagementResult.setWarningDate(result.getWarningDate());
                goodsManagementResult.setBatchManagement(result.getBatchManagement());
                goodsManagementResult.setOwnerCode(result.getOwnerCode());
                goodsManagementResult.setPledgeStatus(result.getPledgeStatus());
                goodsManagementResult.setExternalCode(result.getExternalCode());
                goodsManagementResult.setExternalSku(result.getExternalSku());
                finalGoodsManagementResults.add(goodsManagementResult);
            }
            return finalGoodsManagementResults;
        }
    }

    @Override
    public boolean addExternalCode(List<GoodsManagementAddParam> list) {
        if (CollectionUtil.isEmpty(list)) {
            return Boolean.FALSE;
        }
        GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
        list.forEach(add -> {
            goodsManagementSearch.setUserId(add.getUserId());
            goodsManagementSearch.setOwnerCode(add.getOwnerCode());
            goodsManagementSearch.setGoodsCode(add.getGoodsCode());
            GoodsManagementBO goodsManagementBO = goodsManagementManager.getBySearch(goodsManagementSearch);

            goodsManagementBO.setExternalCode(add.getExternalCode());
            goodsManagementManager.updateById(goodsManagementBO);
        });
        return Boolean.TRUE;
    }

    @Override
    public boolean updateExternalCode(GoodsManagementAddParam addParam) {
        if (StringUtils.isBlank(addParam.getOwnerCode())) {
            throw new BusinessException("货主不能为空!");
        }
        if (StringUtils.isBlank(addParam.getGoodsCode())) {
            throw new BusinessException("货品编码不能为空!");
        }
        try {
            GoodsManagementSearch goodsManagementSearch = new GoodsManagementSearch();
            goodsManagementSearch.setOwnerCode(addParam.getOwnerCode());
            goodsManagementSearch.setGoodsCode(addParam.getGoodsCode());
            GoodsManagementBO goodsManagementBO = goodsManagementManager.getBySearch(goodsManagementSearch);
            Assert.isTrue(goodsManagementBO != null, "货主效期管理未关联货品,【请联系客服对货主进行一键同步货品】 货主编码={},货品id={}", addParam.getOwnerCode(), addParam.getGoodsCode());
            GoodsManagementBO managementBO = new GoodsManagementBO();
            managementBO.setId(goodsManagementBO.getId());

            boolean updated = Boolean.FALSE;
            if (StringUtils.isNotBlank(addParam.getExternalCode())) {
                managementBO.setExternalCode(addParam.getExternalCode());
                updated = goodsManagementManager.updateExternalCodeById(goodsManagementBO.getId(), addParam.getExternalCode());
            }
            if (StringUtils.isNotBlank(addParam.getExternalSku())) {
                managementBO.setExternalSku(addParam.getExternalSku());
                updated = goodsManagementManager.updateExternalSkuById(goodsManagementBO.getId(), addParam.getExternalSku());
            }

            //操作日志
            if (updated && addParam.getOperationLogParam() != null) {
                remoteGoodsFacade.addOperationLog(addParam.getOperationLogParam(), goodsManagementBO.getGoodsCode(),
                        JSON.toJSONString(goodsManagementBO), JSON.toJSONString(managementBO));
            }
            return updated;
        } catch (Exception e) {
            if (e instanceof DuplicateKeyException) {
                throw new BusinessException("更新货品外部ID失败!,可能是重复的外部货品。");
            }
            if (e instanceof BusinessException) {
                throw e;
            }
            log.error("[GoodsManagementFacadeImpl-updateExternalCode]===========更新货品外部ID失败==========ex:", e);
            throw new BusinessException("更新货品外部ID失败!");
        }
    }

    @Override
    public void syncGoodsToReturnOwner(List<String> ownerCodes) {

        if (CollectionUtil.isEmpty(ownerCodes)) {
            OwnerQueryParam queryParam = new OwnerQueryParam();
            queryParam.setOpenStatus(1);
            List<OwnerResult> ownerResults = ownerFacade.listOwnerByParam(queryParam);
            if (CollectionUtil.isEmpty(ownerResults)) {
                return;
            }
            log.info("[货品同步逆向货主job] 数量: {}", ownerResults.size());
            int count = 0;
            for (OwnerResult result : ownerResults) {
                count++;
                log.info("[货品同步逆向货主job] 开始: {}, 计数: {}", result.getOwnerCode(), count);
                goodsManagementManager.syncReturnOwnerGoods(result.getOwnerCode(), null, false, false);
                log.info("[货品同步逆向货主job] 结束: {}, 计数: {}", result.getOwnerCode(), count);
            }
        } else {
            ownerCodes.forEach(ownerCode -> {
                log.info("[货品同步逆向货主job] 指定货主 开始: {}", ownerCode);
                goodsManagementManager.syncReturnOwnerGoods(ownerCode, null, false, false);
                log.info("[货品同步逆向货主job] 指定货主 结束: {}", ownerCode);
            });
        }
    }
}
