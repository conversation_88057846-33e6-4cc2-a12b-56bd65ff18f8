package com.danding.business.server.ares.ewem.manager;

import com.danding.business.core.ares.ewem.service.IEwemApplyService;
import com.danding.business.server.ares.ewem.manager.helper.EwemApplyManagerHelper;
import com.danding.business.server.ares.ewem.BO.EwemApplyBO;
import com.danding.business.core.ares.ewem.search.EwemApplySearch;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.business.core.ares.ewem.entity.EwemApply;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 码申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Component
public class EwemApplyManager {

    @Autowired
    private IEwemApplyService ewemApplyService;

    @Autowired
    private EwemApplyManagerHelper ewemApplyManagerHelper;

    /**
     * id查询单个
     *
     * @param id
     * @return
     */
    public EwemApplyBO getById(Serializable id) {
        return BeanUtils.copyProperties(ewemApplyService.selectById(id), EwemApplyBO.class);
    }

    /**
     * 条件查询单个
     *
     * @param ewemApplySearch
     * @return
     */
    public EwemApplyBO getBySearch(EwemApplySearch ewemApplySearch) {
        EwemApply ewemApply = ewemApplyService.selectBySearch(ewemApplySearch);
        return BeanUtils.copyProperties(ewemApply, EwemApplyBO.class);
    }

    /**
     * 列表查询
     *
     * @param ewemApplySearch
     * @return
     */
    public List<EwemApplyBO> listBySearch(EwemApplySearch ewemApplySearch) {
        List<EwemApply> ewemApplyList = ewemApplyService.selectListBySearch(ewemApplySearch);
        return BeanUtils.copyProperties(ewemApplyList, EwemApplyBO.class);
    }

    /**
     * 分页查询
     *
     * @param ewemApplySearch
     * @return
     */
    @PageSelect
    public ListVO<EwemApplyBO> pageListBySearch(EwemApplySearch ewemApplySearch) {
        ListVO<EwemApplyBO> ewemApplyBOListVO = new ListVO<>();
        List<EwemApply> ewemApplyList = ewemApplyService.selectListBySearch(ewemApplySearch);
        return ListVO.build(ewemApplyBOListVO.getPage(), BeanUtils.copyProperties(ewemApplyList, EwemApplyBO.class));
    }

    /**
     * 功能描述:  插入
     */
    public boolean add(EwemApplyBO ewemApplyBO) {
        return ewemApplyService.insert(BeanUtils.copyProperties(ewemApplyBO, EwemApply.class));
    }

    /**
     * 功能描述:  批量插入
     */
    public boolean addList(List<EwemApplyBO> ewemApplyBOList) {
        return ewemApplyService.insertList(BeanUtils.copyProperties(ewemApplyBOList, EwemApply.class));
    }

    /**
     * 功能描述:  根据主键id修改
     */
    public boolean updateById(EwemApplyBO ewemApplyBO) {
        return ewemApplyService.updateById(BeanUtils.copyProperties(ewemApplyBO, EwemApply.class));
    }

    /**
     * 功能描述:  根据主键id批量修改
     */
    public boolean updateListById(List<EwemApplyBO> ewemApplyBOList) {
        return ewemApplyService.updateListById(BeanUtils.copyProperties(ewemApplyBOList, EwemApply.class));
    }

    /**
     * 功能描述:  根据条件修改
     */
    public boolean updateListBySearch(EwemApplySearch ewemApplySearch, EwemApplyBO ewemApplyBO) {
        return ewemApplyService.updateListBySearch(ewemApplySearch, BeanUtils.copyProperties(ewemApplyBO, EwemApply.class));
    }

    /**
     * 功能描述:  根据主键id删除
     */
    public boolean removeById(Serializable id) {
        return ewemApplyService.deleteById(id);
    }

    /**
     * 功能描述:  根据主键id批量删除
     */
    public boolean removeByIds(List<Long> idList) {
        return ewemApplyService.deleteByIds(idList);
    }

    /**
     * 功能描述:  根据条件删除
     */
    public boolean removeBySearch(EwemApplySearch ewemApplySearch) {
        return ewemApplyService.deleteBySearch(ewemApplySearch);
    }
}
