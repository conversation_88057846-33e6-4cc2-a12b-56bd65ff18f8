package com.danding.business.server.ares.ewem.manager;

import com.danding.business.core.ares.ewem.service.IEwemBatchService;
import com.danding.business.server.ares.ewem.manager.helper.EwemBatchManagerHelper;
import com.danding.business.server.ares.ewem.BO.EwemBatchBO;
import com.danding.business.core.ares.ewem.search.EwemBatchSearch;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.business.core.ares.ewem.entity.EwemBatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 批次表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Component
public class EwemBatchManager {

    @Autowired
    private IEwemBatchService ewemBatchService;

    @Autowired
    private EwemBatchManagerHelper ewemBatchManagerHelper;

    /**
     * id查询单个
     *
     * @param id
     * @return
     */
    public EwemBatchBO getById(Serializable id) {
        return BeanUtils.copyProperties(ewemBatchService.selectById(id), EwemBatchBO.class);
    }

    /**
     * 条件查询单个
     *
     * @param ewemBatchSearch
     * @return
     */
    public EwemBatchBO getBySearch(EwemBatchSearch ewemBatchSearch) {
        EwemBatch ewemBatch = ewemBatchService.selectBySearch(ewemBatchSearch);
        return BeanUtils.copyProperties(ewemBatch, EwemBatchBO.class);
    }

    /**
     * 列表查询
     *
     * @param ewemBatchSearch
     * @return
     */
    public List<EwemBatchBO> listBySearch(EwemBatchSearch ewemBatchSearch) {
        List<EwemBatch> ewemBatchList = ewemBatchService.selectListBySearch(ewemBatchSearch);
        return BeanUtils.copyProperties(ewemBatchList, EwemBatchBO.class);
    }

    /**
     * 分页查询
     *
     * @param ewemBatchSearch
     * @return
     */
    @PageSelect
    public ListVO<EwemBatchBO> pageListBySearch(EwemBatchSearch ewemBatchSearch) {
        ListVO<EwemBatchBO> ewemBatchBOListVO = new ListVO<>();
        List<EwemBatch> ewemBatchList = ewemBatchService.selectListBySearch(ewemBatchSearch);
        return ListVO.build(ewemBatchBOListVO.getPage(), BeanUtils.copyProperties(ewemBatchList, EwemBatchBO.class));
    }

    /**
     * 功能描述:  插入
     */
    public boolean add(EwemBatchBO ewemBatchBO) {
        return ewemBatchService.insert(BeanUtils.copyProperties(ewemBatchBO, EwemBatch.class));
    }

    /**
     * 功能描述:  批量插入
     */
    public boolean addList(List<EwemBatchBO> ewemBatchBOList) {
        return ewemBatchService.insertList(BeanUtils.copyProperties(ewemBatchBOList, EwemBatch.class));
    }

    /**
     * 功能描述:  根据主键id修改
     */
    public boolean updateById(EwemBatchBO ewemBatchBO) {
        return ewemBatchService.updateById(BeanUtils.copyProperties(ewemBatchBO, EwemBatch.class));
    }

    /**
     * 功能描述:  根据主键id批量修改
     */
    public boolean updateListById(List<EwemBatchBO> ewemBatchBOList) {
        return ewemBatchService.updateListById(BeanUtils.copyProperties(ewemBatchBOList, EwemBatch.class));
    }

    /**
     * 功能描述:  根据条件修改
     */
    public boolean updateListBySearch(EwemBatchSearch ewemBatchSearch, EwemBatchBO ewemBatchBO) {
        return ewemBatchService.updateListBySearch(ewemBatchSearch, BeanUtils.copyProperties(ewemBatchBO, EwemBatch.class));
    }

    /**
     * 功能描述:  根据主键id删除
     */
    public boolean removeById(Serializable id) {
        return ewemBatchService.deleteById(id);
    }

    /**
     * 功能描述:  根据主键id批量删除
     */
    public boolean removeByIds(List<Long> idList) {
        return ewemBatchService.deleteByIds(idList);
    }

    /**
     * 功能描述:  根据条件删除
     */
    public boolean removeBySearch(EwemBatchSearch ewemBatchSearch) {
        return ewemBatchService.deleteBySearch(ewemBatchSearch);
    }
}
