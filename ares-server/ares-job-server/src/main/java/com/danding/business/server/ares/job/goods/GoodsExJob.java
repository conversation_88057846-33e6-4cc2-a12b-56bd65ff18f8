package com.danding.business.server.ares.job.goods;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.danding.business.client.ares.Issue.facade.IGoodsIssueFacade;
import com.danding.business.client.ares.Issue.param.GoodsIssueAddParam;
import com.danding.business.client.ares.goods.facade.IGoodsFacade;
import com.danding.business.client.ares.goods.param.GoodsQueryParam;
import com.danding.business.client.ares.goods.result.GoodsResult;
import com.danding.business.client.ares.goodsManagement.facade.IGoodsManagementFacade;
import com.danding.business.client.ares.goodsManagement.param.GoodsManagementQueryParam;
import com.danding.business.client.ares.goodsManagement.result.GoodsManagementResult;
import com.danding.business.client.ares.inventory.result.GoodsInventoryResult;
import com.danding.business.client.rpc.goods.center.facade.IGoodsManagementRpcReadFacade;
import com.danding.business.client.rpc.goods.center.param.GoodsRpcQueryParam;
import com.danding.business.client.rpc.goods.center.result.GoodsManagementRpcResult;
import com.danding.business.common.ares.enums.common.ActionType;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class GoodsExJob   {

    @DubboReference
    private IGoodsFacade goodsFacade;
    @DubboReference
    private IGoodsManagementFacade goodsManagementFacade;
    @DubboReference
    private IGoodsManagementRpcReadFacade goodsManagementRpcReadFacade;
    @DubboReference
    private IGoodsIssueFacade goodsIssueFacade;

    /**
     * 货品字节商品编码重复告警
     *TODO song 告警的业务的租户化改造。。。。。11-07
     * @param params
     * @return
     */
    @XxlJob(value = "goodCargoCodeWarningJob", enableTenant = true)
    public ReturnT<String> goodCargoCodeWarningJob(String params) {
        XxlJobLogger.log(params + "-goodCargoCodeWarningJob 货品字节商品编码重复告警开始。");
        Long tenantId = SimpleTenantHelper.getTenantId();
        CompletableFuture.runAsync(() -> {
            SimpleTenantHelper.setTenantId(tenantId);
            goodsFacade.goodCargoCodeRepeatWarning();});
        XxlJobLogger.log(params + "-goodCargoCodeWarningJob 货品字节商品编码重复告警结束。");
        return ReturnT.SUCCESS;
    }
    @XxlJob(value = "syncMaterialCodeJob", enableTenant = true)
    public ReturnT<String> syncMaterialCodeJob(String params) {
        XxlJobLogger.log(params + "-syncMaterialCodeJob 同步备案料号。");
        Long tenantId = SimpleTenantHelper.getTenantId();
        CompletableFuture.runAsync(() -> {
            SimpleTenantHelper.setTenantId(tenantId);
            goodsManagementFacade.syncMaterialCodeTask();});
        XxlJobLogger.log(params + "-syncMaterialCodeJob 同步备案料号结束。");
        return ReturnT.SUCCESS;
    }
    @Deprecated
    public ReturnT<String> goodDataFix(String params) {
        Map<String, GoodsInventoryResult> goodsInventoryWarningMap = new HashMap<>();
        int currentPage = 0;
        boolean hasMore = true;
        while (hasMore) {
            GoodsQueryParam queryParam = new GoodsQueryParam();
            queryParam.setCurrentPage(++currentPage);
            queryParam.setPageSize(1000);
            ListVO<GoodsResult>  goodsResultListVO = goodsFacade.pageListGoodsByParamV2(queryParam);

            int totalPages = goodsResultListVO.getPage().getTotalPage();
            if (!CollectionUtils.isEmpty(goodsResultListVO.getDataList())) {
                if (currentPage >= totalPages) {
                    // 已经到达最后一页，没有更多了
                    hasMore = false;
                }
                List<GoodsResult>  list =goodsResultListVO.getDataList().stream().filter(goods-> StringUtils.isEmpty(goods.getCargoCode())).collect(Collectors.toList());
                List<String> goodsIdsList =list.stream().map(GoodsResult::getGoodsCode).collect(Collectors.toList());


                GoodsManagementQueryParam goodsManagementQueryParam= new GoodsManagementQueryParam();
                goodsManagementQueryParam.setGoodsCodes(goodsIdsList);
                List<GoodsManagementResult> managementResultList= goodsManagementFacade.listByQueryParam(goodsManagementQueryParam);

            } else {
                hasMore = false;
            }
        }
        GoodsQueryParam queryParam = new GoodsQueryParam();

        goodsFacade.listGoodsByParam(queryParam);
        return null;
    }
    @XxlJob(value = "goodsPushWmsJob", enableTenant = true)
    public ReturnT<String> goodsPushWmsJob(String params) {
        JSONObject jsonObject = JSON.parseObject(params);
        Long userId = Long.valueOf(jsonObject.getLong("userId"));
        String warehouseCode = jsonObject.getString("warehouseCode");
        String entityWarehouseCode = jsonObject.getString("entityWarehouseCode");
        String ownerCode = jsonObject.getString("ownerCode");
        GoodsRpcQueryParam queryParam = new GoodsRpcQueryParam();
        queryParam.setUserId(userId);
        queryParam.setWarehouseCode(warehouseCode);
        if (ownerCode != null) {
            queryParam.setOwnerCode(ownerCode);
        }
        List<GoodsManagementRpcResult> results = goodsManagementRpcReadFacade.ListRpcGoodsByParam(queryParam);
        List<GoodsIssueAddParam> addForms = results.stream().map(m -> {
            GoodsIssueAddParam addForm = new GoodsIssueAddParam();
            addForm.setGoodsCode(m.getGoodsCode());
            addForm.setOwnerCode(m.getOwnerCode());
            addForm.setUserId(m.getUserId());
            addForm.setEntityWarehouseCode(entityWarehouseCode);
            return addForm;
        }).collect(Collectors.toList());

        addForms.forEach(a -> {
            a.setActionType(ActionType.MODIFY);
            a.setOperator(-1L);
            a.setUserName("system");
        });
        boolean b = goodsIssueFacade.issueGoodsList(addForms);
       return null;
    }


    @XxlJob(value = "syncGoodsToReturnOwnerJob", enableTenant = true)
    public ReturnT<String> syncGoodsToReturnOwnerJob(String params) {
        JSONArray jsonArray = JSON.parseArray(params);
        List<String> ownerCodes = jsonArray.toJavaList(String.class);
        XxlJobLogger.log( "syncGoodsToReturnOwnerJob 同步货品到退货货主开始。货主：{}", params);
        Long tenantId = SimpleTenantHelper.getTenantId();
        CompletableFuture.runAsync(() -> {
            SimpleTenantHelper.setTenantId(tenantId);
            goodsManagementFacade.syncGoodsToReturnOwner(ownerCodes);
        });
        XxlJobLogger.log( "syncGoodsToReturnOwnerJob 同步货品到退货货主结束。货主：{}", params);
        return ReturnT.SUCCESS;
    }
}
