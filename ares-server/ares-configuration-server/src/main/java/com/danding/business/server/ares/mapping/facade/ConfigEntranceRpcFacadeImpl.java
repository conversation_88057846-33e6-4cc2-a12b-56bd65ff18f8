package com.danding.business.server.ares.mapping.facade;

import com.danding.business.client.ares.mapping.facade.MappingFacade;
import com.danding.business.client.ares.mapping.param.MappingParam;
import com.danding.business.client.ares.mapping.result.MappingResult;
import com.danding.business.rpc.client.ares.configuration.facade.ConfigEntranceRpcFacade;
import com.danding.business.rpc.client.ares.configuration.param.in.EntranceRpcParam;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.soul.client.common.result.RpcResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 2021/1/5 下午1:34
 */
@DubboService
public class ConfigEntranceRpcFacadeImpl implements ConfigEntranceRpcFacade {

    @DubboReference
    private MappingFacade mappingFacade;

    @Override
    public RpcResult inExecute(EntranceRpcParam entranceRpcParam) {
        entranceRpcParam.setStartTime(System.currentTimeMillis());
        MappingParam mappingParam = BeanUtils.copyProperties(entranceRpcParam, MappingParam.class);
        mappingParam.setDataType(entranceRpcParam.getDateType());
        MappingResult mappingResult = mappingFacade.inExecute(mappingParam);
        if(mappingResult.isSuccess()){
            return RpcResult.success(mappingResult.getData());
        }
        return RpcResult.error(mappingResult.getCode(), mappingResult.getMessage(), mappingResult.getData());
    }

    @Override
    public RpcResult outExecute(EntranceRpcParam entranceRpcParam) {
        entranceRpcParam.setStartTime(System.currentTimeMillis());
        MappingParam mappingParam = BeanUtils.copyProperties(entranceRpcParam, MappingParam.class);
        mappingParam.setDataType(entranceRpcParam.getDateType());
        MappingResult mappingResult = mappingFacade.outExecute(mappingParam);
        if (mappingResult.isSuccess()) {
            return RpcResult.success(mappingResult.getData());
        }
        return RpcResult.error(mappingResult.getCode(), mappingResult.getMessage(), mappingResult.getData());
    }

//    @Override
//    public RpcResult conversionExecute(EntranceRpcParam entranceRpcParam) {
//        entranceRpcParam.setStartTime(System.currentTimeMillis());
//        MappingParam mappingParam = BeanUtils.copyProperties(entranceRpcParam, MappingParam.class);
//        mappingParam.setDataType(entranceRpcParam.getDateType());
//        MappingResult mappingResult = mappingFacade.conversionExecute(mappingParam);
//        if (mappingResult.isSuccess()) {
//            return RpcResult.success(mappingResult.getData());
//        }
//        return RpcResult.error(mappingResult.getCode(), mappingResult.getMessage());
//    }

}
