package com.danding.business.server.ares.request.extension.impl;


import com.danding.business.server.ares.request.common.CommonUtil;
import com.danding.business.server.ares.request.context.Context;
import com.danding.business.server.ares.request.extension.SignChecker;

public class Md5<PERSON>ign<PERSON>heck implements SignChecker {

    @Override
    public Boolean check(Context context) {
        Object param = context.getParam(CommonUtil.getSignKeyAlias(context));
        if (param == null) {
            return false;
        }
        String requestSign = String.valueOf(param);
        context.getSignSourceBuilder().build(context);
        context.getSigner().sign(context);
        String sign = (String) context.getParam(CommonUtil.getSignKeyAlias(context));
        return requestSign.equals(sign);
    }
}
