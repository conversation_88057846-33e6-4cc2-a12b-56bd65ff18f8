package com.danding.business.server.ares.mapping.biz;

import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.configuration.facade.IConfigurationLogFacade;
import com.danding.business.client.ares.configuration.param.ConfigurationLogAddParam;
import com.danding.business.common.ares.utils.LogUtils;
import com.danding.business.server.ares.mapping.BO.HandleBO;
import com.danding.business.server.ares.mapping.BO.MappingBO;
import com.danding.business.server.ares.mapping.BO.ResultBO;
import com.danding.business.server.ares.mapping.handle.HandleFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 回传业务
 */
@Slf4j
public abstract class CallBackHandleBizHandler {

    private IConfigurationLogFacade configurationLogFacade;

    /**
     * 构建参数
     * @param mappingBO
     */
    protected abstract HandleBO buildParams(MappingBO mappingBO);

    /**
     * 执行操作
     *
     * @param mappingBO
     * @return
     */
    public final ResultBO execute(HandleFactory handleFactory, IConfigurationLogFacade configurationLogFacade, MappingBO mappingBO) {
        this.configurationLogFacade = configurationLogFacade;
        Long requestTime = System.currentTimeMillis();
        String mappingData = null;
        String targetMethodCode = mappingBO.getTargetMethodCode();
        try {
            String sourceData = mappingBO.getSourceData();
            if (StringUtils.isBlank(sourceData)) {
                return ResultBO.error("请求参数不能为空!", -1);
            }
            HandleBO handleBO = buildParams(mappingBO);
            mappingData = handleBO.getJsonMapping();
            ResultBO resultBO = handleFactory.handleWork(handleBO);
            log.info("[CallBackAbstractBizHandler-execute]===============原始返回参数===============:" + JSON.toJSONString(resultBO));
            parsingResponse(resultBO, handleBO);
            //日志
            if (StringUtils.isBlank(mappingBO.getBusinessNo())) {
                String object = resultBO.getData() + "";
                if (StringUtils.isNotBlank(object) && object.length() > 64) {
                    mappingBO.setBusinessNo(StringUtils.substring(object, 0, 64));
                } else {
                    mappingBO.setBusinessNo(object);
                }
            }
            targetMethodCode = StringUtils.isNotBlank(mappingBO.getTargetMethodCode()) ? mappingBO.getTargetMethodCode() : "MC0_" + mappingBO.getMethodType();
            saveLog(mappingBO, ConfigurationLogAddParam.of(targetMethodCode, mappingData, null, null, JSON.toJSONString(resultBO)), requestTime, resultBO.getExceptionMsg());
            return resultBO;
        } catch (Exception e) {
            log.error("[CallBackAbstractBizHandler-execute]===============配置业务处理失败(入)============businessNo:{},ex:", mappingBO.getBusinessNo(), e);
            //日志记录
            ConfigurationLogAddParam logBO = ConfigurationLogAddParam.of(targetMethodCode, mappingData, null, null, "系统异常,请稍后再试!");
            saveLog(mappingBO, logBO, requestTime, LogUtils.getPrintStackTraceMessage(e));
            return ResultBO.error("配置业务处理失败!", -1);
        }
    }

    /**
     * 解析响应
     * @param response
     * @return
     */
    protected abstract void parsingResponse(ResultBO resultBO, HandleBO handleBO);

    /**
     * 新增日记记录
     *
     * @param mappingBO
     * @param configurationLogAddParam
     * @param requestTime
     * @return
     */
    private void saveLog(MappingBO mappingBO, ConfigurationLogAddParam configurationLogAddParam, Long requestTime, String exceptionMsg) {
        try {
            configurationLogAddParam.setBusinessNo(mappingBO.getBusinessNo());
            configurationLogAddParam.setSourceSystemCode(mappingBO.getSourceSystemCode());
            configurationLogAddParam.setTargetSystemCode(mappingBO.getTargetSystemCode());
            configurationLogAddParam.setSourceData(mappingBO.getSourceData());
            configurationLogAddParam.setRequestTime(requestTime);
            configurationLogAddParam.setExceptionMsg(exceptionMsg);

            configurationLogFacade.saveLog(configurationLogAddParam);
        } catch (Exception e) {
            log.error("[AbstractBizHandler-saveLog]===============异步新增日志异常===============ex:", e);
        }
    }

}
