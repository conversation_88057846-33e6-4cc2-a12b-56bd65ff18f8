package com.danding.business.server.ares.request.bo;

import lombok.Data;

import java.io.Serializable;

@Data
public class RequestBO implements Serializable {

    /**
     * 系统编码
     */
    private String systemCode;

    /**
     * 方法编码
     */
    private String methodCode;

    /**
     * 原始数据
     */
    private String origin;

    /**
     * 映射后数据
     * 需要根据请求配置确定调用时的具体类型
     */
    private String mapped;

    /**
     * 调用方式
     * dubbo http sdk
     */
    private String methodType;

    /**
     * 日志code
     */
    private String logCode;
}
