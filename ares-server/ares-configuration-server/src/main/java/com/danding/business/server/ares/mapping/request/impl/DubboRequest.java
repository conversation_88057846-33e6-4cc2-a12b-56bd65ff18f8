package com.danding.business.server.ares.mapping.request.impl;

import cn.hutool.json.JSONUtil;
import com.danding.business.client.ares.mapping.param.out.AdjustOrderMappingParam;
import com.danding.business.server.ares.configuration.BO.ConfigurationRequestParamsBO;
import com.danding.business.server.ares.configuration.manager.ConfigurationRequestParamsManager;
import com.danding.business.server.ares.mapping.BO.MappingBO;
import com.danding.business.server.ares.mapping.enums.DubboRequestEnums;
import com.danding.business.server.ares.mapping.request.RequestWorker;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.danding.business.server.ares.mapping.constant.ConfConstant.METHOD_NAME;

/**
 * dubbo, 内部请求
 */
@Slf4j
@Component
public class DubboRequest extends RequestWorker {

    @Autowired
    private ConfigurationRequestParamsManager configurationRequestParamsManager;
    @Autowired
    private DTWMSManager dtwmsManager;

    @Override
    public String doRequest(MappingBO mappingBO) {
        String mappingData = mappingBO.getMappingData();
        String targetSystemCode = mappingBO.getTargetSystemCode();
        String targetMethodCode = mappingBO.getTargetMethodCode();
        List<ConfigurationRequestParamsBO> dtoList = configurationRequestParamsManager.listBySearchCache(targetSystemCode, targetMethodCode);
        Map<String, String> paramMap = dtoList.stream().collect(Collectors.toMap(k -> k.getKey(), v -> v.getValue(), (k1, k2) -> k1));
        String method = paramMap.get(METHOD_NAME);
        DubboRequestEnums dubboRequestEnums = DubboRequestEnums.getDubboRequest(method);
        switch (dubboRequestEnums){
            case SYN_CK:
                return dtwmsManager.pushWareHouse(mappingData);
            case SYN_HZ:
                return dtwmsManager.pushOwner(mappingData);
            case SYN_HP:
                return dtwmsManager.addGoods(mappingData);
            case SYN_HP_UP:
                return dtwmsManager.modifyGoods(mappingData);
            case SYN_RK_NO:
                return dtwmsManager.pushInOrder(mappingData);
            case RK_DD_NO_CL:
                return dtwmsManager.cancelInOrder(mappingData);
            case SYN_CK_NO:
                return dtwmsManager.pushOutOrder(mappingData);
            case CK_DD_NO_CL:
                return dtwmsManager.cancelOutOrder(mappingData);
            case ADJUST_APPROVAL_BACK:
                AdjustOrderMappingParam adjustOrderMappingParam = JSONUtil.toBean(mappingBO.getSourceData(), AdjustOrderMappingParam.class);
                if(Objects.equals(0, adjustOrderMappingParam.getType())){
                    return dtwmsManager.transferOrderCallDT(mappingData);
                }
                return dtwmsManager.adjustOrderCallDT(mappingData);
            case TALLY_APPROVAL_BACK:
                return dtwmsManager.tallyOrderCallDT(mappingData);
            case CK_DD_UP_ADDRESS:
                return dtwmsManager.modifyAddress(mappingData);
            case ORDER_PENDING:
                return dtwmsManager.orderPending(mappingData);
            case ORDER_ASSEMBLY_DISASSEMBLY:
                return dtwmsManager.orderAssemblyOrDisassembly(mappingData);
            default:
                throw new BusinessException("请先配置DT方法, Method Not Found!");
        }
    }

}
