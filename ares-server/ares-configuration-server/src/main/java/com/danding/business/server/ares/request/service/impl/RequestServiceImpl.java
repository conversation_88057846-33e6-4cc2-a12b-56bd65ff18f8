package com.danding.business.server.ares.request.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.danding.business.core.ares.configuration.search.ConfigurationMetaSearch;
import com.danding.business.server.ares.configuration.BO.ConfigurationMetaBO;
import com.danding.business.server.ares.configuration.manager.ConfigurationMetaManager;
import com.danding.business.server.ares.request.bo.RequestBO;
import com.danding.business.server.ares.request.common.ConfigNames;
import com.danding.business.server.ares.request.context.DefaultRequestContext;
import com.danding.business.server.ares.request.context.RequestContext;
import com.danding.business.server.ares.request.service.DWMSService;
import com.danding.business.server.ares.request.service.RequestService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@DubboService
@Slf4j
public class RequestServiceImpl implements RequestService {

    @Autowired
    private ConfigurationMetaManager configurationMetaManager;

    @Autowired
    private DWMSService dwmsService;

    @Override
    public String doRequest(RequestBO requestBO) {
        RequestContext requestContext = buildRequestContext(requestBO);
        ConfigurationMetaSearch configurationMetaSearch = new ConfigurationMetaSearch();
        configurationMetaSearch.setSystemCode(requestBO.getSystemCode());
        List<ConfigurationMetaBO> configurationMetaBOS = configurationMetaManager.listConfigurationMetaBOByConfigurationMetaSearch(configurationMetaSearch);
        Map<String, String> configMap = configurationMetaBOS.stream()
                .filter(configurationMetaBO -> StrUtil.isBlank(configurationMetaBO.getMethodCode()) || requestBO.getMethodCode().equals(configurationMetaBO.getMethodCode()))
                .collect(Collectors.toMap(ConfigurationMetaBO::getConfig, ConfigurationMetaBO::getConfigValue));
        configMap.put(ConfigNames.INTERNAL_LOG_CODE, "LOG" + System.currentTimeMillis());
        requestContext.setConfigs(configMap);

        // build sign source
        requestContext.getSignSourceBuilder().build(requestContext);

        // sign
        requestContext.getSigner().sign(requestContext);

        // request
        return requestContext.getRequester().request(requestContext);
    }

    RequestContext buildRequestContext(RequestBO requestBO) {
        DefaultRequestContext defaultRequestContext = new DefaultRequestContext();
        defaultRequestContext.setOriginRequestBody(requestBO.getOrigin());
        // source
        JSONObject source = JSONUtil.parseObj(requestBO.getOrigin());
        defaultRequestContext.setOriginContext(source);
        // mapped
        JSONObject mapped = JSONUtil.parseObj(requestBO.getMapped());
        defaultRequestContext.setMappedContext(mapped);

        defaultRequestContext.addBean(DWMSService.class.getName(), dwmsService);
        return defaultRequestContext;
    }
}
