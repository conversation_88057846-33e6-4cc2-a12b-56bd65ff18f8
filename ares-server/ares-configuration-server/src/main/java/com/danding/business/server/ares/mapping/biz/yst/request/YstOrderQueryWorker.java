package com.danding.business.server.ares.mapping.biz.yst.request;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.danding.business.client.ares.mapping.param.out.OutOrderMappingParam;
import com.danding.business.client.ares.order.param.back.OutOrderBackParam;
import com.danding.business.server.ares.mapping.BO.MappingBO;
import com.danding.business.server.ares.mapping.BO.ResultBO;
import com.danding.business.server.ares.mapping.biz.RequestHandleBizHandler;
import com.danding.soul.client.common.exception.BusinessException;
import com.google.common.base.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 邮瞬通订查询
 */
@Slf4j
@Component
public class YstOrderQueryWorker extends RequestHandleBizHandler {

    @Override
    protected void buildParams(MappingBO mappingBO) {
        log.info("[YstOrderQueryWorker-buildParams]=====================businessNo:{},邮瞬通订单取消入参:{}", mappingBO.getBusinessNo(), JSON.toJSONString(mappingBO));
        try {
            String sourceData = mappingBO.getSourceData();
            OutOrderMappingParam outOrderMappingParam = JSON.parseObject(sourceData, OutOrderMappingParam.class);

            Map<String, Object> cancelMap = new HashMap<>();
            cancelMap.put("userId", outOrderMappingParam.getSenderID());
            cancelMap.put("orderIds", outOrderMappingParam.getExternalNoList());

            mappingBO.setMappingData(JSON.toJSONString(cancelMap));
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("[YstOrderQueryWorker-buildParams]=====================邮瞬通订单取消异常================ex:", e);
            throw new BusinessException("构建邮瞬通订单取消参数错误!");
        }
    }

    @Override
    protected ResultBO parsingResponse(String response, MappingBO mappingBO) {
        if (StringUtils.isBlank(response)) {
            return ResultBO.error("error", -1);
        }
        List<OutOrderBackParam> outOrderList = new ArrayList<>();

        JSONObject object = JSON.parseObject(response);
        JSONArray jsonArray = object.getJSONArray("items");
        for (int i =0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Boolean status = jsonObject.getBoolean("status");
            if (status) {
                String orderStatus = jsonObject.getString("orderStatus");
                String hasRealOutStore = jsonObject.getString("hasRealOutStore");
                if (Objects.equal("上传成功", orderStatus) && Objects.equal("已出库", hasRealOutStore)) {
                    String orderId = jsonObject.getString("orderId");
                    String wayBillNo = jsonObject.getString("wayBillNo");
                    String logistics = jsonObject.getString("logistics");

                    OutOrderBackParam param = new OutOrderBackParam();
                    param.setExternalNo(orderId);
                    param.setLogisticsNo(wayBillNo);
                    param.setLogisticsCompanyName(logistics);

                    outOrderList.add(param);
                }
            }
        }
        return ResultBO.success(outOrderList);
    }

}
