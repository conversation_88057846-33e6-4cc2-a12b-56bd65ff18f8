package com.danding.business.server.ares.mapping.biz.mp;

import cn.hutool.json.JSONUtil;
import com.danding.business.common.ares.enums.common.DataType;
import com.danding.business.common.ares.enums.configuration.CallTypeEnum;
import com.danding.business.common.ares.utils.JsonXmlFormatUtils;
import com.danding.business.core.ares.configuration.search.ConfigurationMethodSearch;
import com.danding.business.server.ares.configuration.BO.ConfigurationMappingBO;
import com.danding.business.server.ares.configuration.BO.ConfigurationMethodBO;
import com.danding.business.server.ares.configuration.constant.Constant;
import com.danding.business.server.ares.configuration.manager.ConfigurationMappingManager;
import com.danding.business.server.ares.configuration.manager.ConfigurationMethodManager;
import com.danding.business.server.ares.mapping.BO.MappingBO;
import com.danding.business.server.ares.mapping.BO.ResultBO;
import com.danding.business.server.ares.mapping.biz.RequestHandleBizHandler;
import com.danding.business.server.ares.mapping.request.RequestFactory;
import com.danding.business.server.ares.mapping.strategy.MappingContext;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class OutMappingWorker extends RequestHandleBizHandler {

    @Autowired
    private MappingContext mappingContext;
    @Autowired
    private ConfigurationMethodManager configurationMethodManager;
    @Autowired
    private ConfigurationMappingManager configurationMappingManager;
    @Autowired
    private RequestFactory requestFactory;

    @Override
    protected void buildParams(MappingBO mappingBO) {
        try {
            //获取来源方法
            ConfigurationMethodBO sourceMethod = selectMethodByType(mappingBO.getSourceSystemCode(), mappingBO.getMethodType());
            if (Objects.isNull(sourceMethod)) {
                log.error("[MappingWorkerAbstract-buildParams]===============ERP:未配置映射方法===============");
                throw new BusinessException(-2, "未配置来源映射方法!");
            }
            //获取目标方法
            ConfigurationMethodBO targetMethod = selectMethodByType(mappingBO.getTargetSystemCode(), StringUtils.isNotBlank(mappingBO.getTargetMethodType()) ? mappingBO.getTargetMethodType() : mappingBO.getMethodType());
            if (Objects.isNull(targetMethod)) {
                log.error("[MappingWorkerAbstract-buildParams]==============={}:未配置映射方法===============", mappingBO.getTargetSystemCode());
                throw new BusinessException(-2, "未配置目标映射方法!");
            }
            String targetMethodCode = targetMethod.getMethodCode();
            String sourceMethodCode = sourceMethod.getMethodCode();


            //获取方法映射关系
            ConfigurationMappingBO mapping = selectMapping(sourceMethodCode, targetMethodCode);
            if (Objects.equals(CallTypeEnum.CALL_TYPE_MAPPING.getValue(), targetMethod.getCallType())) {
                if (Objects.isNull(mapping)) {
                    log.error("[MappingWorkerAbstract-buildParams]==============={}:未配置映射方法===============", mappingBO.getTargetSystemCode());
                    throw new BusinessException(-2, "未配置映射关系!");
                }
            } else {
                //内部dubbo
                mapping = new ConfigurationMappingBO();
                mapping.setComplexityType(5);
            }
            //原始数据转换
            String data = mappingBO.getSourceData();
            if (Objects.equals(DataType.DATA_TYPE_XML.getValue(), mappingBO.getDataType())) {
                //xml 转 JSON
                data = JsonXmlFormatUtils.xmlToJsonReplaceBlank(data);
                mappingBO.setSourceData(data);
            }
            //映射数据
            mappingBO.setTargetMethodCode(targetMethodCode);
            mappingBO.setSourceMethodCode(sourceMethodCode);
            mappingBO.setMappingCode(mapping.getMappingCode());
            mappingBO.setParamStatus(0);
            mappingContext.operate(mapping.getComplexityType(), mappingBO);
            mappingBO.setTargetMethodDataType(targetMethod.getDataType());
            mappingBO.setComplexityType(mapping.getComplexityType());

            //映射数据转换
            boolean isXML = Objects.equals(DataType.DATA_TYPE_XML.getValue(), targetMethod.getDataType());
            String xmlMapping = null;
            if (isXML) {
                //json to xml
                xmlMapping = JsonXmlFormatUtils.jsonToXmlReplaceBlank(mappingBO.getJsonMapping());
            }
            String mappingData = isXML ? xmlMapping : mappingBO.getJsonMapping();
            mappingBO.setMappingData(mappingData);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("[MappingWorkerAbstract-buildParams]===============构建映射参数失败===============ex:", e);
            throw new BusinessException("构建映射参数失败!");
        }
    }

    @Override
    protected ResultBO parsingResponse(String response, MappingBO mappingBO) {
        //解析返回值
        String callBackData = parsingResponseParams(mappingBO, response);
        //解析后的返回值处理
        return JSONUtil.toBean(callBackData, ResultBO.class);
    }

    /**
     * 获取方法映射
     *
     * @param sourceMethodCode
     * @param targetMethodCode
     * @return
     */
    private ConfigurationMappingBO selectMapping(String sourceMethodCode, String targetMethodCode) {
        return configurationMappingManager.getOneBy(sourceMethodCode, targetMethodCode);
    }

    /**
     * 解析同步响应的数据
     *
     * @param responseData
     * @return
     */
    private String parsingResponseParams(MappingBO mappingBO, String responseData) {
        if(Objects.equals(5, mappingBO.getComplexityType())){
            return responseData;
        }
        //响应数据转换
        if (Objects.equals(DataType.DATA_TYPE_XML.getValue(), mappingBO.getTargetMethodDataType())) {
            //xml 转 JSON
            responseData = JsonXmlFormatUtils.xmlToJsonReplaceBlank(responseData);
        }
        //映射数据, 返回值映射到来源系统
        MappingBO backMappingBO = MappingBO.of(mappingBO.getSourceSystemCode(), responseData, mappingBO.getTargetMethodCode(), mappingBO.getSourceMethodCode(), mappingBO.getMappingCode(), 1);
        mappingContext.operate(mappingBO.getComplexityType(), backMappingBO);

        return backMappingBO.getJsonMapping();
    }

    /**
     * 获取方法
     *
     * @param systemCode
     * @param methodType
     * @return
     */
    @Cacheable(key = "#systemCode+#methodType", value = Constant.CACHE_VALUE, unless = "#result==null")
    public ConfigurationMethodBO selectMethodByType(String systemCode, String methodType) {
        ConfigurationMethodSearch search = new ConfigurationMethodSearch();
        search.setSystemCode(buildSystemCode(systemCode));
        search.setMethodType(methodType);
        return configurationMethodManager.getOneBySearch(search);
    }

    /**
     * 如果是FC系统,查询方法的时候返回FC_V1
     * @param systemCode
     * @return
     */
    public String buildSystemCode(String systemCode){
        if(Objects.equals("FC", systemCode)){
            return "FC_V1";
        }
        return systemCode;
    }


}
