package com.danding.business.server.ares.consumables.facade;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.danding.business.client.ares.consumables.facade.IPackageConsumablesFacade;
import com.danding.business.client.ares.consumables.facade.IPackageConsumablesImportFacade;
import com.danding.business.client.ares.consumables.param.PackageConsumablesAddParam;
import com.danding.business.common.ares.excel.listener.CommonDataListener;
import com.danding.business.common.ares.excelV2.AbstractExcelImportSingleSheetServiceV2;
import com.danding.business.common.ares.excelV2.param.ImportParam;
import com.danding.business.server.ares.consumables.excel.PackageConsumablesImportExcel;
import com.danding.component.common.api.utils.ValidateUtils;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.park.client.ParkClient;
import com.danding.park.client.core.load.form.LoadTaskDataCallbackForm;
import com.danding.soul.client.common.exception.BusinessException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 包耗材供应商导入 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
@Slf4j
@DubboService
public class PackageConsumablesImportFacadeImpl extends AbstractExcelImportSingleSheetServiceV2<PackageConsumablesImportExcel> implements IPackageConsumablesImportFacade {

    @Autowired
    private IPackageConsumablesFacade packageConsumablesFacade;

    @Override
    public void asyncImportTask(ImportParam importParam) {
        // 导入逻辑在这里实现
        try {
            readAndSave(importParam);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 完成任务
            ParkClient.loadClient().finishByUid(importParam.getUid());
        }
    }

    @Override
    public void setOutListener() {
        // 设置data listener
        this.setDataListener(new CommonDataListener<PackageConsumablesImportExcel>());
    }

    @Override
    public boolean doBussiness(List<PackageConsumablesImportExcel> list, String uid, Long userId, String userName, Long realUserId, String realUserName) {
        if (CollectionUtils.isNotEmpty(list)) {
            try {
                // 总行数
                int totalLines = 1;
                List<PackageConsumablesImportExcel> dbPrepareList = Lists.newArrayList();
                // 校验数据合法性
                for (PackageConsumablesImportExcel suppliersPcImportExcel : list) {
                    totalLines++;
                    suppliersPcImportExcel.setLineNo(totalLines);
                    String extraMsg = checkExtraParam(suppliersPcImportExcel);
                    String errorMessage = ValidateUtils.doValidator(suppliersPcImportExcel);
                    if (StringUtils.isNotBlank(errorMessage) || StringUtils.isNotBlank(extraMsg)) {
                        validError(uid, suppliersPcImportExcel, errorMessage);
                    } else {
                        // 校验成功
                        dbPrepareList.add(suppliersPcImportExcel);
                    }
                }
                //新增
                if (CollectionUtils.isNotEmpty(dbPrepareList)) {
                    try {
                        List<PackageConsumablesAddParam> packageConsumablesAddParamList = BeanUtils.copyProperties(dbPrepareList, PackageConsumablesAddParam.class);
                        packageConsumablesFacade.addListPc(packageConsumablesAddParamList);
                    } catch (BusinessException e) {
                        String[] split = e.getMessage().split(":");
                        if (split.length == 3) {
                            PackageConsumablesImportExcel excel = new PackageConsumablesImportExcel();
                            String msg = split[2];
                            if (msg.startsWith("名称")) {
                                excel.setPcName(split[0]);
                            } else {
                                excel.setPcBarcode(split[0]);
                            }
                            excel.setLineNo(Integer.valueOf(split[1]));
                            validError(uid, excel, msg);
                            return Boolean.TRUE;
                        } else {
                            throw e;
                        }
                    }
                    //全部成功
                    for (PackageConsumablesImportExcel importExcel : dbPrepareList) {
                        validSuccess(uid, importExcel);
                    }
                }
            } catch (Exception e) {
                log.error("[PackageConsumablesImportFacadeImpl-doBussiness]=============供应商信息导入异常=============", e);
            }
        }
        return Boolean.TRUE;
    }

    private String checkExtraParam(PackageConsumablesImportExcel importExcel) {
        String extraMsg = "";
        if (importExcel.getThickness() != null) {
            if (importExcel.getThickness().scale() > 3) {
                extraMsg = "厚度(三位小数)";
            }
            if (importExcel.getThickness().compareTo(BigDecimal.ZERO) < 0) {
                extraMsg = "厚度(负数)";
            }
        }
        if (importExcel.getGramsPerSquareMeter() != null) {
            if (importExcel.getGramsPerSquareMeter().scale() > 0) {
                extraMsg = "每平方米克重(g)请输入整数";
            }
            if (importExcel.getGramsPerSquareMeter().compareTo(BigDecimal.ZERO) < 0) {
                extraMsg = "每平方米克重(g)(负数)";
            }
        }
        return extraMsg;
    }

    private void validError(String uid, PackageConsumablesImportExcel importExcel, String errorMessage) {
        // 校验失败
        LoadTaskDataCallbackForm loadTaskDataCallbackForm = new LoadTaskDataCallbackForm();
        // 任务id
        loadTaskDataCallbackForm.setUid(uid);
        // excel行号
        loadTaskDataCallbackForm.set__index__(importExcel.getLineNo());
        // 是否成功：成功时，错误原因可不填写
        loadTaskDataCallbackForm.setSuccess(false);
        // 错误原因
        loadTaskDataCallbackForm.set__reason__("错误信息：（合法性校验失败）->" + errorMessage);
        Map<String, Object> data = new HashMap<>();
        if (StringUtils.isNotBlank(importExcel.getPcName())) {
            data.put("pcName", importExcel.getPcName());
        } else {
            data.put("pcBarcode", importExcel.getPcBarcode());
        }
        if (importExcel.getThickness() != null) {
            if (importExcel.getThickness().scale() > 3) {
                data.put("thickness", importExcel.getThickness() + "(三位小数)");
            }
            if (importExcel.getThickness().compareTo(BigDecimal.ZERO) < 0) {
                data.put("thickness", importExcel.getThickness() + "(大于0)");
            }
        }
        if (importExcel.getGramsPerSquareMeter() != null) {
            if (importExcel.getGramsPerSquareMeter().scale() > 0) {
                data.put("gramsPerSquareMeter", importExcel.getGramsPerSquareMeter() + "(请输入整数)");
            }
            if (importExcel.getGramsPerSquareMeter().compareTo(BigDecimal.ZERO) < 0) {
                data.put("gramsPerSquareMeter", importExcel.getGramsPerSquareMeter() + "(请输入大于0)");
            }
        }

        loadTaskDataCallbackForm.setData(data);
        ParkClient.loadClient().asyncDataCallback(loadTaskDataCallbackForm);
    }

    private void validSuccess(String uid, PackageConsumablesImportExcel importExcel) {
        // 校验失败
        LoadTaskDataCallbackForm loadTaskDataCallbackForm = new LoadTaskDataCallbackForm();
        // 任务id
        loadTaskDataCallbackForm.setUid(uid);
        // excel行号
        loadTaskDataCallbackForm.set__index__(importExcel.getLineNo());
        // 是否成功：成功时，错误原因可不填写
        loadTaskDataCallbackForm.setSuccess(true);
        // 错误原因
        Map<String, Object> data = new HashMap<>();
        data.put("pcBarcode", importExcel.getPcBarcode());
        loadTaskDataCallbackForm.setData(data);
        ParkClient.loadClient().asyncDataCallback(loadTaskDataCallbackForm);
    }
}
