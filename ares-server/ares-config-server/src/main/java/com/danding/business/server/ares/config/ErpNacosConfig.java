package com.danding.business.server.ares.config;

import com.alibaba.fastjson.JSON;
import com.danding.business.common.ares.config.TenantBizConfig;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RefreshScope
@Component
@Data
public class ErpNacosConfig {
    @Autowired
    private TenantBizConfig tenantBizConfig;

    @Value("${rocketmq.topic.ares-admin-new-flow-status-change}")
    private String flowStatusChangeTopic;

    @Value("#{${ares.application.pc.warehouse.map:null}}")
    private Map<String, String> pcWarehouseMap;

    @Value("#{${ares.application.pc.warehouse.black.list:0}}")
    private List<String> pcWarehouseList;

    @Value("#{${ares.application.return.warehouse.map:null}}")
    private Map<String, String> returnWarehouseMap;

    @Value("#{${ares.application.pc.manage.warehouse.map:null}}")
    private Map<String,String> manageWarehouseMap;

    @Value("#{${ares.application.locationCode.orderBy.strategy.warehouseCode.map:null}}")
    private Map<String, String> stWarehouseMap;

    @Value("${ares.config.server.owner.upstream.system.audit:JHSae7e36,JHSa89d89}")
    private String upstreamAuditEntityWarehouseCode;

    @Value("${ares.config.server.risk.control.warehouseCode:0}")
    private String riskControlEntityWarehouseCode;

    @Value("${ares.application.dstp.appkey.zy:0}")
    private String dstpAppKey;

    @Value("${ares.application.dstp.appsecret.zy:0}")
    private String dstpAppSecret;

    @Value("${ares.application.callback.dstp.zy:0}")
    private String callBackUrlDSTP;

    @Value("${cn.businessUnitId}")
    private String businessUnitId;

    @Value("${ares.config.server.owner.create.code:}")
    private String ownerCreateCode;

    @Value("${ares.admin.inventory.back.owner:0}")
    private String backOwnerList;

    @Value("${ares.admin.record.cx:true}")
    private boolean needCNZXC = true;

    @Value("${ares.config.logicWarehouse.getDetailByCode.timeout:180}")
    private long timeout;

    @Value("${ares.config.rpc.logicWarehouse.getDetailByCode.timeout:180}")
    private long rpcTimeout;

    @Value("${ares.config.material.use.robot.url:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ddc01927-75ee-4556-aec5-4e8280c05252}")
    private String robotUrl;

    @Value("${application.mercury.host:0}")
    private String mercuryHost;
    @Value("${ares.config.ewem.goods.limitSize:5000}")
    private int goodsLimitSize;
    @Value("${ares.config.ewem.batch.limitSize:5000}")
    private int batchLimitSize;
    @Value("${ares.config.ewem.trace.detail.url:}")
    private String ewemTraceDetailUrl;

    @Value("${ares.owner.entryarea.warning.robotUrl:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9d4d7e34-4a77-4f50-abe9-bdaee1c36c54}")
    private String returnEntryAreaUrl;



    public Map<String, String> getReturnWarehouseMap() {
        String value = tenantBizConfig.getProperty(TenantBizConfig.KEY_ARES_APPLICATION_RETURN_WAREHOUSE_MAP,null);
        if (StringUtils.isBlank(value)) {
            return new HashMap<>();
        }
        return JSON.parseObject(value, Map.class);

    }

}
