package com.danding.business.server.ares.suppliers.facade;

import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.suppliers.facade.ISuppliersFacade;
import com.danding.business.client.ares.suppliers.param.SuppliersAddParam;
import com.danding.business.client.ares.suppliers.param.SuppliersQueryParam;
import com.danding.business.client.ares.suppliers.result.SuppliersResult;
import com.danding.business.common.ares.context.AresRedisContext;
import com.danding.business.common.ares.enums.common.CustomerStatusType;
import com.danding.business.common.ares.enums.common.SuppliersGoodsSyncStatus;
import com.danding.business.common.ares.utils.DateUtils;
import com.danding.business.common.ares.utils.RedisUtils;
import com.danding.business.core.ares.suppliers.search.SuppliersSearch;
import com.danding.business.server.ares.suppliers.BO.SuppliersBO;
import com.danding.business.server.ares.suppliers.BO.SuppliersOwnerBO;
import com.danding.business.server.ares.suppliers.manager.SuppliersManager;
import com.danding.business.server.ares.suppliers.remote.RemoteGoodsFacade;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.soul.client.common.exception.BusinessException;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <p>
 * 供货商表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
@DubboService
public class SuppliersFacadeImpl implements ISuppliersFacade {

    @Autowired
    private SuppliersManager suppliersManager;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private RemoteGoodsFacade remoteGoodsFacade;

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    @Override
    public SuppliersResult getById(Serializable id) {
        SuppliersBO suppliersBO = suppliersManager.getById(id);
        return BeanUtils.copyProperties(suppliersBO, SuppliersResult.class);
    }

    /**
     * 条件查询单个
     *
     * @param suppliersQueryParam
     * @return
     */
    @Override
    public SuppliersResult getByQueryParam(SuppliersQueryParam suppliersQueryParam) {
        SuppliersBO suppliersBO = suppliersManager.getBySearch(BeanUtils.copyProperties(suppliersQueryParam, SuppliersSearch.class));
        return BeanUtils.copyProperties(suppliersBO, SuppliersResult.class);
    }

    /**
     * 条件查询list
     *
     * @param suppliersQueryParam
     * @return
     */
    @Override
    public List<SuppliersResult> listByQueryParam(SuppliersQueryParam suppliersQueryParam) {
        List<SuppliersBO> suppliersBOList = suppliersManager.listBySearch(BeanUtils.copyProperties(suppliersQueryParam, SuppliersSearch.class));
        return BeanUtils.copyProperties(suppliersBOList, SuppliersResult.class);
    }

    /**
     * 条件分页查询
     *
     * @param suppliersQueryParam
     * @return
     */
    @Override
    public ListVO<SuppliersResult> pageListByQueryParam(SuppliersQueryParam suppliersQueryParam) {
        ListVO<SuppliersBO> suppliersBOListVO = suppliersManager.pageListBySearch(BeanUtils.copyProperties(suppliersQueryParam, SuppliersSearch.class));
        return ListVO.build(suppliersBOListVO.getPage(), BeanUtils.copyProperties(suppliersBOListVO.getDataList(), SuppliersResult.class));
    }

    @Override
    public boolean saveOrUpdate(SuppliersAddParam suppliersAddParam) {
        SuppliersBO suppliersBO = BeanUtils.copyProperties(suppliersAddParam, SuppliersBO.class);
        suppliersBO.setFileJson(JSON.toJSONString(suppliersAddParam.getFileFormList()));
        suppliersBO.setSuppliersOwnerList(BeanUtils.copyProperties(suppliersAddParam.getSuppliersOwnerList(), SuppliersOwnerBO.class));
        if (suppliersBO.getUserId().equals(suppliersBO.getBindingUserId())) {
            throw new BusinessException("不能绑定自己");
        }
        if (StringUtils.isEmpty(suppliersBO.getSuppliersCode())) {
            String key = AresRedisContext.SUPPLIERS_CODE;
            long redisResult = redisUtils.incr(key, 1);
            int redisResultLength = String.valueOf(redisResult).length();
            suppliersBO.setSuppliersCode("GHS" + String.format("%0" + (redisResultLength <= 4 ? 4 : redisResultLength) + "d", redisResult));
        }
        suppliersBO.getSuppliersOwnerList().stream().peek(suppliersOwner -> {
            suppliersOwner.setId(null);
            suppliersOwner.setSuppliersCode(suppliersBO.getSuppliersCode());
            suppliersOwner.setSuppliersName(suppliersAddParam.getSuppliersName());
            suppliersOwner.setUserId(suppliersAddParam.getUserId());
            suppliersOwner.setBindingUserId(suppliersAddParam.getBindingUserId());
        }).collect(Collectors.toList());
        suppliersBO.setSuppliersStatus(CustomerStatusType.OPEN);
        suppliersBO.setCooperationStart(DateUtils.getCurrentDayStartTime(new Date(suppliersBO.getCooperationStart())).getTime());
        suppliersBO.setCooperationEnd(DateUtils.getCurrentDayStartTime(new Date(suppliersBO.getCooperationEnd())).getTime());
        long now = DateUtils.getCurrentDayStartTime(new Date(System.currentTimeMillis())).getTime();
        if (suppliersBO.getCooperationStart() > suppliersBO.getCooperationEnd()) {
            throw new BusinessException("合作时间不正确");
        }
        if (now < suppliersBO.getCooperationStart()) {
            suppliersBO.setSuppliersStatus(CustomerStatusType.TO_OPEN);
        }
        if (suppliersBO.getCooperationEnd() < now) {
            suppliersBO.setSuppliersStatus(CustomerStatusType.CLOSED);
        }
        if (Objects.nonNull(suppliersAddParam.getId())) {
            return suppliersManager.updatedById(suppliersBO);
        } else {
            return suppliersManager.add(suppliersBO);
        }
    }

    /**
     * 插入
     *
     * @param suppliersAddParam
     * @return
     */
    @Override
    public boolean add(SuppliersAddParam suppliersAddParam) {
        return suppliersManager.add(BeanUtils.copyProperties(suppliersAddParam, SuppliersBO.class));
    }

    /**
     * 批量插入
     *
     * @param suppliersAddParamList
     * @return
     */
    @Override
    public boolean addList(List<SuppliersAddParam> suppliersAddParamList) {
        return suppliersManager.addList(BeanUtils.copyProperties(suppliersAddParamList, SuppliersBO.class));
    }

    /**
     * 功能描述:  根据主键id修改
     */
    @Override
    public boolean updatedById(SuppliersAddParam suppliersAddParam) {
        return suppliersManager.updatedById(BeanUtils.copyProperties(suppliersAddParam, SuppliersBO.class));
    }

    /**
     * 根据主键id修改
     *
     * @param suppliersAddParam
     * @return
     */
    @Override
    public boolean updateById(SuppliersAddParam suppliersAddParam) {
        return suppliersManager.updateById(BeanUtils.copyProperties(suppliersAddParam, SuppliersBO.class));
    }

    /**
     * 根据主键id批量修改
     *
     * @param suppliersAddParamList
     * @return
     */
    @Override
    public boolean updateListById(List<SuppliersAddParam> suppliersAddParamList) {
        return suppliersManager.updateListById(BeanUtils.copyProperties(suppliersAddParamList, SuppliersBO.class));
    }

    /**
     * 根据条件修改
     *
     * @param suppliersQueryParam
     * @param suppliersAddParam
     * @return
     */
    @Override
    public boolean updateListByQueryParam(SuppliersQueryParam suppliersQueryParam, SuppliersAddParam suppliersAddParam) {
        return suppliersManager.updateListBySearch(BeanUtils.copyProperties(suppliersQueryParam, SuppliersSearch.class), BeanUtils.copyProperties(suppliersAddParam, SuppliersBO.class));
    }

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    @Override
    public boolean removeById(Serializable id) {
        return suppliersManager.removeById(id);
    }

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    @Override
    public boolean removeByIds(List<Long> idList) {
        return suppliersManager.removeByIds(idList);
    }

    /**
     * 根据条件删除
     *
     * @param suppliersQueryParam
     * @return
     */
    @Override
    public boolean removeByQueryParam(SuppliersQueryParam suppliersQueryParam) {
        return suppliersManager.removeBySearch(BeanUtils.copyProperties(suppliersQueryParam, SuppliersSearch.class));
    }

    @Override
    public boolean goodsSync(SuppliersQueryParam suppliersQueryParam) {
        SuppliersResult suppliersResult = getByQueryParam(suppliersQueryParam);
        if (SuppliersGoodsSyncStatus.SYNC_FAIL.equals(suppliersResult.getSuppliersGoodsSyncStatus()) || SuppliersGoodsSyncStatus.WAIT_SYNC.equals(suppliersResult.getSuppliersGoodsSyncStatus())) {
            if (remoteGoodsFacade.syncGoodsForDaitaT(suppliersResult.getUserId(), suppliersQueryParam.getUserName(), suppliersResult.getBindingUserId())) {
                SuppliersBO curSuppliersBO = BeanUtils.copyProperties(suppliersResult, SuppliersBO.class);
                curSuppliersBO.setSuppliersGoodsSyncStatus(SuppliersGoodsSyncStatus.SYNCING);
                suppliersManager.updateById(curSuppliersBO);
            }
        } else {
            throw new BusinessException("当前状态不允许同步");
        }
        return true;
    }

    @Override
    public void redisTest() {
        String key = AresRedisContext.CREATE_SALE_ORDER_BY_MAPPING + "11111111";
        long time = System.currentTimeMillis();
        try {
            //加锁
            if (!redisUtils.lock(key, time)) {
                throw new BusinessException("重复请求，请稍后再试");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            redisUtils.unlockFinally(key, time);
        }

    }

}
