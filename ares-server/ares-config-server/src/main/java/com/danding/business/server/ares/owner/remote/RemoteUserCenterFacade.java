package com.danding.business.server.ares.owner.remote;

import com.danding.business.client.rpc.user.facade.IUserRoleRpcFacade;
import com.danding.business.client.rpc.user.facade.IUserRpcFacade;
import com.danding.business.client.rpc.user.param.RoleBindRpcParam;
import com.danding.business.client.rpc.user.param.UserCreateRpcParam;
import com.danding.business.client.rpc.user.param.UserRpcQueryParam;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.business.client.rpc.user.result.UserRpcResultPro;
import com.danding.business.rpc.client.oms.explorer.facade.IAppShopRpcFacade;
import com.danding.business.rpc.client.oms.explorer.param.AppShopUpsetParam;
import com.danding.business.server.ares.sequence.manager.SysSequenceManager;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class RemoteUserCenterFacade {

    @DubboReference
    private IUserRpcFacade userRpcFacade;
    @DubboReference
    private IUserRoleRpcFacade userRoleRpcFacade;
    @Autowired
    private SysSequenceManager sysSequenceManager;
    @DubboReference
    private IAppShopRpcFacade appShopRpcFacade;

    /**
     * 根据用户名称查询用户信息
     *
     * @param userName
     * @return
     */
    public UserRpcResult getUserInfoByName(String userName) {
        try {
            UserRpcQueryParam queryParam = new UserRpcQueryParam();
            queryParam.setUserName(userName);
            return userRpcFacade.getByParam(queryParam);
        } catch (Exception e) {
            log.error("RemoteUserCenterFacade.getUserInfoByName查询用户中心异常userName:{},ex:", userName, e);
            throw new BusinessException(userName + ":查询用户中心异常!");
        }
    }

    /**
     * 创建用户
     *
     * @param userName
     * @return
     */
    public UserRpcResult createUser(String userName) {
        try {
            UserCreateRpcParam userCreateRpcParam = new UserCreateRpcParam();
            userCreateRpcParam.setUserName(userName);
            userCreateRpcParam.setMobile(getMobile());
            userCreateRpcParam.setPassword("Abc123456");
            userCreateRpcParam.setType(2);
            userCreateRpcParam.setSystemCode("ARES_WEB");
            Long userId = userRpcFacade.createUser(userCreateRpcParam);

            UserRpcQueryParam queryParam = new UserRpcQueryParam();
            queryParam.setId(userId);
            return userRpcFacade.getByParam(queryParam);
        } catch (Exception e) {
            log.error("RemoteUserCenterFacade.createUser创建用户异常userName:{},ex:", userName, e);
            throw new BusinessException(userName + ":创建用户异常!");
        }
    }

    /**
     * 获取可用的虚拟手机号
     * @return
     */
    private String getMobile() {
        try {
            String mob = sysSequenceManager.getVirtualDigit("MOB", 5);
            String mobile = "100019" + mob;
            UserRpcQueryParam queryParam = new UserRpcQueryParam();
            queryParam.setMobile(mobile);
            UserRpcResult userRpcResult = userRpcFacade.getByParam(queryParam);
            if (Objects.nonNull(userRpcResult)) {
                return getMobile();
            }
            return mobile;
        } catch (Exception e) {
            log.error("RemoteUserCenterFacade.getMobile获取可用的虚拟手机号异常ex:", e);
            throw new BusinessException("获取可用的虚拟手机号异常!");
        }
    }

    /**
     * 绑定角色
     * @return
     */
    public void bindRole(Long userId) {
        try {
            RoleBindRpcParam param = new RoleBindRpcParam();
            param.setSystemCode("ARES_WEB");
            param.setRoleName("淘天角色");
            param.setUserId(userId);
            userRoleRpcFacade.bindRole(param);
        } catch (Exception e) {
            log.error("RemoteUserCenterFacade.bindRole绑定角色异常userId:{},ex:", userId, e);
            throw new BusinessException(userId + ":绑定角色异常!");
        }
    }

    /**
     * 绑定店铺
     * @return
     */
    public void bindShop(Long userId, String outOwnerCode, String customerId) {
        try {
            AppShopUpsetParam submit = new AppShopUpsetParam();
            submit.setChannelCode("TTKJ");
            submit.setName(outOwnerCode);
            submit.setOutShopCode(outOwnerCode);
            submit.setOnOff(true);
            submit.setAuthInfo(customerId);
            appShopRpcFacade.upset(userId, submit);
        } catch (Exception e) {
            log.error("RemoteUserCenterFacade.bindShop绑定店铺异常userId:{},outOwnerCode:{},ex:", userId, outOwnerCode, e);
            throw new BusinessException(userId + ":绑定店铺异常!");
        }
    }

    /**
     * 创建用户
     *
     * @param userIdList
     * @return
     */
    public List<UserRpcResultPro> queryUserInfoList(List<Long> userIdList) {
        try {
            return userRpcFacade.listByIdsPro(userIdList);
        } catch (Exception e) {
            log.error("RemoteUserCenterFacade.queryUserInfoList查询用户信息异常ex:", e);
        }
        return new ArrayList<>();
    }


}
