package com.danding.business.server.ares.customer.BO;

import com.danding.business.common.ares.BO.report.BaseBO;
import com.danding.business.common.ares.enums.common.AccountTradeType;
import com.danding.business.common.ares.enums.common.AccountType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-22
 */

@Data
@ApiModel(value = "CustomerAccount对象", description = "查询")
public class CustomerAccountBO extends BaseBO implements Serializable {
    private Long id;

    private static final long serialVersionUID = 1L;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String customerCode;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long userId;

    /**
     * 境内境外类型
     */
    @ApiModelProperty(value = "境内境外类型")
    private AccountTradeType accountTradeType;

    /**
     * 账号类型
     */
    @ApiModelProperty(value = "账号类型")
    private AccountType accountType;

    /**
     * 开户行
     */
    @ApiModelProperty(value = "开户行")
    private String accountBank;

    /**
     * 开户地址
     */
    @ApiModelProperty(value = "开户地址")
    private String accountBankAddress;

    /**
     * 银行账户
     */
    @ApiModelProperty(value = "银行账户")
    private String accountBankNum;

    /**
     * 行联号
     */
    @ApiModelProperty(value = "行联号")
    private String bankLinkNum;

    /**
     * swift _code
     */
    @ApiModelProperty(value = "swift _code")
    private String swiftCode;


}
