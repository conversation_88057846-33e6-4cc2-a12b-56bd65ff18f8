package com.danding.business.server.ares.consumables.manager;

import com.danding.business.core.ares.consumables.service.IPackageConsumablesShelfTargetDetailService;
import com.danding.business.server.ares.consumables.manager.helper.PackageConsumablesShelfTargetDetailManagerHelper;
import com.danding.business.server.ares.consumables.BO.PackageConsumablesShelfTargetDetailBO;
import com.danding.business.core.ares.consumables.search.PackageConsumablesShelfTargetDetailSearch;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.business.core.ares.consumables.entity.PackageConsumablesShelfTargetDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 包耗材上架单上架明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@Component
public class PackageConsumablesShelfTargetDetailManager {

    @Autowired
    private IPackageConsumablesShelfTargetDetailService packageConsumablesShelfTargetDetailService;

    @Autowired
    private PackageConsumablesShelfTargetDetailManagerHelper packageConsumablesShelfTargetDetailManagerHelper;

    /**
     * id查询单个
     *
     * @param id
     * @return
     */
    public PackageConsumablesShelfTargetDetailBO getById(Serializable id) {
        return BeanUtils.copyProperties(packageConsumablesShelfTargetDetailService.selectById(id), PackageConsumablesShelfTargetDetailBO.class);
    }

    /**
     * 条件查询单个
     *
     * @param packageConsumablesShelfTargetDetailSearch
     * @return
     */
    public PackageConsumablesShelfTargetDetailBO getBySearch(PackageConsumablesShelfTargetDetailSearch packageConsumablesShelfTargetDetailSearch) {
        PackageConsumablesShelfTargetDetail packageConsumablesShelfTargetDetail = packageConsumablesShelfTargetDetailService.selectBySearch(packageConsumablesShelfTargetDetailSearch);
        return BeanUtils.copyProperties(packageConsumablesShelfTargetDetail, PackageConsumablesShelfTargetDetailBO.class);
    }

    /**
     * 列表查询
     *
     * @param packageConsumablesShelfTargetDetailSearch
     * @return
     */
    public List<PackageConsumablesShelfTargetDetailBO> listBySearch(PackageConsumablesShelfTargetDetailSearch packageConsumablesShelfTargetDetailSearch) {
        List<PackageConsumablesShelfTargetDetail> packageConsumablesShelfTargetDetailList = packageConsumablesShelfTargetDetailService.selectListBySearch(packageConsumablesShelfTargetDetailSearch);
        return BeanUtils.copyProperties(packageConsumablesShelfTargetDetailList, PackageConsumablesShelfTargetDetailBO.class);
    }

    /**
     * 分页查询
     *
     * @param packageConsumablesShelfTargetDetailSearch
     * @return
     */
    @PageSelect
    public ListVO<PackageConsumablesShelfTargetDetailBO> pageListBySearch(PackageConsumablesShelfTargetDetailSearch packageConsumablesShelfTargetDetailSearch) {
        ListVO<PackageConsumablesShelfTargetDetailBO> packageConsumablesShelfTargetDetailBOListVO = new ListVO<>();
        List<PackageConsumablesShelfTargetDetail> packageConsumablesShelfTargetDetailList = packageConsumablesShelfTargetDetailService.selectListBySearch(packageConsumablesShelfTargetDetailSearch);
        return ListVO.build(packageConsumablesShelfTargetDetailBOListVO.getPage(), BeanUtils.copyProperties(packageConsumablesShelfTargetDetailList, PackageConsumablesShelfTargetDetailBO.class));
    }

    /**
     * 功能描述:  插入
     */
    public boolean add(PackageConsumablesShelfTargetDetailBO packageConsumablesShelfTargetDetailBO) {
        return packageConsumablesShelfTargetDetailService.insert(BeanUtils.copyProperties(packageConsumablesShelfTargetDetailBO, PackageConsumablesShelfTargetDetail.class));
    }

    /**
     * 功能描述:  批量插入
     */
    public boolean addList(List<PackageConsumablesShelfTargetDetailBO> packageConsumablesShelfTargetDetailBOList) {
        return packageConsumablesShelfTargetDetailService.insertList(BeanUtils.copyProperties(packageConsumablesShelfTargetDetailBOList, PackageConsumablesShelfTargetDetail.class));
    }

    /**
     * 功能描述:  根据主键id修改
     */
    public boolean updateById(PackageConsumablesShelfTargetDetailBO packageConsumablesShelfTargetDetailBO) {
        return packageConsumablesShelfTargetDetailService.updateById(BeanUtils.copyProperties(packageConsumablesShelfTargetDetailBO, PackageConsumablesShelfTargetDetail.class));
    }

    /**
     * 功能描述:  根据主键id批量修改
     */
    public boolean updateListById(List<PackageConsumablesShelfTargetDetailBO> packageConsumablesShelfTargetDetailBOList) {
        return packageConsumablesShelfTargetDetailService.updateListById(BeanUtils.copyProperties(packageConsumablesShelfTargetDetailBOList, PackageConsumablesShelfTargetDetail.class));
    }

    /**
     * 功能描述:  根据条件修改
     */
    public boolean updateListBySearch(PackageConsumablesShelfTargetDetailSearch packageConsumablesShelfTargetDetailSearch, PackageConsumablesShelfTargetDetailBO packageConsumablesShelfTargetDetailBO) {
        return packageConsumablesShelfTargetDetailService.updateListBySearch(packageConsumablesShelfTargetDetailSearch, BeanUtils.copyProperties(packageConsumablesShelfTargetDetailBO, PackageConsumablesShelfTargetDetail.class));
    }

    /**
     * 功能描述:  根据主键id删除
     */
    public boolean removeById(Serializable id) {
        return packageConsumablesShelfTargetDetailService.deleteById(id);
    }

    /**
     * 功能描述:  根据主键id批量删除
     */
    public boolean removeByIds(List<Long> idList) {
        return packageConsumablesShelfTargetDetailService.deleteByIds(idList);
    }

    /**
     * 功能描述:  根据条件删除
     */
    public boolean removeBySearch(PackageConsumablesShelfTargetDetailSearch packageConsumablesShelfTargetDetailSearch) {
        return packageConsumablesShelfTargetDetailService.deleteBySearch(packageConsumablesShelfTargetDetailSearch);
    }
}
