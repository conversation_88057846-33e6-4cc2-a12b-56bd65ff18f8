package com.danding.business.server.ares.consumables.manager;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.consumables.facade.IPackageConsumablesAdjustDetailFacade;
import com.danding.business.client.ares.consumables.facade.IPackageConsumablesInventoryFacade;
import com.danding.business.client.ares.consumables.param.PackageConsumablesCompleteAdjustParam;
import com.danding.business.client.ares.consumables.param.PackageConsumablesGenerateAdjustParam;
import com.danding.business.client.ares.consumables.param.PackageConsumablesInventoryAddParam;
import com.danding.business.common.ares.enums.packageConsumables.*;
import com.danding.business.core.ares.consumables.search.MaterialInventoryCheckDetailSearch;
import com.danding.business.core.ares.consumables.search.MaterialInventoryCheckSearch;
import com.danding.business.core.ares.consumables.search.PackageConsumablesAdjustDetailSearch;
import com.danding.business.core.ares.consumables.service.IPackageConsumablesAdjustService;
import com.danding.business.server.ares.consumables.BO.MaterialInventoryCheckBO;
import com.danding.business.server.ares.consumables.BO.MaterialInventoryCheckDetailBO;
import com.danding.business.server.ares.consumables.BO.PackageConsumablesAdjustDetailBO;
import com.danding.business.server.ares.consumables.manager.helper.PackageConsumablesAdjustManagerHelper;
import com.danding.business.server.ares.consumables.BO.PackageConsumablesAdjustBO;
import com.danding.business.core.ares.consumables.search.PackageConsumablesAdjustSearch;
import com.danding.business.server.ares.entitywarehouse.manager.EntityWarehouseManager;
import com.danding.business.server.ares.sequence.manager.SysSequenceManager;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.business.core.ares.consumables.entity.PackageConsumablesAdjust;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 包耗材调整单主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Component
@Slf4j
public class PackageConsumablesAdjustManager {

    /**
     * 最大可生成盘点单的数据
     */
    private static final int MAX_CHECK_SIZE = 3;
    @Autowired
    private IPackageConsumablesAdjustService packageConsumablesAdjustService;

    @Autowired
    private PackageConsumablesAdjustManagerHelper packageConsumablesAdjustManagerHelper;
    @Autowired
    PackageConsumablesAdjustDetailManager packageConsumablesAdjustDetailManager;

    @Autowired
    IPackageConsumablesInventoryFacade packageConsumablesInventoryFacade;
    @Autowired
    MaterialInventoryCheckDetailManager materialInventoryCheckDetailManager;
    @Autowired
    MaterialInventoryCheckManager materialInventoryCheckManager;
    @Autowired
    SysSequenceManager sysSequenceManager;
    @Autowired
    PackageConsumablesShiftManager packageConsumablesShiftManager;
    @Autowired
    EntityWarehouseManager entityWarehouseManager;


    /**
     * id查询单个
     *
     * @param id
     * @return
     */
    public PackageConsumablesAdjustBO getById(Serializable id) {
        return BeanUtils.copyProperties(packageConsumablesAdjustService.selectById(id), PackageConsumablesAdjustBO.class);
    }

    /**
     * 条件查询单个
     *
     * @param packageConsumablesAdjustSearch
     * @return
     */
    public PackageConsumablesAdjustBO getBySearch(PackageConsumablesAdjustSearch packageConsumablesAdjustSearch) {
        PackageConsumablesAdjust packageConsumablesAdjust = packageConsumablesAdjustService.selectBySearch(packageConsumablesAdjustSearch);
        return BeanUtils.copyProperties(packageConsumablesAdjust, PackageConsumablesAdjustBO.class);
    }

    /**
     * 列表查询
     *
     * @param packageConsumablesAdjustSearch
     * @return
     */
    public List<PackageConsumablesAdjustBO> listBySearch(PackageConsumablesAdjustSearch packageConsumablesAdjustSearch) {
        List<PackageConsumablesAdjust> packageConsumablesAdjustList = packageConsumablesAdjustService.selectListBySearch(packageConsumablesAdjustSearch);
        return BeanUtils.copyProperties(packageConsumablesAdjustList, PackageConsumablesAdjustBO.class);
    }

    /**
     * 分页查询
     *
     * @param packageConsumablesAdjustSearch
     * @return
     */
    @PageSelect
    public ListVO<PackageConsumablesAdjustBO> pageListBySearch(PackageConsumablesAdjustSearch packageConsumablesAdjustSearch) {
        ListVO<PackageConsumablesAdjustBO> packageConsumablesAdjustBOListVO = new ListVO<>();
        List<PackageConsumablesAdjust> packageConsumablesAdjustList = packageConsumablesAdjustService.selectListBySearch(packageConsumablesAdjustSearch);
        return ListVO.build(packageConsumablesAdjustBOListVO.getPage(), BeanUtils.copyProperties(packageConsumablesAdjustList, PackageConsumablesAdjustBO.class));
    }

    /**
     * 功能描述:  插入
     */
    public boolean add(PackageConsumablesAdjustBO packageConsumablesAdjustBO) {
        return packageConsumablesAdjustService.insert(BeanUtils.copyProperties(packageConsumablesAdjustBO, PackageConsumablesAdjust.class));
    }

    /**
     * 功能描述:  批量插入
     */
    public boolean addList(List<PackageConsumablesAdjustBO> packageConsumablesAdjustBOList) {
        return packageConsumablesAdjustService.insertList(BeanUtils.copyProperties(packageConsumablesAdjustBOList, PackageConsumablesAdjust.class));
    }

    /**
     * 功能描述:  根据主键id修改
     */
    public boolean updateById(PackageConsumablesAdjustBO packageConsumablesAdjustBO) {
        return packageConsumablesAdjustService.updateById(BeanUtils.copyProperties(packageConsumablesAdjustBO, PackageConsumablesAdjust.class));
    }

    /**
     * 功能描述:  根据主键id批量修改
     */
    public boolean updateListById(List<PackageConsumablesAdjustBO> packageConsumablesAdjustBOList) {
        return packageConsumablesAdjustService.updateListById(BeanUtils.copyProperties(packageConsumablesAdjustBOList, PackageConsumablesAdjust.class));
    }

    /**
     * 功能描述:  根据条件修改
     */
    public boolean updateListBySearch(PackageConsumablesAdjustSearch packageConsumablesAdjustSearch, PackageConsumablesAdjustBO packageConsumablesAdjustBO) {
        return packageConsumablesAdjustService.updateListBySearch(packageConsumablesAdjustSearch, BeanUtils.copyProperties(packageConsumablesAdjustBO, PackageConsumablesAdjust.class));
    }

    /**
     * 功能描述:  根据主键id删除
     */
    public boolean removeById(Serializable id) {
        return packageConsumablesAdjustService.deleteById(id);
    }

    /**
     * 功能描述:  根据主键id批量删除
     */
    public boolean removeByIds(List<Long> idList) {
        return packageConsumablesAdjustService.deleteByIds(idList);
    }

    /**
     * 功能描述:  根据条件删除
     */
    public boolean removeBySearch(PackageConsumablesAdjustSearch packageConsumablesAdjustSearch) {
        return packageConsumablesAdjustService.deleteBySearch(packageConsumablesAdjustSearch);
    }

    /**
     * @param completeAdjustForm
     * @return
     */
    public boolean completeAdjust(PackageConsumablesCompleteAdjustParam completeAdjustForm) {
        if (StringUtils.isBlank(completeAdjustForm.getAdjustNo())) {
            throw new BusinessException("调整单号不能为空");
        }
        if (StringUtils.isBlank(completeAdjustForm.getDifferenceReason())) {
            throw new BusinessException("差异原因不能为空");
        }
        if (StringUtils.isBlank(completeAdjustForm.getRemark())) {
            throw new BusinessException("备注不能为空");
        }

        PackageConsumablesAdjustSearch packageConsumablesAdjustSearch = new PackageConsumablesAdjustSearch();
        packageConsumablesAdjustSearch.setAdjustNo(completeAdjustForm.getAdjustNo());
        PackageConsumablesAdjustBO packageConsumablesAdjust = getBySearch(packageConsumablesAdjustSearch);
        if (packageConsumablesAdjust == null || packageConsumablesAdjust.getAdjustStatus() == PackageConsumablesAdjustStatusEnum.PC_ADJUST_COMPLETE.getValue()) {
            throw new BusinessException("无效的调拨单，不能操作");
        }
        PackageConsumablesAdjustDetailSearch packageConsumablesAdjustDetailSearch = new PackageConsumablesAdjustDetailSearch();
        packageConsumablesAdjustDetailSearch.setAdjustNo(completeAdjustForm.getAdjustNo());
        List<PackageConsumablesAdjustDetailBO> detailBOList = packageConsumablesAdjustDetailManager.listBySearch(packageConsumablesAdjustDetailSearch);
        if (CollectionUtils.isEmpty(detailBOList)) {
            return true;
        }

        //为正数，调增;为负调减
        List<PackageConsumablesAdjustDetailBO> adjustInDetailBOList = detailBOList.stream().filter(m -> m.getAdjustQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        List<PackageConsumablesAdjustDetailBO> adjustOutDetailBOList = detailBOList.stream().filter(m -> m.getAdjustQty().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());

        List<PackageConsumablesInventoryAddParam> inventoryAddParamList = new ArrayList<>();
        List<PackageConsumablesInventoryAddParam> finalInventoryAddParamList = inventoryAddParamList;
        adjustInDetailBOList.stream().forEach(in -> {
            PackageConsumablesInventoryAddParam addParam = new PackageConsumablesInventoryAddParam();
            addParam.setWarehouseCode(in.getWarehouseCode());
            addParam.setBusinessNo(completeAdjustForm.getAdjustNo());
            addParam.setLocationCode(in.getLocationCode());
            addParam.setWarehouseName(in.getWarehouseName());
            addParam.setAssociatedNo(packageConsumablesAdjust.getRelatedNo());
            addParam.setOperator(SimpleUserHelper.getUserName());
            addParam.setOperationTime(System.currentTimeMillis());
            addParam.setType(BusinessTypeEnum.ADJUST_RK);
            addParam.setPcBarcode(in.getPcBarcode());
            addParam.setPcName(in.getPcName());
            addParam.setQuantity(in.getAdjustQty());
            finalInventoryAddParamList.add(addParam);
        });
        List<PackageConsumablesInventoryAddParam> inventoryReduceParamList = new ArrayList<>();
        adjustOutDetailBOList.stream().forEach(in -> {
            PackageConsumablesInventoryAddParam addParam = new PackageConsumablesInventoryAddParam();
            addParam.setWarehouseCode(in.getWarehouseCode());
            addParam.setBusinessNo(completeAdjustForm.getAdjustNo());
            addParam.setLocationCode(in.getLocationCode());
            addParam.setWarehouseName(in.getWarehouseName());
            addParam.setAssociatedNo(packageConsumablesAdjust.getRelatedNo());
            addParam.setOperator(SimpleUserHelper.getUserName());
            addParam.setOperationTime(System.currentTimeMillis());
            addParam.setType(BusinessTypeEnum.ADJUST_CK);
            addParam.setPcBarcode(in.getPcBarcode());
            addParam.setPcName(in.getPcName());
            addParam.setQuantity(in.getAdjustQty().abs());
            inventoryReduceParamList.add(addParam);
        });
        boolean inventoryList = true;
        if (CollectionUtils.isNotEmpty(finalInventoryAddParamList)) {
            inventoryList = packageConsumablesInventoryFacade.addInventoryList(finalInventoryAddParamList);
        }
        if (!inventoryList) {
            throw new BusinessException("库存操作失败!");
        }
        if (CollectionUtils.isNotEmpty(inventoryReduceParamList)) {
            inventoryList = packageConsumablesInventoryFacade.reduceInventoryList(inventoryReduceParamList);
        }
        if (!inventoryList) {
            throw new BusinessException("库存操作失败!");
        }
        //这种为调拨完成状态
        packageConsumablesAdjust.setAdjustStatus(PackageConsumablesAdjustStatusEnum.PC_ADJUST_COMPLETE.getValue());
        packageConsumablesAdjust.setOperator(SimpleUserHelper.getUserName());
        packageConsumablesAdjust.setOperateTime(System.currentTimeMillis());
        packageConsumablesAdjust.setDifferenceReason(completeAdjustForm.getDifferenceReason());
        packageConsumablesAdjust.setRemark(completeAdjustForm.getRemark());
        updateById(packageConsumablesAdjust);
        return true;
    }

    public boolean generateAdjust(PackageConsumablesGenerateAdjustParam generateAdjustParam) {
        if (CollectionUtils.isEmpty(generateAdjustParam.getInventoryCheckCodeList())) {
            throw new BusinessException("请先勾选盘点完成的盘点单");
        }
        if (generateAdjustParam.getInventoryCheckCodeList().size() > MAX_CHECK_SIZE) {
            throw new BusinessException("一次性只允许勾选1-3个盘点单");
        }
        MaterialInventoryCheckSearch materialInventoryCheckSearch = new MaterialInventoryCheckSearch();
        materialInventoryCheckSearch.setInventoryCheckCodeList(generateAdjustParam.getInventoryCheckCodeList());
        List<MaterialInventoryCheckBO> list = materialInventoryCheckManager.listBySearch(materialInventoryCheckSearch);
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("先勾选盘点完成的盘点单异常");
        }
        Set<String> warehouseSet = list.stream().map(MaterialInventoryCheckBO::getWarehouseCode).collect(Collectors.toSet());
        if (warehouseSet.size() > 1) {
            throw new BusinessException("勾选的非同一仓库的盘点单，请重新勾选");
        }
        List<MaterialInventoryCheckBO> noDoneList = list.stream().filter(m -> !MaterialInventoryCheckStatusEnum.DONE.getCode().equals(m.getStatus())).collect(Collectors.toList());
        if (noDoneList.size() > 0) {
            throw new BusinessException("盘点单非完成状态，不允许生成差异,单据[" + getCheckCodeList(list) + "]");
        }
        List<MaterialInventoryCheckBO> DoneList = list.stream().filter(m -> StringUtils.isNotBlank(m.getAdjustCode())).collect(Collectors.toList());
        if (DoneList.size() > 0) {
            throw new BusinessException("盘点单已生成了调正单，不能再次生产单据[" + getCheckCodeList(list) + "]");

        }

        //1、所有标记为【原始明细】的明细差异数 ≠0 的部分，状态为盘点完成 生成在同一个调整单中
        //当前调整单中，所有明细的差异总数之和（正负相抵）
        MaterialInventoryCheckDetailSearch materialInventoryCheckDetailSearch = new MaterialInventoryCheckDetailSearch();
        materialInventoryCheckDetailSearch.setInventoryCheckCodeList(generateAdjustParam.getInventoryCheckCodeList());
        materialInventoryCheckDetailSearch.setDiff(true);
        materialInventoryCheckDetailSearch.setStatus(MaterialInventoryCheckDetailStatusEnum.DONE.getCode());
        materialInventoryCheckDetailSearch.setDetailType(MaterialInventoryCheckDetailTypeEnum.ORIGIN.getCode());
        List<MaterialInventoryCheckDetailBO> materialInventoryCheckDetailBOList = materialInventoryCheckDetailManager.listBySearch(materialInventoryCheckDetailSearch);
        if (materialInventoryCheckDetailBOList.size() == 0) {
            throw new BusinessException("盘点单无差异明细，不能再次生产单据[" + getCheckCodeList(list) + "]");
        }


        //按 库区条码与耗材条码，库区编码唯一 分组计算
        Map<String, List<MaterialInventoryCheckDetailBO>> materialInventoryCheckDetailGroupMap = materialInventoryCheckDetailBOList.stream().collect(Collectors.groupingBy(o -> o.getLocationCode() + StrUtil.UNDERLINE + o.getMaterialCode()));
        List<MaterialInventoryCheckDetailBO> statisticsList = new ArrayList<>();
        materialInventoryCheckDetailGroupMap.keySet().stream().forEach(key -> {
            MaterialInventoryCheckDetailBO checkDetailBO = new MaterialInventoryCheckDetailBO();
            List<MaterialInventoryCheckDetailBO> listInfo = materialInventoryCheckDetailGroupMap.get(key);
            //差异数
            checkDetailBO = BeanUtils.copyProperties(listInfo.get(0), MaterialInventoryCheckDetailBO.class);
            //二盘数有值，取二盘数； 否则取一盘数
            //二盘账面数有值，取二盘账面数； 否则取一盘账面数
            //计算结果都放在first中
            checkDetailBO.setDiffQty(listInfo.stream().map(MaterialInventoryCheckDetailBO::getDiffQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));

            checkDetailBO.setFirstPlanQty(listInfo.stream().map(m -> {
                return m.getSecondPlanQty() != null ? m.getSecondPlanQty() : m.getFirstPlanQty();
            }).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
            checkDetailBO.setFirstQty(listInfo.stream().map(m -> {
                return m.getSecondQty() != null ? m.getSecondQty() : m.getFirstQty();
            }).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
            statisticsList.add(checkDetailBO);
        });
        log.info("[调整单生产] statisticsList :" + JSON.toJSONString(statisticsList));
        PackageConsumablesAdjustBO adjustBO = generateAdjustBO(generateAdjustParam);
        adjustBO.setWarehouseCode(list.get(0).getWarehouseCode());
        adjustBO.setWarehouseName(entityWarehouseManager.getPcWarehouseName(list.get(0).getWarehouseCode()));
        List<PackageConsumablesAdjustDetailBO> detailBOList = generateAdjustDetail(adjustBO.getAdjustNo(), statisticsList);
        //调整单据上的差异总数
        BigDecimal sum = detailBOList.stream().map(PackageConsumablesAdjustDetailBO::getAdjustQty).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
        // detailBOList.stream().flatMapToDouble(PackageConsumablesAdjustDetailBO::getAdjustQty)
        sum.setScale(3, RoundingMode.HALF_DOWN);
        adjustBO.setDifferenceQty(sum);

        log.info("[调整单生产] 生成detailBOList" + JSON.toJSONString(detailBOList));
        log.info("[调整单生产] 生成AdjustBO" + JSON.toJSONString(adjustBO));
        boolean result = add(adjustBO);
        log.info("[调整单生产] 主单保存结果" + result);
        result = packageConsumablesAdjustDetailManager.addList(detailBOList);
        log.info("[调整单生产] 详情单保存结果" + result);
        //修改盘点单上的 调整单号
        list.stream().forEach(m -> {
            m.setAdjustCode(adjustBO.getAdjustNo());
        });
        log.info("[调整单生产] 盘点单修改" + JSON.toJSONString(list));
        result = materialInventoryCheckManager.updateListById(list);
        log.info("[调整单生产] 盘点单保存结果" + result);
        return true;

    }

    private String getCheckCodeList(List<MaterialInventoryCheckBO> list) {
        return StringUtils.join(list.stream().map(MaterialInventoryCheckBO::getInventoryCheckCode).collect(Collectors.toList()), ",");
    }

    private PackageConsumablesAdjustBO generateAdjustBO(PackageConsumablesGenerateAdjustParam generateAdjustParam) {
        PackageConsumablesAdjustBO packageConsumablesAdjustBO = new PackageConsumablesAdjustBO();
        String adjustNo = sysSequenceManager.getSeqDateNo("TZ");
        packageConsumablesAdjustBO.setAdjustNo(adjustNo);
        packageConsumablesAdjustBO.setAdjustStatus(PackageConsumablesAdjustStatusEnum.PC_ADJUST_SH.getValue());

        packageConsumablesAdjustBO.setCreateBy(SimpleUserHelper.getUserId());
        packageConsumablesAdjustBO.setCreateByName(SimpleUserHelper.getUserName());
        String relateNo = StringUtils.joinWith(",", generateAdjustParam.getInventoryCheckCodeList());
        packageConsumablesAdjustBO.setRelatedNo(relateNo);

        return packageConsumablesAdjustBO;
    }

    private List<PackageConsumablesAdjustDetailBO> generateAdjustDetail(String adjustNo, List<MaterialInventoryCheckDetailBO> checkDetailBOList) {
        if (CollectionUtils.isEmpty(checkDetailBOList)) {
            return new ArrayList<>();
        }
        //二次过滤，分组统计后，可能有0的数据，过滤为明细差异数 ≠0 的部分
        checkDetailBOList = checkDetailBOList.stream().filter(m -> m.getDiffQty().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        List<PackageConsumablesAdjustDetailBO> adjustDetailList = checkDetailBOList.stream().map(m -> {
            PackageConsumablesAdjustDetailBO adjustDetailBO = new PackageConsumablesAdjustDetailBO();
            adjustDetailBO.setAdjustNo(adjustNo);
            adjustDetailBO.setAdjustQty(m.getDiffQty());
            adjustDetailBO.setLocationCode(m.getLocationCode());
            adjustDetailBO.setLocationType(m.getLocationType());
            adjustDetailBO.setPcBarcode(m.getMaterialCode());
            adjustDetailBO.setPcName(m.getMaterialName());
            adjustDetailBO.setWarehouseCode(m.getWarehouseCode());
            adjustDetailBO.setWarehouseName(entityWarehouseManager.getPcWarehouseName(m.getWarehouseCode()));
            //都取一盘数
            adjustDetailBO.setCheckQty(m.getFirstQty());
            //都取一盘数
            adjustDetailBO.setPlanQty(m.getFirstPlanQty());
            adjustDetailBO.setAdjustQty(m.getDiffQty());
            adjustDetailBO.setCreateBy(SimpleUserHelper.getUserId());
            return adjustDetailBO;
        }).collect(Collectors.toList());
        return adjustDetailList;
    }

}
