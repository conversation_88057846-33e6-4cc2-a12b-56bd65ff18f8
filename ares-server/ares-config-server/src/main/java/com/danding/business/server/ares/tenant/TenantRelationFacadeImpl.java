package com.danding.business.server.ares.tenant;

import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.area.facade.IAreaErrorFacade;
import com.danding.business.client.ares.area.facade.IMappingAreaFacade;
import com.danding.business.client.ares.area.param.AreaErrorAddParam;
import com.danding.business.client.ares.area.param.AreaQueryParam;
import com.danding.business.client.ares.area.result.AreaCodeResult;
import com.danding.business.client.ares.area.result.AreaResult;
import com.danding.business.client.ares.gaode.result.*;
import com.danding.business.client.ares.tenant.facade.ITenantRelationFacade;
import com.danding.business.core.ares.area.search.AreaSearch;
import com.danding.business.core.ares.supplier.service.ISupplierAccountService;
import com.danding.business.core.ares.tenant.service.ITenantRelationService;
import com.danding.business.server.ares.area.BO.AreaBO;
import com.danding.business.server.ares.area.manager.AreaManager;
import com.danding.business.server.ares.area.remote.RemoteAreaFacade;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.core.tenant.SimpleTenantHelper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.*;


/**
 * <p>
 * 地区码表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@Slf4j
@DubboService
public class TenantRelationFacadeImpl implements ITenantRelationFacade {

    @Autowired
    private ITenantRelationService tenantRelationService;
    private final static String DEFAULT_TENANT = "1001";

    @Override
    public String getTenantId(Integer entityType, String entityId) {
        String tenantId = null;
        tenantId = getTenantIdWithoutDefault(entityType, entityId);
        if (tenantId == null) {
            log.info("getTenantId return default tenant entityType:{},entityId:{},tenantId:{}", entityType, entityId, DEFAULT_TENANT);
        }
        return tenantId == null ? DEFAULT_TENANT : tenantId;

    }

    @Override
    public String getTenantIdWithoutDefault(Integer entityType, String entityId) {
        String tenantId = null;
        try {
            tenantId = tenantRelationService.getTenantId(entityType, entityId);
            log.info("getTenantId entityType:{},entityId:{},tenantId:{}", entityType, entityId, tenantId);
        } catch (Throwable e) {
            log.error("TenantRelationFacadeImpl getTenantId exception{},{}", entityType, entityId);
            throw e;
        }
        return tenantId;
    }

    @Override
    public boolean createRelation(Integer entityType, String entityId, String tenantId) {
        return tenantRelationService.createRelation(entityType, entityId, tenantId);
    }
}
