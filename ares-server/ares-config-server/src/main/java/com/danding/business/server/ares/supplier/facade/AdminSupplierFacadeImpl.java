package com.danding.business.server.ares.supplier.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.danding.business.client.ares.goods.facade.IGoodsFacade;
import com.danding.business.client.ares.supplier.facade.ISupplierGoodFacade;
import com.danding.business.client.ares.supplier.facade.ISupplierGoodPriceFacade;
import com.danding.business.client.ares.supplier.param.SupplierQueryParam;
import com.danding.business.client.ares.supplier.result.SupplierResult;
import com.danding.business.common.ares.config.CurrencyConfig;
import com.danding.business.common.ares.excel.AbstractExcelExportServiceThreeSheetServiceV2;
import com.danding.business.common.ares.excel.ThreeExcelListsResult;
import com.danding.business.core.ares.supplier.search.SupplierSearch;
import com.danding.business.server.ares.supplier.BO.SupplierBO;
import com.danding.business.server.ares.supplier.excel.SupplierAccountExcel;
import com.danding.business.server.ares.supplier.excel.SupplierContactExcel;
import com.danding.business.server.ares.supplier.excel.SupplierExcel;
import com.danding.business.server.ares.supplier.manager.SupplierManager;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.google.common.collect.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-22
 */
@Component("adminSupplierOrderExcelService")
public class AdminSupplierFacadeImpl extends AbstractExcelExportServiceThreeSheetServiceV2<SupplierExcel, SupplierContactExcel, SupplierAccountExcel, SupplierQueryParam>{


    @Autowired
    private SupplierManager supplierManager;

    @DubboReference
    private ISupplierGoodFacade iSupplierGoodFacade;

    @DubboReference
    private ISupplierGoodPriceFacade iSupplierGoodPriceFacade;


    @DubboReference
    private IGoodsFacade iGoodsFacade;
    @Autowired
    private CurrencyConfig billConfig;




    public ListVO<SupplierResult> querySupplierByPage(SupplierQueryParam supplierQueryParam) {
        SupplierSearch supplierSearch = BeanUtils.copyProperties(supplierQueryParam, SupplierSearch.class);
        ListVO<SupplierBO> supplierBOListVO = supplierManager.querySupplierByPage(supplierSearch);
        ListVO<SupplierResult> resultListVO = new ListVO<>();
        if (CollectionUtil.isNotEmpty(supplierBOListVO.getDataList())) {
            List<SupplierResult> resultList = BeanUtils.copyProperties(supplierBOListVO.getDataList(), SupplierResult.class);
            resultListVO.setDataList(resultList);

        }
        resultListVO.setPage(supplierBOListVO.getPage());

        return resultListVO;
    }







    @Override
    public ThreeExcelListsResult<SupplierExcel, SupplierContactExcel, SupplierAccountExcel> getDataList(SupplierQueryParam searchParam) {
        ListVO<SupplierResult> supplierResultListVO = querySupplierByPage(searchParam);
        // 设置总页数
        this.setTotalPage(supplierResultListVO.getPage().getTotalPage());
        List<SupplierResult> list = supplierResultListVO.getDataList();
        ThreeExcelListsResult<SupplierExcel, SupplierContactExcel, SupplierAccountExcel> twoListsResult = new ThreeExcelListsResult<>();
        if (CollectionUtils.isNotEmpty(list)) {

            // 生成sheet1对应的数据列表:结算单列表
            List<SupplierExcel> firstList = Lists.newArrayList();
        }

        return twoListsResult;

    }

    @Override
    public void before(SupplierQueryParam searchParam) {

    }


}

