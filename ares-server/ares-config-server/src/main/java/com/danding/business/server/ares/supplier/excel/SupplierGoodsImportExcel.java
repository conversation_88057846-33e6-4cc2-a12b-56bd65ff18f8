package com.danding.business.server.ares.supplier.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SupplierGoodsImportExcel {

    /**
     * 在excel表中的行号：回传用
     */
    @ExcelIgnore
    private Integer lineNo;



    /**
     * 供应商名称
     */
    @NotBlank(message = "供应商名称不能为空")
    private String supplierName;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品code
     */
    @NotBlank(message = "SKU不能为空")
    private String goodsCode;

    /**
     * 名牌
     */
    private String brandName;





}
