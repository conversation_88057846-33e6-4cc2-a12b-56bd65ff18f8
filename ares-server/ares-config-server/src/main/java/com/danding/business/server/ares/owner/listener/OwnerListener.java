package com.danding.business.server.ares.owner.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.entitywarehouse.result.EntityWarehouseResult;
import com.danding.business.client.ares.newflow.message.NewFlowStatusChangeMessage;
import com.danding.business.client.ares.risk.facade.IContactFacade;
import com.danding.business.client.ares.tenant.common.EntityTypeEnum;
import com.danding.business.client.ares.tenant.facade.ITenantRelationFacade;
import com.danding.business.client.rpc.crm.customer.facade.ICustomerWarehouseRpcFacade;
import com.danding.business.client.rpc.crm.customer.param.CustomerWarehouseAddParam;
import com.danding.business.client.rpc.user.result.UserRpcResultPro;
import com.danding.business.common.ares.context.AresContext;
import com.danding.business.common.ares.context.Tag1Constant;
import com.danding.business.common.ares.enums.common.*;
import com.danding.business.common.ares.enums.newflow.FlowLogStatus;
import com.danding.business.common.ares.enums.newflow.FlowTypeEnum;
import com.danding.business.common.ares.utils.ConfigTagHelper;
import com.danding.business.core.ares.entitywarehouse.search.EntityWarehouseSearch;
import com.danding.business.server.ares.config.ErpNacosConfig;
import com.danding.business.server.ares.entitywarehouse.BO.EntityWarehouseBO;
import com.danding.business.server.ares.entitywarehouse.manager.EntityWarehouseManager;
import com.danding.business.server.ares.owner.BO.OwnerBO;
import com.danding.business.server.ares.owner.manager.OwnerManager;
import com.danding.business.server.ares.owner.remote.RemoteOwnerInfoFacade;
import com.danding.business.server.ares.owner.remote.RemoteUserCenterFacade;
import com.danding.component.rocketmq.consume.PushMQConsumer;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.soul.client.common.exception.BusinessException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
@RocketMQMessageListener(topic = "${rocketmq.topic.ares-admin-new-flow-status-change}", selectorExpression = "CREATE_OWNER", consumerGroup = "ares-config-owner-consumer")
public class OwnerListener extends PushMQConsumer<NewFlowStatusChangeMessage> {

    @Autowired
    private OwnerManager ownerManager;
    @DubboReference
    private IContactFacade contactFacade;
    @Autowired
    private RemoteOwnerInfoFacade remoteOwnerInfoFacade;
    @DubboReference
    private ICustomerWarehouseRpcFacade customerWarehouseRpcFacade;
    @Autowired
    private ITenantRelationFacade tenantRelationFacade;
    @Autowired
    private ErpNacosConfig erpNacosConfig;
    @Autowired
    private RemoteUserCenterFacade remoteUserCenterFacade;
    @Autowired
    private EntityWarehouseManager entityWarehouseManager;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handleMessage(NewFlowStatusChangeMessage message) throws RuntimeException {
        log.info("[OwnerListener-onMessage]===================message:" + JSON.toJSONString(message));
        List<FlowLogStatus> allowList = Lists.newArrayList();
        allowList.add(FlowLogStatus.APPROVED);
        allowList.add(FlowLogStatus.REJECTED);
        if (!allowList.contains(message.getFlowLogStatus())) {
            // 不是我想要的消息类型，直接抛弃
            log.info("抛弃一个无用的MQ消息：" + message.getBusinessNo() + "----" + message.getFlowLogStatus().getValue());
            return;
        }
        if (!FlowTypeEnum.CREATE_OWNER.equals(message.getFlowType())) {
            log.info("不是创建货主单据类型，抛弃此MQ消息：" + message.getBusinessNo() + "----" + message.getFlowLogStatus().getValue());
            return;
        }
        OwnerBO ownerBO = ownerManager.getByCode(message.getBusinessNo());
        if (Objects.isNull(ownerBO)) {
            throw new BusinessException("不存此货主 ownerCode:" + message.getBusinessNo());
        }
        if (!OwnerEntryStatus.WAIT_APPROVAL.equals(ownerBO.getOwnerEntryStatus())) {
            log.error("该货主不是待审核状态，ownerCode:" + message.getBusinessNo());
            return;
        }
        if (FlowLogStatus.APPROVED.equals(message.getFlowLogStatus())) {
            // 审核通过
            ownerBO.setOwnerEntryStatus(OwnerEntryStatus.EFFECTED);
            ownerBO.setOperatorRemark("");
        } else if (FlowLogStatus.REJECTED.equals(message.getFlowLogStatus())) {
            // 审核失败
            ownerBO.setOwnerEntryStatus(OwnerEntryStatus.TO_BE_EDITED);
            ownerBO.setOperatorRemark(message.getOperatorRemark());
        } else {
            log.error("[OwnerListener-onMessage]==========未匹配到对应状态=========message:" + JSON.toJSONString(message));
            return;
        }
        ownerBO.setOperatorId(message.getOperator());
        ownerBO.setOperatorName(message.getOperatorName());
        if (StringUtils.isNotBlank(message.getOperatorRemark())) {
            ownerBO.setOperatorRemark(message.getOperatorRemark());
        } else {
            ownerBO.setOperatorRemark("");
        }
        if (!ownerManager.update(ownerBO)) {
            throw new BusinessException("审批失败，请稍后再试");
        }
        EntityWarehouseBO warehouseBO = entityWarehouseManager.getDetailByCode(ownerBO.getEntityWarehouseCode());
        if (FlowLogStatus.APPROVED.equals(message.getFlowLogStatus())) {
            try {
                remoteOwnerInfoFacade.addRisk(ownerBO);
            } catch (Exception e) {
                log.error("addRisk error", e);
            }
            try {
                //客户平台接口调用
                if (Objects.nonNull(ownerBO.getCustomerWarehouserId())) {
                    CustomerWarehouseAddParam customerWarehouseAddParam = new CustomerWarehouseAddParam();
                    customerWarehouseAddParam.setOwnerCode(ownerBO.getOwnerCode());
                    customerWarehouseAddParam.setOwnerName(ownerBO.getOwnerName());
                    EntityWarehouseResult entityWarehouseResultByCode = remoteOwnerInfoFacade.getEntityWarehouseResultByCode(ownerBO.getEntityWarehouseCode());
                    customerWarehouseAddParam.setWarehouseCode(entityWarehouseResultByCode.getWarehouseCode());
                    customerWarehouseAddParam.setEntityWarehouseName(entityWarehouseResultByCode.getEntityWarehouseName());
                    customerWarehouseAddParam.setId(String.valueOf(ownerBO.getCustomerWarehouserId()));
                    customerWarehouseRpcFacade.addOwnerMsg(customerWarehouseAddParam);
                }
            } catch (Exception e) {
                log.error("addOwnerMsg error", e);
            }
            // 审核通过,自动创建退货仓货主
            autoCreateReturnOwner(ownerBO, warehouseBO);
        }
        log.info("OwnerListener-tenantRelationFacade create {}", JSON.toJSONString(ownerBO.getOwnerCode()));
        String tenantId = tenantRelationFacade.getTenantIdWithoutDefault(EntityTypeEnum.OWNER_CODE_TYPE.getValue(), ownerBO.getOwnerCode());
        if (StringUtils.isBlank(tenantId)) {
            tenantRelationFacade.createRelation(EntityTypeEnum.OWNER_CODE_TYPE.getValue(), ownerBO.getOwnerCode(), String.valueOf(SimpleTenantHelper.getTenantId()));
        }
        //发送告警分组消息
        if (StringUtils.isNotBlank(erpNacosConfig.getOwnerCreateCode()) && FlowLogStatus.APPROVED.equals(message.getFlowLogStatus())) {
            //查询客户简称
            List<Long> userIdList = Arrays.asList(ownerBO.getUserId());
            List<UserRpcResultPro> userInfoList = remoteUserCenterFacade.queryUserInfoList(userIdList);
            String customerName = "";
            if (CollectionUtil.isNotEmpty(userInfoList)) {
                String customerName1 = userInfoList.get(0).getCustomerName();
                if (StringUtils.isNotBlank(customerName1)) {
                    customerName = customerName1;
                }
            }
            contactFacade.sendAlarmMessageByCode(erpNacosConfig.getOwnerCreateCode(), ownerBO.getOwnerCode(),
                    "新货主入驻成功：\n" +
                             "货主名称：" + ownerBO.getOwnerName() + "\n" +
                             "用户名称：" + ownerBO.getUserName() + "\n" +
                             "客户简称：" + customerName + "\n" +
                             "所属实体仓：" + warehouseBO.getEntityWarehouseName());
        }
    }

    /**
     * 货主审核通过,自动创建退货仓货主
     *
     * @param ownerBO
     * @param warehouseBO
     */
    private void autoCreateReturnOwner(OwnerBO ownerBO, EntityWarehouseBO warehouseBO) {
        try {
            if (Objects.isNull(ownerBO)
                    || ConfigTagHelper.isOpenTag(ownerBO.getTag1(), Tag1Constant.IS_TAOTIAN)
                    || Objects.isNull(warehouseBO)
                    || StringUtils.isBlank(warehouseBO.getReturnWarehouseCode())
                    || !Objects.equals(AresContext.SYSTEM_DT, warehouseBO.getSystemCode())
                    || !Objects.equals(TradeType.BONDED, warehouseBO.getTradeType())) {
                return;
            }
            EntityWarehouseSearch warehouseSearch = new EntityWarehouseSearch();
            warehouseSearch.setWarehouseCode(warehouseBO.getReturnWarehouseCode());
            EntityWarehouseBO returnWarehouseBO = entityWarehouseManager.getBySearch(warehouseSearch);
            if (Objects.isNull(returnWarehouseBO)) {
                return;
            }
            ownerManager.saveOwner(buildOwnerCode(ownerBO, returnWarehouseBO));
        } catch (Exception e) {
            log.error("autoCreateReturnOwner error", e);
        }
    }

    private OwnerBO buildOwnerCode(OwnerBO oldOwner, EntityWarehouseBO returnWarehouseBO) {
        OwnerBO ownerBO = new OwnerBO();
        ownerBO.setUserId(oldOwner.getId());
        ownerBO.setUserName(oldOwner.getUserName());
        ownerBO.setOwnerCode(oldOwner.getOwnerCode() + "-" + returnWarehouseBO.getWarehouseCode());
        ownerBO.setOwnerName(oldOwner.getOwnerName() + "-" + returnWarehouseBO.getEntityWarehouseName());
        ownerBO.setOwnerEntryStatus(OwnerEntryStatus.EFFECTED);
        ownerBO.setIsTallyType(IsTallyType.NO_TALLY);
        ownerBO.setTallReportAuditType(TallReportAuditType.DEFAULT);
        ownerBO.setEntityWarehouseCode(returnWarehouseBO.getEntityWarehouseCode());
        ownerBO.setMixType(OwnerMixType.MIX_ENTITY);
        ownerBO.setOwnerTaxType(OwnerTaxType.AUTO_TAX);
        ownerBO.setIsFinancialOwner(0);
        ownerBO.setNonBonded(2);
        ownerBO.setOpenStatus(1);
        ownerBO.setPledgeOwner(0);
        ownerBO.setOwnerPreSaleType(OwnerPreSaleType.NO);
        ownerBO.setNewGoodsIsBatch(1);
        ownerBO.setIsTrusteeship(TrusteeshipType.NO);
        ownerBO.setFourPl(0);
        ownerBO.setInventoryMode(0);
        ownerBO.setIsBatch(0);
        ownerBO.setSendStatus(SendStatus.READY_SEND.getValue());
        ownerBO.setSingleWh(Boolean.TRUE);
        return ownerBO;
    }

}
