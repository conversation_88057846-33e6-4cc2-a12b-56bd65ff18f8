package com.danding.business.server.ares.owner.facade;

import com.danding.business.client.ares.entitywarehouse.facade.IEntityWarehouseFacade;
import com.danding.business.client.ares.entitywarehouse.param.EntityWarehouseQueryParam;
import com.danding.business.client.ares.entitywarehouse.result.EntityWarehouseResult;
import com.danding.business.client.ares.owner.facade.IOwnerFacade;
import com.danding.business.client.ares.owner.param.OwnerAddParam;
import com.danding.business.client.ares.owner.param.OwnerQueryParam;
import com.danding.business.client.ares.owner.result.OwnerResult;
import com.danding.business.client.rpc.config.facade.OwnerRpcFacade;
import com.danding.business.client.rpc.config.param.OwnerAddRpcParam;
import com.danding.business.client.rpc.config.param.OwnerCrmRpcParam;
import com.danding.business.client.rpc.config.param.OwnerRpcParam;
import com.danding.business.client.rpc.config.result.OwnerRpcResult;
import com.danding.business.client.rpc.config.result.WarehouseInfoResult;
import com.danding.business.common.ares.enums.common.InventoryGroupType;
import com.danding.business.common.ares.enums.common.IsTallyType;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.soul.client.common.exception.BusinessException;
import com.danding.soul.client.common.result.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class OwnerRpcFacadeImpl implements OwnerRpcFacade {
    @DubboReference
    private IOwnerFacade iOwnerFacade;
    @DubboReference
    private IEntityWarehouseFacade entityWarehouseFacade;

    @Override
    public OwnerRpcResult getOneByOwnerCode(String ownerCode) {
        OwnerResult ownerResult = iOwnerFacade.getByCode(ownerCode);
        return BeanUtils.copyProperties(ownerResult, OwnerRpcResult.class);
    }

    @Override
    public Boolean updateOwnerIsTally(String ownerCode, IsTallyType isTallyType) {
        OwnerResult ownerResult = iOwnerFacade.getByCode(ownerCode);
        ownerResult.setIsTallyType(isTallyType);
        return iOwnerFacade.update(ownerResult);
    }

    @Override
    public List<OwnerRpcResult> litOwnerByUserId(Long userId) {
        OwnerQueryParam queryParam = new OwnerQueryParam();
        queryParam.setUserId(userId);
        List<OwnerResult> ownerResultList = iOwnerFacade.listOwnerByParam(queryParam);
        return BeanUtils.copyProperties(ownerResultList, OwnerRpcResult.class);
    }

    @Override
    public List<OwnerRpcResult> listOwnerByParam(OwnerRpcParam ownerRpcParam) {
        if (ownerRpcParam == null) {
            ownerRpcParam = new OwnerRpcParam();
        }
        OwnerQueryParam queryParam = BeanUtils.copyProperties(ownerRpcParam, OwnerQueryParam.class);
        List<OwnerResult> ownerResultList = iOwnerFacade.listOwnerByParam(queryParam);
        //根据实体仓编码查询贸易类型进行过滤
        if (!CollectionUtils.isEmpty(ownerResultList) && Objects.nonNull(ownerRpcParam.getTradeType())) {
            Set<String> entityWarehouseCodeSet = ownerResultList.stream().map(OwnerResult::getEntityWarehouseCode).collect(Collectors.toSet());
            EntityWarehouseQueryParam entityWarehouseQueryParam = new EntityWarehouseQueryParam();
            entityWarehouseQueryParam.setEntityWarehouseCodeSet(entityWarehouseCodeSet);
            List<EntityWarehouseResult> entityWarehouseResults = entityWarehouseFacade.listEntityByParam(entityWarehouseQueryParam);
            if (CollectionUtils.isEmpty(entityWarehouseResults)) {
                return null;
            }
            OwnerRpcParam finalOwnerRpcParam = ownerRpcParam;
            Map<String, EntityWarehouseResult> entityWarehouseResultMap = entityWarehouseResults.stream().filter(entityWarehouseResult -> finalOwnerRpcParam.getTradeType().equals(entityWarehouseResult.getTradeType())).collect(Collectors.toMap(EntityWarehouseResult::getEntityWarehouseCode, o -> o, (oldValue, newValue) -> oldValue));
            if (CollectionUtils.isEmpty(entityWarehouseResultMap)) {
                return null;
            }
            ownerResultList = ownerResultList.stream().filter(ownerResult -> {
                EntityWarehouseResult entityWarehouseResult = entityWarehouseResultMap.get(ownerResult.getEntityWarehouseCode());
                return Objects.nonNull(entityWarehouseResult);
            }).collect(Collectors.toList());
        }
        return BeanUtils.copyProperties(ownerResultList, OwnerRpcResult.class);
    }

    @Override
    public List<OwnerRpcResult> listOwnerByParamForCrm(OwnerCrmRpcParam ownerRpcParam) {
        if (ownerRpcParam == null) {
            ownerRpcParam = new OwnerCrmRpcParam();
        }
        OwnerQueryParam queryParam = BeanUtils.copyProperties(ownerRpcParam, OwnerQueryParam.class);
        List<OwnerResult> ownerResultList = iOwnerFacade.listOwnerByParam(queryParam);
        //根据实体仓编码查询贸易类型进行过滤
        if (!CollectionUtils.isEmpty(ownerResultList) && Objects.nonNull(ownerRpcParam.getTradeType())) {
            Set<String> entityWarehouseCodeSet = ownerResultList.stream().map(OwnerResult::getEntityWarehouseCode).collect(Collectors.toSet());
            EntityWarehouseQueryParam entityWarehouseQueryParam = new EntityWarehouseQueryParam();
            entityWarehouseQueryParam.setEntityWarehouseCodeSet(entityWarehouseCodeSet);
            List<EntityWarehouseResult> entityWarehouseResults = entityWarehouseFacade.listEntityByParam(entityWarehouseQueryParam);
            if (CollectionUtils.isEmpty(entityWarehouseResults)) {
                return null;
            }
            OwnerCrmRpcParam finalOwnerRpcParam = ownerRpcParam;
            Map<String, EntityWarehouseResult> entityWarehouseResultMap = entityWarehouseResults.stream().filter(entityWarehouseResult -> finalOwnerRpcParam.getTradeType().equals(entityWarehouseResult.getTradeType())).collect(Collectors.toMap(EntityWarehouseResult::getEntityWarehouseCode, o -> o, (oldValue, newValue) -> oldValue));
            if (CollectionUtils.isEmpty(entityWarehouseResultMap)) {
                return null;
            }
            ownerResultList = ownerResultList.stream().filter(ownerResult -> {
                EntityWarehouseResult entityWarehouseResult = entityWarehouseResultMap.get(ownerResult.getEntityWarehouseCode());
                return Objects.nonNull(entityWarehouseResult);
            }).collect(Collectors.toList());
        }
        return BeanUtils.copyProperties(ownerResultList, OwnerRpcResult.class);
    }

    @Override
    public Boolean addOwner(OwnerAddRpcParam ownerAddRpcParam) {
        OwnerAddParam ownerAddParam = BeanUtils.copyProperties(ownerAddRpcParam, OwnerAddParam.class);
        //默认不启用库存分组
        ownerAddParam.setIsInventoryGroup(InventoryGroupType.NO);
        //默认开启
        ownerAddParam.setOpenStatus(1);
        return iOwnerFacade.saveOwner(ownerAddParam);
    }

    @Override
    public RpcResult<WarehouseInfoResult> getUserIdByCode(String ownerCode) {
        try {
            if (StringUtils.isBlank(ownerCode)) {
                throw new BusinessException("货主编码不能为空!");
            }
            OwnerResult ownerResult = iOwnerFacade.getByCode(ownerCode);
            if (Objects.isNull(ownerResult)) {
                throw new BusinessException(-900, ownerCode + ":货主不存在!");
            }
            EntityWarehouseResult entityWarehouseResult = entityWarehouseFacade.getDetailByCode(ownerResult.getEntityWarehouseCode());
            if (Objects.isNull(entityWarehouseResult)) {
                throw new BusinessException(-900, ownerCode + ":货主对应实体仓不存在!");
            }
            WarehouseInfoResult warehouseInfoResult = new WarehouseInfoResult();
            warehouseInfoResult.setUserId(ownerResult.getUserId());
            warehouseInfoResult.setOwnerCode(ownerResult.getOwnerCode());
            warehouseInfoResult.setOwnerName(ownerResult.getOwnerName());
            warehouseInfoResult.setWarehouseCode(entityWarehouseResult.getWarehouseCode());
            warehouseInfoResult.setWarehouseName(entityWarehouseResult.getEntityWarehouseName());
            return RpcResult.success("success", warehouseInfoResult);
        } catch (BusinessException e) {
            return RpcResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("[OwnerRpcFacadeImpl-getUserIdByCode]============查询货主信息异常===========ex:", e);
            return RpcResult.error(-900, ownerCode + ":查询货主信息异常!");
        }
    }

    @Override
    public String getTaoTianSalePlatformNameByOwner(String ownerCode) {
        return null;
    }

    @Override
    public OwnerRpcResult getReturnOwnerByCode(String ownerCode) {
        return BeanUtils.copyProperties(iOwnerFacade.getReturnOwnerByCode(ownerCode), OwnerRpcResult.class);
    }

}
