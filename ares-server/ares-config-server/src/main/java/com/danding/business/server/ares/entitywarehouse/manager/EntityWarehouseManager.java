package com.danding.business.server.ares.entitywarehouse.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.mapping.facade.MappingFacade;
import com.danding.business.client.ares.mapping.param.MappingParam;
import com.danding.business.client.ares.mapping.param.out.WarehouseMappingParam;
import com.danding.business.client.ares.mapping.result.MappingResult;
import com.danding.business.client.ares.tenant.common.EntityTypeEnum;
import com.danding.business.client.ares.tenant.facade.ITenantRelationFacade;
import com.danding.business.common.ares.enums.common.OnlineCustomsStatus;
import com.danding.business.common.ares.enums.common.SendStatus;
import com.danding.business.common.ares.enums.common.TradeType;
import com.danding.business.core.ares.entitywarehouse.entity.EntityWarehouse;
import com.danding.business.core.ares.entitywarehouse.search.EntityWarehouseSearch;
import com.danding.business.core.ares.entitywarehouse.service.IEntityWarehouseService;
import com.danding.business.core.ares.express.entity.Express;
import com.danding.business.core.ares.express.service.IExpressService;
import com.danding.business.server.ares.config.ErpNacosConfig;
import com.danding.business.server.ares.entitywarehouse.BO.EntityWarehouseBO;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.soul.client.common.exception.BusinessException;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static com.danding.business.common.ares.context.AresContext.SYSTEM_DT;
import static com.danding.business.common.ares.context.AresContext.SYSTEM_ERP;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@Component
public class EntityWarehouseManager {

    @Autowired
    private IEntityWarehouseService entityWarehouseService;
    @Autowired
    private IExpressService iExpressService;

    @DubboReference
    private MappingFacade mappingFacade;
    @Autowired
    private ITenantRelationFacade tenantRelationFacade;

    @Autowired
    private ErpNacosConfig erpNacosConfig;

    public Boolean saveEntityWarehouse(EntityWarehouseBO entityWarehouseBO) {
        if (StringUtils.isEmpty(entityWarehouseBO.getId())) {
            EntityWarehouse warehouse = entityWarehouseService.getEntityByEntityName(entityWarehouseBO.getEntityWarehouseName());
            if (warehouse != null) {
                throw new BusinessException("实体仓名称不允许重复");
            }
            List<EntityWarehouse> warehouseList = entityWarehouseService.getEntityByWarehouseCode(entityWarehouseBO.getWarehouseCode());
            if (CollectionUtil.isNotEmpty(warehouseList)) {
                throw new BusinessException("WMS仓编码不允许重复");
            }
            EntityWarehouse entityWarehouse = BeanUtils.copyProperties(entityWarehouseBO, EntityWarehouse.class);
            //根据快递code快递表获取快递名称
            String expressCode = entityWarehouseBO.getExpressCode();
            //如果没有填写实体仓编码则自动生成
            if (StringUtils.isEmpty(entityWarehouse.getEntityWarehouseCode())) {
                //生成城市名称首字母大写
                String FirstWord = getCityWord(entityWarehouse.getCity());
                //实体仓编码
                entityWarehouse.setEntityWarehouseCode(FirstWord + UUID.randomUUID().toString().substring(0, 6));
            } else {
                entityWarehouse.setEntityWarehouseCode(entityWarehouseBO.getEntityWarehouseCode());
            }

            if (!StringUtils.isEmpty(entityWarehouseBO.getExpressList())) {
                entityWarehouse.setExpressCode(JSON.toJSONString(entityWarehouseBO.getExpressList()));
            } else {
                List<String> expressCodeList = new ArrayList<>();
                entityWarehouse.setExpressCode(JSON.toJSONString(expressCodeList));
            }
            //DT自己的保税仓,默认开启关仓
            if (Objects.equals(TradeType.BONDED, entityWarehouseBO.getTradeType()) && Objects.equals(SYSTEM_DT, entityWarehouseBO.getSystemCode())) {
                entityWarehouse.setOnlineCustomsStatus(OnlineCustomsStatus.OPEN);
            }
            if (!StringUtils.isEmpty(entityWarehouseBO.getReturnWarehouseCode()) && CollectionUtil.isNotEmpty(erpNacosConfig.getReturnWarehouseMap())) {
                entityWarehouse.setReturnWarehouseName(erpNacosConfig.getReturnWarehouseMap().get(entityWarehouseBO.getReturnWarehouseCode()));
            }
            entityWarehouseService.insert(entityWarehouse);
         /*
            String tenantId = tenantRelationFacade.getTenantIdWithoutDefault(EntityTypeEnum.WAREHOUSE_CODE_TYPE.getValue(), entityWarehouse.getWarehouseCode());
            if (org.apache.commons.lang3.StringUtils.isBlank(tenantId)) {
                tenantRelationFacade.createRelation(EntityTypeEnum.WAREHOUSE_CODE_TYPE.getValue(), entityWarehouse.getWarehouseCode(), SimpleTenantHelper.getTenantIdStr());
            }*/
            return true;
        } else {
            List<EntityWarehouse> warehouseList = entityWarehouseService.getEntityByWarehouseCode(entityWarehouseBO.getWarehouseCode());
            if (CollectionUtil.isNotEmpty(warehouseList)) {
                if (warehouseList.size() > 1) {
                    throw new BusinessException("WMS仓编码不允许重复");
                }
                Long id = warehouseList.get(0).getId();
                if (!Objects.equals(id, entityWarehouseBO.getId())) {
                    throw new BusinessException("WMS仓编码不允许重复");
                }
            }
            EntityWarehouse entityWarehouse = entityWarehouseService.selectById(entityWarehouseBO.getId());
        /*    if (SendStatus.BEEN_SEND.getValue().equals(entityWarehouse.getSendStatus())) {
                throw new BusinessException("已下发实体仓不允许修改");
            }*/
            if (entityWarehouse != null) {
                entityWarehouse = BeanUtils.copyProperties(entityWarehouseBO, EntityWarehouse.class);
                if (!StringUtils.isEmpty(entityWarehouseBO.getExpressList())) {
                    entityWarehouse.setExpressCode(JSON.toJSONString(entityWarehouseBO.getExpressList()));
                } else {
                    List<String> expressCodeList = new ArrayList<>();
                    entityWarehouse.setExpressCode(JSON.toJSONString(expressCodeList));
                }
                if (Objects.equals(TradeType.BONDED, entityWarehouseBO.getTradeType()) && Objects.equals(SYSTEM_DT, entityWarehouseBO.getSystemCode())) {
                    entityWarehouse.setOnlineCustomsStatus(OnlineCustomsStatus.OPEN);
                } else if (Objects.equals(TradeType.DUTY_PAID, entityWarehouseBO.getTradeType()) && Objects.equals(SYSTEM_DT, entityWarehouseBO.getSystemCode())) {
                    entityWarehouse.setOnlineCustomsStatus(OnlineCustomsStatus.CLOSED);
                }
                if (StringUtils.isEmpty(entityWarehouseBO.getReturnWarehouseCode())) {
                    entityWarehouse.setReturnWarehouseCode("");
                    entityWarehouse.setReturnWarehouseName("");
                }
                if (!StringUtils.isEmpty(entityWarehouseBO.getReturnWarehouseCode()) && CollectionUtil.isNotEmpty(erpNacosConfig.getReturnWarehouseMap())) {
                    entityWarehouse.setReturnWarehouseName(erpNacosConfig.getReturnWarehouseMap().get(entityWarehouseBO.getReturnWarehouseCode()));
                }
                entityWarehouseService.updateById(entityWarehouse);
            } else {
                return false;
            }
        }
        return false;
    }

    public void insert(EntityWarehouseBO entityWarehouseBO) {
        EntityWarehouse entityWarehouse = BeanUtils.copyProperties(entityWarehouseBO, EntityWarehouse.class);
        entityWarehouseService.insert(entityWarehouse);
    }

    public static String getCityWord(String city) {
        String convert = "";
        for (int j = 0; j < city.length(); j++) {
            char word = city.charAt(j);
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(word);
            if (pinyinArray != null) {
                convert += pinyinArray[0].charAt(0);
            } else {
                convert += word;
            }
        }
        return convert.toUpperCase();
    }

    public EntityWarehouseBO getDetailById(Long id) {
        EntityWarehouse entityWarehouse = entityWarehouseService.selectById(id);
        if (entityWarehouse != null) {
            EntityWarehouseBO entityWarehouseBO = BeanUtils.copyProperties(entityWarehouse, EntityWarehouseBO.class);
            entityWarehouseBO.setTradeTypeName(entityWarehouseBO.getTradeType().getDesc());
            entityWarehouseBO.setTradeTypeCode(entityWarehouseBO.getTradeType().getValue());
            Express express = iExpressService.getExpressByCode(entityWarehouseBO.getExpressCode());
            if (express != null) {
                entityWarehouseBO.setExpressName(express.getExpressName());
            }
            return entityWarehouseBO;
        } else {
            return null;
        }
    }

    public EntityWarehouseBO getDetailByCode(String entityWarehouseCode) {
        EntityWarehouse entityWarehouse = entityWarehouseService.getDetailByCode(entityWarehouseCode);
        EntityWarehouseBO entityWarehouseBO = BeanUtils.copyProperties(entityWarehouse, EntityWarehouseBO.class);
        return entityWarehouseBO;
    }

    public List<EntityWarehouseBO> listEntityCode() {
        List<EntityWarehouse> entityWarehouseList = entityWarehouseService.listEntityCode();
        List<EntityWarehouseBO> entityWarehouseBOList = BeanUtils.copyProperties(entityWarehouseList, EntityWarehouseBO.class);
        return entityWarehouseBOList;
    }

    @PageSelect
    public ListVO<EntityWarehouseBO> listEntityWarehouseByPage(EntityWarehouseSearch search) {
        List<EntityWarehouse> entityWarehouseList = entityWarehouseService.listEntityWarehouseByPage(search);
        List<EntityWarehouseBO> entityWarehouseBOList = BeanUtils.copyProperties(entityWarehouseList, EntityWarehouseBO.class);
        ListVO<EntityWarehouseBO> boListVO = new ListVO<EntityWarehouseBO>();
        boListVO.setDataList(entityWarehouseBOList);
        return boListVO;
    }

    public List<EntityWarehouseBO> listEntityByParam(EntityWarehouseSearch search) {
        List<EntityWarehouse> entityWarehouseList = entityWarehouseService.listEntityWarehouseByPage(search);
        List<EntityWarehouseBO> entityWarehouseBOList = BeanUtils.copyProperties(entityWarehouseList, EntityWarehouseBO.class);
        return entityWarehouseBOList;
    }

    public Boolean entityOutExecute(String entityWarehouseCode, String remark) {
        String sysytemCode = "";
        EntityWarehouse entityWarehouse = entityWarehouseService.getEntityByEntityCode(entityWarehouseCode);
        if (!StringUtils.isEmpty(entityWarehouse.getSystemCode())) {
            sysytemCode = entityWarehouse.getSystemCode();
        } else {
            return false;
        }
        WarehouseMappingParam warehouseDTO = new WarehouseMappingParam();
        warehouseDTO.setWarehouseName(entityWarehouse.getEntityWarehouseName());
        warehouseDTO.setWarehouseCode(entityWarehouse.getWarehouseCode());
        warehouseDTO.setProvinceCode(entityWarehouse.getProvinceCode().toString());
        warehouseDTO.setCityCode(entityWarehouse.getCityCode().toString());
        warehouseDTO.setDistrictCode(entityWarehouse.getZoneCode().toString());
        warehouseDTO.setAddress(entityWarehouse.getAddress());
        warehouseDTO.setRemark(remark);
        warehouseDTO.setTradeType(entityWarehouse.getTradeType().getValue());
        warehouseDTO.setContactName(entityWarehouse.getLinkman());
        warehouseDTO.setContactPhone(entityWarehouse.getTelephone());
        MappingParam mappingParam = MappingParam.of(entityWarehouseCode, SYSTEM_ERP, sysytemCode, "1", 1, JSON.toJSONString(warehouseDTO));
        MappingResult result = mappingFacade.outExecute(mappingParam);
        if (result.isSuccess()) {
            EntityWarehouse entityWarehouse1 = entityWarehouseService.getEntityByEntityCode(entityWarehouseCode);
            entityWarehouse1.setSendStatus(SendStatus.BEEN_SEND.getValue());
            entityWarehouse1.setCallbackJson(result.getMessage());
            entityWarehouseService.updateById(entityWarehouse1);
            return true;
        } else {
            EntityWarehouse entityWarehouse1 = entityWarehouseService.getEntityByEntityCode(entityWarehouseCode);
            entityWarehouse1.setSendStatus(SendStatus.READY_SEND.getValue());
            entityWarehouse1.setCallbackJson(result.getMessage());
            entityWarehouseService.updateById(entityWarehouse1);
            return false;
        }
    }

    /**
     * 条件查询单个
     *
     * @param search
     * @return
     */
    public EntityWarehouseBO getBySearch(EntityWarehouseSearch search) {
        EntityWarehouse entityWarehouse = entityWarehouseService.selectBySearch(search);
        return BeanUtils.copyProperties(entityWarehouse, EntityWarehouseBO.class);
    }

    /**
     * 功能描述:  根据主键id修改
     */
    public boolean updateById(EntityWarehouseBO entityWarehouseBO) {
        return entityWarehouseService.updateById(BeanUtils.copyProperties(entityWarehouseBO, EntityWarehouse.class));
    }

    /**
     * 查询包耗材仓库
     *
     * @return
     */
    public List<EntityWarehouseBO> getPcWarehouse() {
        List<EntityWarehouseBO> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(erpNacosConfig.getPcWarehouseMap())) {
            erpNacosConfig.getPcWarehouseMap().forEach((k, v) -> {
                EntityWarehouseBO warehouseBO = new EntityWarehouseBO();
                warehouseBO.setWarehouseCode(k);
                warehouseBO.setEntityWarehouseName(v);
                list.add(warehouseBO);
            });
        }
        return list;
    }

    public String getPcWarehouseName(String warehouseCode) {
        return this.erpNacosConfig.getPcWarehouseMap().get(warehouseCode);
    }

}

