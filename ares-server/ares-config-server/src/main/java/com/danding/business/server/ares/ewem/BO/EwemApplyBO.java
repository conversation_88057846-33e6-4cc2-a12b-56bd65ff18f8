package com.danding.business.server.ares.ewem.BO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 码申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */

@Data
@ApiModel(value="EwemApply对象", description="码申请表查询")
public class EwemApplyBO implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;

    /**
    * 创建时间
    */
    private Long createTime;

    /**
    * 更新时间
    */
    private Long updateTime;

    /**
    * 创建人
    */
    private Long createBy;

    /**
    * 更新人
    */
    private Long updateBy;

    /**
    * 乐观锁版本号
    */
    private Long version;

    /**
     * 申请名称
     */
    @ApiModelProperty(value = "申请名称")
    private String name;

    /**
     * 申请数量
     */
    @ApiModelProperty(value = "申请数量")
    private Long quantity;

    /**
     * 申请状态
     */
    @ApiModelProperty(value = "申请状态")
    private Integer applyStatus;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID")
    private Long batchId;

    /**
     * 状态（0正常 1停用）
     */
    @ApiModelProperty(value = "状态（0正常 1停用）")
    private Integer status;
    /**
     * 规则
     */
    private Integer rule;
    /**
     * 码长度
     */
    private Integer length;

    /**
     * 防伪码长度
     */
    @ApiModelProperty(value = "防伪码长度")
    private Integer antiLength;

    private  Integer antiRule;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
