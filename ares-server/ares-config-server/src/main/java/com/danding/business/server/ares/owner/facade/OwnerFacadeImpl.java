package com.danding.business.server.ares.owner.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.entitywarehouse.facade.IEntityWarehouseFacade;
import com.danding.business.client.ares.entitywarehouse.param.EntityWarehouseQueryParam;
import com.danding.business.client.ares.entitywarehouse.result.EntityWarehouseResult;
import com.danding.business.client.ares.express.facade.IExpressFacade;
import com.danding.business.client.ares.owner.facade.IOwnerFacade;
import com.danding.business.client.ares.owner.param.OwnerAddParam;
import com.danding.business.client.ares.owner.param.OwnerPlegedEditParam;
import com.danding.business.client.ares.owner.param.OwnerQueryParam;
import com.danding.business.client.ares.owner.result.GetEntityByOwnerResult;
import com.danding.business.client.ares.owner.result.OwnerGoodsSyncStatusResult;
import com.danding.business.client.ares.owner.result.OwnerIdNameResult;
import com.danding.business.client.ares.owner.result.OwnerResult;
import com.danding.business.client.rpc.crm.contract.facade.IContractRpcFacade;
import com.danding.business.client.rpc.crm.contract.result.ContractRpcResult;
import com.danding.business.client.rpc.user.result.UserRpcResultPro;
import com.danding.business.common.ares.BO.report.OwnerCallbackResponse;
import com.danding.business.common.ares.context.Tag1Constant;
import com.danding.business.common.ares.enums.common.*;
import com.danding.business.common.ares.utils.ConfigTagHelper;
import com.danding.business.core.ares.entitywarehouse.search.EntityWarehouseSearch;
import com.danding.business.core.ares.owner.search.OwnerSearch;
import com.danding.business.server.ares.entitywarehouse.BO.EntityWarehouseBO;
import com.danding.business.server.ares.entitywarehouse.manager.EntityWarehouseManager;
import com.danding.business.server.ares.owner.BO.OwnerBO;
import com.danding.business.server.ares.owner.manager.OwnerManager;
import com.danding.business.server.ares.owner.remote.RemoteOwnerInfoFacade;
import com.danding.business.server.ares.owner.remote.RemoteUserCenterFacade;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.shardingsphere.transaction.annotation.ShardingTransactionType;
import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.util.StringUtil;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.stream.Collectors;

import static com.danding.business.common.ares.context.AresContext.VIRTUAL_ENTITY;
import static com.danding.business.common.ares.context.AresContext.VIRTUAL_OWNER;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@Slf4j
@DubboService
public class OwnerFacadeImpl implements IOwnerFacade {


    @Autowired
    private OwnerManager ownerManager;
    @DubboReference
    private IEntityWarehouseFacade iEntityWarehouseFacade;
    @DubboReference
    private IExpressFacade iExpressFacade;
    @Autowired
    private RemoteOwnerInfoFacade remoteOwnerInfoFacade;
    @DubboReference
    private IContractRpcFacade contractRpcFacade;

    private static final ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);

    @Autowired
    private RemoteUserCenterFacade remoteUserCenterFacade;
    @Autowired
    private EntityWarehouseManager entityWarehouseManager;


    @Override
    public GetEntityByOwnerResult getEntityCodeByOwnerCode(String ownerCode) {
        GetEntityByOwnerResult getEntityByOwnerResult = ownerManager.selectOneByOwnerCode(ownerCode);
        List<String> expressNameList = new ArrayList<>();
        List<String> expressCodeList = new ArrayList<>();
        if (getEntityByOwnerResult.getExpressCode().contains("[")) {
            expressCodeList = JSON.parseArray(getEntityByOwnerResult.getExpressCode(), String.class);
            if (expressCodeList != null) {
                for (String code : expressCodeList) {
                    String expressName = iExpressFacade.getNameByCode(code);
                    expressNameList.add(expressName);
                }
            }
        }
        getEntityByOwnerResult.setExpressCodeList(expressCodeList);
        getEntityByOwnerResult.setExpressNameList(expressNameList);
        return getEntityByOwnerResult;
    }

    @Override
    public List<OwnerIdNameResult> listOwnerByUserId(Long userId) {
        List<OwnerBO> ownerBOList = ownerManager.listOwnerByUserId(userId);
        List<OwnerIdNameResult> ownerIdNameResultList = new ArrayList<>();
        for (OwnerBO ownerBO : ownerBOList) {
            if (OpenStatus.CLOSED.getValue().equals(ownerBO.getOpenStatus())) {
                continue;
            }
            OwnerIdNameResult result = new OwnerIdNameResult();
            result.setIsBatch(ownerBO.getIsBatch());
            result.setOwnerPreSaleType(ownerBO.getOwnerPreSaleType());
            result.setId(ownerBO.getOwnerCode());
            result.setName(ownerBO.getOwnerName());
            result.setTrusteeshipType(ownerBO.getIsTrusteeship());
            if (StringUtil.isEmpty(ownerBO.getSystemCode())) {
                EntityWarehouseResult entityWarehouseResult = iEntityWarehouseFacade.getDetailByCode(ownerBO.getEntityWarehouseCode());
                result.setSystemCode(entityWarehouseResult.getSystemCode());
            } else {
                result.setSystemCode(ownerBO.getSystemCode());
            }
            result.setIsFinancialOwner(ownerBO.getIsFinancialOwner());
            result.setFourPl(ownerBO.getFourPl());
            result.setUpEntityWarehouseCode(ownerBO.getUpEntityWarehouseCode());
            ownerIdNameResultList.add(result);
        }

        return ownerIdNameResultList;
    }

    @Transactional(rollbackFor = Exception.class)
    @ShardingTransactionType(value = TransactionType.BASE)
    @Override
    public Boolean saveOwner(OwnerAddParam addParam) {
        try {
            OwnerBO ownerBO = BeanUtils.copyProperties(addParam, OwnerBO.class);
            ownerManager.saveOwner(ownerBO);
            return Boolean.TRUE;
        } catch (DuplicateKeyException e) {
            throw new BusinessException("货主编码或名称已经存在!");
        }
    }


    @Override
    public List<OwnerIdNameResult> listOwner() {
        List<OwnerBO> ownerBOList = ownerManager.listEnableOwner();
        List<OwnerIdNameResult> ownerIdNameResultList = new ArrayList<>();
        for (OwnerBO ownerBO : ownerBOList) {
            OwnerIdNameResult result = new OwnerIdNameResult();
            result.setId(ownerBO.getOwnerCode());
            result.setName(ownerBO.getOwnerName());
            if (StringUtil.isEmpty(ownerBO.getSystemCode())) {
                EntityWarehouseResult entityWarehouseResult = iEntityWarehouseFacade.getDetailByCode(ownerBO.getEntityWarehouseCode());
                if (entityWarehouseResult != null) {
                    result.setSystemCode(entityWarehouseResult.getSystemCode());
                }
            } else {
                result.setSystemCode(ownerBO.getSystemCode());
            }
            ownerIdNameResultList.add(result);
        }

        return ownerIdNameResultList;
    }

    @Override
    public ListVO<OwnerResult> listOwnerByPage(OwnerQueryParam queryParam) {
        boolean queryExcludeWh = Boolean.FALSE;
        if (Objects.equals("entityWarehouseName", queryParam.getQueryType())) { //查询实体仓
            List<String> nameList = Arrays.asList(queryParam.getQueryInfo().split(","));
            EntityWarehouseSearch search = new EntityWarehouseSearch();
            search.setEntityWarehouseNameList(nameList);
            List<EntityWarehouseBO> warehouseBOList = entityWarehouseManager.listEntityByParam(search);
            List<String> codeList = warehouseBOList.stream().map(EntityWarehouse -> EntityWarehouse.getEntityWarehouseCode()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(codeList)) {
                codeList.add("null");
            }
            queryParam.setEntityWarehouseCodeList(codeList);
            queryExcludeWh = Boolean.TRUE;
        }

        OwnerSearch search = BeanUtils.copyProperties(queryParam, OwnerSearch.class);
        search.setQueryExcludeWh(queryExcludeWh);
        ListVO<OwnerBO> ownerBOListVO = ownerManager.listOwnerByPage(search);
        List<OwnerResult> results = BeanUtils.copyProperties(ownerBOListVO.getDataList(), OwnerResult.class);

        //大货主名称获取
        Set<String> parentOwnerCodeSet = results.stream().filter(ownerResult -> !StringUtils.isEmpty(ownerResult.getParentOwnerCode()) && !VIRTUAL_OWNER.equals(ownerResult.getParentOwnerCode()))
                .map(o -> o.getParentOwnerCode()).collect(Collectors.toSet());

        List<OwnerResult> parentOwnerResults;
        Map<String, OwnerResult> parentOwnerResultMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(parentOwnerCodeSet)) {
            OwnerQueryParam ownerQueryParam = new OwnerQueryParam();
            ownerQueryParam.setOwnerCodeSet(parentOwnerCodeSet);
            parentOwnerResults = this.listOwnerByParam(ownerQueryParam);
            if (!CollectionUtils.isEmpty(parentOwnerResults)) {
                parentOwnerResultMap = parentOwnerResults.stream().collect(Collectors.toMap(OwnerResult::getParentOwnerCode, o -> o, (k1, k2) -> k1));
            }
        }

        //实体仓名称,系统编码获取
        Set<String> entityWarehouseCodeSet = results.stream().filter(ownerResult -> !StringUtils.isEmpty(ownerResult.getEntityWarehouseCode()))
                .map(o -> o.getEntityWarehouseCode()).collect(Collectors.toSet());

        List<EntityWarehouseResult> entityWarehouseResults;
        Map<String, EntityWarehouseResult> entityWarehouseResultMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(entityWarehouseCodeSet)) {
            EntityWarehouseQueryParam entityWarehouseQueryParam = new EntityWarehouseQueryParam();
            entityWarehouseQueryParam.setEntityWarehouseCodeSet(entityWarehouseCodeSet);
            entityWarehouseResults = iEntityWarehouseFacade.listEntityByParam(entityWarehouseQueryParam);
            if (!CollectionUtils.isEmpty(entityWarehouseResults)) {
                entityWarehouseResultMap = entityWarehouseResults.stream().collect(Collectors.toMap(EntityWarehouseResult::getEntityWarehouseCode, o -> o, (k1, k2) -> k1));
            }
        }

        //查询客户简称
        List<Long> collect = results.stream().map(OwnerResult::getUserId).collect(Collectors.toList());
        List<UserRpcResultPro> userInfoList = remoteUserCenterFacade.queryUserInfoList(collect);
        Map<Long, String> stringMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(userInfoList)) {
            stringMap = userInfoList.stream().filter(u -> !StringUtils.isEmpty(u.getCustomerName())).collect(Collectors.toMap(UserRpcResultPro::getId, UserRpcResultPro::getCustomerName,  (k1, k2) -> k1));
        }
        for (OwnerResult result : results) {
            //大货主名称获取
            if (!StringUtils.isEmpty(result.getParentOwnerCode())) {
                if (VIRTUAL_OWNER.equals(result.getParentOwnerCode())) {
                    result.setBigOwnerName("虚拟货主");
                } else {
                    if (!CollectionUtils.isEmpty(parentOwnerResultMap)) {
                        OwnerResult ownerResult = parentOwnerResultMap.get(result.getParentOwnerCode());
                        if (Objects.nonNull(ownerResult)) {
                            result.setBigOwnerName(ownerResult.getOwnerName());
                        }
                    }
                }
            }

            //实体仓名称,系统编码获取
            if (!StringUtils.isEmpty(result.getEntityWarehouseCode())) {
                if (!CollectionUtils.isEmpty(entityWarehouseResultMap)) {
                    EntityWarehouseResult entityWarehouseResult = entityWarehouseResultMap.get(result.getEntityWarehouseCode());
                    if (Objects.nonNull(entityWarehouseResult)) {
                        result.setSystemCode(entityWarehouseResult.getSystemCode());
                        result.setEntityWarehouseName(entityWarehouseResult.getEntityWarehouseName());
                    }
                }
            }

            if (result.getSendStatus() == null || result.getSendStatus().equals(SendStatus.READY_SEND.getValue())) {
                result.setSendStatusName("待下发");
            } else {
                result.setSendStatusName("已下发");
            }

            if ("1".equals(result.getIsBatch())) {
                result.setIsBatchName("开启");
            } else if ("0".equals(result.getIsBatch())) {
                result.setIsBatchName("关闭");
            }
            result.setCustomerName(stringMap.get(result.getUserId()));
        }
        ListVO<OwnerResult> resultListVO = new ListVO<OwnerResult>();
        resultListVO.setDataList(results);
        resultListVO.setPage(ownerBOListVO.getPage());
        return resultListVO;
    }

    @Override
    public List<OwnerResult> listOwnerByParam(OwnerQueryParam queryParam) {
        OwnerSearch search = BeanUtils.copyProperties(queryParam, OwnerSearch.class);
        List<OwnerBO> boList = ownerManager.listOwnerByParam(search);
        List<OwnerResult> results = BeanUtils.copyProperties(boList, OwnerResult.class);
        return results;
    }

    @Override
    public OwnerCallbackResponse ownerOutExecute(List<String> codeList) {
        return ownerManager.ownerOutExecute(codeList);
    }

    @Override
    public OwnerResult getByCode(String ownerCode) {
        if (VIRTUAL_OWNER.equals(ownerCode)) {
            OwnerBO ownerBO = new OwnerBO();
            ownerBO.setOwnerCode(VIRTUAL_OWNER);
            ownerBO.setEntityWarehouseCode(VIRTUAL_ENTITY);
            ownerBO.setOwnerName("虚拟货主");
            OwnerResult ownerResult = BeanUtils.copyProperties(ownerBO, OwnerResult.class);
            return ownerResult;
        } else {
            OwnerBO ownerBO = ownerManager.getByCode(ownerCode);
            OwnerResult ownerResult = BeanUtils.copyProperties(ownerBO, OwnerResult.class);
            return ownerResult;
        }

    }

    @Override
    public Boolean openAndClosed(String ownerCode) {
        return ownerManager.openAndClosed(ownerCode);
    }

    @Override
    public Boolean update(OwnerResult ownerResult) {
        OwnerBO ownerBO = BeanUtils.copyProperties(ownerResult, OwnerBO.class);
        return ownerManager.update(ownerBO);
    }

    @Override
    public Boolean editOwnerByPlegde(OwnerPlegedEditParam ownerPlegedEditParam) {
        OwnerBO ownerBO = ownerManager.getByCode(ownerPlegedEditParam.getOwnerCode());
        ownerBO.setControlStatus(ownerPlegedEditParam.getControlStatus());
        ownerBO.setFinancialUser(ownerPlegedEditParam.getCapital());
        return ownerManager.update(ownerBO);
    }

    @Override
    public void ownerTallyReportFlowAdd(String ownerCode) {
        OwnerSearch ownerSearch = new OwnerSearch();
        ownerSearch.setTallReportAuditType(TallReportAuditType.INTERNAL_AUDIT);
        ownerSearch.setOwnerCode(ownerCode);
        List<OwnerBO> ownerBOs = ownerManager.listOwnerByParam(ownerSearch);
        if (!CollectionUtils.isEmpty(ownerBOs)) {
            Set<Long> userIdSet = ownerBOs.stream().map(OwnerBO::getUserId).collect(Collectors.toSet());
            userIdSet.stream().forEach(userId -> {
                if (Objects.nonNull(userId)) {
                    if (ownerManager.addTallyReportTargetFlow(userId)) {
                        log.info("ownerTallyReportFlowAdd success {}", userId);
                    } else {
                        log.error("ownerTallyReportFlowAdd fail {}", userId);
                    }
                }
            });
        }
        return;
    }

    @Override
    public String getTaskId(OwnerEntryStatus ownerEntryStatus, String businessId, Long userId) {
        if (OwnerEntryStatus.WAIT_APPROVAL.equals(ownerEntryStatus)) {
            return remoteOwnerInfoFacade.selectNewFlowTask(businessId, userId);
        }
        return null;
    }

    @Override
    public List<String> getContractListByUserId(Long userId) {
        List<ContractRpcResult> contractRpcResults = contractRpcFacade.listByUserId(userId);
        return contractRpcResults.stream().map(ContractRpcResult::getContractNo).collect(Collectors.toList());
    }

    @Override
    public List<OwnerGoodsSyncStatusResult> getGoodsSyncStatusInfo(Long userId) {
        if (userId == null) {
            return null;
        }
        return ownerManager.getGoodsSyncStatusInfo(userId);
    }

    @Override
    public void addRiskOwner() {
        ownerManager.addRiskOwner();
    }

    @Override
    public void synPreLoanInfo(String ownerCode) {
        //1.库存快照
        Long tenantId = SimpleTenantHelper.getTenantId();
        executorService.execute(() -> {
            try {
                SimpleTenantHelper.setTenantId(tenantId);
                ownerManager.pushInventoryToDSTP(ownerCode);
            } catch (Exception e) {
                log.error("[OwnerFacadeImpl-synPreLoanInfo]===============贷前信息同步异常(库存快照)===============ex:", e);
            }
        });
        //2.入库单
        executorService.execute(() -> {
            try {
                SimpleTenantHelper.setTenantId(tenantId);
                ownerManager.pushInOrderToDSTP(ownerCode);
            } catch (Exception e) {
                log.error("[OwnerFacadeImpl-synPreLoanInfo]===============贷前信息同步异常(入库单)===============ex:", e);
            }
        });
        //3.出库单
        executorService.execute(() -> {
            try {
                SimpleTenantHelper.setTenantId(tenantId);
                ownerManager.pushOutOrderToDSTP(ownerCode);
            } catch (Exception e) {
                log.error("[OwnerFacadeImpl-synPreLoanInfo]===============贷前信息同步异常(出库单)===============ex:", e);
            }
        });
        //4.推送货品
        executorService.execute(() -> {
            try {
                SimpleTenantHelper.setTenantId(tenantId);
                ownerManager.pushGoodsToDSTP(ownerCode);
            } catch (Exception e) {
                log.error("[OwnerFacadeImpl-synPreLoanInfo]===============货品同步异常(DSTP)===============ex:", e);
            }
        });
    }

    @Override
    public List<OwnerResult> listOwnerByReturn(OwnerQueryParam queryParam) {
        EntityWarehouseQueryParam warehouseQueryParam = new EntityWarehouseQueryParam();
        warehouseQueryParam.setReturnWarehouseCodeSet(queryParam.getReturnWarehouseCodeSet());
        warehouseQueryParam.setReturnWarehouseCode(queryParam.getReturnWarehouseCode());
        List<EntityWarehouseResult> entityWarehouseResults = iEntityWarehouseFacade.listEntityByParam(warehouseQueryParam);
        List<String> collect = entityWarehouseResults.stream().map(EntityWarehouseResult::getEntityWarehouseCode).collect(Collectors.toList());

        warehouseQueryParam.setReturnWarehouseCodeSet(null);
        warehouseQueryParam.setReturnWarehouseCode(null);
        warehouseQueryParam.setWarehouseCode(queryParam.getReturnWarehouseCode());
        List<EntityWarehouseResult> warehouseResultList = iEntityWarehouseFacade.listEntityByParam(warehouseQueryParam);
        if (CollectionUtil.isNotEmpty(warehouseResultList)) {
            collect.add(warehouseResultList.get(0).getEntityWarehouseCode());
        }
        OwnerSearch search = new OwnerSearch();
        search.setEntityWarehouseCodeList(collect);
        List<OwnerBO> ownerBOS = ownerManager.listOwnerByParam(search);
        return BeanUtils.copyProperties(ownerBOS, OwnerResult.class);
    }

    @Override
    public List<OwnerResult> financingRelationshipList() {
        OwnerSearch search = new OwnerSearch();
        search.setIsFinancialOwner(1);
        search.setFinanceModeType(3);
        List<OwnerBO> ownerBOList = ownerManager.listOwnerByParam(search);
        return BeanUtils.copyProperties(ownerBOList, OwnerResult.class);
    }

    @Override
    public boolean checkReturnEntryArea(String ownerCode, String entityWarehouseCode) {
        if (!StringUtils.isEmpty(ownerCode)) {
            OwnerBO ownerBO = ownerManager.getByCode(ownerCode);
            if (Objects.equals(1, ownerBO.getReturnEntryArea())) {
                return Boolean.TRUE;
            }
            boolean openTag = ConfigTagHelper.isOpenTag(ownerBO.getTag1(), Tag1Constant.IS_TAOTIAN);
            if (openTag) {
                return Boolean.FALSE;
            }
            entityWarehouseCode = ownerBO.getEntityWarehouseCode();
        }
        if (StringUtils.isEmpty(entityWarehouseCode)) {
            throw new BusinessException("实体仓编码不能为空");
        }
        return checkEntityWarehouse(entityWarehouseCode);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addTag(List<Integer> tagList, List<String> ownerCodeList) {
        if (CollectionUtils.isEmpty(ownerCodeList)) {
            throw new BusinessException("货主编码不能为空");
        }
        for (String ownerCode : ownerCodeList) {
            OwnerBO ownerBO = ownerManager.getByCode(ownerCode);
            if (Objects.isNull(ownerBO)) {
                throw new BusinessException("货主不存在");
            }
            Long tag1 = ownerBO.getTag1();
            if (ConfigTagHelper.isOpenTag(tag1, Tag1Constant.IS_TAOTIAN)) {
                throw new BusinessException("淘天货主禁止打标");
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(ownerBO.getUpEntityWarehouseCode())) {
                throw new BusinessException("字节货主禁止打标");
            }
            //TODO 目前页面只有抖超标记,后续多标,需要判断
            if (CollectionUtils.isEmpty(tagList)) {
                //去标
                tag1 = ConfigTagHelper.closeTag(tag1, Tag1Constant.IS_DYDM);
            } else {
                //打标
                tag1 = ConfigTagHelper.openTag(tag1, Tag1Constant.IS_DYDM);
            }
            ownerBO.setTag1(tag1);
            Boolean update = ownerManager.update(ownerBO);
            if (!update) {
                throw new BusinessException("货主打标失败");
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public OwnerResult getReturnOwnerByCode(String ownerCode) {
        if (StringUtils.isEmpty(ownerCode)) {
            throw new BusinessException("货主编码不能为空");
        }
        OwnerBO ownerBO = ownerManager.getByCode(ownerCode);
        if (Objects.isNull(ownerBO)) {
            throw new BusinessException("货主查询不存在");
        }
        EntityWarehouseBO warehouseBO = entityWarehouseManager.getDetailByCode(ownerBO.getEntityWarehouseCode());
        if (Objects.isNull(warehouseBO)) {
            throw new BusinessException("仓库查询不存在");
        }
        OwnerBO returnOwner = ownerManager.getByCode(ownerBO.getOwnerCode() + "-" + warehouseBO.getReturnWarehouseCode());
        if (Objects.isNull(returnOwner)) {
            throw new BusinessException("退货仓货主不存在");
        }
        return BeanUtils.copyProperties(returnOwner, OwnerResult.class);
    }

    private Boolean checkEntityWarehouse(String entityWarehouseCode) {
        EntityWarehouseBO entityWarehouse = entityWarehouseManager.getDetailByCode(entityWarehouseCode);
        if (Objects.isNull(entityWarehouse)) {
            throw new BusinessException("实体仓不存在");
        }
        if (Objects.equals(TradeType.DUTY_PAID, entityWarehouse.getTradeType())) {
            return Boolean.FALSE;
        }
        if (StringUtils.isEmpty(entityWarehouse.getReturnWarehouseCode())) {
            return Boolean.FALSE;
        }
        if (!Objects.equals("DT", entityWarehouse.getSystemCode())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

}
