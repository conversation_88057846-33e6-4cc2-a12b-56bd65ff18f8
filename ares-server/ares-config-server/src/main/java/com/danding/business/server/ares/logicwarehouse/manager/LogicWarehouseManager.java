package com.danding.business.server.ares.logicwarehouse.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.logicwarehouse.param.LogicWarehouseQueryParam;
import com.danding.business.client.ares.logicwarehouse.result.LogicWarehouseResult;
import com.danding.business.client.ares.owner.facade.IOutOwnerFacade;
import com.danding.business.client.ares.owner.param.OutOwnerAddParam;
import com.danding.business.client.ares.owner.param.OutOwnerQueryParam;
import com.danding.business.client.ares.owner.result.OutOwnerResult;
import com.danding.business.client.ares.purchase.facade.IPurchaseOrderFacade;
import com.danding.business.client.ares.purchase.result.PurchaseOrderResult;
import com.danding.business.client.ares.readyorder.facade.IReadyOrderFacade;
import com.danding.business.client.ares.record.facade.IGoodsRecordFacade;
import com.danding.business.client.ares.sale.facade.ISaleOrderFacade;
import com.danding.business.client.ares.tenant.common.EntityTypeEnum;
import com.danding.business.client.ares.transferorder.facade.ITransferOrderFacade;
import com.danding.business.client.ares.transferorder.param.TransferOrderQueryParam;
import com.danding.business.client.ares.transferorder.result.TransferOrderResult;
import com.danding.business.common.ares.context.AresContext;
import com.danding.business.common.ares.context.Tag1Constant;
import com.danding.business.common.ares.enums.common.*;
import com.danding.business.common.ares.enums.goods.GoodsType;
import com.danding.business.common.ares.utils.ConfigTagHelper;
import com.danding.business.core.ares.area.service.IAreaService;
import com.danding.business.core.ares.entitywarehouse.entity.EntityWarehouse;
import com.danding.business.core.ares.entitywarehouse.service.IEntityWarehouseService;
import com.danding.business.core.ares.express.service.IExpressService;
import com.danding.business.core.ares.logicwarehouse.entity.LogicWarehouse;
import com.danding.business.core.ares.logicwarehouse.search.LogicWarehouseSearch;
import com.danding.business.core.ares.logicwarehouse.service.ILogicWarehouseService;
import com.danding.business.core.ares.owner.entity.Owner;
import com.danding.business.core.ares.owner.service.IOwnerService;
import com.danding.business.core.ares.tenant.service.ITenantRelationService;
import com.danding.business.server.ares.entitywarehouse.BO.EntityWarehouseBO;
import com.danding.business.server.ares.logicwarehouse.BO.LogicWarehouseBO;
import com.danding.business.server.ares.logicwarehouse.manager.helper.LogicWarehouseManagerHelper;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.danding.business.common.ares.context.AresContext.*;

/**
 * <p>
 * 云仓表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@Component
@Slf4j
public class LogicWarehouseManager {

    @Autowired
    private ILogicWarehouseService logicWarehouseService;
    @Autowired
    private LogicWarehouseManagerHelper logicWarehouseManagerHelper;
    @Autowired
    private IAreaService iAreaService;
    @Autowired
    private IOwnerService iOwnerService;
    @Autowired
    private IEntityWarehouseService iEntityWarehouseService;
    @Autowired
    private IExpressService iExpressService;
    @DubboReference
    private IPurchaseOrderFacade iPurchaseOrderFacade;
    @DubboReference
    private ISaleOrderFacade iSaleOrderFacade;
    @DubboReference
    private IReadyOrderFacade iReadyOrderFacade;
    @DubboReference
    private ITransferOrderFacade iTransferOrderFacade;
    @DubboReference
    private IGoodsRecordFacade goodsRecordFacade;
    @Autowired
    private IOutOwnerFacade outOwnerFacade;
    @Autowired
    ITenantRelationService tenantRelationService;

    public Boolean saveLogicWarehouse(LogicWarehouseBO logicWarehouseBO) {
        //新增云仓
        if (StringUtils.isEmpty(logicWarehouseBO.getId())) {
            return buildLogic(logicWarehouseBO);
        } else {
            //编辑云仓
            LogicWarehouse logicWarehouse = logicWarehouseService.selectById(logicWarehouseBO.getId());
            if (logicWarehouse != null) {
                if (!logicWarehouse.getLogicWarehouseName().equals(logicWarehouseBO.getLogicWarehouseName())) {
                    if (!checkLogicWarehouseName(logicWarehouseBO.getUserId(), logicWarehouseBO.getLogicWarehouseName())) {
                        throw new BusinessException("云仓名称已存在");
                    }
                }
                // 同步淘天货主类型
                if (!Objects.equals(logicWarehouse.getOutOwnerType(), logicWarehouseBO.getOutOwnerType())) {
                    logicWarehouse.setOutOwnerType(logicWarehouseBO.getOutOwnerType());
                    OutOwnerQueryParam queryParam = new OutOwnerQueryParam();
                    queryParam.setOutOwnerCode(logicWarehouseBO.getOutOwnerCode());
                    queryParam.setOutWarehouseCode(logicWarehouseBO.getOutWarehouseCode());
                    OutOwnerResult byQueryParam = outOwnerFacade.getByQueryParam(queryParam);
                    if (Objects.nonNull(byQueryParam)) {
                        OutOwnerAddParam addParam = new OutOwnerAddParam();
                        addParam.setType(logicWarehouseBO.getOutOwnerType());
                        addParam.setId(byQueryParam.getId());
                        outOwnerFacade.updateById(addParam);
                    }
                }
                if (LogicWarehouseType.LOGIC.equals(logicWarehouse.getLogicWarehouseType())) {
                    logicWarehouseBO.setIsBatch(logicWarehouse.getIsBatch());
                } else {
                    logicWarehouseBO.setIsBatch(logicWarehouse.getIsBatch());
                    logicWarehouseBO.setIsTrusteeship(logicWarehouse.getIsTrusteeship());
                    logicWarehouseBO.setIsRegulatory(logicWarehouse.getIsRegulatory());
                }
                logicWarehouse = BeanUtils.copyProperties(logicWarehouseBO, LogicWarehouse.class);
                if (!StringUtils.isEmpty(logicWarehouseBO.getExpressList())) {
                    logicWarehouse.setExpressCode(JSON.toJSONString(logicWarehouseBO.getExpressList()));
                } else {
                    List<String> expressCodeList = new ArrayList<>();
                    logicWarehouse.setExpressCode(JSON.toJSONString(expressCodeList));
                }
                logicWarehouse.setUserId(logicWarehouseBO.getUserId());
                logicWarehouseService.updateById(logicWarehouse);
            } else {
                return false;
            }
        }
        return true;
    }

    public Boolean buildLogic(LogicWarehouseBO logicWarehouseBO) {

        // 新增品牌判断品牌编码是否重复
        if (!checkLogicWarehouseName(logicWarehouseBO.getUserId(), logicWarehouseBO.getLogicWarehouseName())) {
            throw new BusinessException("云仓名称已存在");
        }
        LogicWarehouse logicWarehouse = BeanUtils.copyProperties(logicWarehouseBO, LogicWarehouse.class);
        String logicCode = logicWarehouse.getLogicWarehouseCode();
        if (StringUtils.isEmpty(logicCode)) {
            logicCode = buildNewLogicCode(logicWarehouse.getCity(), logicWarehouse.getOwnerCode());
//            String FirstName = getCityWord(logicWarehouse.getCity());
//            String logicCode = FirstName + UUID.randomUUID().toString().substring(0, 6) + logicWarehouse.getOwnerCode();
        }
        String systemCode = "";
        Owner owner = null;
        //云仓编码生成
        if (logicWarehouse.getOwnerCode() != null) {
            owner = iOwnerService.getOwnerNameByCode(logicWarehouse.getOwnerCode());
            if (owner == null) {
                throw new BusinessException("逻辑仓绑定的货主不存在");
            }
            if (TrusteeshipType.NO.equals(owner.getIsTrusteeship()) && RegulatoryType.YES.equals(logicWarehouseBO.getIsRegulatory())) {
                throw new BusinessException("设置监管仓的属性前提必须该货主开启托管功能");
            }
            //增加实体仓的账册,保税 || 非保
            if (TradeType.BONDED.equals(logicWarehouse.getTradeType()) || Objects.equals(1, owner.getNonBonded())) {
                EntityWarehouse entityWarehouseResult = iEntityWarehouseService.getDetailByCode(logicWarehouse.getEntityWarehouseCode());
                if (Objects.isNull(entityWarehouseResult)) {
                    throw new BusinessException("当前实体仓不存在或者已经被删除");
                }
                systemCode = entityWarehouseResult.getSystemCode();
                if (StringUtils.isEmpty(entityWarehouseResult.getAccountCode()) && Objects.equals("DT", systemCode)) {
                    throw new BusinessException("当前实体仓【" + logicWarehouse.getEntityWarehouseCode() + "】账册为空，无法新建云仓");
                }
                logicWarehouse.setAccountCode(entityWarehouseResult.getAccountCode());
                logicWarehouseBO.setAccountCode(entityWarehouseResult.getAccountCode());
            }
            logicWarehouse.setLogicWarehouseCode(logicCode);
            if (OwnerPreSaleType.YES.equals(owner.getOwnerPreSaleType())) {
                buildReSaleLogic(logicWarehouseBO, logicCode);
                logicWarehouse.setPreSaleRelatedLogicWarehouseCode(logicCode + "_YS");
            }
            // 单仓判断
            if (ConfigTagHelper.isOpenTag(owner.getTag1(), Tag1Constant.SINGLE_WH)) {
                LogicWarehouseSearch search = new LogicWarehouseSearch();
                search.setOwnerCode(owner.getOwnerCode());
                List<LogicWarehouse> warehouseList = logicWarehouseService.listLogicWarehouseBySearch(search);
                if (CollectionUtil.isNotEmpty(warehouseList)) {
                    throw new BusinessException("单仓货主,不能建多个云仓");
                }
            }
        } else {
            logicWarehouse.setOwnerCode(VIRTUAL_OWNER);
            logicWarehouse.setLogicWarehouseCode(buildNewLogicCode(logicWarehouse.getCity(), logicWarehouse.getOwnerCode()));
            logicWarehouse.setEntityWarehouseCode(VIRTUAL_ENTITY);
        }

        if (!StringUtils.isEmpty(logicWarehouseBO.getExpressList())) {
            logicWarehouse.setExpressCode(JSON.toJSONString(logicWarehouseBO.getExpressList()));
        } else {
            List<String> expressCodeList = new ArrayList<>();
            logicWarehouse.setExpressCode(JSON.toJSONString(expressCodeList));
        }
        logicWarehouse.setUserId(logicWarehouseBO.getUserId());
        logicWarehouse.setCreateTime(System.currentTimeMillis());
        logicWarehouseService.saveLogicWarehouse(logicWarehouse);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(logicWarehouseBO.getOutOwnerCode()) && org.apache.commons.lang3.StringUtils.isNotBlank(logicWarehouseBO.getOutWarehouseCode())) {
            outOwnerFacade.bindDtOwner(logicWarehouseBO.getUserId(), logicWarehouseBO.getOutOwnerCode(), logicWarehouseBO.getOutWarehouseCode(), logicCode, logicWarehouseBO.getOutOwnerType());
        }
        createTenantRelation(logicWarehouse);
        //同步创建退货仓云仓
        autoCreateReturnLogic(logicWarehouse, owner);

        /**
         确认后：
         1.若实体仓对应的口岸已备案
         则用户下的备案信息，新增云仓关系。
         2.增加备案-料号ID新云仓关系，同步至CCS
         */

        ExecutorService executorService = Executors.newSingleThreadExecutor();
        Long tenantId = SimpleTenantHelper.getTenantId();
        String finalSystemCode = systemCode;
        CompletableFuture cf = CompletableFuture.runAsync(() -> {
            SimpleTenantHelper.setTenantId(tenantId);
            try {
                SimpleTenantHelper.setTenantId(tenantId);
                if (TradeType.BONDED.equals(logicWarehouse.getTradeType()) && LogicWarehouseType.ENTITY.equals(logicWarehouse.getLogicWarehouseType()) && Objects.equals("DT", finalSystemCode)) {
                    goodsRecordFacade.buildLogicSyncEntityWarehouse(logicWarehouse.getEntityWarehouseCode(), logicWarehouse.getLogicWarehouseCode(), logicWarehouse.getUserId());
                }
            } catch (ArgsErrorException aee) {
                log.error("新建云仓同步备案异常 提交ccs备案审核失败", aee);
                //   throw new BusinessException(aee.getErrorMessage());
            } catch (ArgsInvalidException aie) {
                log.error("新建云仓同步备案异常 buildLogic aie error", aie);
                // throw new BusinessException(aie.getMessage());
            } catch (Exception ex) {
                log.error("新建云仓同步备案异常 buildLogic error ", ex);
                //throw new BusinessException("云仓新建失败，实体仓关系维护失败");
            }
        }, executorService);
        return true;
    }

    /**
     * 自动创建退货仓云仓
     *
     * @param oldLogic
     * @param owner
     */
    private void autoCreateReturnLogic(LogicWarehouse oldLogic, Owner owner) {
        try {
            if (Objects.isNull(oldLogic)
                    || Objects.isNull(owner)
                    || ConfigTagHelper.isOpenTag(owner.getTag1(), Tag1Constant.IS_TAOTIAN)) {
                return;
            }
            EntityWarehouse warehouse = iEntityWarehouseService.getDetailByCode(oldLogic.getEntityWarehouseCode());
            if (Objects.isNull(warehouse)
                    || StringUtils.isEmpty(warehouse.getReturnWarehouseCode())
                    || !Objects.equals(AresContext.SYSTEM_DT, warehouse.getSystemCode())
                    || !Objects.equals(TradeType.BONDED, warehouse.getTradeType())) {
                return;
            }
            Owner returnOwner = iOwnerService.getOwnerNameByCode(oldLogic.getOwnerCode() + "-" + warehouse.getReturnWarehouseCode());
            if (Objects.isNull(returnOwner)) {
                throw new BusinessException("退货仓货主不存在");
            }
            EntityWarehouse returnWarehouse = iEntityWarehouseService.getDetailByCode(returnOwner.getEntityWarehouseCode());
            if (Objects.isNull(returnWarehouse)) {
                throw new BusinessException("退货仓不存在");
            }
            logicWarehouseService.saveLogicWarehouse(buildLogicWarehouse(oldLogic, returnOwner.getOwnerCode(), returnWarehouse));
        } catch (Exception e) {
            log.error("autoCreateReturnLogic error", e);
        }
    }

    private LogicWarehouse buildLogicWarehouse(LogicWarehouse oldLogic, String ownerCode, EntityWarehouse entityWarehouse) {
        LogicWarehouse logicWarehouse = new LogicWarehouse();
        logicWarehouse.setUserId(oldLogic.getUserId());
        logicWarehouse.setOwnerCode(ownerCode);
        logicWarehouse.setAddress(entityWarehouse.getAddress());
        logicWarehouse.setCity(entityWarehouse.getCity());
        logicWarehouse.setCityCode(entityWarehouse.getCityCode());
        logicWarehouse.setIsAutoPush(AutoPushType.AUTOPUSH);
        logicWarehouse.setIsBatch(0);
        logicWarehouse.setLinkman(entityWarehouse.getLinkman());
        logicWarehouse.setLogicWarehouseCode(buildNewLogicCode(logicWarehouse.getCity(), logicWarehouse.getOwnerCode()));
        logicWarehouse.setLogicWarehouseName(oldLogic.getLogicWarehouseName() + "-" + entityWarehouse.getEntityWarehouseName());
        logicWarehouse.setLogicWarehouseType(LogicWarehouseType.ENTITY);
        logicWarehouse.setPort(entityWarehouse.getPort());
        logicWarehouse.setPostcode(entityWarehouse.getPostcode());
        logicWarehouse.setProvince(entityWarehouse.getProvince());
        logicWarehouse.setProvinceCode(entityWarehouse.getProvinceCode());
        logicWarehouse.setTelephone(entityWarehouse.getTelephone());
        logicWarehouse.setTradeType(entityWarehouse.getTradeType());
        logicWarehouse.setZone(entityWarehouse.getZone());
        logicWarehouse.setZoneCode(entityWarehouse.getZoneCode());
        return logicWarehouse;
    }

    public Boolean buildReSaleLogic(LogicWarehouseBO logicWarehouseBO, String logicCode) {
        // 新增品牌判断品牌编码是否重复
        if (!checkLogicWarehouseName(logicWarehouseBO.getUserId(), logicWarehouseBO.getLogicWarehouseName())) {
            throw new BusinessException("云仓名称已存在");
        }
        LogicWarehouse logicWarehouse = BeanUtils.copyProperties(logicWarehouseBO, LogicWarehouse.class);

        logicWarehouse.setId(null);
        //云仓编码生成
        if (logicWarehouse.getOwnerCode() != null) {
            Owner owner = iOwnerService.getOwnerNameByCode(logicWarehouse.getOwnerCode());
            if (owner == null) {
                throw new BusinessException("逻辑仓绑定的货主不存在");
            }
            if (TrusteeshipType.NO.equals(owner.getIsTrusteeship()) && RegulatoryType.YES.equals(logicWarehouseBO.getIsRegulatory())) {
                throw new BusinessException("设置监管仓的属性前提必须该货主开启托管功能");
            }
            logicWarehouse.setOwnerPreSaleType(OwnerPreSaleType.YES);
            logicWarehouse.setLogicWarehouseCode(logicCode + "_YS");
            logicWarehouse.setLogicWarehouseName(logicWarehouse.getLogicWarehouseName() + "（预售仓）");
            logicWarehouse.setPreSaleRelatedLogicWarehouseCode(logicCode);
        } else {
            logicWarehouse.setOwnerPreSaleType(OwnerPreSaleType.YES);
            logicWarehouse.setOwnerCode(VIRTUAL_OWNER);
            logicWarehouse.setLogicWarehouseCode(logicCode + "_YS");
            logicWarehouse.setEntityWarehouseCode(VIRTUAL_ENTITY);
            logicWarehouse.setLogicWarehouseName(logicWarehouse.getLogicWarehouseName() + "（预售仓）");
            logicWarehouse.setPreSaleRelatedLogicWarehouseCode(logicCode);
        }
        if (!StringUtils.isEmpty(logicWarehouseBO.getExpressList())) {
            logicWarehouse.setExpressCode(JSON.toJSONString(logicWarehouseBO.getExpressList()));
        } else {
            List<String> expressCodeList = new ArrayList<>();
            logicWarehouse.setExpressCode(JSON.toJSONString(expressCodeList));
        }
        if (logicWarehouse.getUserId() == null) {
            logicWarehouse.setUserId(SimpleUserHelper.getUserId());
        }
        logicWarehouse.setCreateTime(System.currentTimeMillis());
        logicWarehouseService.saveLogicWarehouse(logicWarehouse);
        createTenantRelation(logicWarehouse);
        return true;
    }

    public Boolean updateReSaleLogic(LogicWarehouseBO logicWarehouseBO, String logicCode) {
        // 新增品牌判断品牌编码是否重复
        if (!checkLogicWarehouseName(logicWarehouseBO.getUserId(), logicWarehouseBO.getLogicWarehouseName() + "（预售仓）")) {
            throw new BusinessException("云仓名称已存在");
        }
        LogicWarehouse logicWarehouse = BeanUtils.copyProperties(logicWarehouseBO, LogicWarehouse.class);

        logicWarehouse.setId(null);
        //云仓编码生成
        if (logicWarehouse.getOwnerCode() != null) {
            Owner owner = iOwnerService.getOwnerNameByCode(logicWarehouse.getOwnerCode());
            if (owner == null) {
                throw new BusinessException("逻辑仓绑定的货主不存在");
            }
            if (TrusteeshipType.NO.equals(owner.getIsTrusteeship()) && RegulatoryType.YES.equals(logicWarehouseBO.getIsRegulatory())) {
                throw new BusinessException("设置监管仓的属性前提必须该货主开启托管功能");
            }
            logicWarehouse.setOwnerPreSaleType(OwnerPreSaleType.YES);
            logicWarehouse.setLogicWarehouseCode(logicCode + "_YS");
            logicWarehouse.setLogicWarehouseName(logicWarehouse.getLogicWarehouseName() + "（预售仓）");
            logicWarehouse.setPreSaleRelatedLogicWarehouseCode(logicCode);
        } else {
            logicWarehouse.setOwnerPreSaleType(OwnerPreSaleType.YES);
            logicWarehouse.setOwnerCode(VIRTUAL_OWNER);
            logicWarehouse.setLogicWarehouseCode(logicCode + "_YS");
            logicWarehouse.setEntityWarehouseCode(VIRTUAL_ENTITY);
            logicWarehouse.setLogicWarehouseName(logicWarehouse.getLogicWarehouseName() + "（预售仓）");
            logicWarehouse.setPreSaleRelatedLogicWarehouseCode(logicCode);
        }

        if (logicWarehouse.getUserId() == null) {
            logicWarehouse.setUserId(SimpleUserHelper.getUserId());
        }
        logicWarehouse.setCreateTime(System.currentTimeMillis());
        logicWarehouseService.saveLogicWarehouse(logicWarehouse);
        createTenantRelation(logicWarehouse);
        return true;
    }

    private void createTenantRelation(LogicWarehouse logicWarehouse) {
        try {
            this.tenantRelationService.createRelation(EntityTypeEnum.LOGIC_WAREHOUSE_TYPE.getValue(), logicWarehouse.getLogicWarehouseCode(), SimpleTenantHelper.getTenantIdStr());
            if (org.apache.commons.lang.StringUtils.isNotBlank(logicWarehouse.getUpEntityWarehouseCode())) {
                this.tenantRelationService.createRelation(EntityTypeEnum.UP_ENTITY_WAREHOUSE_CODE_TYPE.getValue(), logicWarehouse.getUpEntityWarehouseCode(), SimpleTenantHelper.getTenantIdStr());
            }
        } catch (Throwable e) {
            log.error("createTenantRelation error", e);
        }
    }

    /**
     * 检查云仓名称是否重复
     *
     * @param logoWarehouseName
     * @return
     */
    private boolean checkLogicWarehouseName(Long userId, String logoWarehouseName) {
        return this.getLogicWarehouseByUserIdAndLogicWarehouseName(userId, logoWarehouseName) == null;
    }

    /**
     * 检查云仓名称是否重复
     *
     * @param logoWarehouseName
     * @return
     */
    private boolean checkLogicWarehouseName(String logoWarehouseName) {
        return this.getLogicWarehouseByUserIdAndLogicWarehouseName(SimpleUserHelper.getUserId(), logoWarehouseName) == null;
    }

    private LogicWarehouseBO getLogicWarehouseByUserIdAndLogicWarehouseName(Long userId, String logoWarehouseName) {
        LogicWarehouse warehouse = logicWarehouseService.getLogicWarehouseByUserIdAndLogicWarehouseName(userId, logoWarehouseName);
        return BeanUtils.copyProperties(warehouse, LogicWarehouseBO.class);
    }

    public Boolean syncInsert(LogicWarehouseBO logicWarehouseBO) {
        LogicWarehouse logicWarehouse = BeanUtils.copyProperties(logicWarehouseBO, LogicWarehouse.class);
        boolean ret = logicWarehouseService.saveLogicWarehouse(logicWarehouse);
        createTenantRelation(logicWarehouse);
        return ret;
    }

    /**
     * 获取城市首字母
     *
     * @param city
     * @return
     */
    public static String getCityWord(String city) {
        String convert = "";
        for (int j = 0; j < city.length(); j++) {
            char word = city.charAt(j);
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(word);
            if (pinyinArray != null) {
                convert += pinyinArray[0].charAt(0);
            } else {
                convert += word;
            }
        }
        return convert.toUpperCase();
    }

    /**
     * 分页查询
     *
     * @param logicWarehouseSearch
     * @return
     */
    @PageSelect
    public ListVO<LogicWarehouseBO> listLogicWarehouseByPage(LogicWarehouseSearch logicWarehouseSearch) {
        List<LogicWarehouse> logicWarehouseList = logicWarehouseService.listLogicWarehouseByPage(logicWarehouseSearch);
        ListVO<LogicWarehouseBO> listVO = new ListVO<>();
        List<LogicWarehouseBO> logicWarehouseBOList = BeanUtils.copyProperties(logicWarehouseList, LogicWarehouseBO.class);
        listVO.setDataList(logicWarehouseBOList);
        return listVO;
    }

    /**
     * 根据id获取逻辑仓详情
     *
     * @param id
     * @return
     */
    public LogicWarehouseResult getDetailById(Long id) {
        LogicWarehouse logicWarehouse = logicWarehouseService.getDetailById(id);
        if (logicWarehouse != null) {
            LogicWarehouseResult logicWarehouseResult = BeanUtils.copyProperties(logicWarehouse, LogicWarehouseResult.class);
            return logicWarehouseResult;
        } else {
            return null;
        }
    }

    public List<LogicWarehouseBO> getListByCodeSet(Set<String> logicWarehouseCodeSet, Long userId) {
        List<LogicWarehouse> logicWarehouseList = logicWarehouseService.getLogicWarehouseListByCodes(logicWarehouseCodeSet, userId);
        return BeanUtils.copyProperties(logicWarehouseList, LogicWarehouseBO.class);
    }

    public LogicWarehouseBO getDetailByUserIdAndCode(Long userId, String logicWarehouseCode) {
        LogicWarehouse logicWarehouse = logicWarehouseService.getDetailByCode(userId, logicWarehouseCode);
        if (logicWarehouse != null) {
            LogicWarehouseBO logicWarehouseBO = BeanUtils.copyProperties(logicWarehouse, LogicWarehouseBO.class);
            return logicWarehouseBO;
        } else {
            return null;
        }
    }

    /**
     * 优化接口
     *
     * @param
     * @return
     */
    public EntityWarehouseBO getEntityDetailByLogicCodeV2(LogicWarehouseBO logicWarehouse) {
        String warehouseCode = logicWarehouse.getEntityWarehouseCode();
        if (warehouseCode.equals(VIRTUAL_ENTITY)) {
            EntityWarehouse entityWarehouse = new EntityWarehouse();
            entityWarehouse.setLinkman(logicWarehouse.getLinkman());
            entityWarehouse.setEntityWarehouseName(logicWarehouse.getLogicWarehouseName());
            entityWarehouse.setTelephone(logicWarehouse.getTelephone());
            entityWarehouse.setProvinceCode(logicWarehouse.getProvinceCode());
            entityWarehouse.setProvince(logicWarehouse.getProvince());
            entityWarehouse.setCityCode(logicWarehouse.getZoneCode());
            entityWarehouse.setCity(logicWarehouse.getCity());
            entityWarehouse.setZone(logicWarehouse.getZone());
            entityWarehouse.setZoneCode(logicWarehouse.getZoneCode());
            entityWarehouse.setAddress(logicWarehouse.getAddress());
            entityWarehouse.setTradeType(logicWarehouse.getTradeType());
            entityWarehouse.setEntityWarehouseCode(VIRTUAL_ENTITY);
            entityWarehouse.setWarehouseCode(VIRTUAL_ENTITY);
            entityWarehouse.setSystemCode(VIRTUAL_SYSTEM_CODE);
            EntityWarehouseBO entityWarehouseBO = BeanUtils.copyProperties(entityWarehouse, EntityWarehouseBO.class);
            return entityWarehouseBO;
        } else {
            EntityWarehouse entityWarehouse = iEntityWarehouseService.getEntityByEntityCode(warehouseCode);
            EntityWarehouseBO entityWarehouseBO = BeanUtils.copyProperties(entityWarehouse, EntityWarehouseBO.class);
            entityWarehouseBO.setProvince(logicWarehouse.getProvince());
            entityWarehouseBO.setProvinceCode(logicWarehouse.getProvinceCode());
            entityWarehouseBO.setCity(logicWarehouse.getCity());
            entityWarehouseBO.setCityCode(logicWarehouse.getCityCode());
            entityWarehouseBO.setZone(logicWarehouse.getZone());
            entityWarehouseBO.setZoneCode(logicWarehouse.getZoneCode());
            entityWarehouseBO.setAddress(logicWarehouse.getAddress());
            return entityWarehouseBO;
        }
    }

    /**
     * 根据逻辑仓编码获取逻辑仓详情
     *
     * @param logicWarehouseCode
     * @return
     */
    public LogicWarehouseBO getDetailByCode(String logicWarehouseCode) {
        LogicWarehouse logicWarehouse = logicWarehouseService.getDetailByCode(logicWarehouseCode);
        if (logicWarehouse != null) {
            LogicWarehouseBO logicWarehouseBO = BeanUtils.copyProperties(logicWarehouse, LogicWarehouseBO.class);
            return logicWarehouseBO;
        } else {
            return null;
        }
    }

    public EntityWarehouseBO getEntityDetailByLogicCode(String logicWarehouseCode) {
        log.debug("getEntityDetailByLogicCode logicWarehouseCode入参============{}", logicWarehouseCode);
        LogicWarehouse logicWarehouse = logicWarehouseService.getDetailByCode(logicWarehouseCode);
        if (logicWarehouse == null) {
            throw new BusinessException("逻辑仓不存在!");
        }
        String ownerCode = logicWarehouse.getOwnerCode();
        String warehouseCode = logicWarehouse.getEntityWarehouseCode();
        //实体仓编码为空,就查询大货主
        if (org.apache.commons.lang3.StringUtils.isBlank(warehouseCode)) {
            Owner owner = iOwnerService.selectOneByOwnerCode(logicWarehouse.getOwnerCode());
            if (Objects.isNull(owner)) {
                throw new BusinessException("仓库货主信息不存在!");
            }
            warehouseCode = owner.getEntityWarehouseCode();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(owner.getParentOwnerCode())) {
                owner = iOwnerService.selectOneByOwnerCode(owner.getParentOwnerCode());
                if (Objects.isNull(owner)) {
                    throw new BusinessException("仓库大货主信息不存在!");
                }
                warehouseCode = owner.getEntityWarehouseCode();
                ownerCode = owner.getOwnerCode();
            }
        }
        if (warehouseCode.equals(VIRTUAL_ENTITY)) {
            EntityWarehouse entityWarehouse = new EntityWarehouse();
            entityWarehouse.setLinkman(logicWarehouse.getLinkman());
            entityWarehouse.setEntityWarehouseName(logicWarehouse.getLogicWarehouseName());
            entityWarehouse.setTelephone(logicWarehouse.getTelephone());
            entityWarehouse.setProvinceCode(logicWarehouse.getProvinceCode());
            entityWarehouse.setProvince(logicWarehouse.getProvince());
            entityWarehouse.setCityCode(logicWarehouse.getZoneCode());
            entityWarehouse.setCity(logicWarehouse.getCity());
            entityWarehouse.setZone(logicWarehouse.getZone());
            entityWarehouse.setZoneCode(logicWarehouse.getZoneCode());
            entityWarehouse.setAddress(logicWarehouse.getAddress());
            entityWarehouse.setTradeType(logicWarehouse.getTradeType());
            entityWarehouse.setEntityWarehouseCode(VIRTUAL_ENTITY);
            entityWarehouse.setWarehouseCode(VIRTUAL_ENTITY);
            entityWarehouse.setSystemCode(VIRTUAL_SYSTEM_CODE);
            EntityWarehouseBO entityWarehouseBO = BeanUtils.copyProperties(entityWarehouse, EntityWarehouseBO.class);
            entityWarehouseBO.setOwnerCode(ownerCode);
            return entityWarehouseBO;
        } else {
            EntityWarehouse entityWarehouse = iEntityWarehouseService.getEntityByEntityCode(warehouseCode);
            EntityWarehouseBO entityWarehouseBO = BeanUtils.copyProperties(entityWarehouse, EntityWarehouseBO.class);
            entityWarehouseBO.setProvince(logicWarehouse.getProvince());
            entityWarehouseBO.setProvinceCode(logicWarehouse.getProvinceCode());
            entityWarehouseBO.setCity(logicWarehouse.getCity());
            entityWarehouseBO.setCityCode(logicWarehouse.getCityCode());
            entityWarehouseBO.setZone(logicWarehouse.getZone());
            entityWarehouseBO.setZoneCode(logicWarehouse.getZoneCode());
            entityWarehouseBO.setAddress(logicWarehouse.getAddress());
            entityWarehouseBO.setOwnerCode(ownerCode);
            return entityWarehouseBO;
        }
    }

    /**
     * 用户web端逻辑仓列表
     *
     * @return
     */
    public List<LogicWarehouseBO> listLogicWarehouseByUserId() {
        List<LogicWarehouse> logicWarehouseList = logicWarehouseService.listLogicWarehouseByUserId();
        List<LogicWarehouseBO> logicWarehouseBOList = BeanUtils.copyProperties(logicWarehouseList, LogicWarehouseBO.class);
        return logicWarehouseBOList;
    }

    public List<LogicWarehouseBO> queryBySearch(LogicWarehouseQueryParam param) {
        LogicWarehouseSearch search = BeanUtils.copyProperties(param, LogicWarehouseSearch.class);
        List<LogicWarehouse> logicWarehouseList = logicWarehouseService.queryBySearch(search);
        List<LogicWarehouseBO> logicWarehouseBOList = BeanUtils.copyProperties(logicWarehouseList, LogicWarehouseBO.class);
        return logicWarehouseBOList;
    }

    public List<LogicWarehouseBO> listLogicTradeByUserId() {
        List<LogicWarehouse> logicWarehouseList = logicWarehouseService.listLogicTradeByUserId();
        List<LogicWarehouseBO> logicWarehouseBOList = new ArrayList<>();
        if (logicWarehouseList.size() != 0) {
            List<LogicWarehouseBO> logicWarehouseVOList = BeanUtils.copyProperties(logicWarehouseList, LogicWarehouseBO.class);
            for (LogicWarehouseBO logicWarehouseBO : logicWarehouseVOList) {
                LogicWarehouseBO logicTradeListBO = new LogicWarehouseBO();
                Owner owner = iOwnerService.getOwnerNameByCode(logicWarehouseBO.getOwnerCode());
                if (owner != null) {
                    logicTradeListBO.setOwnerName(owner.getOwnerName());
                    EntityWarehouse entityWarehouse = iEntityWarehouseService.getEntityByEntityCode(owner.getEntityWarehouseCode());
                    EntityWarehouseBO entityWarehouseVO = BeanUtils.copyProperties(entityWarehouse, EntityWarehouseBO.class);
                    if (entityWarehouseVO != null) {
                        logicTradeListBO.setLogicWarehouseCode(logicWarehouseBO.getLogicWarehouseCode());
                        logicTradeListBO.setLogicWarehouseName(logicWarehouseBO.getLogicWarehouseName());
                        logicTradeListBO.setTradeTypeName(entityWarehouseVO.getTradeType().getDesc());
                        logicTradeListBO.setTradeType(entityWarehouseVO.getTradeType());
                        logicTradeListBO.setTradeTypeCode(entityWarehouseVO.getTradeType().getValue());
                        logicTradeListBO.setAccountCode(logicWarehouseBO.getAccountCode());
                        logicTradeListBO.setIsBatch(logicWarehouseBO.getIsBatch());
                        logicTradeListBO.setIsTrusteeship(logicWarehouseBO.getIsTrusteeship());
                        logicTradeListBO.setOpenStatus(logicWarehouseBO.getOpenStatus());
                        logicWarehouseBOList.add(logicTradeListBO);
                    }
                } else {
                    if ((LogicWarehouseType.LOGIC).equals(logicWarehouseBO.getLogicWarehouseType())) {
                        logicTradeListBO.setLogicWarehouseCode(logicWarehouseBO.getLogicWarehouseCode());
                        logicTradeListBO.setLogicWarehouseName(logicWarehouseBO.getLogicWarehouseName());
                        logicTradeListBO.setTradeTypeName(TradeType.DUTY_PAID.getDesc());
                        logicTradeListBO.setTradeType(TradeType.DUTY_PAID);
                        logicTradeListBO.setTradeTypeCode(TradeType.DUTY_PAID.getValue());
                        logicTradeListBO.setAccountCode(logicWarehouseBO.getAccountCode());
                        logicTradeListBO.setIsBatch(logicWarehouseBO.getIsBatch());
                        logicTradeListBO.setIsTrusteeship(logicWarehouseBO.getIsTrusteeship());
                        logicTradeListBO.setOpenStatus(logicWarehouseBO.getOpenStatus());
                        logicWarehouseBOList.add(logicTradeListBO);
                    }
                }
            }
            return logicWarehouseBOList;
        } else {
            return null;
        }
    }

    /**
     * 新增出入库单下拉收货仓库
     *
     * @param orderNo
     * @param type
     * @return
     */
    public List<LogicWarehouseResult> listLogicWarehouseByInOrder(String orderNo, Integer type) {
        //type =1 为采购单  2为调拨单
        //如果入库单为保税 去搜备货单中的关联仓库
        if (type == 1) {
            PurchaseOrderResult purchaseOrderResult = iPurchaseOrderFacade.getPurchaseOrderByNo(orderNo);
            if (purchaseOrderResult == null) {
                throw new BusinessException("采购单为空");
            }
            GoodsType goodsType = purchaseOrderResult.getType();
            if (goodsType.equals(GoodsType.BONDED)) {
//                ReadyOrderQueryParam readyOrderQueryParam = new ReadyOrderQueryParam();
//                readyOrderQueryParam.setRelatedNo(orderNo);
//                // 不包含作废的订单
//                readyOrderQueryParam.setIsCancel(2);
//                ReadyOrderResult readyOrderResult = iReadyOrderFacade.getReadyOrderByReadyOrderQueryParam(readyOrderQueryParam);
//                List<LogicWarehouse> logicWarehouseList = new ArrayList<>();
//                if (readyOrderResult != null) {
//                    LogicWarehouse logicWarehouse = new LogicWarehouse();
//                    logicWarehouse.setLogicWarehouseCode(readyOrderResult.getLogicWarehouseCode());
//                    LogicWarehouse logicWarehouse1 = logicWarehouseService.getDetailByCode(readyOrderResult.getLogicWarehouseCode());
//                    if (logicWarehouse1 != null) {
//                        logicWarehouse.setLogicWarehouseName(logicWarehouse1.getLogicWarehouseName());
//                    } else {
//                        throw new BusinessException("逻辑仓不存在");
//                    }
//                    logicWarehouse.setLogicWarehouseType(logicWarehouse1.getLogicWarehouseType());
//                    logicWarehouseList.add(logicWarehouse);
//                } else {
//                    throw new BusinessException("未找到关联的备货单,请配货再入库");
//                }
                List<LogicWarehouse> logicWarehouseList = new ArrayList<>();
                LogicWarehouse logicWarehouse = logicWarehouseService.getDetailByCode(purchaseOrderResult.getLogicWarehouseCode());
                if (logicWarehouse == null) {
                    throw new BusinessException("逻辑仓不存在");
                }
                logicWarehouseList.add(logicWarehouse);
                return BeanUtils.copyProperties(logicWarehouseList, LogicWarehouseResult.class);
            } else {
                //新增入库单 采购入库时仓库只拉取虚拟仓+未托管（强控成本）
                LogicWarehouseSearch logicWarehouseSearch = new LogicWarehouseSearch();
                logicWarehouseSearch.setUserId(SimpleUserHelper.getUserId());
                logicWarehouseSearch.setTradeType(TradeType.DUTY_PAID);
                logicWarehouseSearch.setIsTrusteeship(TrusteeshipType.NO);
                List<LogicWarehouse> logicWarehouseList = logicWarehouseService.listLogicByOrderNo(logicWarehouseSearch);

                LogicWarehouseSearch logicWarehouseSearch1 = new LogicWarehouseSearch();
                logicWarehouseSearch1.setUserId(SimpleUserHelper.getUserId());
                logicWarehouseSearch1.setLogicWarehouseType(LogicWarehouseType.LOGIC);
                List<LogicWarehouse> logicWarehouseList1 = logicWarehouseService.listLogicByOrderNo(logicWarehouseSearch1);

                logicWarehouseList.addAll(logicWarehouseList1);
                return BeanUtils.copyProperties(logicWarehouseList, LogicWarehouseResult.class);
            }
            //调拨
        } else if (Objects.equals(type, 2)) {
            TransferOrderQueryParam param = new TransferOrderQueryParam();
            param.setTransferNo(orderNo);
            TransferOrderResult result = iTransferOrderFacade.getTransferOrderByTransferOrderQueryParam(param);
            LogicWarehouse logicWarehouse = logicWarehouseService.getDetailByCode(result.getToWarehouseCode());
            if (Objects.isNull(logicWarehouse)) {
                throw new BusinessException("逻辑仓不存在");
            }
            LogicWarehouseResult logicWarehouseResult = BeanUtils.copyProperties(logicWarehouse, LogicWarehouseResult.class);
            List<LogicWarehouseResult> resultList = new ArrayList<>();
            resultList.add(logicWarehouseResult);
            return resultList;
        } else if (Objects.equals(type, 3)) {
            throw new BusinessException("单据类型不合法");
            //其他入库
        } else if (Objects.equals(type, 4)) {
            LogicWarehouseSearch search = new LogicWarehouseSearch();
            //全链路支持创建其他入库,兼容老的非强控成本仓
            List<TrusteeshipType> trusteeshipTypeList = new ArrayList<>();
            trusteeshipTypeList.add(TrusteeshipType.NO);
            trusteeshipTypeList.add(TrusteeshipType.NO_NO_PRICE);
//            search.setTrusteeshipTypeList(trusteeshipTypeList);
            search.setUserId(SimpleUserHelper.getUserId());
            List<LogicWarehouse> logicWarehouseList = logicWarehouseService.listLogicWarehouseBySearch(search);
            List<LogicWarehouseResult> logicWarehouseResult = BeanUtils.copyProperties(logicWarehouseList, LogicWarehouseResult.class);
            //酱酱说其他出入库不要虚拟仓！！！！！
//            LogicWarehouseSearch search1 = new LogicWarehouseSearch();
//            //虚拟仓
//            search1.setLogicWarehouseType(LogicWarehouseType.LOGIC);
//            List<LogicWarehouse> logicWarehouseList1 = logicWarehouseService.listLogicWarehouseBySearch(search1);
//            List<LogicWarehouseResult> logicWarehouseResult1 = BeanUtils.copyProperties(logicWarehouseList1, LogicWarehouseResult.class);
//            if (!CollectionUtils.isEmpty(logicWarehouseResult)) {
//                logicWarehouseResult.addAll(logicWarehouseResult1);
//            }
            return logicWarehouseResult;
        } else if (Objects.equals(type, 15)) {
            LogicWarehouseSearch search = new LogicWarehouseSearch();
            //拉所有仓库，排除虚拟仓
//            search.setTradeType(TradeType.DUTY_PAID);
            search.setLogicWarehouseType(LogicWarehouseType.ENTITY);
            search.setUserId(SimpleUserHelper.getUserId());
            List<LogicWarehouse> logicWarehouseList = logicWarehouseService.listLogicWarehouseBySearch(search);
            List<LogicWarehouseResult> logicWarehouseResult = BeanUtils.copyProperties(logicWarehouseList, LogicWarehouseResult.class);
            return logicWarehouseResult;
        } else {
            throw new BusinessException("单据类型不合法");
        }
    }

    public List<LogicWarehouseResult> listLogicWarehouseByTradType(TradeType type) {
        LogicWarehouseSearch logicWarehouseSearch = new LogicWarehouseSearch();
        logicWarehouseSearch.setTradeType(type);
        logicWarehouseSearch.setUserId(SimpleUserHelper.getUserId());
        List<LogicWarehouse> logicWarehouseList = logicWarehouseService.listLogicWarehouseByTradType(logicWarehouseSearch);
        LogicWarehouseSearch logicWarehouseSearch1 = new LogicWarehouseSearch();
        logicWarehouseSearch1.setUserId(SimpleUserHelper.getUserId());
        logicWarehouseSearch1.setLogicWarehouseType(LogicWarehouseType.LOGIC);
        List<LogicWarehouse> logicWarehouseList1 = logicWarehouseService.listLogicByOrderNo(logicWarehouseSearch1);
        logicWarehouseList.addAll(logicWarehouseList1);
        List<LogicWarehouseResult> logicWarehouseResultList = BeanUtils.copyProperties(logicWarehouseList, LogicWarehouseResult.class);
        return logicWarehouseResultList;
    }

    public List<LogicWarehouseResult> listLogicWarehouseByTradTypeByAdmin(TradeType type) {
        LogicWarehouseSearch logicWarehouseSearch = new LogicWarehouseSearch();
        logicWarehouseSearch.setTradeType(type);
        List<LogicWarehouse> logicWarehouseList = logicWarehouseService.listLogicWarehouseByTradType(logicWarehouseSearch);
        List<LogicWarehouseResult> logicWarehouseResultList = BeanUtils.copyProperties(logicWarehouseList, LogicWarehouseResult.class);
        return logicWarehouseResultList;
    }

    public Boolean openOrClosed(Long id) {
        return logicWarehouseService.openOrClosed(id);
    }

    public void checkParam(LogicWarehouseBO bo) {
        if (bo.getLogicWarehouseType() == LogicWarehouseType.ENTITY) {
            if (StringUtils.isEmpty(bo.getOwnerCode())) {
                throw new BusinessException("货主编码不能为空");
            }
            if (bo.getTradeType() == null) {
                throw new BusinessException("贸易类型不能为空");
            }
//            if (Objects.equals(bo.getTradeType(), TradeType.BONDED) && StringUtils.isEmpty(bo.getDeclareCompanyCode())) {
//                throw new BusinessException("清关企业不能为空");
//            }
//            if (Objects.equals(bo.getTradeType(), TradeType.BONDED) && StringUtils.isEmpty(bo.getAccountCode())) {
//                throw new BusinessException("账册号不能为空");
//            }
        }
    }

    public List<LogicWarehouseResult> listLogicWarehouseBySearch(LogicWarehouseSearch search) {
        return BeanUtils.copyProperties(logicWarehouseService.listLogicWarehouseBySearch(search), LogicWarehouseResult.class);
    }

    public List<LogicWarehouseResult> listBondedWarehouseByAdmin(LogicWarehouseSearch search) {
        return BeanUtils.copyProperties(logicWarehouseService.listBondedWarehouseByAdmin(search), LogicWarehouseResult.class);
    }

    /**
     * 生成云仓编码
     *
     * @param city
     * @param ownerCode
     * @return
     */
    public String buildNewLogicCode(String city, String ownerCode) {
        return getCityWord(city) + UUID.randomUUID().toString().substring(0, 6) + ownerCode;
    }

}
