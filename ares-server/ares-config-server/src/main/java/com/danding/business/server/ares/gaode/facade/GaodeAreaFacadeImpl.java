package com.danding.business.server.ares.gaode.facade;

import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.gaode.facade.IGaodeAreaFacade;
import com.danding.business.client.ares.gaode.param.GaodeAreaQueryParam;
import com.danding.business.client.ares.gaode.result.*;
import com.danding.business.server.ares.gaode.manager.GaodeAreaManager;
import com.danding.business.core.ares.gaode.search.GaodeAreaSearch;
import com.danding.business.server.ares.gaode.BO.GaodeAreaBO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.common.utils.EnumUtils;
import com.danding.component.common.utils.StringUtils;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.DubboService;
import tk.mybatis.mapper.util.StringUtil;


import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-28
 */
@DubboService
public class GaodeAreaFacadeImpl implements IGaodeAreaFacade {


    @Autowired
    private GaodeAreaManager gaodeAreaManager;


    @Override
    public boolean addGaodeArea() {
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
                .url("https://restapi.amap.com/v3/config/district?key=221fb29d19d40e6193ab055af7066301&output=JSON&subdistrict=3")
                .build();
        try (Response response = client.newCall(request).execute()) {
            WebGaoDeResponse gaoDeResponse = JSON.parseObject(response.body().string(), WebGaoDeResponse.class);
            //国家
            for (CountryDistricts districts : gaoDeResponse.getDistricts()) {
                GaodeAreaBO countryAreaBO = BeanUtils.copyProperties(districts, GaodeAreaBO.class);
                countryAreaBO.setParentAdcode("-1");
                gaodeAreaManager.add(countryAreaBO);
            }
            //省份
            List<ProvinceDistricts> provinceDistrictsList = gaoDeResponse.getDistricts().get(0).getDistricts();
            for (ProvinceDistricts provinceDistricts : provinceDistrictsList) {
                GaodeAreaBO provinceBO = BeanUtils.copyProperties(provinceDistricts, GaodeAreaBO.class);
                //中国adcode
                provinceBO.setParentAdcode(gaoDeResponse.getDistricts().get(0).getAdcode());
                gaodeAreaManager.add(provinceBO);
                // 市
                for (CityDistricts cityDistricts : provinceDistricts.getDistricts()) {
                    if (cityDistricts.getLevel().equals("street")) {
                        continue;
                    }
                    GaodeAreaBO cityBo = BeanUtils.copyProperties(cityDistricts, GaodeAreaBO.class);
                    cityBo.setParentAdcode(provinceDistricts.getAdcode());
                    gaodeAreaManager.add(cityBo);
                    for (DistrictDistrict districtDistrict : cityDistricts.getDistricts()) {
                        if (districtDistrict.getLevel().equals("street")) {
                            continue;
                        }
                        GaodeAreaBO districtBO = BeanUtils.copyProperties(districtDistrict, GaodeAreaBO.class);
                        districtBO.setParentAdcode(cityDistricts.getAdcode());
                        gaodeAreaManager.add(districtBO);
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return true;

    }

    @Override
    public boolean listNoChildArea() {
        GaodeAreaSearch search = new GaodeAreaSearch();
        search.setLevel("province");
        List<String> noChildAreaList = new ArrayList<>();
        Map<String, GaodeAreaBO> map = new HashMap<>();
        List<GaodeAreaBO> gaodeAreaBOList = gaodeAreaManager.listBySearch(search);
        for (GaodeAreaBO areaBO : gaodeAreaBOList) {
            GaodeAreaSearch search1 = new GaodeAreaSearch();
            search1.setParentAdcode(areaBO.getAdcode());
            search1.setLevel("city");
            List<GaodeAreaBO> boList = gaodeAreaManager.listBySearch(search1);
            if (boList.size() == 0) {
                map.put(areaBO.getAdcode(), areaBO);
                noChildAreaList.add(areaBO.getAdcode());
            }
        }
        //不包含市————省级别默然添加一个市级地区
        for (String areaNo : noChildAreaList) {
            GaodeAreaBO gaodeAreaBO = new GaodeAreaBO();
            gaodeAreaBO.setLevel("city");
            gaodeAreaBO.setParentAdcode(areaNo);
            gaodeAreaBO.setAdcode(areaNo + "X");
            gaodeAreaBO.setCenter(map.get(areaNo).getCenter());
            gaodeAreaBO.setName(map.get(areaNo).getName());
            gaodeAreaManager.add(gaodeAreaBO);
        }


        //不包含区————市级别下面默认添加一个区
        GaodeAreaSearch citySearch = new GaodeAreaSearch();
        citySearch.setLevel("city");
        Map<String, GaodeAreaBO> cityMap = new HashMap<>();
        List<GaodeAreaBO> gaodeCityAreaBOList = gaodeAreaManager.listBySearch(citySearch);
        for (GaodeAreaBO gaodeAreaBO : gaodeCityAreaBOList) {
            GaodeAreaSearch search1 = new GaodeAreaSearch();
            search1.setLevel("district");
            search1.setParentAdcode(gaodeAreaBO.getAdcode());
            List<GaodeAreaBO> boList = gaodeAreaManager.listBySearch(search1);
            if (boList.size() == 0) {
                cityMap.put(gaodeAreaBO.getAdcode(), gaodeAreaBO);
            }
        }

        for (String areaNo : cityMap.keySet()) {
            GaodeAreaBO gaodeAreaBO = new GaodeAreaBO();
            gaodeAreaBO.setLevel("district");
            gaodeAreaBO.setParentAdcode(areaNo);
            gaodeAreaBO.setAdcode(areaNo + "X");
            gaodeAreaBO.setCenter(cityMap.get(areaNo).getCenter());
            gaodeAreaBO.setName(cityMap.get(areaNo).getName());
            gaodeAreaManager.add(gaodeAreaBO);
        }
        return false;
    }

    @Override
    public boolean fixLevel() {
        GaodeAreaSearch search = new GaodeAreaSearch();
        search.setLevel("province");
        List<GaodeAreaBO> gaodeAreaBOList = gaodeAreaManager.listBySearch(search);
        for (GaodeAreaBO areaBO : gaodeAreaBOList) {
            GaodeAreaSearch search1 = new GaodeAreaSearch();
            search1.setParentAdcode(areaBO.getAdcode());
            search1.setLevel("district");
            //省下面直接是区的  将区的父类转换成市的code
            List<GaodeAreaBO> gaodeAreaBOList1 = gaodeAreaManager.listBySearch(search1);
            if (gaodeAreaBOList1.size() != 0) {
                GaodeAreaSearch search2 = new GaodeAreaSearch();
                search2.setParentAdcode(areaBO.getAdcode());
                search2.setLevel("city");
                List<GaodeAreaBO> gaodeAreaBOList2 = gaodeAreaManager.listBySearch(search2);
                GaodeAreaBO gaodeAreaBO = gaodeAreaBOList2.get(0);
                String cityCode = gaodeAreaBO.getAdcode();
                for (GaodeAreaBO bo : gaodeAreaBOList1) {
                    bo.setParentAdcode(cityCode);
                }
                gaodeAreaManager.updateListById(gaodeAreaBOList1);
            } else {
            }
        }
        return true;
    }

    @Override
    public List<GaodeAreaResult> queryArea(GaodeAreaQueryParam queryParam) {
        GaodeAreaSearch search = BeanUtils.copyProperties(queryParam, GaodeAreaSearch.class);
        List<GaodeAreaBO> gaodeAreaBOList = gaodeAreaManager.listBySearch(search);
        return BeanUtils.copyProperties(gaodeAreaBOList, GaodeAreaResult.class);
    }


    @Override
    public List<EnumUtils> getChildArea(String adcode) {
        if (StringUtil.isEmpty(adcode)) {
            GaodeAreaSearch search = new GaodeAreaSearch();
            search.setParentAdcode("100000");
            List<GaodeAreaBO> gaodeAreaBOList = gaodeAreaManager.listBySearch(search);
            List<EnumUtils> enumUtilsList = EnumUtils.build(gaodeAreaBOList, "adcode", "name");
            return enumUtilsList;
        } else {
            GaodeAreaSearch search = new GaodeAreaSearch();
            search.setAdcode(adcode);
            GaodeAreaBO gaodeAreaBO = gaodeAreaManager.getBySearch(search);
            String parentAdcode = gaodeAreaBO.getAdcode();
            GaodeAreaSearch search1 = new GaodeAreaSearch();
            search1.setParentAdcode(parentAdcode);
            List<GaodeAreaBO> gaodeAreaBOList = gaodeAreaManager.listBySearch(search1);
            List<EnumUtils> enumUtilsList = EnumUtils.build(gaodeAreaBOList, "adcode", "name");
            return enumUtilsList;
        }
    }

/*
    @Override
    public boolean queryArea(GaodeAreaQueryParam queryParam) {
        gaodeAreaManager
    }
*/


}
