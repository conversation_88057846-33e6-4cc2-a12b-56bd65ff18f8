package com.danding.business.server.ares.supplier.manager;

import com.danding.business.client.ares.goods.facade.IGoodsFacade;
import com.danding.business.client.ares.goods.result.GoodsResult;
import com.danding.business.core.ares.supplier.entity.SupplierGood;
import com.danding.business.core.ares.supplier.search.SupplierGoodSearch;
import com.danding.business.core.ares.supplier.service.ISupplierGoodService;
import com.danding.business.server.ares.supplier.BO.SupplierGoodBO;
import com.danding.business.server.ares.supplier.manager.helper.SupplierGoodManagerHelper;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-22
 */
@Slf4j
@Component
public class SupplierGoodManager {

    @Autowired
    private ISupplierGoodService supplierGoodService;

    @Autowired
    private SupplierGoodManagerHelper supplierGoodManagerHelper;


    @DubboReference
    private IGoodsFacade iGoodsFacade;

    /**
     * id查询单个
     *
     * @param id
     * @return
     */
    public SupplierGoodBO getSupplierGoodBOById(Serializable id) {
        return BeanUtils.copyProperties(supplierGoodService.selectById(id), SupplierGoodBO.class);
    }

    /**
     * 条件查询单个
     *
     * @param supplierGoodSearch
     * @return
     */
    public SupplierGoodBO getSupplierGoodBOBySupplierGoodSearch(SupplierGoodSearch supplierGoodSearch) {
        SupplierGood supplierGood = supplierGoodService.selectBySupplierGoodSearch(supplierGoodSearch);
        return BeanUtils.copyProperties(supplierGood, SupplierGoodBO.class);
    }

    /**
     * 列表查询
     *
     * @param supplierGoodSearch
     * @return
     */
    public List<SupplierGoodBO> listSupplierGoodBOBySupplierGoodSearch(SupplierGoodSearch supplierGoodSearch) {
        List<SupplierGood> supplierGoodList = supplierGoodService.selectListBySupplierGoodSearch(supplierGoodSearch);
        return BeanUtils.copyProperties(supplierGoodList, SupplierGoodBO.class);
    }

    /**
     * 分页查询
     *
     * @param supplierGoodSearch
     * @return
     */
    @PageSelect
    public ListVO<SupplierGoodBO> pageListSupplierGoodBOBySupplierGoodSearch(SupplierGoodSearch supplierGoodSearch) {
        ListVO<SupplierGoodBO> supplierGoodBOListVO = new ListVO<>();
        List<SupplierGood> supplierGoodList = supplierGoodService.selectListBySupplierGoodSearch(supplierGoodSearch);
        List<SupplierGoodBO> supplierGoodBOList = BeanUtils.copyProperties(supplierGoodList, SupplierGoodBO.class);
        return ListVO.build(supplierGoodBOListVO.getPage(), supplierGoodBOList);
    }

    /**
     * //先删后加 覆盖
     * 功能描述:  插入
     */
    public boolean addSupplierGood(SupplierGoodBO supplierGoodBO) {
        SupplierGoodSearch supplierGoodSearch = new SupplierGoodSearch();
        List<SupplierGood> supplierGoodList = new ArrayList<>();
        supplierGoodSearch.setSupplierCode(supplierGoodBO.getSupplierCode());

        List<SupplierGoodBO> supplierGoodBOList = listSupplierGoodBOBySupplierGoodSearch(supplierGoodSearch);
        Map<String, Long> map = new HashMap<>();
        for (SupplierGoodBO goodsBO : supplierGoodBOList) {
            map.put(goodsBO.getGoodsCode(), goodsBO.getCreateTime());
        }
        Set<String> codeList = map.keySet();
        removeSupplierGoodBySupplierGoodSearch(supplierGoodSearch);
        for (String goodCode : supplierGoodBO.getGoodsCodeString()) {
            SupplierGood supplierGood = new SupplierGood();
            if (codeList.contains(goodCode)) {
                Long createTime = map.get(goodCode);
                supplierGood.setCreateTime(createTime);
            }
            supplierGood.setGoodsCode(goodCode);
            supplierGood.setSupplierCode(supplierGoodBO.getSupplierCode());
            GoodsResult result = iGoodsFacade.getGoodsByParam(SimpleUserHelper.getUserId(), goodCode);
            if (result != null) {
                supplierGood.setGoodsType(result.getType());
                supplierGood.setGoodsName(result.getGoodsName());
                supplierGood.setSku(result.getSku());
            }
            supplierGoodList.add(supplierGood);
        }
        return supplierGoodService.insertList(supplierGoodList);
    }


    public boolean mixSupplierByCode(SupplierGoodBO supplierGoodBO) {
        SupplierGoodSearch supplierGoodSearch = new SupplierGoodSearch();
        supplierGoodSearch.setSupplierCode(supplierGoodBO.getSupplierCodeString().get(0));
        supplierGoodSearch.setGoodsCode(supplierGoodBO.getGoodsCode());
        supplierGoodSearch.setUserId(supplierGoodBO.getUserId());
        SupplierGood isExist = supplierGoodService.selectBySupplierGoodSearch(supplierGoodSearch);
        if (Objects.nonNull(isExist)) {
            log.warn(supplierGoodSearch.getSupplierCode() + ":" + supplierGoodSearch.getGoodsCode() + ":供应商合作货品已存在请勿重复添加！");
            return true;
        }
        List<String> supplierCodeList = supplierGoodBO.getSupplierCodeString();
        List<SupplierGood> supplierGoodList = new ArrayList<>();
        for (String supplierCode : supplierCodeList) {
            SupplierGood supplierGood = new SupplierGood();
            supplierGood.setGoodsCode(supplierGoodBO.getGoodsCode());
            supplierGood.setSupplierCode(supplierCode);
            supplierGood.setUserId(supplierGoodBO.getUserId());
            //采购次数/数量/金额默认为0
            supplierGood.setPurchaseNum(0);
            supplierGood.setPurchaseQty(0);
            supplierGood.setPurchasePrice(BigDecimal.ZERO);
            supplierGoodList.add(supplierGood);
        }
        for (SupplierGood good : supplierGoodList) {
            GoodsResult result = iGoodsFacade.getGoodsByParam(supplierGoodBO.getUserId(), good.getGoodsCode());
            if (result != null) {
                good.setGoodsType(result.getType());
                good.setGoodsName(result.getGoodsName());
                good.setSku(result.getSku());
            }

        }

        return supplierGoodService.insertList(supplierGoodList);
    }

    public boolean importSupplierGoods(SupplierGoodBO supplierGoodBO, Long userId) {
        SupplierGood supplierGood = new SupplierGood();
        supplierGood.setGoodsCode(supplierGoodBO.getGoodsCode());
        supplierGood.setSupplierCode(supplierGoodBO.getSupplierCode());
        GoodsResult result = iGoodsFacade.getGoodsByParam(userId, supplierGoodBO.getGoodsCode());
        if (result != null) {
            supplierGood.setGoodsType(result.getType());
            supplierGood.setGoodsName(result.getGoodsName());
        }
        return supplierGoodService.insert(supplierGood);

    }

    /**
     * 功能描述:  批量插入
     */
    public boolean addSupplierGoodList(List<SupplierGoodBO> supplierGoodBOList) {
        return supplierGoodService.insertList(BeanUtils.copyProperties(supplierGoodBOList, SupplierGood.class));
    }

    /**
     * 功能描述:  根据主键id修改
     */
    public boolean updateSupplierGoodById(SupplierGoodBO supplierGoodBO) {
        SupplierGood supplierGood = supplierGoodService.selectById(supplierGoodBO.getId());
        SupplierGoodBO supplierGoodBO1 = BeanUtils.copyProperties(supplierGood, SupplierGoodBO.class);
        supplierGoodBO1.setDiscountPrice(supplierGoodBO.getDiscountPrice());
        supplierGoodBO1.setDiscountRate(supplierGoodBO.getDiscountRate());
        return supplierGoodService.updateById(BeanUtils.copyProperties(supplierGoodBO1, SupplierGood.class));
    }

    /**
     * 功能描述:  根据主键id批量修改
     */
    public boolean updateSupplierGoodListById(List<SupplierGoodBO> supplierGoodBOList) {
        return supplierGoodService.updateListById(BeanUtils.copyProperties(supplierGoodBOList, SupplierGood.class));
    }

    /**
     * 功能描述:  根据条件修改
     */
    public boolean updateSupplierGoodListBySupplierGoodSearch(SupplierGoodSearch supplierGoodSearch, SupplierGoodBO supplierGoodBO) {
        return supplierGoodService.updateListBySupplierGoodSearch(supplierGoodSearch, BeanUtils.copyProperties(supplierGoodBO, SupplierGood.class));
    }

    /**
     * 功能描述:  根据主键id删除
     */
    public boolean removeSupplierGoodByIds(Serializable id) {
        return supplierGoodService.deleteById(id);
    }

    /**
     * 功能描述:  根据主键id批量删除
     */
    public boolean removeSupplierGoodByIds(List<Long> idList) {
        return supplierGoodService.deleteByIds(idList);
    }

    /**
     * 功能描述:  根据条件删除
     */
    public boolean removeSupplierGoodBySupplierGoodSearch(SupplierGoodSearch supplierGoodSearch) {
        return supplierGoodService.deleteBySupplierGoodSearch(supplierGoodSearch);
    }

}
