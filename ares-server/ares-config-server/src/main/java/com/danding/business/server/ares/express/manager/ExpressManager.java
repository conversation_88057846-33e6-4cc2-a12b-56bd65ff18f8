package com.danding.business.server.ares.express.manager;


import com.danding.business.client.ares.express.param.ExpressQueryParam;
import com.danding.business.core.ares.express.entity.Express;
import com.danding.business.core.ares.express.search.ExpressSearch;
import com.danding.business.core.ares.express.service.IExpressService;
import com.danding.business.server.ares.express.BO.ExpressBO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.soul.client.common.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 快递管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@Component
public class ExpressManager {

    @Autowired
    private IExpressService expressService;

    public List<ExpressBO> listExpress() {
        List<Express> expressList = expressService.listExpress();
        List<ExpressBO> logicWarehouseBOList = BeanUtils.copyProperties(expressList, ExpressBO.class);
        return logicWarehouseBOList;
    }

    public String getNameByCode(String code) {
        return expressService.getExpressNameByCode(code);
    }

    public String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            throw new BusinessException("名称不能为空!");
        }
        return expressService.getExpressCodeByName(name);
    }

    public ExpressBO getExpressByCode(String code) {
        Express express = expressService.getExpressByCode(code);
        return BeanUtils.copyProperties(express, ExpressBO.class);
    }

    public List<ExpressBO> queryBySearch(ExpressQueryParam param) {
        ExpressSearch search = BeanUtils.copyProperties(param, ExpressSearch.class);
        List<Express> expressList = expressService.queryBySearch(search);
        List<ExpressBO> expressBOList = BeanUtils.copyProperties(expressList, ExpressBO.class);
        return expressBOList;
    }

}
