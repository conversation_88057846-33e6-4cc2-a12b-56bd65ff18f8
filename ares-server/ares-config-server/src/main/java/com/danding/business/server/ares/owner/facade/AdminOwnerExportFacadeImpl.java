package com.danding.business.server.ares.owner.facade;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.danding.business.client.ares.owner.facade.IOwnerFacade;
import com.danding.business.client.ares.owner.param.OwnerQueryParam;
import com.danding.business.client.ares.owner.result.OwnerResult;
import com.danding.business.common.ares.excel.AbstractExcelExportSingleSheetServiceV2;
import com.danding.business.server.ares.owner.excel.AdminOwnerExportExcel;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component("adminOwnerExcelService")
public class AdminOwnerExportFacadeImpl extends AbstractExcelExportSingleSheetServiceV2<AdminOwnerExportExcel, OwnerQueryParam> {

    @Autowired
    private IOwnerFacade ownerFacade;

    @Override
    public void before(OwnerQueryParam searchParam) {

    }

    @Override
    public List<AdminOwnerExportExcel> getDataList(OwnerQueryParam searchParam) {
        ListVO<OwnerResult> ownerResultListVO = ownerFacade.listOwnerByPage(searchParam);
        // 设置总页数
        this.setTotalPage(ownerResultListVO.getPage().getTotalPage());
        List<OwnerResult> dataList = ownerResultListVO.getDataList();
        if (!CollectionUtils.isNotEmpty(dataList)) {
            return null;
        }
        List<AdminOwnerExportExcel> firstList = new ArrayList<>();
        dataList.stream().forEach(data -> {
            AdminOwnerExportExcel adminOwnerExportExcel = BeanUtils.copyProperties(data, AdminOwnerExportExcel.class);
            if (data.getCreateTime() != null && data.getCreateTime() > 0) {
                adminOwnerExportExcel.setCreateTime(DateUtil.formatDateTime(DateUtil.date(data.getCreateTime())));
            }
            if (data.getUpdateTime() != null && data.getUpdateTime() > 0) {
                adminOwnerExportExcel.setUpdateTime(DateUtil.formatDateTime(DateUtil.date(data.getUpdateTime())));
            }
            adminOwnerExportExcel.setFinancialOwner(Objects.equals(1, data.getIsFinancialOwner()) ? "是" : "否");
            if (Objects.nonNull(data.getFinanceModeType())) {
                if (Objects.equals(1, data.getFinanceModeType())) {
                    adminOwnerExportExcel.setFinanceModeTypeName("代采监管模式");
                } else if (Objects.equals(2, data.getFinanceModeType())) {
                    adminOwnerExportExcel.setFinanceModeTypeName("质押监管模式");
                }
            }
            firstList.add(adminOwnerExportExcel);
        });

        return firstList;
    }
}
