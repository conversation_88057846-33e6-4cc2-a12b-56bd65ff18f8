package com.danding.business.server.ares.consumables.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 结算单导出excel结构
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/2 09:13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SuppliersPcRelationShipImportExcel {
    /**
     * 在excel表中的行号：回传用
     */
    @ExcelIgnore
    private Integer lineNo;

    @NotBlank(message = "包耗材条码不能为空!")
    private String pcBarcode;

    /**
     * 供应商名称
     */
    @NotBlank(message = "供应商编码不能为空!")
    private String suppliersCode;



}
