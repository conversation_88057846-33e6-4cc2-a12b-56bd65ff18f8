package com.danding.business.server.ares.owner.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.business.client.ares.entitywarehouse.facade.IEntityWarehouseFacade;
import com.danding.business.client.ares.entitywarehouse.result.EntityWarehouseResult;
import com.danding.business.client.ares.flow.facade.IFlowFacade;
import com.danding.business.client.ares.flow.param.FlowStepParam;
import com.danding.business.client.ares.flow.param.TargetFlowAddParam;
import com.danding.business.client.ares.flow.result.TargetFlowResult;
import com.danding.business.client.ares.goods.facade.IGoodsFacade;
import com.danding.business.client.ares.goods.param.GoodsQueryParam;
import com.danding.business.client.ares.goods.result.GoodsResult;
import com.danding.business.client.ares.goodsManagement.facade.IGoodsManagementFacade;
import com.danding.business.client.ares.goodsManagement.param.GoodsManagementQueryParam;
import com.danding.business.client.ares.goodsManagement.result.GoodsManagementResult;
import com.danding.business.client.ares.inventory.result.GoodsBatchInventoryResult;
import com.danding.business.client.ares.logicwarehouse.facade.ILogicWarehouseFacade;
import com.danding.business.client.ares.logicwarehouse.param.LogicWarehouseQueryParam;
import com.danding.business.client.ares.logicwarehouse.result.LogicWarehouseResult;
import com.danding.business.client.ares.mapping.facade.MappingFacade;
import com.danding.business.client.ares.mapping.param.MappingParam;
import com.danding.business.client.ares.mapping.param.out.OwnerMappingParam;
import com.danding.business.client.ares.mapping.result.MappingResult;
import com.danding.business.client.ares.newflow.facade.INewFlowFacade;
import com.danding.business.client.ares.newflow.message.NewFlowStatusChangeMessage;
import com.danding.business.client.ares.newflow.param.NewFlowAddParam;
import com.danding.business.client.ares.order.result.InOrderDetailResult;
import com.danding.business.client.ares.order.result.InOrderResult;
import com.danding.business.client.ares.order.result.OutOrderDetailResult;
import com.danding.business.client.ares.order.result.OutOrderResult;
import com.danding.business.client.ares.owner.result.GetEntityByOwnerResult;
import com.danding.business.client.ares.owner.result.OwnerGoodsSyncStatusResult;
import com.danding.business.client.ares.record.result.GoodsRecordResult;
import com.danding.business.common.ares.BO.report.OwnerCallbackResponse;
import com.danding.business.common.ares.BO.report.OwnerFail;
import com.danding.business.common.ares.BO.report.OwnerSuccess;
import com.danding.business.common.ares.context.Tag1Constant;
import com.danding.business.common.ares.enums.common.*;
import com.danding.business.common.ares.enums.flow.CandidateType;
import com.danding.business.common.ares.enums.newflow.FlowLogStatus;
import com.danding.business.common.ares.enums.newflow.FlowTypeEnum;
import com.danding.business.common.ares.enums.order.OutOrderType;
import com.danding.business.common.ares.enums.trade.ReadyBusinessType;
import com.danding.business.common.ares.utils.ConfigTagHelper;
import com.danding.business.common.ares.utils.DateUtils;
import com.danding.business.common.ares.utils.DstpSignUtils;
import com.danding.business.common.ares.utils.WarningUtils;
import com.danding.business.core.ares.entitywarehouse.entity.EntityWarehouse;
import com.danding.business.core.ares.entitywarehouse.service.IEntityWarehouseService;
import com.danding.business.core.ares.logicwarehouse.entity.LogicWarehouse;
import com.danding.business.core.ares.logicwarehouse.service.ILogicWarehouseService;
import com.danding.business.core.ares.owner.entity.Owner;
import com.danding.business.core.ares.owner.search.OwnerSearch;
import com.danding.business.core.ares.owner.service.IOwnerService;
import com.danding.business.server.ares.config.ErpNacosConfig;
import com.danding.business.server.ares.logicwarehouse.BO.LogicWarehouseBO;
import com.danding.business.server.ares.logicwarehouse.manager.LogicWarehouseManager;
import com.danding.business.server.ares.owner.BO.OwnerBO;
import com.danding.business.server.ares.owner.manager.helper.OwnerManagerHelper;
import com.danding.business.server.ares.owner.remote.RemoteInventoryFacade;
import com.danding.business.server.ares.owner.remote.RemoteOrderFacade;
import com.danding.business.server.ares.owner.remote.RemoteOwnerInfoFacade;
import com.danding.business.server.ares.supplier.remote.RemoteMappingFacade;
import com.danding.business.server.ares.suppliers.remote.RemoteGoodsFacade;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.danding.business.common.ares.context.AresContext.*;
import static com.danding.business.common.ares.utils.HttpRequestUtils.postJson;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@Component
@Slf4j
public class OwnerManager {

    @Autowired
    private IOwnerService ownerService;
    @Autowired
    private OwnerManagerHelper ownerManagerHelper;
    @Autowired
    private IEntityWarehouseService iEntityWarehouseService;
    @DubboReference
    private IEntityWarehouseFacade iEntityWarehouseFacade;
    @Autowired
    private LogicWarehouseManager logicWarehouseManager;
    @DubboReference
    private ILogicWarehouseFacade iLogicWarehouseFacade;
    @DubboReference
    private MappingFacade mappingFacade;
    @DubboReference
    private IGoodsFacade iGoodsFacade;
    @DubboReference
    private IGoodsManagementFacade iGoodsManagementFacade;
    @Autowired
    private RemoteMappingFacade remoteMappingFacade;
    @Autowired
    private ILogicWarehouseService logicWarehouseService;
    @DubboReference
    private IFlowFacade flowFacade;
    @DubboReference
    private INewFlowFacade newFlowFacade;
    @Autowired
    private RemoteOwnerInfoFacade remoteOwnerInfoFacade;
    @Autowired
    private RemoteOrderFacade remoteOrderFacade;
    @Autowired
    private RemoteInventoryFacade remoteInventoryFacade;
    @Autowired
    private RemoteGoodsFacade remoteGoodsFacade;
    @Autowired
    private ErpNacosConfig erpNacosConfig;

    public GetEntityByOwnerResult selectOneByOwnerCode(String ownerCode) {
        Owner owner = ownerService.selectOneByOwnerCode(ownerCode);
        if (owner != null) {
            //如果绑定了大货主,查询大货主信息
            if (org.apache.commons.lang3.StringUtils.isNotBlank(owner.getParentOwnerCode())) {
                owner = ownerService.selectOneByOwnerCode(owner.getParentOwnerCode());
            }
            String entityWarehouseCode = owner.getEntityWarehouseCode();
            EntityWarehouse entityWarehouse = iEntityWarehouseService.getEntityByEntityCode(entityWarehouseCode);
            GetEntityByOwnerResult getEntityByOwnerResult = BeanUtils.copyProperties(entityWarehouse, GetEntityByOwnerResult.class);
            getEntityByOwnerResult.setTradeTypeCode(getEntityByOwnerResult.getTradeType().getValue());
            getEntityByOwnerResult.setTradeTypeName(getEntityByOwnerResult.getTradeType().getDesc());
            getEntityByOwnerResult.setIsBatch(owner.getIsBatch());
            getEntityByOwnerResult.setIsTrusteeship(owner.getIsTrusteeship());
            return getEntityByOwnerResult;
        } else {
            return null;
        }
    }

    public List<OwnerBO> listOwnerByUserId(Long userId) {
        List<Owner> ownerList = ownerService.listOwnerByUserId(userId);
        List<OwnerBO> ownerBOList = BeanUtils.copyProperties(ownerList, OwnerBO.class);
        return ownerBOList;
    }


    public List<OwnerBO> listOwner() {
        List<Owner> ownerList = ownerService.listOwner();
        List<OwnerBO> ownerBOList = BeanUtils.copyProperties(ownerList, OwnerBO.class);
        return ownerBOList;
    }

    public List<OwnerBO> listEnableOwner() {
        List<Owner> ownerList = ownerService.listEnableOwner();
        List<OwnerBO> ownerBOList = BeanUtils.copyProperties(ownerList, OwnerBO.class);
        return ownerBOList;
    }


    public Boolean saveOwner(OwnerBO ownerBO) {
        String entityWarehouseCode;
        Integer operationType;
        //开启理货报告后，理货报告审核方式不能为空
        if (IsTallyType.IS_TALLY.equals(ownerBO.getIsTallyType())) {
            if (Objects.isNull(ownerBO.getTallReportAuditType())) {
                throw new BusinessException("开启理货报告后，理货报告审核方式不能为空");
            }
        } else {
            //未开启理货报告，理货报告审核方式为默认
            ownerBO.setTallReportAuditType(TallReportAuditType.DEFAULT);
            ownerBO.setReceiveContactGroupId(null);
            ownerBO.setCcContactGroupId(null);
        }

        if (StringUtils.isEmpty(ownerBO.getId())) {
            Owner owner = BeanUtils.copyProperties(ownerBO, Owner.class);
            if (StringUtils.isEmpty(owner.getOwnerCode())) {
                throw new BusinessException("请填写货主编码!");
            }
            Owner owner1 = ownerService.selectOneByOwnerCode(owner.getOwnerCode());
            if (Objects.nonNull(owner1)) {
                throw new BusinessException("货主编码重复!");
            }
            if (StringUtils.isEmpty(owner.getOwnerName())) {
                throw new BusinessException("请填写货主名称!");
            }
            OwnerSearch search = new OwnerSearch();
            search.setOwnerName(owner.getOwnerName());
            List<Owner> ownerList = ownerService.listOwnerByPage(search);
            if (!CollectionUtils.isEmpty(ownerList)) {
                throw new BusinessException("货主名称重复!");
            }
            //自建单，货主入驻状态为待审核
            if (Objects.isNull(owner.getOwnerEntryStatus())) {
                owner.setOwnerEntryStatus(OwnerEntryStatus.WAIT_APPROVAL);
            }
            //绑定实体仓
            if (OwnerMixType.MIX_ENTITY.equals(owner.getMixType())) {
                owner.setIsBigOwner(1);
                EntityWarehouse entityWarehouse = iEntityWarehouseService.getEntityByEntityCode(owner.getEntityWarehouseCode());
                if (Objects.isNull(entityWarehouse)) {
                    throw new BusinessException("实体仓不存在");
                }
                checkNonBonded(ownerBO.getNonBonded(), entityWarehouse);
                checkReturnEntryArea(ownerBO.getReturnEntryArea(), ownerBO.getTag1(), ownerBO.getUserName(), entityWarehouse);

                //金义保税5号仓，默认审核方式改为上游审核
                if (!StringUtils.isEmpty(erpNacosConfig.getUpstreamAuditEntityWarehouseCode())) {
                    String[] arrUpstreamAuditEntityWarehouseCode = erpNacosConfig.getUpstreamAuditEntityWarehouseCode().split(",");
                    if (Arrays.asList(arrUpstreamAuditEntityWarehouseCode).contains(owner.getEntityWarehouseCode())) {
                        owner.setIsTallyType(IsTallyType.IS_TALLY);
                        owner.setTallReportAuditType(TallReportAuditType.UPSTREAM_SYSTEM_AUDIT);
                        owner.setReceiveContactGroupId(null);
                        owner.setCcContactGroupId(null);
                    }
                }
                owner.setSystemCode(entityWarehouse.getSystemCode());
                if (Objects.equals(1, ownerBO.getIsFinancialOwner()) && Objects.equals(1, entityWarehouse.getFinanceType())) {
                    throw new BusinessException("所属实体仓未开启金融监管业务");
                }
                entityWarehouseCode = entityWarehouse.getEntityWarehouseCode();
                operationType = entityWarehouse.getOperationType();
            } else {
                //绑定大货主
                Owner bigOwner = ownerService.selectOneByOwnerCode(owner.getParentOwnerCode());
                if (Objects.isNull(bigOwner)) {
                    throw new BusinessException("大货主不存在");
                }
                //是否效期根据大货主来
                owner.setIsBatch(bigOwner.getIsBatch());
                owner.setIsBigOwner(0);
                EntityWarehouse entityWarehouse = iEntityWarehouseService.getEntityByEntityCode(bigOwner.getEntityWarehouseCode());
                if (Objects.isNull(entityWarehouse)) {
                    throw new BusinessException("大货主的实体仓不存在");
                }
                checkNonBonded(ownerBO.getNonBonded(), entityWarehouse);

                owner.setSystemCode(entityWarehouse.getSystemCode());
                owner.setEntityWarehouseCode(entityWarehouse.getEntityWarehouseCode());
                //检测菜鸟货主分库存属性
                checkCnInventory(bigOwner, owner);
                if (Objects.equals(1, ownerBO.getIsFinancialOwner()) && Objects.equals(1, entityWarehouse.getFinanceType())) {
                    throw new BusinessException("所属实体仓未开启金融监管业务");
                }
                entityWarehouseCode = entityWarehouse.getEntityWarehouseCode();
                operationType = entityWarehouse.getOperationType();
            }
            //金融货主不开启预售仓
            if (Objects.equals(1, owner.getIsFinancialOwner())) {
                owner.setOwnerPreSaleType(OwnerPreSaleType.NO);
                if (Objects.equals(3, ownerBO.getFinanceModeType())) {
                    if (!ownerBO.getSingleWh()) {
                        throw new BusinessException("存货质押,只支持单仓模式");
                    } else if (!Objects.equals(FinancialPlatformEnum.DT, ownerBO.getFinancialPlatform())) {
                        throw new BusinessException("存货质押,金融平台只支持【ERP内部】");
                    }
                }
            }
            if (ownerBO.getSingleWh()) {
                // 打标, 只能创建单个云仓
                owner.setTag1(ConfigTagHelper.openTag(ownerBO.getTag1(), Tag1Constant.SINGLE_WH));
            }
            if (!ownerService.saveOwner(owner)) {
                throw new BusinessException("新增货主失败");
            }
            //新增货主,判断是否需要加入风控
            if (Arrays.asList(erpNacosConfig.getRiskControlEntityWarehouseCode().split(",")).contains(entityWarehouseCode) && Objects.equals(1, operationType)) {
                //加入风控货主
                remoteOwnerInfoFacade.addRisk(ownerBO);
            }
            //如果是内部审核，则需要配置理货报告的审批流程
            addTallyReportTargetFlow(owner.getUserId(), owner.getTallReportAuditType());
            //如果是待审核
            if (OwnerEntryStatus.WAIT_APPROVAL.equals(owner.getOwnerEntryStatus())) {
                NewFlowAddParam newFlowAddParam = new NewFlowAddParam();
                newFlowAddParam.setFlowType(FlowTypeEnum.CREATE_OWNER);
                newFlowAddParam.setBusinessNo(owner.getOwnerCode());
                newFlowAddParam.setUserId(owner.getUserId());
                newFlowAddParam.setSubmitUserId(owner.getUserId());
                if (!newFlowFacade.startFlow(newFlowAddParam)) {
                    NewFlowStatusChangeMessage newFlowStatusChangeMessage = new NewFlowStatusChangeMessage();
                    newFlowStatusChangeMessage.setFlowLogStatus(FlowLogStatus.APPROVED);
                    newFlowStatusChangeMessage.setBusinessNo(owner.getOwnerCode());
                    newFlowStatusChangeMessage.setFlowType(FlowTypeEnum.CREATE_OWNER);
                    remoteOwnerInfoFacade.sendMessage(newFlowStatusChangeMessage);
                }
            }
        } else {
            Owner owner = ownerService.selectById(ownerBO.getId());
            if (Objects.isNull(owner)) {
                throw new BusinessException("请确认货主是否存在!");
            }
            if (OwnerEntryStatus.WAIT_APPROVAL.equals(ownerBO.getOwnerEntryStatus())) {
                throw new BusinessException("当前货主待审核，无法修改");
            }
            //金融货主不开启预售仓
            if (Objects.equals(1, ownerBO.getIsFinancialOwner())) {
                ownerBO.setOwnerPreSaleType(OwnerPreSaleType.NO);
                if (Objects.equals(3, ownerBO.getFinanceModeType())) {
                    if (!ConfigTagHelper.isOpenTag(owner.getTag1(), Tag1Constant.SINGLE_WH)) {
                        throw new BusinessException("存货质押,只支持单仓模式");
                    } else if (!Objects.equals(FinancialPlatformEnum.DT, ownerBO.getFinancialPlatform())) {
                        throw new BusinessException("存货质押,金融平台只支持【ERP内部】");
                    }
                }
            }
            EntityWarehouse entityWarehouse;
            if (OwnerMixType.MIX_BIG_OWNER.equals(ownerBO.getMixType())) {
                Owner bigOwner = ownerService.selectOneByOwnerCode(ownerBO.getParentOwnerCode());
                entityWarehouse = iEntityWarehouseService.getEntityByEntityCode(bigOwner.getEntityWarehouseCode());
            } else {
                entityWarehouse = iEntityWarehouseService.getEntityByEntityCode(ownerBO.getEntityWarehouseCode());
            }
            //开启
            if (Objects.equals(2, owner.getNonBonded()) && Objects.equals(1, ownerBO.getNonBonded())) {
                checkNonBonded(ownerBO.getNonBonded(), entityWarehouse);
            }
            if (Objects.equals(2, owner.getReturnEntryArea())) {
                checkReturnEntryArea(ownerBO.getReturnEntryArea(), ownerBO.getTag1(), ownerBO.getUserName(), entityWarehouse);
            }

            //关闭
            if (Objects.equals(1, owner.getNonBonded()) && Objects.equals(2, ownerBO.getNonBonded())) {
                remoteOrderFacade.checkOrder(ownerBO.getOwnerCode());
            }

            if (SendStatus.BEEN_SEND.equals(owner.getSendStatus())) {
                updatePreSaleWarehouseCode(owner, ownerBO);
                owner.setOwnerName(ownerBO.getOwnerName());
                checkInventoryMode(owner);
                return ownerService.updateById(owner);
            } else {
                updatePreSaleWarehouseCode(owner, ownerBO);
                Owner owner1 = BeanUtils.copyProperties(ownerBO, Owner.class);
                if (entityWarehouse != null) {
                    owner1.setSystemCode(entityWarehouse.getSystemCode());
                }
                //待编辑修改后，默认去提交
                if (OwnerEntryStatus.TO_BE_EDITED.equals(ownerBO.getOwnerEntryStatus())) {
                    owner1.setOwnerEntryStatus(OwnerEntryStatus.WAIT_APPROVAL);
                }
                checkInventoryMode(owner);
                if (!ownerService.updateById(owner1)) {
                    throw new BusinessException("修改货主失败");
                }
                //如果是内部审核，则需要配置理货报告的审批流程
                addTallyReportTargetFlow(owner1.getUserId(), owner1.getTallReportAuditType());
                //如果是待审核
                if (OwnerEntryStatus.WAIT_APPROVAL.equals(owner1.getOwnerEntryStatus())) {
                    NewFlowAddParam newFlowAddParam = new NewFlowAddParam();
                    newFlowAddParam.setFlowType(FlowTypeEnum.CREATE_OWNER);
                    newFlowAddParam.setBusinessNo(owner1.getOwnerCode());
                    newFlowAddParam.setUserId(owner1.getUserId());
                    newFlowAddParam.setSubmitUserId(owner1.getUserId());
                    if (!newFlowFacade.startFlow(newFlowAddParam)) {
                        NewFlowStatusChangeMessage newFlowStatusChangeMessage = new NewFlowStatusChangeMessage();
                        newFlowStatusChangeMessage.setFlowLogStatus(FlowLogStatus.APPROVED);
                        newFlowStatusChangeMessage.setBusinessNo(owner1.getOwnerCode());
                        newFlowStatusChangeMessage.setFlowType(FlowTypeEnum.CREATE_OWNER);
                        remoteOwnerInfoFacade.sendMessage(newFlowStatusChangeMessage);
                    }
                }
            }
        }
        return true;
    }

    private void checkNonBonded(Integer nonBonded, EntityWarehouse entityWarehouse) {
        if (Objects.isNull(entityWarehouse)) {
            return;
        }
        if (Objects.equals(1, nonBonded) && Objects.equals(TradeType.BONDED, entityWarehouse.getTradeType())) {
            throw new BusinessException("所属实体仓的贸易类型是保税，无法开启非保仓");
        }
    }

    /**
     * 退货入区校验
     *
     * @param returnEntryArea
     * @param tag1
     * @param entityWarehouse
     */
    private void checkReturnEntryArea(Integer returnEntryArea, Long tag1, String userName, EntityWarehouse entityWarehouse) {
        if (Objects.isNull(entityWarehouse)) {
            return;
        }
        if (Objects.equals(1, returnEntryArea)) {
            if (Objects.equals(TradeType.DUTY_PAID, entityWarehouse.getTradeType())) {
                throw new BusinessException("所属实体仓的贸易类型是完税，无法开启退货入区");
            }
            if (StringUtils.isEmpty(entityWarehouse.getReturnWarehouseCode())) {
                throw new BusinessException("所属实体仓未关联退货仓，无法开启退货入区");
            }
            if (!Objects.equals("DT", entityWarehouse.getSystemCode())) {
                throw new BusinessException("所属实体仓非代塔仓，无法开启退货入区");
            }
            boolean openTag = ConfigTagHelper.isOpenTag(tag1, Tag1Constant.IS_TAOTIAN);
            if (openTag) {
                throw new BusinessException("淘天货主，无法开启退货入区");
            }
        } else {
            String msg = "用户[" + userName + "]已关闭退货入区服务";
            //告警
            WarningUtils.backWarning(msg, erpNacosConfig.getReturnEntryAreaUrl(), "");
        }
    }

    /**
     * 如果是实时批次库存，则需要判断当前已创建的云仓是否大于1
     *
     * @param owner
     */
    private void checkInventoryMode(Owner owner) {
//        if (Objects.equals(1, owner.getInventoryMode())) {
//            LogicWarehouseSearch logicWarehouseSearch = new LogicWarehouseSearch();
//            logicWarehouseSearch.setOpenStatus(OpenStatus.OPEN);
//            logicWarehouseSearch.setOwnerCode(owner.getOwnerCode());
//            List<LogicWarehouseResult> logicWarehouseResults = logicWarehouseManager.listLogicWarehouseBySearch(logicWarehouseSearch);
//            if (!CollectionUtils.isEmpty(logicWarehouseResults)) {
//                //过滤掉预售仓
//                logicWarehouseResults = logicWarehouseResults.stream().filter(logicWarehouseResult -> OwnerPreSaleType.NO.equals(logicWarehouseResult.getOwnerPreSaleType())).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(logicWarehouseResults) && logicWarehouseResults.size() > 1) {
//                    throw new BusinessException("实时批次库存货主只能创建一个云仓，现在有" + logicWarehouseResults.size() + "个云仓，无法修改为实时批次库存");
//                }
//            }
//        }
    }

    /**
     * 设置理货报告审批流程
     *
     * @param userId
     * @param tallReportAuditType
     */
    public boolean addTallyReportTargetFlow(Long userId, TallReportAuditType tallReportAuditType) {
        try {
            if (TallReportAuditType.INTERNAL_AUDIT.equals(tallReportAuditType)) {
                TargetFlowResult targetFlow = flowFacade.getTargetFlow(userId, DocumentType.TALLY_REPORT);
                if (Objects.isNull(targetFlow)) {
                    TargetFlowAddParam targetFlowAddParam = new TargetFlowAddParam();
                    targetFlowAddParam.setDocumentType(DocumentType.TALLY_REPORT);
                    List<FlowStepParam> flowStepParamList = new ArrayList<>();
                    FlowStepParam flowStepParam = new FlowStepParam();
                    flowStepParam.setCandidateType(CandidateType.ALL);
                    flowStepParam.setStep(1);
                    flowStepParamList.add(flowStepParam);
                    targetFlowAddParam.setFlowStepParamList(flowStepParamList);
                    targetFlowAddParam.setUserId(userId);
                    flowFacade.targetFlowAdd(targetFlowAddParam);
                }
                return true;
            }
        } catch (Exception e) {
            log.error("addTallyReportTargetFlow error", e);
        }
        return false;
    }

    /**
     * 设置理货报告审批流程
     *
     * @param userId
     */
    public boolean addTallyReportTargetFlow(Long userId) {
        return addTallyReportTargetFlow(userId, TallReportAuditType.INTERNAL_AUDIT);
    }

    public void updatePreSaleWarehouseCode(Owner owner, OwnerBO ownerBO) {
        //如果一开始不是预售
        if (OwnerPreSaleType.NO.equals(owner.getOwnerPreSaleType()) && OwnerPreSaleType.YES.equals(ownerBO.getOwnerPreSaleType())) {
            LogicWarehouseQueryParam search = new LogicWarehouseQueryParam();
            search.setOwnerCode(owner.getOwnerCode());
            List<LogicWarehouseResult> resultList = iLogicWarehouseFacade.listLogicWarehouseByParam(search);
            for (LogicWarehouseResult logicWarehouseResult : resultList) {
                if (logicWarehouseResult.getPreSaleRelatedLogicWarehouseCode() != null) {
                    continue;
                }
                LogicWarehouseBO logicWarehouseBO = BeanUtils.copyProperties(logicWarehouseResult, LogicWarehouseBO.class);
                logicWarehouseResult.setPreSaleRelatedLogicWarehouseCode(logicWarehouseBO.getLogicWarehouseCode() + "_YS");
                LogicWarehouse logicWarehouse = BeanUtils.copyProperties(logicWarehouseResult, LogicWarehouse.class);
                //更新关联仓库码
                logicWarehouseService.updateById(logicWarehouse);
                logicWarehouseBO.setUserId(owner.getUserId());
                logicWarehouseManager.updateReSaleLogic(logicWarehouseBO, logicWarehouseBO.getLogicWarehouseCode());
            }
        }

    }

    private void checkCnInventory(Owner bigOwner, Owner owner) {
        MappingResult mappingResult = null;
        //菜鸟中心仓的货主且启用分组调用菜鸟库存分组接口
        if (SYSTEM_CNZXC.equals(bigOwner.getSystemCode()) && InventoryGroupType.YES.equals(owner.getIsInventoryGroup())) {
            try {
                mappingResult = remoteMappingFacade.inventoryGroup(owner);
            } catch (Exception e) {
                log.info("菜鸟库存分组返回转换出错={}", e.getMessage());
                e.printStackTrace();
                throw new BusinessException("菜鸟库存分组返回转换出错");
            }
            String code = (String) mappingResult.getData();
            if (org.apache.commons.lang3.StringUtils.isBlank(code)) {
                throw new BusinessException("调用菜鸟库存分组接口失败，请联系技术处理");
            }
            owner.setChannelCode(code);
        } else if (SYSTEM_CNZXC.equals(bigOwner.getSystemCode()) && InventoryGroupType.NO.equals(owner.getIsInventoryGroup())) {
            OwnerSearch ownerSearch = new OwnerSearch();
            ownerSearch.setChannelCode("NONTAO_TOC");
            ownerSearch.setParentOwnerCode(owner.getParentOwnerCode());
            ownerSearch.setIsInventoryGroup(InventoryGroupType.NO);
            Owner owner2 = ownerService.getOwnerBySearch(ownerSearch);
            if (owner2 != null) {
                throw new BusinessException("该菜鸟大货主下，只允许有一个默认不指定库存分组");
            } else {
                owner.setChannelCode("NONTAO_TOC");
            }
        }

    }

    @PageSelect
    public ListVO<OwnerBO> listOwnerByPage(OwnerSearch search) {
        ListVO<OwnerBO> ownerBOListVO = new ListVO<OwnerBO>();
        List<Owner> ownerList = ownerService.listOwnerByPage(search);
        List<OwnerBO> ownerBOList = BeanUtils.copyProperties(ownerList, OwnerBO.class);
        ownerBOListVO.setPage(ownerBOListVO.getPage());
        ownerBOListVO.setDataList(ownerBOList);
        return ownerBOListVO;
    }


    public List<OwnerBO> listOwnerByParam(OwnerSearch search) {
        List<Owner> ownerList = ownerService.listOwnerByPage(search);
        List<OwnerBO> ownerBOList = BeanUtils.copyProperties(ownerList, OwnerBO.class);
        return ownerBOList;
    }

    public OwnerCallbackResponse ownerOutExecute(List<String> codeList) {
        OwnerCallbackResponse callbackResponse = new OwnerCallbackResponse();
        List<OwnerSuccess> successList = new ArrayList<>();
        List<OwnerFail> failList = new ArrayList<>();
        for (String ownerCode : codeList) {
            String systemCode = "";
            EntityWarehouse entityWarehouse = null;
            Owner owner = ownerService.getOwnerNameByCode(ownerCode);
            if (SendStatus.BEEN_SEND.getValue().equals(owner.getSendStatus())) {
                throw new BusinessException(-10888, "货主已经下发请勿重复下发!");
            }
            if (!OwnerEntryStatus.EFFECTED.equals(owner.getOwnerEntryStatus())) {
                throw new BusinessException("货主入驻状态不是【" + OwnerEntryStatus.EFFECTED.getDes() + "】不能下发");
            }
            if (!StringUtils.isEmpty(owner.getEntityWarehouseCode())) {
                entityWarehouse = iEntityWarehouseService.getEntityByEntityCode(owner.getEntityWarehouseCode());
                systemCode = entityWarehouse.getSystemCode();
            } else {
                owner.setCallbackJson("实体仓编码不存在");
                ownerService.updateById(owner);
                continue;
            }
            //菜鸟的货主，直接修改为已下发状态
            if (SYSTEM_CNZXC.equals(owner.getSystemCode())){
                owner.setSendStatus(SendStatus.BEEN_SEND.getValue());
                ownerService.updateById(owner);
                continue;
            }
            OwnerMappingParam param = new OwnerMappingParam();
            param.setOwnerCode(ownerCode);
            param.setOwnerName(owner.getOwnerName());
            param.setRemark("");
            param.setIsBatch(owner.getIsBatch());
            param.setWarehouseCode(entityWarehouse.getWarehouseCode());
            boolean openTag = ConfigTagHelper.isOpenTag(owner.getTag1(), Tag1Constant.IS_TAOTIAN);
            param.setTaoTianTag(openTag);
            String sourceDate = JSON.toJSONString(param);
            MappingParam mappingParam = MappingParam.of(ownerCode, SYSTEM_ERP, systemCode, "2", DataType.DATA_TYPE_JSON.getValue(), sourceDate);
            MappingResult mappingResult = mappingFacade.outExecute(mappingParam);
            if (mappingResult.isSuccess()) {
                owner.setSendStatus(SendStatus.BEEN_SEND.getValue());
                owner.setCallbackJson(mappingResult.getMessage());
                ownerService.updateById(owner);
                OwnerSuccess ownerSuccess = new OwnerSuccess();
                ownerSuccess.setCode(owner.getOwnerCode());
                ownerSuccess.setOwnerName(owner.getOwnerName());
                ownerSuccess.setReason(mappingResult.getMessage());
                successList.add(ownerSuccess);
            } else {
                owner.setSendStatus(SendStatus.READY_SEND.getValue());
                owner.setCallbackJson(mappingResult.getMessage());
                ownerService.updateById(owner);
                OwnerFail fail = new OwnerFail();
                fail.setCode(owner.getOwnerCode());
                fail.setOwnerName(owner.getOwnerName());
                fail.setReason(mappingResult.getMessage());
                failList.add(fail);
            }
        }
        callbackResponse.setSuccessList(successList);
        callbackResponse.setFailList(failList);
        return callbackResponse;
    }

    public OwnerBO getByCode(String ownerCode) {
        Owner owner = ownerService.getOwnerNameByCode(ownerCode);
        OwnerBO ownerBO = BeanUtils.copyProperties(owner, OwnerBO.class);
        return ownerBO;

    }


    public Boolean openAndClosed(String ownerCode) {
        Owner owner = ownerService.getOwnerNameByCode(ownerCode);
        if (owner.getOpenStatus() == 1) {
            owner.setOpenStatus(2);
        } else {
            owner.setOpenStatus(1);
        }
        return ownerService.updateById(owner);
    }


    public Boolean update(OwnerBO ownerBO) {
        Owner owner = BeanUtils.copyProperties(ownerBO, Owner.class);
        return ownerService.updateById(owner);
    }

    /**
     * 查询指定货主的货品同步状态信息
     *
     * @param userId
     * @return
     */
    public List<OwnerGoodsSyncStatusResult> getGoodsSyncStatusInfo(Long userId) {
        List<Owner> owners = ownerService.listOwnerByUserId(userId);
        if (CollectionUtils.isEmpty(owners)) {
            return Collections.emptyList();
        }
        Set<String> ownerCodes = owners.stream().map(Owner::getOwnerCode).collect(Collectors.toSet());

        //已有货品数， key = ownerCode, value = 货品数量
        Map<String, Integer> hasNumMap = findOwnerGoodsCount(userId, ownerCodes);

        //默认货品数
        int totalNum = findGoodsCount(userId);

        return owners.stream().map(owner -> {
            Integer hasNum = hasNumMap.get(owner.getOwnerCode());
            if (hasNum == null) {
                hasNum = 0;
            }

            return new OwnerGoodsSyncStatusResult(owner.getOwnerCode(), owner.getOwnerName(), totalNum - hasNum);
        }).collect(Collectors.toList());
    }

    /**
     * 查询用户的货品数量
     *
     * @param userId
     * @return
     */
    private int findGoodsCount(Long userId) {
        GoodsQueryParam param = new GoodsQueryParam();
        param.setUserId(userId);
        List<GoodsResult> list = iGoodsFacade.listGoodsByParam(param);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }

        return list.size();
    }

    /**
     * 查询货主的货品数量
     *
     * @param userId
     * @return key = ownerCode, value = 货品数量
     */
    private Map<String, Integer> findOwnerGoodsCount(Long userId, Set<String> ownerCodes) {
        GoodsManagementQueryParam param = new GoodsManagementQueryParam();
        param.setUserId(userId);
        param.setGoodsCodes(Arrays.asList("owner_code"));
        List<GoodsManagementResult> list = iGoodsManagementFacade.listByQueryParam(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        Map<String, List<GoodsManagementResult>> map = list.stream().collect(Collectors.groupingBy(GoodsManagementResult::getOwnerCode));

        return ownerCodes.stream().map(ownerCode -> {
            int count = 0;
            List<GoodsManagementResult> subList = map.get(ownerCode);
            if (!CollectionUtils.isEmpty(subList)) {
                count = subList.size();
            }

            return Pair.of(ownerCode, count);
        }).collect(Collectors.toMap(Pair::getLeft, Pair::getRight, (k1, k2) -> k1));
    }


/*    public void buildOwnerWarehouse(Owner owner) {
        //反向生成一个货主仓
        LogicWarehouseAddParam param = new LogicWarehouseAddParam();
        param.setLogicWarehouseName(owner.getOwnerName() + "【货主仓】");
        param.setLogicWarehouseType(LogicWarehouseType.OWNER);
        param.setOwnerCode(owner.getOwnerCode());
        EntityWarehouseResult result = iEntityWarehouseFacade.getDetailByCode(owner.getEntityWarehouseCode());
        List<String> expressCodeList = new ArrayList<>();
        if (result.getExpressCode().contains("[")) {
            expressCodeList = JSON.parseArray(result.getExpressCode(), String.class);
        } else {
            expressCodeList.add(result.getExpressCode());
        }
        param.setExpressList(expressCodeList);
        iLogicWarehouseFacade.saveLogicWarehouse()
    }*/

    public void addRiskOwner(){
        List<String> list = Arrays.asList(erpNacosConfig.getRiskControlEntityWarehouseCode().split(","));
        OwnerSearch search = new OwnerSearch();
        search.setEntityWarehouseCodeList(list);
        List<Owner> ownerList = ownerService.listOwnerByPage(search);
        if (CollectionUtils.isEmpty(ownerList)) {
            return;
        }
        for (Owner owner : ownerList) {
            EntityWarehouse entityWarehouse = iEntityWarehouseService.getEntityByEntityCode(owner.getEntityWarehouseCode());
            if (Objects.isNull(entityWarehouse)) {
                continue;
            }
            try {
                //新增货主,判断是否需要加入风控
                if (list.contains(entityWarehouse.getEntityWarehouseCode()) && Objects.equals(1, entityWarehouse.getOperationType())) {
                    //加入风控货主
                    remoteOwnerInfoFacade.addRisk(BeanUtils.copyProperties(owner, OwnerBO.class));
                }
            } catch (Exception e) {
                log.error("[OwnerManager-addRiskOwner]==============加入风控异常==========owner:{},ex:", owner.getOwnerCode(), e);
            }
        }
    }

    /**
     * 推送库存快照
     * @param ownerCode
     */
    public void pushInventoryToDSTP(String ownerCode) {
        List<Long> resultList = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) + 1);
        for (int i = 0; i < 7; i++) {
            cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) - 1);
            int year = cal.get(Calendar.YEAR);
            int month = cal.get(Calendar.MONTH) + 1;
            resultList.add(DateUtils.getFirstDay(year, month).getTime());
            if (i == 0) {
                if (new Date().getDate() > 15) {
                    Long aLong = DateUtils.stringToLong(year + "-" + month + "-" + 15, "yyy-MM-dd");
                    resultList.add(aLong);
                }
            } else {
                Long aLong = DateUtils.stringToLong(year + "-" + month + "-" + 15, "yyy-MM-dd");
                resultList.add(aLong);
                resultList.add(DateUtils.getLastDay(year, month).getTime());
            }
        }
        for (Long time : resultList) {
            List<GoodsBatchInventoryResult> inventoryResultList = remoteInventoryFacade.listInventory(ownerCode, time);
            Map<String, Object> dstpMap = new HashMap<>();
            dstpMap.put("customerCode", ownerCode);
            dstpMap.put("atTime", DateUtils.defaultFormat(time));
            List<Object> skuBatchList = new ArrayList<>();
            for (GoodsBatchInventoryResult result : inventoryResultList) {
                Map<String, Object> batchDetailMap = new HashMap<>();
                batchDetailMap.put("gCode", result.getSku());
                batchDetailMap.put("productName", result.getGoodsName());
                batchDetailMap.put("warehouseId", result.getWarehouseCode());
                batchDetailMap.put("batchCode", result.getBatchCode());
                if (Objects.nonNull(result.getInstockTime()) && !Objects.equals(0L, result.getInstockTime())) {
                    batchDetailMap.put("warehouseTime", DateUtils.format(result.getInstockTime()));
                }
//            batchDetailMap.put("productionTime", result);
//            batchDetailMap.put("expireTime", result);
                batchDetailMap.put("stockCount", result.getAvailableNum());
                skuBatchList.add(batchDetailMap);
            }
            dstpMap.put("inventoryList", skuBatchList);

            String request = postJson(dstpMap, erpNacosConfig.getCallBackUrlDSTP() + "/open/logistics/inventory-snapshot"
                    , SYSTEM_DSTP, DstpSignUtils.buildRequestHeaders(erpNacosConfig.getDstpAppKey(), erpNacosConfig.getDstpAppSecret()));
            log.info("[OwnerManager-pushInventoryToDSTP]================推送dstp库存快照===============DSTP回传返回值:" + request);
            JSONObject jsonObject = JSON.parseObject(request);
            String results = jsonObject.getString("flag");
            if (!Objects.equals("success", results)) {
                String notes = jsonObject.getString("message");
                log.info("[OwnerManager-pushInventoryToDSTP]=================推送dstp库存快照失败:{},参数:{}", notes, JSON.toJSONString(dstpMap));
            }
        }
    }

    public void pushInOrderToDSTP(String ownerCode) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) - 6);
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        Long aLong = DateUtils.stringToLong(year + "-" + month + "-01 00:00:00", "yyyy-MM-dd HH:mm:ss");

        ListVO<InOrderResult> inOrderResultListVO = remoteOrderFacade.listInOrderPage(ownerCode, aLong, 1);
        sendInOrderToDSTP(inOrderResultListVO.getDataList());

        int totalPage = inOrderResultListVO.getPage().getTotalPage();
        if (totalPage > 1) {
            for (int i = 2; i <= totalPage; i++) {
                inOrderResultListVO = remoteOrderFacade.listInOrderPage(ownerCode, aLong, i);
                sendInOrderToDSTP(inOrderResultListVO.getDataList());
            }
        }
    }

    private void sendInOrderToDSTP(List<InOrderResult> inOrderResultList) {
        inOrderResultList.forEach(orderResult -> {
            Map<String, Object> orderMap = new HashMap<>();
            orderMap.put("entOrderNo", orderResult.getInOrderNo());
            orderMap.put("commitTime", DateUtils.format(System.currentTimeMillis()));
            orderMap.put("warehouseId", orderResult.getWarehouseCode());
            orderMap.put("warehouseName", orderResult.getWarehouseCode());
            orderMap.put("customerCode", orderResult.getOwnerCode());
            if (Objects.equals(ReadyBusinessType.FRONTLINE_ENTRY, orderResult.getReadyBusinessType())) {
                orderMap.put("inputType", 10);
            } else {
                orderMap.put("inputType", 20);
            }
            orderMap.put("declareQty", orderResult.getActualQuantity());
            orderMap.put("orderCreateTime", DateUtils.format(orderResult.getCreateTime()));

            List<InOrderDetailResult> detailResultList = remoteOrderFacade.listInOrderDetail(orderResult.getInOrderNo());
            List<Object> skuList = new ArrayList<>();
            detailResultList.forEach(detailResult -> {
                GoodsRecordResult goodsRecord = remoteGoodsFacade.getGoodsRecord(orderResult.getUserId(), orderResult.getLogicWarehouseCode(), detailResult.getGoodsCode());

                Map<String, Object> detailMap = new HashMap<>();
                detailMap.put("gNum", detailResult.getLineNo());
                detailMap.put("entGoodsNo", detailResult.getSku());
                detailMap.put("entGoodsName", detailResult.getGoodsName());
                detailMap.put("hsCode", goodsRecord.getHsCode());
                detailMap.put("specification", detailResult.getModel());
                if (StringUtils.isEmpty(detailResult.getModel())) {
                    detailMap.put("specification", goodsRecord.getModel());
                }
                detailMap.put("declareQty", detailResult.getActualQuantity());
                detailMap.put("firstLegalUnit", goodsRecord.getFirstUnit());
                detailMap.put("firstLegalQty", goodsRecord.getFirstQuantity());
                skuList.add(detailMap);
            });
            orderMap.put("goodsList", skuList);

            String request = postJson(orderMap, erpNacosConfig.getCallBackUrlDSTP() + "/open/logistics/inbound-order", SYSTEM_DSTP
                    , DstpSignUtils.buildRequestHeaders(erpNacosConfig.getDstpAppKey(), erpNacosConfig.getDstpAppSecret()));
            log.info("[OwnerManager-sendInOrderToDSTP]===============推送dstp入库单==============DSTP回传返回值:" + request);
            JSONObject jsonObject = JSON.parseObject(request);
            String results = jsonObject.getString("flag");
            if (!Objects.equals("success", results)) {
                String notes = jsonObject.getString("message");
                log.info("[OwnerManager-sendInOrderToDSTP]=================推送dstp入库单失败:{},参数:{}", notes, JSON.toJSONString(orderMap));
            }
        });
    }


    public void pushOutOrderToDSTP(String ownerCode) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) - 6);
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        Long aLong = DateUtils.stringToLong(year + "-" + month + "-01 00:00:00", "yyyy-MM-dd HH:mm:ss");

        ListVO<OutOrderResult> outOrderResultListVO = remoteOrderFacade.listOutOrderPage(ownerCode, aLong, 1);
        sendOutOrderToDSTP(outOrderResultListVO.getDataList());

        int totalPage = outOrderResultListVO.getPage().getTotalPage();
        if (totalPage > 1) {
            for (int i = 2; i <= totalPage; i++) {
                outOrderResultListVO = remoteOrderFacade.listOutOrderPage(ownerCode, aLong, i);
                sendOutOrderToDSTP(outOrderResultListVO.getDataList());
            }
        }
    }

    private void sendOutOrderToDSTP(List<OutOrderResult> outOrderResultList) {
        outOrderResultList.forEach(orderResult -> {
            Map<String, Object> orderMap = new HashMap<>();
            orderMap.put("entOrderNo", orderResult.getOutOrderNo());
            orderMap.put("commitTime", DateUtils.format(System.currentTimeMillis()));
            orderMap.put("warehouseId", orderResult.getWarehouseCode());
            orderMap.put("warehouseName", orderResult.getWarehouseCode());
            orderMap.put("customerCode", orderResult.getOwnerCode());
            if (Objects.equals(OutOrderType.CD_CK.getValue(), orderResult.getType()) || Objects.equals(OutOrderType.CD_FX_CK.getValue(), orderResult.getType())) {
                orderMap.put("outputType", 10);
            } else {
                orderMap.put("outputType", 20);
            }
            orderMap.put("declareQty", orderResult.getActualQuantity());
            orderMap.put("orderCreateTime", DateUtils.format(orderResult.getCreateTime()));

            List<OutOrderDetailResult> detailResultList = remoteOrderFacade.listOutOrderDetail(orderResult.getOutOrderNo());
            List<Object> skuList = new ArrayList<>();
            detailResultList.forEach(detailResult -> {
                GoodsRecordResult goodsRecord = remoteGoodsFacade.getGoodsRecord(orderResult.getUserId(), orderResult.getLogicWarehouseCode(), detailResult.getGoodsCode());

                Map<String, Object> detailMap = new HashMap<>();
                detailMap.put("gNum", detailResult.getLineNo());
                detailMap.put("entGoodsNo", detailResult.getSku());
                detailMap.put("entGoodsName", detailResult.getGoodsName());
                detailMap.put("hsCode", goodsRecord.getHsCode());
                detailMap.put("specification", goodsRecord.getModel());
                detailMap.put("declareUnit", goodsRecord.getDeclaredUnit());
                detailMap.put("declareQty", detailResult.getActualQuantity());
                detailMap.put("firstLegalUnit", goodsRecord.getFirstUnit());
                detailMap.put("firstLegalQty", goodsRecord.getFirstQuantity());
                skuList.add(detailMap);
            });
            orderMap.put("goodsList", skuList);

            String request = postJson(orderMap, erpNacosConfig.getCallBackUrlDSTP() + "/open/logistics/outbound-order", SYSTEM_DSTP
                    , DstpSignUtils.buildRequestHeaders(erpNacosConfig.getDstpAppKey(), erpNacosConfig.getDstpAppSecret()));
            log.info("[OwnerManager-sendOutOrderToDSTP]===============推送dstp出库单==============DSTP回传返回值:" + request);
            JSONObject jsonObject = JSON.parseObject(request);
            String results = jsonObject.getString("flag");
            if (!Objects.equals("success", results)) {
                String notes = jsonObject.getString("message");
                log.info("[OwnerManager-sendOutOrderToDSTP]=================推送dstp出库单失败:{},参数:{}", notes, JSON.toJSONString(orderMap));
            }
        });
    }

    public void pushGoodsToDSTP(String ownerCode) {
        if (StringUtils.isEmpty(ownerCode)) {
            throw new BusinessException("货主编码不能为空!");
        }
        Owner owner = ownerService.selectOneByOwnerCode(ownerCode);
        EntityWarehouseResult entityWarehouseResult = remoteOwnerInfoFacade.getEntityWarehouseResultByCode(owner.getEntityWarehouseCode());

        ListVO<GoodsManagementResult> goodsManagementList = remoteGoodsFacade.getGoodsManagementList(ownerCode, 1);
        sendGoodsToDSTP(goodsManagementList.getDataList(), entityWarehouseResult.getWarehouseCode());

        int totalPage = goodsManagementList.getPage().getTotalPage();
        if (totalPage > 1) {
            for (int i = 2; i <= totalPage; i++) {
                goodsManagementList = remoteGoodsFacade.getGoodsManagementList(ownerCode, i);
                sendGoodsToDSTP(goodsManagementList.getDataList(), entityWarehouseResult.getWarehouseCode());
            }
        }
    }

    private void sendGoodsToDSTP(List<GoodsManagementResult> managementResultList, String warehouseCode) {
        Map<String, Object> pushMap = new HashMap<>();
        pushMap.put("enterpriseCode", SOURCE_CODE_DSTP);
        pushMap.put("enterpriseName", "金华代塔供应链管理有限公司");
        managementResultList.forEach(goodsManagementResult -> {
            try {
                GoodsRecordResult goodsRecord = remoteGoodsFacade.getGoodsRecordBy(goodsManagementResult.getUserId(), goodsManagementResult.getGoodsCode(), warehouseCode);
                List<Object> skuBatchList = new ArrayList<>();
                Map<String, Object> goodsMap = new HashMap<>();
                goodsMap.put("customerCode", goodsManagementResult.getOwnerCode());
                goodsMap.put("customerName", goodsManagementResult.getOwnerCode());
                goodsMap.put("entGoodsNo", goodsManagementResult.getSku());
                goodsMap.put("entGoodsName", goodsManagementResult.getGoodsName());
                goodsMap.put("brand", goodsManagementResult.getBrandName());
                goodsMap.put("barCode", goodsManagementResult.getBarcode());
                if (Objects.nonNull(goodsRecord)) {
                    goodsMap.put("price", goodsRecord.getDeclaredUnitPrice());
                }
                skuBatchList.add(goodsMap);
                pushMap.put("goodsList", skuBatchList);

                String request = postJson(pushMap, erpNacosConfig.getCallBackUrlDSTP() + "/open/logistics/goods", SYSTEM_DSTP
                        , DstpSignUtils.buildRequestHeaders(erpNacosConfig.getDstpAppKey(), erpNacosConfig.getDstpAppSecret()));
                log.info("[GoodsFacadeImpl-pushGoodsToDSTP]================推送dstp货品信息===============DSTP回传返回值:" + request);
                JSONObject jsonObject = JSON.parseObject(request);
                String results = jsonObject.getString("flag");
                if (!Objects.equals("success", results)) {
                    String notes = jsonObject.getString("message");
                    log.info("[GoodsFacadeImpl-pushGoodsToDSTP]=================推送dstp货品信息失败:{},参数:{}", notes, JSON.toJSONString(pushMap));
                }
            } catch (Exception e) {
                log.error("[GoodsFacadeImpl-pushGoodsToDSTP]=================推送dstp货品信息异常,goodsCode:{},ex:", goodsManagementResult.getGoodsCode(), e);
            }
        });
    }

}
