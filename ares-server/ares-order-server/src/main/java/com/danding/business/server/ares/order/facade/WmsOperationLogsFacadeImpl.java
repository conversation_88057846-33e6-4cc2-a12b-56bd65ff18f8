package com.danding.business.server.ares.order.facade;

import com.danding.business.client.ares.order.facade.IWmsOperationLogsFacade;
import com.danding.business.server.ares.order.manager.WmsOperationLogsManager;
import com.danding.business.client.ares.order.result.WmsOperationLogsResult;
import com.danding.business.client.ares.order.param.WmsOperationLogsQueryParam;
import com.danding.business.client.ares.order.param.WmsOperationLogsAddParam;
import com.danding.business.core.ares.order.search.WmsOperationLogsSearch;
import com.danding.business.server.ares.order.BO.WmsOperationLogsBO;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.DubboService;


import java.io.Serializable;
import java.util.List;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@DubboService
public class WmsOperationLogsFacadeImpl implements IWmsOperationLogsFacade {



    @Autowired
    private WmsOperationLogsManager wmsOperationLogsManager;


    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    @Override
    public WmsOperationLogsResult getById(Serializable id) {
        WmsOperationLogsBO wmsOperationLogsBO = wmsOperationLogsManager.getById(id);
        return BeanUtils.copyProperties(wmsOperationLogsBO, WmsOperationLogsResult.class);
    }

    /**
     * 条件查询单个
     *
     * @param wmsOperationLogsQueryParam
     * @return
     */
    @Override
    public WmsOperationLogsResult getByQueryParam(WmsOperationLogsQueryParam wmsOperationLogsQueryParam) {
        WmsOperationLogsBO wmsOperationLogsBO = wmsOperationLogsManager.getBySearch(BeanUtils.copyProperties(wmsOperationLogsQueryParam, WmsOperationLogsSearch.class));
        return BeanUtils.copyProperties(wmsOperationLogsBO, WmsOperationLogsResult.class);
    }

    /**
     * 条件查询list
     *
     * @param wmsOperationLogsQueryParam
     * @return
     */
    @Override
    public List<WmsOperationLogsResult> listByQueryParam(WmsOperationLogsQueryParam wmsOperationLogsQueryParam) {
        List<WmsOperationLogsBO> wmsOperationLogsBOList = wmsOperationLogsManager.listBySearch(BeanUtils.copyProperties(wmsOperationLogsQueryParam, WmsOperationLogsSearch.class));
        return BeanUtils.copyProperties(wmsOperationLogsBOList, WmsOperationLogsResult.class);
    }

    /**
     * 条件分页查询
     *
     * @param wmsOperationLogsQueryParam
     * @return
     */
    @Override
    public ListVO<WmsOperationLogsResult> pageListByQueryParam(WmsOperationLogsQueryParam wmsOperationLogsQueryParam) {
        ListVO<WmsOperationLogsBO> wmsOperationLogsBOListVO = wmsOperationLogsManager.pageListBySearch(BeanUtils.copyProperties(wmsOperationLogsQueryParam, WmsOperationLogsSearch.class));
        return ListVO.build(wmsOperationLogsBOListVO.getPage(), BeanUtils.copyProperties(wmsOperationLogsBOListVO.getDataList(), WmsOperationLogsResult.class));
    }


    /**
     * 插入
     *
     * @param wmsOperationLogsAddParam
     * @return
     */
    @Override
    public boolean add(WmsOperationLogsAddParam wmsOperationLogsAddParam) {
        return wmsOperationLogsManager.add(BeanUtils.copyProperties(wmsOperationLogsAddParam, WmsOperationLogsBO.class));
    }

    /**
     * 批量插入
     *
     * @param wmsOperationLogsAddParamList
     * @return
     */
    @Override
    public boolean addList(List<WmsOperationLogsAddParam> wmsOperationLogsAddParamList) {
        return wmsOperationLogsManager.addList(BeanUtils.copyProperties(wmsOperationLogsAddParamList, WmsOperationLogsBO.class));
    }

    /**
     * 根据主键id修改
     *
     * @param wmsOperationLogsAddParam
     * @return
     */
    @Override
    public boolean updateById(WmsOperationLogsAddParam wmsOperationLogsAddParam) {
        return wmsOperationLogsManager.updateById(BeanUtils.copyProperties(wmsOperationLogsAddParam, WmsOperationLogsBO.class));
    }

    /**
     * 根据主键id批量修改
     *
     * @param wmsOperationLogsAddParamList
     * @return
     */
    @Override
    public boolean updateListById(List<WmsOperationLogsAddParam> wmsOperationLogsAddParamList) {
        return wmsOperationLogsManager.updateListById(BeanUtils.copyProperties(wmsOperationLogsAddParamList, WmsOperationLogsBO.class));
    }

    /**
     * 根据条件修改
     *
     * @param wmsOperationLogsQueryParam
     * @param wmsOperationLogsAddParam
     * @return
     */
    @Override
    public boolean updateListByQueryParam(WmsOperationLogsQueryParam wmsOperationLogsQueryParam, WmsOperationLogsAddParam wmsOperationLogsAddParam) {
        return wmsOperationLogsManager.updateListBySearch(BeanUtils.copyProperties(wmsOperationLogsQueryParam, WmsOperationLogsSearch.class), BeanUtils.copyProperties(wmsOperationLogsAddParam, WmsOperationLogsBO.class));
    }

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    @Override
    public boolean removeById(Serializable id) {
        return wmsOperationLogsManager.removeById(id);
    }

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    @Override
    public boolean removeByIds(List<Long> idList) {
        return wmsOperationLogsManager.removeByIds(idList);
    }

    /**
     * 根据条件删除
     *
     * @param wmsOperationLogsQueryParam
     * @return
     */
    @Override
    public boolean removeByQueryParam(WmsOperationLogsQueryParam wmsOperationLogsQueryParam) {
        return wmsOperationLogsManager.removeBySearch(BeanUtils.copyProperties(wmsOperationLogsQueryParam, WmsOperationLogsSearch.class));
    }

}
