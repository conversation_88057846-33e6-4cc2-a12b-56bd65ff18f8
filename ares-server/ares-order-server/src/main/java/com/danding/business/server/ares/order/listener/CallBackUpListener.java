package com.danding.business.server.ares.order.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.business.client.ares.configuration.param.ConfigurationLogAddParam;
import com.danding.business.client.ares.entitywarehouse.result.EntityAndOwnerResult;
import com.danding.business.client.ares.logicwarehouse.result.LogicWarehouseResult;
import com.danding.business.client.ares.order.message.OrderMessage;
import com.danding.business.client.ares.order.param.back.trace.WmsTraceBackParam;
import com.danding.business.common.ares.context.AresContext;
import com.danding.business.common.ares.enums.adjustOrder.AdjustType;
import com.danding.business.common.ares.enums.common.TradeType;
import com.danding.business.common.ares.enums.inventory.InventoryType;
import com.danding.business.common.ares.enums.order.*;
import com.danding.business.common.ares.enums.trade.TallyReportMultiType;
import com.danding.business.common.ares.utils.DateUtils;
import com.danding.business.common.ares.utils.HttpRequestUtils;
import com.danding.business.common.ares.utils.LogUtils;
import com.danding.business.core.ares.order.entity.InOrderDetail;
import com.danding.business.core.ares.order.entity.ReceiveSendInfo;
import com.danding.business.core.ares.order.search.InOrderDetailBatchSearch;
import com.danding.business.core.ares.order.search.InOrderDetailSearch;
import com.danding.business.core.ares.order.search.OutOrderLogisticsDetailSearch;
import com.danding.business.core.ares.order.search.OutOrderLogisticsSearch;
import com.danding.business.core.ares.order.service.IInOrderDetailService;
import com.danding.business.rpc.client.oms.explorer.bo.MessageNewRpcDTO;
import com.danding.business.rpc.client.oms.explorer.facade.IMessageRpcFacade;
import com.danding.business.server.ares.config.OrderNacosConfig;
import com.danding.business.server.ares.order.BO.*;
import com.danding.business.server.ares.order.manager.*;
import com.danding.business.server.ares.order.remote.RemoteCdsFacade;
import com.danding.business.server.ares.order.remote.RemoteConfigurationFacade;
import com.danding.business.server.ares.order.remote.RemoteScfFacade;
import com.danding.business.server.ares.order.remote.RemoteWarehouseFacade;
import com.danding.component.cache.common.config.RedisLockUtils;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.rocketmq.consume.PushMQConsumer;
import com.danding.soul.client.common.exception.BusinessException;
import com.kingdee.bos.webapi.entity.*;
import com.kingdee.bos.webapi.sdk.K3CloudApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.danding.business.common.ares.context.AresContext.*;
import static com.danding.business.common.ares.utils.HttpRequestUtils.*;

/**
 * 描述:
 * 回传上游  目前回传奇门使用
 *
 * <AUTHOR>
 * @date 2021/1/25 上午9:38
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = "${rocketmq.topic.ares-web-order-push-back}", consumerGroup = "${rocketmq.consumer.group.push.back.up}")
public class CallBackUpListener extends PushMQConsumer<OrderMessage> {

    @Autowired
    private InOrderManager inOrderManager;
    @Autowired
    private IInOrderDetailService inOrderDetailService;
    @Autowired
    private InOrderDetailBatchManager inOrderDetailBatchManager;
    @Autowired
    private OutOrderManager outOrderManager;
    @Autowired
    private OutOrderDetailManager outOrderDetailManager;
    @Autowired
    private InOrderDetailManager inOrderDetailManager;
    @Autowired
    private OutOrderLogisticsManager outOrderLogisticsManager;
    @Autowired
    private OutOrderLogisticsDetailManager outOrderLogisticsDetailManager;
    @Autowired
    private RemoteWarehouseFacade remoteWarehouseFacade;
    @Autowired
    private RemoteConfigurationFacade remoteConfigurationFacade;
    @Autowired
    private OrderNodeManager orderNodeManager;
    @DubboReference
    private IMessageRpcFacade messageRpcFacade;
    @Autowired
    private RemoteScfFacade remoteScfFacade;
    @Autowired
    private ReceiveSendInfoManager receiveSendInfoManager;
    @Autowired
    private RedisLockUtils redisLockUtils;
    @Autowired
    private RemoteCdsFacade remoteCdsFacade;
    @Autowired
    private OrderNacosConfig orderNacosConfig;

    @Override
    public void handleMessage(OrderMessage message) throws RuntimeException {
        log.info("[CallBackUpListener-onMessage]==========消息监听==========message:" + message);
        //如果包含对应系统编码,直接返回
        if (orderNacosConfig.getCallBackSwitch().contains(message.getOrigSystem())) {
            return;
        }
        Integer backType = message.getBackType();
        String lockKey = "BACK_UP_" + message.getOrderNo() + backType;
        Long time = System.currentTimeMillis() + 180000;
        boolean isLock = Boolean.FALSE;
        try {
            isLock = redisLockUtils.lock(lockKey, time);
            if (!isLock) {
                return;
            }
            if (SYSTEM_QM.equals(message.getOrigSystem())) {
                if (Objects.equals(MessageType.CALL_BACK_IN_ORDER.getValue(), backType)) {
                    backQMInOrder(message);
                } else if (Objects.equals(MessageType.CALL_BACK_OUT_ORDER.getValue(), backType)) {
                    backQMOutOrder(message, "order.stock.out.confirm.notify");
                }
            } else if (SYSTEM_OMS.equals(message.getOrigSystem())) {
                if (Objects.equals(MessageType.CALL_BACK_OUT_ORDER.getValue(), backType)) {
                    backOMSOutOrder(message);
                } else if (Objects.equals(MessageType.CALL_BACK_IN_ORDER.getValue(), backType)) {
                    backOMSInOrder(message);
                }
            } else if (SYSTEM_GLP.equals(message.getOrigSystem())) {
                if (Objects.equals(MessageType.CALL_BACK_IN_ORDER.getValue(), backType)) {
                    backGLPInOrder(message);
                } else if (Objects.equals(MessageType.CALL_BACK_OUT_ORDER.getValue(), backType)) {
                    singleBackGLPOutOrder(message);
                }
            } else if (SYSTEM_DYZJ.equals(message.getOrigSystem())) {
                if (Objects.equals(MessageType.CALL_BACK_IN_ORDER.getValue(), backType)) {
                    backDYZJInOrder(message);
                } else if (Objects.equals(MessageType.CALL_BACK_OUT_ORDER.getValue(), backType)) {
                    backDYZJOutOrder(message);
                }
            } else if (SYSTEM_SCF.equals(message.getOrigSystem())) {
                if (Objects.equals(MessageType.CALL_BACK_IN_ORDER.getValue(), backType)) {
                    backSCFInOrder(message);
                } else if (Objects.equals(MessageType.CALL_BACK_OUT_ORDER.getValue(), backType)) {
                    backSCFOutOrder(message);
                }
            } else if (SYSTEM_ASCP.equals(message.getOrigSystem())) {
                if (Objects.equals(MessageType.CALL_BACK_IN_ORDER.getValue(), backType)) {
                    backASCPInOrder(message);
                }
            } else if (SYSTEM_JIND.equals(message.getOrigSystem())) {
                if (Objects.equals(MessageType.CALL_BACK_IN_ORDER.getValue(), backType)) {
                    backJINDInOrder(message);
                } else if (Objects.equals(MessageType.CALL_BACK_OUT_ORDER.getValue(), backType)) {
                    backJINDOutOrder(message);
                }
            }  else {
                log.warn("[CallBackUpListener-onMessage]===========未匹配到来源系统=========:" + message);
            }
        } catch (Exception e) {
            log.error("[CallBackUpListener-onMessage]===================回传上游异常===================ex:", e);
        } finally {
            //释放redis锁
            if (isLock) {
                redisLockUtils.unlock(lockKey, time);
            }
        }
    }

    /**
     * 淘天入库完成,切最后一次无批次明细
     *
     * @param orderBO
     * @return
     */
    private boolean backTaoTianLsat(InOrderBO orderBO) {
        if (Objects.equals(InOrderStatus.RK_ALL.getValue(), orderBO.getStatus())
                && StringUtils.isNotBlank(orderBO.getBusinessValue())
                && orderBO.getBusinessValue().contains(SYSTEM_TAG_TAOTIAN)) {
            InOrderDetailBatchSearch batchSearch = new InOrderDetailBatchSearch();
            batchSearch.setInOrderNo(orderBO.getInOrderNo());
            batchSearch.setIsConfirm(0);
            List<InOrderDetailBatchBO> boList = inOrderDetailBatchManager.listBySearch(batchSearch);
            if (CollectionUtil.isEmpty(boList)) {
                Map<String, Object> backMap = new HashMap<>();
                String businessValue = orderBO.getBusinessValue();
                backMap.put("batchCode", orderBO.getInOrderNo());
                backMap.put("businessValue", businessValue);
                backMap.put("orderSn", orderBO.getUpstreamNo());
                backMap.put("depotEntryOrderNo", orderBO.getInOrderNo());
                backMap.put("outBizCode", orderBO.getInOrderNo());
                backMap.put("operationTime", orderBO.getActualTime());
                backMap.put("callBackUrl", orderNacosConfig.getCallBackUrlQM());
                backMap.put("confirmType", 0);
                backMap.put("warehouseSn", orderBO.getLogicWarehouseCode());//仓库
                backMap.put("shipperSn", orderBO.getOwnerCode());//货主
                backMap.put("userId", orderBO.getUserId());

                //淘天入库单,在最后一次完成的时候,需要回传所有单据
                InOrderDetailBatchSearch search = new InOrderDetailBatchSearch();
                search.setInOrderNo(orderBO.getInOrderNo());
                List<InOrderDetailBatchBO> batchBOList = inOrderDetailBatchManager.listBySearch(search);
                Map<String, InOrderDetailBatchBO> mergedDetail = batchBOList.stream()
                        .collect(Collectors.toMap(batchBO -> getKey(batchBO.getGoodsCode(), batchBO.getLineNo(), null),
                                Function.identity(),
                                (existing, replacement) -> {
                                    existing.setActualQuantity(existing.getActualQuantity() + replacement.getActualQuantity());
                                    return existing;
                                }
                        ));
                List<Object> totalSkuList = new ArrayList<>();
                for (InOrderDetailBatchBO detailBatchBO : mergedDetail.values()) {
                    Map<String, Object> skuMap = new HashMap<>();
                    skuMap.put("lineNo", detailBatchBO.getLineNo());
                    skuMap.put("skuSn", detailBatchBO.getSku());
                    skuMap.put("name", detailBatchBO.getGoodsName());
                    skuMap.put("qty", detailBatchBO.getActualQuantity());
                    totalSkuList.add(skuMap);
                }
                backMap.put("totalItemList", totalSkuList);
                //主表扩展字段 taotian
                String extensionJson = orderBO.getExtensionJson();
                if (StringUtils.isNotBlank(extensionJson)) {
                    JSONObject parseObject = JSON.parseObject(extensionJson);
                    if (Objects.nonNull(parseObject.get("tallyOrderCode"))) {
                        Map<String, Object> extendPropsMap = new HashMap<>();
                        extendPropsMap.put("tallyOrderCode", parseObject.get("tallyOrderCode"));
                        backMap.put("extendInfo", extendPropsMap);
                    }
                }
                //请求参数
                Map<String, Object> requestMap = new HashMap<>();
                requestMap.put("bizData", JSON.toJSONString(backMap));
                requestMap.put("method", "entry.order.confirm.notify");

                String request = postForm(requestMap, orderNacosConfig.getCallBackUrlQM());
                log.info("[CallBackUpListener-backQMInOrder]===============================orderNo:{},奇门回传返回值:{}", orderBO.getInOrderNo(), request);
                JSONObject jsonObject = JSON.parseObject(request);
                boolean isSuccess = jsonObject.getBooleanValue("success");
                String errorMessage = jsonObject.getString("errorMessage");

                InOrderBO inOrderBO = new InOrderBO();
                inOrderBO.setInOrderNo(orderBO.getInOrderNo());
                //处理返回后的状态
                boolean repeat = !isSuccess && (errorMessage.contains("该单据入库回执已存在") || errorMessage.contains("单据已完成，请重新刷新数据"));
                if (isSuccess || repeat) {
                    inOrderBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                    inOrderBO.setBackReturn("");
                    inOrderManager.updateInOrderByNo(inOrderBO);
                    //节点
                    orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "收货上架回告上游成功", System.currentTimeMillis(), Boolean.FALSE);
                } else {
                    inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                    inOrderBO.setBackReturn(request);
                    inOrderManager.updateInOrderByNo(inOrderBO);
                    //节点
                    orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败：" + request, System.currentTimeMillis(), Boolean.TRUE);
                    backWarning("入库单回传奇门异常orderNo:" + orderBO.getInOrderNo() + ",奇门返回值:" + errorMessage);
                }
                remoteConfigurationFacade.printLog(ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_QM, orderBO.getInOrderNo(), JSON.toJSONString(requestMap), request));
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 回传奇门 入库单
     *
     * @param message
     */
    private void backQMInOrder(OrderMessage message) {
        InOrderBO orderBO = inOrderManager.getInOrderByNo(message.getOrderNo(), message.getUserId());
        if (Objects.isNull(orderBO) || Objects.equals(OrderStatus.ORDER_SUCCESS_ALL.getValue(), orderBO.getBackStatus())) {
            log.warn("[CallBackUpListener-backQMInOrder]=========入库单不存在,或已回传完成=========orderNo:" + message.getOrderNo());
            return;
        }
        // 淘天最后一次回传,且无明细
        if (backTaoTianLsat(orderBO)) {
            return;
        }
        InOrderDetailBatchSearch search = new InOrderDetailBatchSearch();
        search.setInOrderNo(message.getOrderNo());
        search.setBackStatus(OrderStatus.ORDER_WAIT.getValue());
        if (StringUtils.isNotBlank(message.getBackFlag())) {
            search.setBackFlag(message.getBackFlag());
        }
        List<InOrderDetailBatchBO> batchBOList = inOrderDetailBatchManager.listBySearch(search);
        //sku分类
        Map<String, List<InOrderDetailBatchBO>> batchNoMap = new HashMap<>();
        for (InOrderDetailBatchBO batchBO : batchBOList) {
            List<InOrderDetailBatchBO> boList = batchNoMap.get(batchBO.getBackFlag());
            if (CollectionUtils.isEmpty(boList)) {
                boList = new ArrayList<>();
                batchNoMap.put(batchBO.getBackFlag(), boList);
            }
            boList.add(batchBO);
        }
        InOrderDetailSearch inOrderDetailSearch = new InOrderDetailSearch();
        inOrderDetailSearch.setInOrderNo(message.getOrderNo());
        List<InOrderDetailBO> inOrderDetailBOList = inOrderDetailManager.listBySearch(inOrderDetailSearch);
        Map<String, Integer> planQuantityMap = inOrderDetailBOList.stream().collect(Collectors.toMap(d -> getKey(d.getGoodsCode(), d.getLineNo(), d.getBatchCode()), o -> o.getPlanQuantity(), (oldValue, newValue) -> oldValue));

        //回传sku
        Collection<List<InOrderDetailBatchBO>> values = batchNoMap.values();
        int size = values.size();
        int count = 0;
        //所有都回传成功标记,有一个失败,都变成false
        boolean allSuccess = Boolean.TRUE;
        for (List<InOrderDetailBatchBO> list : values) {
            InOrderBO inOrderBO = new InOrderBO();
            inOrderBO.setInOrderNo(orderBO.getInOrderNo());
            Map<String, Object> orderBack = null;
            String errorMessage = null;
            try {
                count++;

                Integer confirmType = 1;
                //设置回传最终状态
                if (Objects.equals(InOrderStatus.RK_ALL.getValue(), orderBO.getStatus()) && Objects.equals(size, count) && allSuccess) {
                    confirmType = 0;
                }
                //组装回传奇门参数
                orderBack = buildInOrderBack(list, orderBO, confirmType, "entry.order.confirm.notify", planQuantityMap);

                String request = postForm(orderBack, orderNacosConfig.getCallBackUrlQM());
                log.info("[CallBackUpListener-backQMInOrder]===============================orderNo{},奇门回传返回值:{}", message.getOrderNo(), request);
                JSONObject jsonObject = JSON.parseObject(request);
                boolean isSuccess = jsonObject.getBooleanValue("success");
                errorMessage = jsonObject.getString("errorMessage");

                //处理返回后的状态
                boolean repeat = !isSuccess && (errorMessage.contains("该单据入库回执已存在") || errorMessage.contains("单据已完成，请重新刷新数据"));
                if (isSuccess || repeat) {
                    //如果是入库完成 && 且是最后一个 && 都完成回传
                    if (Objects.equals(InOrderStatus.RK_ALL.getValue(), orderBO.getStatus()) && Objects.equals(size, count) && allSuccess) {
                        //更新入库单状态为 成功 OrderStatus.ORDER_SUCCESS_ALL
                        inOrderBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                        inOrderBO.setBackReturn("");
                        inOrderManager.updateInOrderByNo(inOrderBO);
                    }
                    //如果是入库 [部分] 完成 && 且是最后一个 && 都完成回传
                    if (Objects.equals(InOrderStatus.RK_PART.getValue(), orderBO.getStatus()) && Objects.equals(size, count) && allSuccess) {
                        //更新入库单状态为 待回传 OrderStatus.ORDER_WAIT
                        inOrderBO.setBackStatus(OrderStatus.ORDER_WAIT.getValue());
                        inOrderBO.setBackReturn("");
                        inOrderManager.updateInOrderByNo(inOrderBO);
                    }
                    //更新入库批次状态
                    for (InOrderDetailBatchBO batchBO : list) {
                        batchBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                        batchBO.setInventoryStatus(null);//防止库存状态被更新
                    }
                    inOrderDetailBatchManager.updateListByIdAndOrderNo(list);
                    //节点
                    orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "收货上架回告上游成功", System.currentTimeMillis(), Boolean.FALSE);
                } else {
                    log.error("[CallBackUpListener-backQMInOrder]=========回传QM异常=========orderNo:{},ex:{}", message.getOrderNo(), repeat);
                    //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
                    allSuccess = Boolean.FALSE;
                    inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                    inOrderBO.setBackReturn(request);
                    inOrderManager.updateInOrderByNo(inOrderBO);
                    //节点
                    orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败：" + request, System.currentTimeMillis(), Boolean.TRUE);

                    backWarning("入库单回传奇门异常orderNo:"+message.getOrderNo() + ",奇门返回值:" + errorMessage);
                }
                remoteConfigurationFacade.printLog(ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_QM, message.getOrderNo(), JSON.toJSONString(orderBack), request));
            } catch (Exception e) {
                log.error("[CallBackUpListener-backQMInOrder]=========回传QM异常=========orderNo:{},ex:", message.getOrderNo(), e);
                //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
                allSuccess = Boolean.FALSE;
                inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                inOrderBO.setBackReturn("回传上游失败!");
                inOrderManager.updateInOrderByNo(inOrderBO);

                ConfigurationLogAddParam logAddParam = ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_QM, message.getOrderNo(), JSON.toJSONString(orderBack), null);
                logAddParam.setExceptionMsg(LogUtils.getPrintStackTraceMessage(e));
                remoteConfigurationFacade.printLog(logAddParam);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败：回传上游失败!", System.currentTimeMillis(), Boolean.TRUE);

                backWarning("入库单回传奇门异常orderNo:"+message.getOrderNo() + ",奇门返回值:" + errorMessage);
            }
        }
    }

    /**
     * 组装奇门回传参数
     *
     * @param list
     * @param orderBO
     * @return
     */
    private Map<String, Object> buildInOrderBack(List<InOrderDetailBatchBO> list, InOrderBO orderBO, Integer confirmType, String method, Map<String, Integer> planQuantityMap) {
        Map<String, Object> backMap = new HashMap<>();
        String backFlag = list.get(0).getBackFlag();
        String businessValue = orderBO.getBusinessValue();
        backMap.put("batchCode", backFlag);
        backMap.put("businessValue", businessValue);
        backMap.put("orderSn", orderBO.getUpstreamNo());
        backMap.put("depotEntryOrderNo", orderBO.getInOrderNo());
        backMap.put("outBizCode", orderBO.getInOrderNo() + backFlag);
        backMap.put("operationTime", orderBO.getActualTime());
        backMap.put("callBackUrl", orderNacosConfig.getCallBackUrlQM());
        backMap.put("confirmType", confirmType);
        backMap.put("warehouseSn", orderBO.getLogicWarehouseCode());//仓库
        backMap.put("shipperSn", orderBO.getOwnerCode());//货主
        backMap.put("userId", orderBO.getUserId());
        boolean isTaoTian = StringUtils.isNotBlank(businessValue) && businessValue.contains(SYSTEM_TAG_TAOTIAN);

        List<Object> skuList = new ArrayList<>();
        for (InOrderDetailBatchBO detailBatchBO : list) {
            if (!Objects.equals(TallyReportMultiType.NORMAL, detailBatchBO.getMultiType()) && !isTaoTian) {
                continue;
            }
            Map<String, Object> skuMap = new HashMap<>();
            skuMap.put("lineNo", detailBatchBO.getLineNo());
            skuMap.put("batchCode", detailBatchBO.getBatchCode());
            skuMap.put("inventoryType", detailBatchBO.getInventoryType());
            skuMap.put("inventoryTypeLevel", detailBatchBO.getInventoryTypeLevel());
            if (Objects.nonNull(detailBatchBO.getProductionDate())) {
                skuMap.put("productionDate", DateUtils.defaultFormat(detailBatchBO.getProductionDate().getTime()));
            }
            if (Objects.nonNull(detailBatchBO.getExpireDate())) {
                skuMap.put("shelfLife", DateUtils.defaultFormat(detailBatchBO.getExpireDate().getTime()));
            }
            skuMap.put("skuSn", detailBatchBO.getSku());
            skuMap.put("externalSku", detailBatchBO.getExternalSku());
            skuMap.put("wmsSkuSn", detailBatchBO.getSku());
            skuMap.put("qty", detailBatchBO.getActualQuantity());
            skuMap.put("planQty", getPlanQtyValue(detailBatchBO.getGoodsCode(), detailBatchBO.getLineNo(), detailBatchBO.getBatchCode(), planQuantityMap));
            if (Objects.equals(InventoryType.QUALITY.getValue(), detailBatchBO.getInventoryType())) {
                skuMap.put("qtyZp", detailBatchBO.getActualQuantity());
                skuMap.put("qtyCp", 0);
            } else {
                skuMap.put("qtyZp", 0);
                skuMap.put("qtyCp", detailBatchBO.getActualQuantity());
            }
            //明细扩展字段 taotian
            if (StringUtils.isNotBlank(detailBatchBO.getExtendInfo())) {
                Map<String, Object> map = JSON.parseObject(detailBatchBO.getExtendInfo(), Map.class);
                if (Objects.nonNull(map.get("snList"))) {
                    skuMap.put("snList", map.get("snList"));
                    map.remove("snList");
                }
                skuMap.put("extendInfo", map);
            }
            skuMap.put("inventoryTypeLevel", detailBatchBO.getInventoryTypeLevel());
            skuList.add(skuMap);
        }
        backMap.put("itemList", skuList);

        //淘天入库单,在最后一次完成的时候,需要回传所有单据
        if (Objects.equals(0, confirmType) && isTaoTian) {
            InOrderDetailBatchSearch search = new InOrderDetailBatchSearch();
            search.setInOrderNo(orderBO.getInOrderNo());
            List<InOrderDetailBatchBO> batchBOList = inOrderDetailBatchManager.listBySearch(search);
            Map<String, InOrderDetailBatchBO> mergedDetail = batchBOList.stream()
                    .collect(Collectors.toMap(batchBO -> getKey(batchBO.getGoodsCode(), batchBO.getLineNo(), null),
                            Function.identity(),
                            (existing, replacement) -> {
                                existing.setActualQuantity(existing.getActualQuantity() + replacement.getActualQuantity());
                                return existing;
                            }
                    ));
            List<Object> totalSkuList = new ArrayList<>();
            for (InOrderDetailBatchBO detailBatchBO : mergedDetail.values()) {
                Map<String, Object> skuMap = new HashMap<>();
                skuMap.put("lineNo", detailBatchBO.getLineNo());
                skuMap.put("skuSn", detailBatchBO.getSku());
                skuMap.put("name", detailBatchBO.getGoodsName());
                skuMap.put("qty", detailBatchBO.getActualQuantity());
                totalSkuList.add(skuMap);
            }
            backMap.put("totalItemList", totalSkuList);
        }
        //主表扩展字段 taotian
        String extensionJson = orderBO.getExtensionJson();
        if (StringUtils.isNotBlank(extensionJson)) {
            JSONObject parseObject = JSON.parseObject(extensionJson);
            if (Objects.nonNull(parseObject.get("tallyOrderCode"))) {
                Map<String, Object> extendPropsMap = new HashMap<>();
                extendPropsMap.put("tallyOrderCode", parseObject.get("tallyOrderCode"));
                backMap.put("extendInfo", extendPropsMap);
            }
        }

        Integer type = orderBO.getType();
        if (StringUtils.isNotBlank(businessValue)) {
            JSONObject jsonObject = JSON.parseObject(businessValue);
            String orderType = jsonObject.getString("orderType");
            if (Objects.equals("THRK", orderType) || Objects.equals("HHRK", orderType)) {
                type = InOrderType.TH_RK.getValue();
            } else if (Objects.equals("ECRQDBRK", orderType)) {
                type = 0;//淘天的二次入区调拨入库(实际走入库回传),故意设置成0,避免method走到退货入库回传
            }
        }
        if (Objects.equals(InOrderType.TH_RK.getValue(), type)) {
            method = "returnorder.confirm";
        }
        //请求参数
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("bizData", JSON.toJSONString(backMap));
        requestMap.put("method", method);
        return requestMap;
    }

    /**
     * 回传奇门 出库单
     *
     * @param message
     */
    private void backQMOutOrder(OrderMessage message, String method) {
        OutOrderBO orderBO = outOrderManager.getOutOrderByNo(message.getOrderNo(), message.getUserId());
        if (Objects.isNull(orderBO) || Objects.equals(OrderStatus.ORDER_SUCCESS_ALL.getValue(), orderBO.getBackStatus())) {
            log.warn("[CallBackUpListener-backQMOutOrder]=========出库单不存在,或已回传完成=========orderNo:" + message.getOrderNo());
            return;
        }
        if (!Objects.equals(OutOrderStatus.CK_ALL.getValue(), orderBO.getStatus())) {
            log.warn("[CallBackUpListener-backQMOutOrder]=========出库单不是全部回传状态=========orderNo:" + message.getOrderNo());
            return;
        }

        OutOrderBO outOrderBO = new OutOrderBO();
        outOrderBO.setOutOrderNo(orderBO.getOutOrderNo());
        outOrderBO.setUserId(orderBO.getUserId());
        Map<String, Object> orderBack = null;
        String errorMessage = null;
        try {
            OutOrderLogisticsSearch outOrderLogisticsSearch = new OutOrderLogisticsSearch();
            outOrderLogisticsSearch.setOrderNo(message.getOrderNo());
            outOrderLogisticsSearch.setBackStatus(OrderStatus.ORDER_WAIT.getValue());
            List<OutOrderLogisticsBO> logisticsBOList = outOrderLogisticsManager.listBySearch(outOrderLogisticsSearch);
            if (CollectionUtils.isEmpty(logisticsBOList) || logisticsBOList.size() == 0) {
                log.warn("[CallBackUpListener-backQMOutOrder]=========出库单包裹信息不存在=========orderNo:" + orderBO.getOutOrderNo());
                return;
            }
            for (OutOrderLogisticsBO logisticsBO : logisticsBOList) {
                OutOrderLogisticsDetailSearch orderLogisticsDetailSearch = new OutOrderLogisticsDetailSearch();
                orderLogisticsDetailSearch.setOrderNo(message.getOrderNo());
                orderLogisticsDetailSearch.setBackFlag(logisticsBO.getBackFlag());
                List<OutOrderLogisticsDetailBO> list = outOrderLogisticsDetailManager.listBySearch(orderLogisticsDetailSearch);
                logisticsBO.setSkuList(list);
            }
            List<OutOrderDetailBO> outOrderDetailBOList = outOrderDetailManager.listOutOrderDetailByNO(message.getOrderNo());
            Map<String, String> lineNoMap = outOrderDetailBOList.stream().collect(Collectors.toMap(d -> getKey(d.getGoodsCode(), null, d.getBatchCode()), o -> o.getLineNo(), (oldValue, newValue) -> oldValue));

            orderBack = buildOutOrderBack(logisticsBOList, orderBO, method, lineNoMap);

            String request = postForm(orderBack, orderNacosConfig.getCallBackUrlQM());
            log.info("[CallBackUpListener-backQMOutOrder]===============================orderNo:{},奇门回传返回值:{}", message.getOrderNo(), request);
            JSONObject jsonObject = JSON.parseObject(request);
            boolean isSuccess = jsonObject.getBooleanValue("success");
            errorMessage = jsonObject.getString("errorMessage");

            //处理返回后的状态
            if (isSuccess) {
                //更新出库单状态为 成功 OrderStatus.ORDER_SUCCESS_ALL
                outOrderBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                outOrderBO.setBackReturn("");
                //更新出库回传状态
                for (OutOrderLogisticsBO logisticsBO : logisticsBOList) {
                    logisticsBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                    outOrderLogisticsManager.updateByNo(logisticsBO);
                }
                //节点
                orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "回传上游成功", System.currentTimeMillis(), Boolean.FALSE);
            } else {
                log.error("[CallBackUpListener-backQMOutOrder]=========回传QM异常=========orderNo:{},ex:{}", message.getOrderNo(), request);
                //发送失败,更新出库单为 定时待回传 OrderStatus.ORDER_FAILED
                outOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                outOrderBO.setBackReturn(request);
                //节点
                orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "失败:"+request, System.currentTimeMillis(), Boolean.TRUE);

                backWarning("出库单回传奇门异常orderNo:"+message.getOrderNo() + ",奇门返回值:" + errorMessage);
            }
            outOrderManager.updateOutOrderByNo(outOrderBO);
            remoteConfigurationFacade.printLog(ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_QM, message.getOrderNo(), JSON.toJSONString(orderBack), request));
        } catch (Exception e) {
            log.error("[CallBackUpListener-backQMOutOrder]=========回传QM异常=========orderNo:{},ex:", message.getOrderNo(), e);
            //发送失败,更新出库单为 定时待回传 OrderStatus.ORDER_FAILED
            outOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
            outOrderBO.setBackReturn("回传上游异常!");
            outOrderManager.updateOutOrderByNo(outOrderBO);

            ConfigurationLogAddParam logAddParam = ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_QM, message.getOrderNo(), JSON.toJSONString(orderBack), null);
            logAddParam.setExceptionMsg(LogUtils.getPrintStackTraceMessage(e));
            remoteConfigurationFacade.printLog(logAddParam);
            //节点
            orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "失败:回传上游异常！", System.currentTimeMillis(), Boolean.TRUE);

            backWarning("出库单回传奇门异常orderNo:"+message.getOrderNo() + ",奇门返回值:" + errorMessage);
        }
    }

    /**
     * 组装奇门回传参数
     *
     * @param logisticsBOList
     * @param orderBO
     * @return
     */
    private Map<String, Object> buildOutOrderBack(List<OutOrderLogisticsBO> logisticsBOList, OutOrderBO orderBO, String method, Map<String, String> lineNoMap) {
        Map<String, Object> backMap = new HashMap<>();
        backMap.put("businessValue", orderBO.getBusinessValue());
        backMap.put("orderSn", orderBO.getUpstreamNo());
        backMap.put("depotOutboundOrderNo", orderBO.getOutOrderNo());
        backMap.put("callBackUrl", orderNacosConfig.getCallBackUrlQM());
        backMap.put("warehouseSn", orderBO.getLogicWarehouseCode());
        backMap.put("shipperSn", orderBO.getOwnerCode());
        backMap.put("userId", orderBO.getUserId());
        if (Objects.nonNull(orderBO.getActualTime())) {
            backMap.put("orderConfirmTime", DateUtils.format(orderBO.getActualTime()));
        }

        List<Object> logisticsList = new ArrayList<>();
        for (OutOrderLogisticsBO logisticsBO : logisticsBOList) {
            Map<String, Object> logisticsMap = new HashMap<>();
            logisticsMap.put("weight", logisticsBO.getPackageWeight());
            logisticsMap.put("expressNo", logisticsBO.getLogisticsNo());
            logisticsMap.put("logisticCode", logisticsBO.getLogisticsCompanyCode());
            logisticsMap.put("deliverTime", orderBO.getActualTime());

            List<Object> skuList = new ArrayList<>();
            for (OutOrderLogisticsDetailBO detailBatchBO : logisticsBO.getSkuList()) {
                Map<String, Object> skuMap = new HashMap<>();
//                skuMap.put("lineNo", getLineNoValue(detailBatchBO.getGoodsCode(), detailBatchBO.getBatchCode(), lineNoMap));
                skuMap.put("lineNo", detailBatchBO.getLineNo());
                skuMap.put("batchCode", detailBatchBO.getBatchCode());
                skuMap.put("inventoryType", detailBatchBO.getInventoryType());
                skuMap.put("productionDate", DateUtil.formatDate(detailBatchBO.getProductionDate()));
                if (Objects.nonNull(detailBatchBO.getProductionDate())) {
                    skuMap.put("productDate", DateUtils.defaultFormat(detailBatchBO.getProductionDate().getTime()));
                }
                if (Objects.nonNull(detailBatchBO.getExpireDate())) {
                    skuMap.put("expireDate", DateUtils.defaultFormat(detailBatchBO.getExpireDate().getTime()));
                }
                skuMap.put("skuSn", detailBatchBO.getSku());
                skuMap.put("externalSku", detailBatchBO.getExternalSku());
                skuMap.put("qty", detailBatchBO.getActualQuantity());
                skuMap.put("inventoryTypeLevel", detailBatchBO.getInventoryTypeLevel());
                skuList.add(skuMap);
            }
            logisticsMap.put("itemList", skuList);
            logisticsList.add(logisticsMap);
        }
        backMap.put("packList", logisticsList);

        //请求参数
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("bizData", JSON.toJSONString(backMap));
        requestMap.put("method", method);
        requestMap.put("packFormat", true);
        return requestMap;
    }

    /**
     * 组装奇门回传参数
     *
     * @param list
     * @param orderBO
     * @return
     */
    private Map<String, Object> buildOutOrderBack(List<OutOrderDetailBO> list, OutOrderBO orderBO) {
        Map<String, Object> backMap = new HashMap<>();
        backMap.put("businessValue", orderBO.getBusinessValue());
        backMap.put("orderSn", orderBO.getUpstreamNo());
        backMap.put("depotOutboundOrderNo", orderBO.getOutOrderNo());
        backMap.put("callBackUrl", orderNacosConfig.getCallBackUrlQM());
        backMap.put("weight", orderBO.getWeight());
        backMap.put("warehouseSn", orderBO.getLogicWarehouseCode());
        backMap.put("operationTime", orderBO.getActualTime());

        List<Object> skuList = new ArrayList<>();
        for (OutOrderDetailBO detailBatchBO : list) {
            Map<String, Object> skuMap = new HashMap<>();
            skuMap.put("batchCode", detailBatchBO.getBatchCode());
            skuMap.put("inventoryType", detailBatchBO.getInventoryType());
            skuMap.put("productionDate", DateUtil.formatDateTime(detailBatchBO.getProductionDate()));
            skuMap.put("expireDate", DateUtil.formatDateTime(detailBatchBO.getExpireDate()));
            skuMap.put("skuSn", detailBatchBO.getSku());
            skuMap.put("qty", detailBatchBO.getActualQuantity());
            skuList.add(skuMap);
        }
        backMap.put("itemList", skuList);

        EntityAndOwnerResult entityAndOwnerResult = remoteWarehouseFacade.getEntityAndOwnerResult(orderBO.getLogicWarehouseCode());
        backMap.put("shipperSn", entityAndOwnerResult.getOwnerCode());

        OutOrderLogisticsSearch outOrderLogisticsSearch = new OutOrderLogisticsSearch();
        outOrderLogisticsSearch.setOrderNo(orderBO.getOutOrderNo());
        outOrderLogisticsSearch.setBackStatus(OrderStatus.ORDER_WAIT.getValue());
        List<OutOrderLogisticsBO> logisticsBOList = outOrderLogisticsManager.listBySearch(outOrderLogisticsSearch);
        if (CollectionUtil.isNotEmpty(logisticsBOList) && logisticsBOList.size() > 0) {
            OutOrderLogisticsBO logisticsBO = logisticsBOList.get(0);
            backMap.put("expressCode", logisticsBO.getLogisticsCompanyCode());//快递公司编码
            backMap.put("shippingCode", logisticsBO.getLogisticsNo());//快递单号
        }

        //请求参数
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("bizData", JSON.toJSONString(backMap));
        requestMap.put("method", "order.stock.out.confirm.notify");
        return requestMap;
    }

    /**
     * 回传OMS 出库单
     *
     * @param message
     */
    private void backOMSOutOrder(OrderMessage message) {
        OutOrderBO orderBO = outOrderManager.getOutOrderByNo(message.getOrderNo(), message.getUserId());
        if (Objects.isNull(orderBO) || Objects.equals(OrderStatus.ORDER_SUCCESS_ALL.getValue(), orderBO.getBackStatus())) {
            log.warn("[CallBackUpListener-backOMSOutOrder]=========出库单不存在,或已回传完成=========orderNo:" + message.getOrderNo());
            return;
        }
        if (!Objects.equals(OutOrderStatus.CK_ALL.getValue(), orderBO.getStatus())) {
            log.warn("[CallBackUpListener-backOMSOutOrder]=========出库单不是全部回传状态=========orderNo:" + message.getOrderNo());
            return;
        }
        //轨迹查询
        Boolean trajectory = queryTrajectory(orderBO);
        if (!trajectory) {
            return;
        }
        OutOrderLogisticsSearch outOrderLogisticsSearch = new OutOrderLogisticsSearch();
        outOrderLogisticsSearch.setOrderNo(message.getOrderNo());
        outOrderLogisticsSearch.setBackStatus(OrderStatus.ORDER_WAIT.getValue());
        List<OutOrderLogisticsBO> logisticsBOList = outOrderLogisticsManager.listBySearch(outOrderLogisticsSearch);
        if (CollectionUtils.isEmpty(logisticsBOList) || logisticsBOList.size() == 0) {
            log.warn("[CallBackUpListener-backOMSOutOrder]=========出库单包裹信息不存在=========orderNo:" + orderBO.getOutOrderNo());
            return;
        }

        for (OutOrderLogisticsBO logisticsBO : logisticsBOList) {
            OutOrderLogisticsDetailSearch orderLogisticsDetailSearch = new OutOrderLogisticsDetailSearch();
            orderLogisticsDetailSearch.setOrderNo(message.getOrderNo());
            orderLogisticsDetailSearch.setBackFlag(logisticsBO.getBackFlag());
            List<OutOrderLogisticsDetailBO> list = outOrderLogisticsDetailManager.listBySearch(orderLogisticsDetailSearch);
            logisticsBO.setSkuList(list);
        }

        OutOrderBO outOrderBO = new OutOrderBO();
        outOrderBO.setOutOrderNo(orderBO.getOutOrderNo());
        Map<String, Object> orderBack = null;
        String errorMessage = null;
        try {
            // 回传ccs,保税且是菜鸟单
            if (Objects.equals(1, orderBO.getTradeType()) && orderBO.getDownstreamNo().startsWith("LP")) {
                remoteCdsFacade.backCompletedToCCS(orderBO.getUpstreamNo());
            }

            //组装回传OMS参数
            orderBack = buildOutOrderBackOMS(orderBO, logisticsBOList, 0);

            String request = postJson(orderBack, orderNacosConfig.getCallBackUrlOMS(), SYSTEM_OMS);
            log.info("[CallBackUpListener-backOMSOutOrder]===============================orderNo:{},OMS回传返回值:{}", message.getOrderNo(), request);
            JSONObject jsonObject = JSON.parseObject(request);
            Integer code = jsonObject.getInteger("code");
            errorMessage = jsonObject.getString("message");

            //处理返回后的状态
            if (Objects.equals(AresContext.ARES_SUCCESS, code)) {
                outOrderBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                outOrderBO.setBackReturn("");
                //更新出库回传状态
                for (OutOrderLogisticsBO logisticsBO : logisticsBOList) {
                    logisticsBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                    outOrderLogisticsManager.updateByNo(logisticsBO);
                }
                //节点
                orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "回传上游成功", System.currentTimeMillis(), Boolean.FALSE);
            } else {
                log.error("[CallBackUpListener-backOMSOutOrder]=========回传OMS异常=========orderNO:{},ex:{}", message.getOrderNo(), request);
                outOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                outOrderBO.setBackReturn(request.substring(0,255));
                //节点
                orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "失败:"+request, System.currentTimeMillis(), Boolean.TRUE);

                backWarning("C单回传OMS异常orderNo:"+message.getOrderNo() + ",OMS返回值:" + errorMessage);
            }
            outOrderManager.updateOutOrderByNo(outOrderBO);
            remoteConfigurationFacade.printLog(ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_OMS, message.getOrderNo(), JSON.toJSONString(orderBack), request));
        } catch (BusinessException e) {
            log.error("[CallBackUpListener-backOMSOutOrder]=========回传OMS异常=========orderNO:{},ex:", message.getOrderNo(), e);
            //发送失败,更新出库单为 定时待回传 OrderStatus.ORDER_FAILED
            outOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
            outOrderBO.setBackReturn("回传上游异常:" + e.getMessage());
            outOrderManager.updateOutOrderByNo(outOrderBO);

            ConfigurationLogAddParam logAddParam = ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_OMS, message.getOrderNo(), JSON.toJSONString(orderBack), null);
            logAddParam.setExceptionMsg(LogUtils.getPrintStackTraceMessage(e));
            remoteConfigurationFacade.printLog(logAddParam);
            //节点
            orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "失败:"+e.getMessage(), System.currentTimeMillis(), Boolean.TRUE);

            backWarning("C单回传OMS异常orderNo:"+message.getOrderNo() + ",OMS返回值:" + errorMessage);
        } catch (Exception e) {
            log.error("[CallBackUpListener-backOMSOutOrder]=========回传OMS异常=========orderNO:{},ex:", message.getOrderNo(), e);
            //发送失败,更新出库单为 定时待回传 OrderStatus.ORDER_FAILED
            outOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
            outOrderBO.setBackReturn("回传上游异常!");
            outOrderManager.updateOutOrderByNo(outOrderBO);

            ConfigurationLogAddParam logAddParam = ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_OMS, message.getOrderNo(), JSON.toJSONString(orderBack), null);
            logAddParam.setExceptionMsg(LogUtils.getPrintStackTraceMessage(e));
            remoteConfigurationFacade.printLog(logAddParam);
            //节点
            orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "失败:回传上游失败!", System.currentTimeMillis(), Boolean.TRUE);

            backWarning("C单回传OMS异常orderNo:"+message.getOrderNo() + ",OMS返回值:" + errorMessage);
        }
    }

    /**
     * 菜鸟轨迹查询
     * @param orderBO
     * @return
     */
    private Boolean queryTrajectory(OutOrderBO orderBO) {
        log.info("[CallBackUpListener-queryTrajectory]=========菜鸟轨迹节点查询=========:{}", JSON.toJSONString(orderBO));
        //是否查询轨迹
        if (!Objects.equals("0", orderNacosConfig.getTrajectoryQueryStatus())) {
            return Boolean.TRUE;
        }
        //保税C单,查询是否菜鸟仓
        if (Objects.equals(TradeType.BONDED.getValue(), orderBO.getTradeType()) && Objects.equals("PDD", orderBO.getSourcePlatform()) && StringUtils.isNotEmpty(orderBO.getDownstreamNo()) && orderBO.getDownstreamNo().startsWith("LP")) {
            List<WmsTraceBackParam> wmsTraceBackParams = remoteConfigurationFacade.trajectoryQuery(orderBO.getDownstreamNo(), orderBO.getOutOrderNo(), orderBO.getOwnerCode());
            String errorMsg = "";
            log.info("[CallBackUpListener-queryTrajectory]========={}菜鸟轨迹节点查询返回值=========:{}", orderBO.getOutOrderNo(), JSON.toJSONString(wmsTraceBackParams));
            List<Integer> collect = wmsTraceBackParams.stream().map(trace -> trace.getStatus()).collect(Collectors.toList());
            boolean traceSendSuccess = wmsTraceBackParams.size() == 7 || (collect.size() == 6 && !collect.contains(OrderTraceStatus.PRINT.getValue()));
            if (traceSendSuccess) {
                for (WmsTraceBackParam wmsTraceBackParam : wmsTraceBackParams) {
                    wmsTraceBackParam.setOrderNo(orderBO.getOutOrderNo());
                    wmsTraceBackParam.setOwnerCode(orderBO.getOwnerCode());
                    wmsTraceBackParam.setWarehouseCode(orderBO.getWarehouseCode());
                    wmsTraceBackParam.setOrderType(orderBO.getType());
                    Boolean trajectorySend = remoteConfigurationFacade.trajectorySend(wmsTraceBackParam);
                    if (!trajectorySend) {
                        log.error("[CallBackUpListener-queryTrajectory]=========菜鸟轨迹节点回传OMS异常=========orderNo:{},节点信息:{}", orderBO.getOutOrderNo(), JSON.toJSONString(wmsTraceBackParam));
                        traceSendSuccess = trajectorySend;
                        errorMsg = "菜鸟轨迹节点回传OMS异常!";
                        break;
                    }
                }
            } else {
                errorMsg = "菜鸟轨迹节点查询异常!";
                log.error("[CallBackUpListener-queryTrajectory]=========菜鸟轨迹节点查询异常=========orderNo:{},节点信息:{}", orderBO.getOutOrderNo(), JSON.toJSONString(wmsTraceBackParams));
                sendTrajectoryMsg(orderBO);
            }
            if (!traceSendSuccess) {
                OutOrderBO outOrderBO1 = new OutOrderBO();
                outOrderBO1.setOutOrderNo(orderBO.getOutOrderNo());
                outOrderBO1.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                outOrderBO1.setBackReturn("回传上游异常:" + errorMsg);
                outOrderManager.updateOutOrderByNo(outOrderBO1);
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    private void sendTrajectoryMsg(OutOrderBO orderBO) {
        if(Objects.nonNull(orderBO.getActualTime())) {
            Long aLong = DateUtils.afterNumMinute(new Date(orderBO.getActualTime()), 10);
            if (aLong > System.currentTimeMillis()) {
                return;
            }
        }
        try {
            String message = "C单回传OMS异常orderNo:" + orderBO.getOutOrderNo() + ",轨迹节点缺少";
            String msg = "{\"msgtype\": \"text\",\"text\": {\"content\": \"" + message + "\",\"mentioned_mobile_list\":" + Arrays.toString(orderNacosConfig.getCnTrajectoryMobile().split(",")) + "}}";
            HttpRequestUtils.postJson(msg, orderNacosConfig.getCnTrajectoryRobotUrl(), "单据回传上游失败");
        } catch (Exception e) {
            log.error("菜鸟轨迹节点查询,企业微信通知异常=========outOrderNo:{},ex:", orderBO.getOutOrderNo(), e);
        }
    }

    /**
     * 组装OMS回传参数
     *
     * @param orderBO
     * @param logisticsBOList
     * @param orderBO
     * @return
     */
    private Map<String, Object> buildOutOrderBackOMS(OutOrderBO orderBO, List<OutOrderLogisticsBO> logisticsBOList, Integer confirmType) {
        Map<String, Object> backMap = new HashMap<>();
        backMap.put("orderNo", orderBO.getOutOrderNo());
        backMap.put("businessNo", orderBO.getBusinessNo());
        backMap.put("upstreamNo", orderBO.getUpstreamNo());
        backMap.put("externalNo", orderBO.getExternalNo());
        if (Objects.nonNull(orderBO.getActualTime())) {
            backMap.put("actualTime", DateUtil.formatDateTime(new Date(orderBO.getActualTime())));
            backMap.put("operationTime", DateUtil.formatDateTime(new Date(orderBO.getActualTime())));
        }
        backMap.put("isConfirm", confirmType);

        List<Object> logisticsList = new ArrayList<>();
        for (OutOrderLogisticsBO logisticsBO : logisticsBOList) {
            Map<String, Object> logisticsMap = new HashMap<>();
            logisticsMap.put("packageLength", logisticsBO.getPackageLength());
            logisticsMap.put("packageWidth", logisticsBO.getPackageWidth());
            logisticsMap.put("packageHeight", logisticsBO.getPackageHeight());
            logisticsMap.put("packageVolume", logisticsBO.getPackageVolume());
            logisticsMap.put("packageWeight", logisticsBO.getPackageWeight());
            logisticsMap.put("logisticsNo", logisticsBO.getLogisticsNo());
            logisticsMap.put("waybillNo", logisticsBO.getLogisticsNo());
            logisticsMap.put("logisticsCompanyCode", logisticsBO.getLogisticsCompanyCode());
            logisticsMap.put("packagingMaterials", logisticsBO.getPackagingMaterials());
            logisticsMap.put("consumablesMaterials", logisticsBO.getConsumablesMaterials());

            List<Object> skuList = new ArrayList<>();
            for (OutOrderLogisticsDetailBO detailBatchBO : logisticsBO.getSkuList()) {
                Map<String, Object> skuMap = new HashMap<>();
                skuMap.put("lineNo", detailBatchBO.getLineNo());
                skuMap.put("batchCode", detailBatchBO.getBatchCode());
                skuMap.put("internalBatchCode", detailBatchBO.getInternalBatchCode());
                skuMap.put("sku", detailBatchBO.getSku());
                skuMap.put("actualQuantity", detailBatchBO.getActualQuantity());
                skuMap.put("barcode", detailBatchBO.getBarcode());
                skuMap.put("inventoryType", detailBatchBO.getInventoryType());
                skuMap.put("productDate", DateUtil.formatDate(detailBatchBO.getProductionDate()));
                skuMap.put("productionDate", DateUtil.formatDate(detailBatchBO.getProductionDate()));
                skuMap.put("expireDate", DateUtil.formatDate(detailBatchBO.getExpireDate()));
                skuMap.put("extendInfo", detailBatchBO.getExtendInfo());
                skuList.add(skuMap);
            }
            logisticsMap.put("skuList", skuList);
            logisticsList.add(logisticsMap);
        }
        backMap.put("logisticsParamList", logisticsList);

        String extensionJson = orderBO.getExtensionJson();
        if (extensionJson.startsWith("{") && extensionJson.endsWith("}")) {
            Map<String, Object> map = JSON.parseObject(extensionJson, Map.class);
            Map<String, Object> logisticsMap = new HashMap<>();
            logisticsMap.put("sendContactName", map.get("sendContactName"));
            logisticsMap.put("sendContactPhone", map.get("sendContactPhone"));
            logisticsMap.put("sendProvince", map.get("sendProvince"));
            logisticsMap.put("sendCity", map.get("sendCity"));
            logisticsMap.put("sendDistrict", map.get("sendDistrict"));
            logisticsMap.put("sendAddress", map.get("sendAddress"));
            backMap.put("backLogisticsInfo", logisticsMap);
        }

        return backMap;
    }

    /**
     * 回传OMS 入库单
     *
     * @param message
     */
    private void backOMSInOrder(OrderMessage message) {
        InOrderBO orderBO = inOrderManager.getInOrderByNo(message.getOrderNo(), message.getUserId());
        if (Objects.isNull(orderBO) || Objects.equals(OrderStatus.ORDER_NORMAL.getValue(), orderBO.getBackStatus()) || Objects.equals(OrderStatus.ORDER_SUCCESS_ALL.getValue(), orderBO.getBackStatus())) {
            log.warn("[CallBackUpListener-backOMSInOrder]=========入库单不存在,或无需回传,或已回传完成=========orderNo:" + message.getOrderNo());
            return;
        }
        if (!Objects.equals(InOrderStatus.RK_ALL.getValue(), orderBO.getStatus())) {
            log.warn("[CallBackUpListener-backOMSInOrder]=========入库单不是全部回传状态=========orderNo:" + message.getOrderNo());
            return;
        }
        //抖音且是保税的,需要回传字节
        if (Objects.equals(SOURCE_PLATFORM_DOUYIN, orderBO.getSourcePlatform()) && Objects.equals(TradeType.BONDED.getValue(), orderBO.getTradeType())) {
            InOrderBO inOrderBO = new InOrderBO();
            inOrderBO.setInOrderNo(orderBO.getInOrderNo());
            Map<String, Object> orderBack = null;
            String errorMessage = null;
            try {
                //查询回传的批次信息
                InOrderDetailBatchSearch search = new InOrderDetailBatchSearch();
                search.setInOrderNo(message.getOrderNo());
                search.setBackStatus(OrderStatus.ORDER_WAIT.getValue());
                List<InOrderDetailBatchBO> batchBOList = inOrderDetailBatchManager.listBySearch(search);
                if (CollectionUtil.isEmpty(batchBOList)) {
                    log.warn("[CallBackUpListener-backOMSInOrder]=========回传的批次库存为空=========orderNo:" + message.getOrderNo());
                    return;
                }

                //组装回传抖音参数
                orderBack = buildInOrderBackOMS(orderBO, batchBOList);
                String request = postForm(orderBack, orderNacosConfig.getCallBackUrlQM());
                log.info("[CallBackUpListener-backOMSInOrder]===============================orderNo:{},抖音退货入库返回值:{}", message.getOrderNo(), request);
                JSONObject jsonObject = JSON.parseObject(request);
                boolean isSuccess = jsonObject.getBooleanValue("success");
                errorMessage = jsonObject.getString("errorMessage");
                if (isSuccess || Objects.equals("幂等调用异常", errorMessage)) {
                    //更新入库单状态为 成功 OrderStatus.ORDER_SUCCESS_ALL
                    inOrderBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                    inOrderBO.setBackReturn("");
                    inOrderManager.updateInOrderByNo(inOrderBO);
                    //更新入库批次状态
                    for (InOrderDetailBatchBO batchBO : batchBOList) {
                        batchBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                        batchBO.setInventoryStatus(null);//防止库存状态被更新
                    }
                    inOrderDetailBatchManager.updateListByIdAndOrderNo(batchBOList);
                    //节点
                    orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "收货上架回告上游成功", System.currentTimeMillis(), Boolean.FALSE);
                } else {
                    log.error("[CallBackUpListener-backOMSInOrder]=========回传抖音退货入库异常=========orderNo:{},ex:{}", message.getOrderNo(), request);
                    //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
                    inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                    inOrderBO.setBackReturn(request);
                    inOrderManager.updateInOrderByNo(inOrderBO);
                    //节点
                    orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败:"+request, System.currentTimeMillis(), Boolean.TRUE);

                    backWarning("退货入库单回传字节异常orderNo:"+message.getOrderNo() + ",字节返回值:" + errorMessage);
                }
                remoteConfigurationFacade.printLog(ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_QM, message.getOrderNo(), JSON.toJSONString(orderBack), request));
            } catch (Exception e) {
                log.error("[CallBackUpListener-backOMSInOrder]=========回传抖音退货入库异常=========orderNo:{},ex:", message.getOrderNo(), e);
                //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
                inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                inOrderBO.setBackReturn("回传上游失败!");
                inOrderManager.updateInOrderByNo(inOrderBO);

                ConfigurationLogAddParam logAddParam = ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_QM, message.getOrderNo(), JSON.toJSONString(orderBack), null);
                logAddParam.setExceptionMsg(LogUtils.getPrintStackTraceMessage(e));
                remoteConfigurationFacade.printLog(logAddParam);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败:回传上游失败!", System.currentTimeMillis(), Boolean.TRUE);

                backWarning("退货入库单回传字节异常orderNo:"+message.getOrderNo() + ",字节返回值:" + errorMessage);
            }
        } else {
            InOrderBO inOrderBO = new InOrderBO();
            inOrderBO.setInOrderNo(orderBO.getInOrderNo());
            inOrderBO.setBackStatus(OrderStatus.ORDER_NORMAL.getValue());
            inOrderBO.setBackReturn("无需回传!");
            inOrderManager.updateInOrderByNo(inOrderBO);
        }
    }

    /**
     * 组装OMS回传参数
     *
     * @param orderBO
     * @param batchBOList
     * @return
     */
    private Map<String, Object> buildInOrderBackOMS(InOrderBO orderBO, List<InOrderDetailBatchBO> batchBOList) {
        LogicWarehouseResult warehouseResult = remoteWarehouseFacade.getLogicWarehouseByCode(orderBO.getLogicWarehouseCode());

        Map<String, Object> backMap = new HashMap<>();
        backMap.put("userId", orderBO.getUserId());
        backMap.put("adjustOrderNo", orderBO.getInOrderNo());
        backMap.put("adjustType", AdjustType.ADD.getValue());
        backMap.put("idempotentNo", orderBO.getInOrderNo());
        backMap.put("ownerCode", orderBO.getOwnerCode());
        backMap.put("upWarehouseCode", warehouseResult.getUpEntityWarehouseCode());

        List<Object> skuList = new ArrayList<>();
        for (InOrderDetailBatchBO detailBatchBO : batchBOList) {
            Map<String, Object> skuMap = new HashMap<>();
            skuMap.put("userId", orderBO.getUserId());
            skuMap.put("adjustOrderNo", orderBO.getInOrderNo());
            skuMap.put("inventoryType", detailBatchBO.getInventoryType());
            skuMap.put("sku", detailBatchBO.getExternalSku());
            skuMap.put("adjustNum", detailBatchBO.getActualQuantity());
            if (Objects.equals(InventoryType.DEFECTIVE.getValue(), detailBatchBO.getInventoryType())) {
                skuMap.put("transformType", 4);
            } else {
                skuMap.put("transformType", 1);
            }
            skuList.add(skuMap);
        }
        backMap.put("details", skuList);

        //请求参数
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("method", "erp.adjustment.notify");
        requestMap.put("bizData", JSON.toJSONString(backMap));
        return requestMap;
    }

    /**
     * 回传GLP 入库单
     *
     * @param message
     */
    private void backGLPInOrder(OrderMessage message) {
        InOrderBO orderBO = inOrderManager.getInOrderByNo(message.getOrderNo(), message.getUserId());
        if (Objects.isNull(orderBO) || Objects.equals(OrderStatus.ORDER_SUCCESS_ALL.getValue(), orderBO.getBackStatus())) {
            log.warn("[CallBackUpListener-backGLPInOrder]=========入库单不存在,或已回传完成=========orderNo:" + message.getOrderNo());
            return;
        }

        List<InOrderDetail> orderDetailList = inOrderDetailService.selectListByOrderNo(message.getOrderNo());
        Map<String, InOrderDetail> orderDetailMap = orderDetailList.stream()
                .collect(Collectors.toMap(detail -> detail.getSku() + detail.getLineNo(), inOrderDetail -> inOrderDetail, (v1, v2) -> {
                    v1.setPlanQuantity(v1.getPlanQuantity() + v2.getPlanQuantity());
                    return v1;
                }));
        InOrderDetailBatchSearch search = new InOrderDetailBatchSearch();
        search.setInOrderNo(message.getOrderNo());
        search.setBackStatus(OrderStatus.ORDER_WAIT.getValue());
        search.setMultiType(TallyReportMultiType.NORMAL);
        if (StringUtils.isNotBlank(message.getBackFlag())) {
            search.setBackFlag(message.getBackFlag());
        }
        List<InOrderDetailBatchBO> batchBOList = inOrderDetailBatchManager.listBySearch(search);
        //sku分类
        Map<String, List<InOrderDetailBatchBO>> batchNoMap = new HashMap<>();
        for (InOrderDetailBatchBO batchBO : batchBOList) {
            List<InOrderDetailBatchBO> boList = batchNoMap.get(batchBO.getBackFlag());
            if (CollectionUtils.isEmpty(boList)) {
                boList = new ArrayList<>();
                batchNoMap.put(batchBO.getBackFlag(), boList);
            }
            String key = StringUtils.isNotBlank(batchBO.getLineNo()) ? batchBO.getSku() + batchBO.getLineNo() : batchBO.getSku();
            InOrderDetail inOrderDetail = orderDetailMap.get(key);
            if (Objects.nonNull(inOrderDetail)) {
                batchBO.setGoodsName(inOrderDetail.getGoodsName());
                batchBO.setPlanQuantity(inOrderDetail.getPlanQuantity());
                if (StringUtils.isBlank(batchBO.getLineNo())) {
                    batchBO.setLineNo(inOrderDetail.getLineNo());
                }
            }
            boList.add(batchBO);
        }
        //回传sku
        Collection<List<InOrderDetailBatchBO>> values = batchNoMap.values();
        int size = values.size();
        int count = 0;
        //所有都回传成功标记,有一个失败,都变成false
        boolean allSuccess = Boolean.TRUE;
        for (List<InOrderDetailBatchBO> list : values) {
            InOrderBO inOrderBO = new InOrderBO();
            inOrderBO.setInOrderNo(orderBO.getInOrderNo());
            Map<String, Object> orderBack = null;
            String request = null;
            try {
                count++;

                Integer confirmType = 1;
                //设置回传最终状态
                if (Objects.equals(InOrderStatus.RK_ALL.getValue(), orderBO.getStatus()) && Objects.equals(size, count) && allSuccess) {
                    confirmType = 0;
                }
                //组装回传GLP参数
                orderBack = buildGLPInOrderBack(BeanUtils.copyProperties(list, InOrderDetailBatchBO.class), orderBO, confirmType);

                String url = orderNacosConfig.getGlpCallBackUrl();
                Map<String, String> headers = new HashMap<>();
                boolean contains = orderNacosConfig.getGlpNewChannelOwner().contains(orderBO.getOwnerCode());
                if (contains) {
                    url = orderNacosConfig.getGlpNewCallBackUrl();
                    headers.put("targetAppkey", orderNacosConfig.getGlpNewTargetAppkey());
                }

                request = postJson(orderBack, url, SYSTEM_GLP, headers);
                log.info("[CallBackUpListener-backGLPInOrder]===============================orderNo:{},GLP回传返回值:{}", message.getOrderNo(), request);
                Integer isSuccess = 1;
                JSONObject jsonObject = JSON.parseObject(request);
                if (contains) {
                    Boolean success = jsonObject.getBoolean("success");
                    if (success) {
                        JSONObject biz_result = jsonObject.getJSONObject("biz_result");
                        isSuccess = biz_result.getInteger("status");
                    }
                } else {
                    isSuccess = jsonObject.getInteger("status");
                }
                //处理返回后的状态
                if (Objects.equals(0, isSuccess)) {
                    //如果是入库完成 && 且是最后一个 && 都完成回传
                    if (Objects.equals(InOrderStatus.RK_ALL.getValue(), orderBO.getStatus()) && Objects.equals(size, count) && allSuccess) {
                        //更新入库单状态为 成功 OrderStatus.ORDER_SUCCESS_ALL
                        inOrderBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                        inOrderBO.setBackReturn("");
                        inOrderManager.updateInOrderByNo(inOrderBO);
                    }
                    //如果是入库 [部分] 完成 && 且是最后一个 && 都完成回传
                    if (Objects.equals(InOrderStatus.RK_PART.getValue(), orderBO.getStatus()) && Objects.equals(size, count) && allSuccess) {
                        //更新入库单状态为 待回传 OrderStatus.ORDER_WAIT
                        inOrderBO.setBackStatus(OrderStatus.ORDER_WAIT.getValue());
                        inOrderBO.setBackReturn("");
                        inOrderManager.updateInOrderByNo(inOrderBO);
                    }
                    //更新入库批次状态
                    for (InOrderDetailBatchBO batchBO : list) {
                        batchBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                        batchBO.setInventoryStatus(null);//防止库存状态被更新
                    }
                    inOrderDetailBatchManager.updateListByIdAndOrderNo(list);
                    //节点
                    orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "收货上架回告上游成功", System.currentTimeMillis(), Boolean.FALSE);
                } else {
                    log.error("[CallBackUpListener-backGLPInOrder]=========回传GLP异常=========orderNo:{},ex:{}", message.getOrderNo(), request);
                    //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
                    allSuccess = Boolean.FALSE;
                    inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                    inOrderBO.setBackReturn(request);
                    inOrderManager.updateInOrderByNo(inOrderBO);
                    //节点
                    orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败:"+request, System.currentTimeMillis(), Boolean.TRUE);

                    backWarning("入库单回传GLP异常orderNo:"+message.getOrderNo() + ",GLP返回值:" + request);
                }
                remoteConfigurationFacade.printLog(ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_GLP, message.getOrderNo(), JSON.toJSONString(orderBack), request));
            } catch (Exception e) {
                log.error("[CallBackUpListener-backGLPInOrder]=========回传GLP异常=========orderNo:{},ex:", message.getOrderNo(), e);
                //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
                allSuccess = Boolean.FALSE;
                inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                inOrderBO.setBackReturn("回传上游失败!");
                inOrderManager.updateInOrderByNo(inOrderBO);

                ConfigurationLogAddParam logAddParam = ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_GLP, message.getOrderNo(), JSON.toJSONString(orderBack), null);
                logAddParam.setExceptionMsg(LogUtils.getPrintStackTraceMessage(e));
                remoteConfigurationFacade.printLog(logAddParam);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败:回传上游失败！", System.currentTimeMillis(), Boolean.TRUE);

                backWarning("入库单回传GLP异常orderNo:"+message.getOrderNo() + ",GLP返回值:" + request);
            }
        }
    }

    /**
     * 组装GLP回传参数
     *
     * @param list
     * @param orderBO
     * @return
     */
    private Map<String, Object> buildGLPInOrderBack(List<InOrderDetailBatchBO> list, InOrderBO orderBO, Integer confirmType) {
        Map<String, Object> backMap = new HashMap<>();
        backMap.put("entryNotice", orderBO.getUpstreamNo());
        backMap.put("sourceCode", orderBO.getSourcePlatform());
        backMap.put("investorCode", buildGLPFirstOwnerCode(orderBO.getExtensionJson()));
        backMap.put("customerId", buildOwnerCode(orderBO.getOwnerCode()));
        backMap.put("warehouseId", orderBO.getWarehouseCode());
        backMap.put("applyNo", orderBO.getBusinessNo());
        if (Objects.nonNull(orderBO.getActualTime()) && !Objects.equals(0L, orderBO.getActualTime())) {
            backMap.put("enterDate", DateUtil.formatDateTime(new Date(orderBO.getActualTime())));
        }
        backMap.put("entrycompleted", Objects.equals(0, confirmType) ? "10" : "20");
        //合并明细
        List<InOrderDetailBatchBO> collect = list.stream().collect(Collectors.toMap(inOrderDetailBatchBO -> getDetailKey(inOrderDetailBatchBO), b -> b, (o1, o2) -> {
            o1.setActualQuantity(o1.getActualQuantity() + o2.getActualQuantity());
            return o1;
        })).values().stream().collect(Collectors.toList());
        //组装返回值
        List<Object> skuList = new ArrayList<>();
        for (InOrderDetailBatchBO detailBatchBO : collect) {
            backMap.put("entryNo", orderBO.getInOrderNo() + detailBatchBO.getBackFlag());
            Map<String, Object> skuMap = new HashMap<>();
            skuMap.put("goodsNo", detailBatchBO.getSku());
            skuMap.put("goodsName", detailBatchBO.getGoodsName());
            skuMap.put("purchaseQty", detailBatchBO.getPlanQuantity());
            skuMap.put("enteryQty", detailBatchBO.getActualQuantity());
            skuMap.put("rowNum", detailBatchBO.getLineNo());
            if (StringUtils.isNotBlank(detailBatchBO.getBatchCode())) {
                skuMap.put("batchId", detailBatchBO.getBatchCode());
            }
            if (Objects.nonNull(detailBatchBO.getProductionDate())) {
                skuMap.put("produceDate", DateUtil.formatDateTime(detailBatchBO.getProductionDate()));
            }
            if (Objects.nonNull(detailBatchBO.getExpireDate())) {
                skuMap.put("expireDate", DateUtil.formatDateTime(detailBatchBO.getExpireDate()));
            }
            skuMap.put("stockType", Objects.equals(InventoryType.QUALITY.getValue(), detailBatchBO.getInventoryType()) ? 0 : 1);
            skuList.add(skuMap);
        }
        backMap.put("goodList", skuList);
        backMap.put("method", "wms.entry.notice.result");

        buildGLPSign(backMap, orderBO.getOwnerCode());
        return backMap;
    }

    private String getDetailKey(InOrderDetailBatchBO inOrderDetailBatchBO) {
        String key = inOrderDetailBatchBO.getGoodsCode() + inOrderDetailBatchBO.getInventoryType();
//        if (StringUtils.isNotBlank(inOrderDetailBatchBO.getBatchCode())) {
//            key = key + inOrderDetailBatchBO.getBatchCode();
//        }
        if (StringUtils.isNotBlank(inOrderDetailBatchBO.getInternalBatchCode())) {
            key = key + inOrderDetailBatchBO.getInternalBatchCode();
        }
        if (StringUtils.isNotBlank(inOrderDetailBatchBO.getLineNo())) {
            key = key + inOrderDetailBatchBO.getLineNo();
        }
        return key;
    }

    /**
     * 构建glp签名
     * @param backMap
     * @param ownerCode
     */
    private void buildGLPSign(Map<String, Object> backMap, String ownerCode) {
        String appKey = orderNacosConfig.getGlpAppKey();
        String appSecret = orderNacosConfig.getGlpAppSecret();
        Boolean channel = orderNacosConfig.getGlpNewChannelOwner().contains(ownerCode);
        if (channel) {
            appKey = orderNacosConfig.getGlpNewAppKey();
            appSecret = orderNacosConfig.getGlpNewAppSecret();
        }
        backMap.put("appKey", appKey);
        backMap.put("appSecret", appSecret);
        backMap.put("sign", channel ? buildMD5(backMap, appSecret) : buildBaseMD5(backMap, appSecret));
    }

    /**
     * 回传GLP(单次) 出库单
     *
     * @param message
     */
    private void singleBackGLPOutOrder(OrderMessage message) {
        OutOrderBO orderBO = outOrderManager.getOutOrderByNo(message.getOrderNo(), message.getUserId());
        if (Objects.isNull(orderBO) || Objects.equals(OrderStatus.ORDER_SUCCESS_ALL.getValue(), orderBO.getBackStatus())) {
            log.warn("[CallBackUpListener-singleBackGLPOutOrder]=========出库单不存在,或已回传完成=========orderNo:" + message.getOrderNo());
            return;
        }
        if (!Objects.equals(OutOrderStatus.CK_ALL.getValue(), orderBO.getStatus())) {
            log.warn("[CallBackUpListener-singleBackGLPOutOrder]=========出库单不是全部回传状态=========orderNo:" + message.getOrderNo());
            return;
        }
        OutOrderBO outOrderBO = new OutOrderBO();
        outOrderBO.setOutOrderNo(orderBO.getOutOrderNo());
        Map<String, Object> orderBack = null;
        String request = null;
        try {
            List<OutOrderDetailBO> orderDetailBOList = outOrderDetailManager.listOutOrderDetailByNO(message.getOrderNo());
            orderBack = buildGLPOutOrderBack(orderDetailBOList, orderBO);

            String url = orderNacosConfig.getGlpCallBackUrl();
            Map<String, String> headers = new HashMap<>();
            boolean contains = orderNacosConfig.getGlpNewChannelOwner().contains(orderBO.getOwnerCode());
            if (contains) {
                url = orderNacosConfig.getGlpNewCallBackUrl();
                headers.put("targetAppkey", orderNacosConfig.getGlpNewTargetAppkey());
            }

            request = postJson(orderBack, url, SYSTEM_GLP, headers);
            log.info("[CallBackUpListener-singleBackGLPOutOrder]===============================orderNo:{},GLP回传返回值:{}", message.getOrderNo(), request);
            Integer isSuccess = 1;
            JSONObject jsonObject = JSON.parseObject(request);
            if (contains) {
                Boolean success = jsonObject.getBoolean("success");
                if (success) {
                    JSONObject biz_result = jsonObject.getJSONObject("biz_result");
                    isSuccess = biz_result.getInteger("status");
                }
            } else {
                isSuccess = jsonObject.getInteger("status");
            }
            //处理返回后的状态
            if (Objects.equals(0, isSuccess)) {
                //更新出库单状态为 成功 OrderStatus.ORDER_SUCCESS_ALL
                orderBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                outOrderBO.setBackReturn("");
                //节点
                orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "回传上游成功", System.currentTimeMillis(), Boolean.FALSE);
            } else {
                log.error("[CallBackUpListener-singleBackGLPOutOrder]=========回传GLP异常=========orderNo:{},ex:{}", message.getOrderNo(), request);
                //发送失败,更新出库单为 定时待回传 OrderStatus.ORDER_FAILED
                orderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                outOrderBO.setBackReturn(request);
                //节点
                orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "失败:"+request, System.currentTimeMillis(), Boolean.TRUE);

                backWarning("出库单回传GLP异常orderNo:"+message.getOrderNo() + ",GLP返回值:" + request);
            }
            outOrderManager.updateOutOrderByNo(orderBO);
            remoteConfigurationFacade.printLog(ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_GLP, message.getOrderNo(), JSON.toJSONString(orderBack), request));
        } catch (Exception e) {
            log.error("[CallBackUpListener-singleBackGLPOutOrder]=========回传GLP异常=========orderNo:{},ex:", message.getOrderNo(), e);
            //发送失败,更新出库单为 定时待回传 OrderStatus.ORDER_FAILED
            outOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
            outOrderBO.setBackReturn("回传上游异常!");
            outOrderManager.updateOutOrderByNo(outOrderBO);

            ConfigurationLogAddParam logAddParam = ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_GLP, message.getOrderNo(), JSON.toJSONString(orderBack), null);
            logAddParam.setExceptionMsg(LogUtils.getPrintStackTraceMessage(e));
            remoteConfigurationFacade.printLog(logAddParam);
            //节点
            orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "失败:回传上游失败!", System.currentTimeMillis(), Boolean.TRUE);

            backWarning("出库单回传GLP异常orderNo:"+message.getOrderNo() + ",GLP返回值:" + request);
        }
    }

    /**
     * 组装GLP回传参数
     *
     * @param list
     * @param orderBO
     * @return
     */
    private Map<String, Object> buildGLPOutOrderBack(List<OutOrderDetailBO> list, OutOrderBO orderBO) {
        Map<String, Object> backMap = new HashMap<>();
        backMap.put("sendNotice", orderBO.getUpstreamNo());
        backMap.put("sendNo", orderBO.getOutOrderNo());
        backMap.put("sourceCode", orderBO.getSourcePlatform());
        backMap.put("investorCode", buildGLPFirstOwnerCode(orderBO.getExtensionJson()));
        backMap.put("customerId", buildOwnerCode(orderBO.getOwnerCode()));
        backMap.put("warehouseId", orderBO.getWarehouseCode());
        backMap.put("applyNo", orderBO.getBusinessNo());
        if (Objects.nonNull(orderBO.getActualTime()) && !Objects.equals(0L, orderBO.getActualTime())) {
            backMap.put("sendTime", DateUtil.formatDateTime(new Date(orderBO.getActualTime())));
        }

        List<Object> skuList = new ArrayList<>();
        for (OutOrderDetailBO detailBatchBO : list) {
            Map<String, Object> skuMap = new HashMap<>();
            skuMap.put("goodsno", detailBatchBO.getSku());
            skuMap.put("goodsname", detailBatchBO.getGoodsName());
            skuMap.put("noticesendqty", detailBatchBO.getPlanQuantity());
            skuMap.put("sendqty", detailBatchBO.getActualQuantity());
            if (StringUtils.isNotBlank(detailBatchBO.getBatchCode())) {
                skuMap.put("batchid", detailBatchBO.getBatchCode());
            }
            if (Objects.nonNull(detailBatchBO.getProductionDate())) {
                skuMap.put("producedate", DateUtil.formatDateTime(detailBatchBO.getProductionDate()));
            }
            if (Objects.nonNull(detailBatchBO.getExpireDate())) {
                skuMap.put("expiredate", DateUtil.formatDateTime(detailBatchBO.getExpireDate()));
            }
            skuMap.put("stockType", Objects.equals(InventoryType.QUALITY.getValue(), detailBatchBO.getInventoryType()) ? 0 : 1);
            skuList.add(skuMap);
        }
        backMap.put("goodList", skuList);
        backMap.put("method", "wms.entry.out.result");

        buildGLPSign(backMap, orderBO.getOwnerCode());
        return backMap;
    }

    /**
     * 货主转换
     *
     * @param ownerCode
     * @return
     */
    private String buildOwnerCode(String ownerCode) {
        String[] ownerCodes = orderNacosConfig.getRegulatoryOwnerCodes().split(",");
        for (String o : ownerCodes) {
            String[] split = o.split("-");
            if (Objects.equals(ownerCode, split[0])) {
                return split[1];
            }
        }
        return ownerCode;
    }

    /**
     * GLP第一货主转换
     *
     * @param extensionJson
     * @return
     */
    private Object buildGLPFirstOwnerCode(String extensionJson) {
        if (StringUtils.isNotBlank(extensionJson)) {
            Map parseObject = JSON.parseObject(extensionJson, Map.class);
            return parseObject.get("investorCode");
        }
        return null;
    }

    /**
     * 回传DYZJ 入库完成
     *
     * @param message
     */
    private void backDYZJInOrder(OrderMessage message) {
        InOrderBO orderBO = inOrderManager.getInOrderByNo(message.getOrderNo(), message.getUserId());
        if (Objects.isNull(orderBO) || Objects.equals(OrderStatus.ORDER_SUCCESS_ALL.getValue(), orderBO.getBackStatus())) {
            log.info("[CallBackUpListener-backDYZJInOrder]=========入库单不存在,或已回传完成=========orderNo:{}, orderBO:{}", message.getOrderNo(), JSON.toJSONString(orderBO));
            return;
        }
        if (!Objects.equals(InOrderStatus.RK_ALL.getValue(), orderBO.getStatus())) {
            log.warn("[CallBackUpListener-backDYZJInOrder]=========入库单不是全部回传状态=========orderNo:" + message.getOrderNo());
            return;
        }

        //字节的退货入库
        if (Objects.equals(InOrderType.TH_RK.getValue(), orderBO.getType())) {
            backDYZJInOrderTH(orderBO);
            return;
        }

        InOrderBO inOrderBO = new InOrderBO();
        inOrderBO.setInOrderNo(orderBO.getInOrderNo());
        Map<String, Object> orderBack = null;
        String errorMessage = null;
        try {
            //查询回传的批次信息
            InOrderDetailBatchSearch search = new InOrderDetailBatchSearch();
            search.setInOrderNo(message.getOrderNo());
            search.setBackStatus(OrderStatus.ORDER_WAIT.getValue());
            search.setMultiType(TallyReportMultiType.NORMAL);
            List<InOrderDetailBatchBO> batchBOList = inOrderDetailBatchManager.listBySearch(search);

            //组装回传DYZJ参数
            orderBack = buildInOrderBack(batchBOList, orderBO, 0, "duoyin.entry.order.confirm.notify", new HashMap<>());
            String request = postForm(orderBack, orderNacosConfig.getCallBackUrlQM());
            log.info("[CallBackUpListener-backDYZJInOrder]===============================orderNo:{},奇门回传返回值:{}", message.getOrderNo(), request);
            JSONObject jsonObject = JSON.parseObject(request);
            boolean isSuccess = jsonObject.getBooleanValue("success");
            errorMessage = jsonObject.getString("errorMessage");
            if (isSuccess) {
                //更新入库单状态为 成功 OrderStatus.ORDER_SUCCESS_ALL
                inOrderBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                inOrderBO.setBackReturn("");
                inOrderManager.updateInOrderByNo(inOrderBO);
                //更新入库批次状态
                for (InOrderDetailBatchBO batchBO : batchBOList) {
                    batchBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                    batchBO.setInventoryStatus(null);//防止库存状态被更新
                }
                inOrderDetailBatchManager.updateListByIdAndOrderNo(batchBOList);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_TALLIED, "下游WMS：理货完成", System.currentTimeMillis(), Boolean.TRUE);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER, "下游WMS：完成收货上架", System.currentTimeMillis(), Boolean.TRUE);
            } else {
                log.error("[CallBackUpListener-backDYZJInOrder]=========回传DYZJ异常=========orderNo:{},ex:{}", message.getOrderNo(), request);
                //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
                inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                inOrderBO.setBackReturn(request);
                inOrderManager.updateInOrderByNo(inOrderBO);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败:"+request, System.currentTimeMillis(), Boolean.TRUE);

                backWarning("入库单回传字节异常orderNo:"+message.getOrderNo() + ",字节返回值:" + errorMessage);
            }
            remoteConfigurationFacade.printLog(ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_DYZJ, message.getOrderNo(), JSON.toJSONString(orderBack), request));
        } catch (Exception e) {
            log.error("[CallBackUpListener-backDYZJInOrder]=========回传DYZJ异常=========orderNo:{},ex:", message.getOrderNo(), e);
            //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
            inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
            inOrderBO.setBackReturn("回传上游失败!");
            inOrderManager.updateInOrderByNo(inOrderBO);

            ConfigurationLogAddParam logAddParam = ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_DYZJ, message.getOrderNo(), JSON.toJSONString(orderBack), null);
            logAddParam.setExceptionMsg(LogUtils.getPrintStackTraceMessage(e));
            remoteConfigurationFacade.printLog(logAddParam);
            //节点
            orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败:回传上游失败!", System.currentTimeMillis(), Boolean.TRUE);

            backWarning("入库单回传字节异常orderNo:"+message.getOrderNo() + ",字节返回值:" + errorMessage);
        }
    }

    /**
     * 回传DYZJ 退货入库完成
     *
     * @param orderBO
     */
    private void backDYZJInOrderTH(InOrderBO orderBO) {
        LogicWarehouseResult logicWarehouseResult = remoteWarehouseFacade.getLogicWarehouseByCode(orderBO.getLogicWarehouseCode());

        InOrderBO inOrderBO = new InOrderBO();
        inOrderBO.setInOrderNo(orderBO.getInOrderNo());
        MessageNewRpcDTO messageRpcDTO = new MessageNewRpcDTO();
        try {
            Map<String, Object> backMap = new HashMap<>();
            backMap.put("logistics_no", orderBO.getLogisticsNo());
            backMap.put("logistics_fulfil_no", orderBO.getLogisticsFulfilNo());
            backMap.put("warehouse_code", logicWarehouseResult.getUpEntityWarehouseCode());
            backMap.put("status", 7);
            backMap.put("occurrence_time", orderBO.getActualTime());
            backMap.put("ownerCode", orderBO.getOwnerCode());
            backMap.put("userId", orderBO.getUserId());

            messageRpcDTO.setBusinessCode(orderBO.getInOrderNo());
            messageRpcDTO.setUrl(orderNacosConfig.getCallBackUrlRT());
            messageRpcDTO.setActiveData(JSON.toJSONString(backMap));
            messageRpcDTO.setMessageType("COMMON");
            messageRpcFacade.sendMessageV2(messageRpcDTO);

            //更新入库单状态为 成功 OrderStatus.ORDER_SUCCESS_ALL
            inOrderBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
            inOrderBO.setBackReturn("");
            inOrderManager.updateInOrderByNo(inOrderBO);
            //节点
            orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_TALLIED, "下游WMS：理货完成", System.currentTimeMillis(), Boolean.TRUE);
            //节点
            orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER, "下游WMS：完成收货上架", System.currentTimeMillis(), Boolean.TRUE);
            remoteConfigurationFacade.printLog(ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_DYZJ, orderBO.getInOrderNo(), JSON.toJSONString(messageRpcDTO), null));
        } catch (Exception e) {
            log.error("[CallBackUpListener-backDYZJInOrderTH]=========回传DYZJ异常=========orderNo:{},ex:", orderBO.getInOrderNo(), e);
            //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
            inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
            inOrderBO.setBackReturn("回传上游失败!");
            inOrderManager.updateInOrderByNo(inOrderBO);

            ConfigurationLogAddParam logAddParam = ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_DYZJ, orderBO.getInOrderNo(), JSON.toJSONString(messageRpcDTO), null);
            logAddParam.setExceptionMsg(LogUtils.getPrintStackTraceMessage(e));
            remoteConfigurationFacade.printLog(logAddParam);
            //节点
            orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败:回传上游失败!", System.currentTimeMillis(), Boolean.TRUE);
            backWarning("退货入库回传字节异常orderNo:" + orderBO.getInOrderNo());
        }
    }

    /**
     * 回传DYZJ 出库完成
     *
     * @param message
     */
    private void backDYZJOutOrder(OrderMessage message) {
        backQMOutOrder(message, "duoyin.order.stock.out.confirm.notify");
    }

    /**
     * 回传SCF 入库完成
     *
     * @param message
     */
    private void backSCFInOrder(OrderMessage message) {

        InOrderBO orderBO = inOrderManager.getInOrderByNo(message.getOrderNo(), message.getUserId());
        if (Objects.isNull(orderBO) || Objects.equals(OrderStatus.ORDER_SUCCESS_ALL.getValue(), orderBO.getBackStatus())) {
            log.info("[CallBackUpListener-backSCFInOrder]=========入库单不存在,或已回传完成=========orderNo:{}, orderBO:{}", message.getOrderNo(), JSON.toJSONString(orderBO));
            return;
        }
        if (!Objects.equals(InOrderStatus.RK_ALL.getValue(), orderBO.getStatus())) {
            log.warn("[CallBackUpListener-backSCFInOrder]=========入库单不是全部回传状态=========orderNo:" + message.getOrderNo());
            return;
        }
        //不是代采监管入库
        if (!Objects.equals(InOrderType.DC_RK.getValue(), orderBO.getType())) {
            log.info("[CallBackUpListener-backSCFInOrder]=========不是代采监管入库单=========orderNo:{}, orderBO:{}", message.getOrderNo(), JSON.toJSONString(orderBO));
            return;
        }

        InOrderBO inOrderBO = new InOrderBO();
        inOrderBO.setInOrderNo(orderBO.getInOrderNo());
        try {
            boolean completeStatus = remoteScfFacade.backInOrderCompleteStatus(orderBO.getBusinessNo());
            if (completeStatus) {
                //更新入库单状态为 成功 OrderStatus.ORDER_SUCCESS_ALL
                inOrderBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                inOrderBO.setBackReturn("");
                inOrderManager.updateInOrderByNo(inOrderBO);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_TALLIED, "下游WMS：理货完成", System.currentTimeMillis(), Boolean.TRUE);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER, "下游WMS：完成收货上架", System.currentTimeMillis(), Boolean.TRUE);
            } else {
                //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
                inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                inOrderBO.setBackReturn("回传失败!");
                inOrderManager.updateInOrderByNo(inOrderBO);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败:回传失败", System.currentTimeMillis(), Boolean.TRUE);
                backWarning("入库单回传供金失败,orderNo:"+message.getOrderNo());
            }
        } catch (Exception e) {
            log.error("[CallBackUpListener-backSCFInOrder]=========回传SCF异常=========orderNo:{},ex:", message.getOrderNo(), e);
            //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
            inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
            inOrderBO.setBackReturn("回传上游失败!");
            inOrderManager.updateInOrderByNo(inOrderBO);
            //节点
            orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败:回传上游失败!", System.currentTimeMillis(), Boolean.TRUE);
            backWarning("入库单回传供金失败,orderNo:"+message.getOrderNo());
        }
    }

    /**
     * 回传SCF 出库完成
     *
     * @param message
     */
    private void backSCFOutOrder(OrderMessage message) {
        OutOrderBO orderBO = outOrderManager.getOutOrderByNo(message.getOrderNo(), message.getUserId());
        if (Objects.isNull(orderBO) || Objects.equals(OrderStatus.ORDER_SUCCESS_ALL.getValue(), orderBO.getBackStatus())) {
            log.info("[CallBackUpListener-backSCFOutOrder]=========出库单不存在,或已回传完成=========orderNo:{}, orderBO:{}", message.getOrderNo(), JSON.toJSONString(orderBO));
            return;
        }
        if (!Objects.equals(OutOrderStatus.CK_ALL.getValue(), orderBO.getStatus())) {
            log.warn("[CallBackUpListener-backSCFOutOrder]=========出库单不是全部回传状态=========orderNo:" + message.getOrderNo());
            return;
        }
        //不是代采赎回出库
        if (!Objects.equals(OutOrderType.SH_CK.getValue(), orderBO.getType())) {
            log.info("[CallBackUpListener-backSCFOutOrder]=========不是代采赎回出库单=========orderNo:{}, orderBO:{}", message.getOrderNo(), JSON.toJSONString(orderBO));
            return;
        }

        OutOrderBO outOrderBO = new OutOrderBO();
        outOrderBO.setOutOrderNo(orderBO.getOutOrderNo());
        try {
            boolean completeStatus = remoteScfFacade.backOutOrderCompleteStatus(orderBO.getBusinessNo());
            if (completeStatus) {
                outOrderBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                outOrderBO.setBackReturn("");
                outOrderManager.updateOutOrderByNo(outOrderBO);
                //节点
                orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "回传上游成功", System.currentTimeMillis(), Boolean.FALSE);
            } else {
                outOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                outOrderBO.setBackReturn("回传失败!");
                outOrderManager.updateOutOrderByNo(outOrderBO);
                //节点
                orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "失败:回传失败", System.currentTimeMillis(), Boolean.TRUE);
                backWarning("出库单回传供金失败,orderNo:"+message.getOrderNo());
            }
        } catch (Exception e) {
            log.error("[CallBackUpListener-backSCFInOrder]=========回传SCF异常=========orderNo:{},ex:", message.getOrderNo(), e);
            outOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
            outOrderBO.setBackReturn("回传上游失败!");
            outOrderManager.updateOutOrderByNo(outOrderBO);
            //节点
            orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "失败:回传上游失败!", System.currentTimeMillis(), Boolean.TRUE);
            backWarning("出库单回传供金失败,orderNo:"+message.getOrderNo());
        }
    }

    /**
     * 回传ASCP 入库单
     *
     * @param message
     */
    private void backASCPInOrder(OrderMessage message) {
        InOrderBO orderBO = inOrderManager.getInOrderByNo(message.getOrderNo(), message.getUserId());
        if (Objects.isNull(orderBO) || Objects.equals(OrderStatus.ORDER_SUCCESS_ALL.getValue(), orderBO.getBackStatus())) {
            log.warn("[CallBackUpListener-backASCPInOrder]=========入库单不存在,或已回传完成=========orderNo:" + message.getOrderNo());
            return;
        }
        //回传goods
        InOrderBO inOrderBO = new InOrderBO();
        inOrderBO.setInOrderNo(orderBO.getInOrderNo());
        Map<String, Object> orderBack = null;
        String errorMessage = null;
        try {
            InOrderDetailSearch inOrderDetailSearch = new InOrderDetailSearch();
            inOrderDetailSearch.setInOrderNo(message.getOrderNo());
            List<InOrderDetailBO> inOrderDetailBOList = inOrderDetailManager.listBySearch(inOrderDetailSearch);

            InOrderDetailBatchSearch search = new InOrderDetailBatchSearch();
            search.setInOrderNo(message.getOrderNo());
            search.setBackStatus(OrderStatus.ORDER_WAIT.getValue());
            search.setMultiType(TallyReportMultiType.NORMAL);
            if (StringUtils.isNotBlank(message.getBackFlag())) {
                search.setBackFlag(message.getBackFlag());
            }
            List<InOrderDetailBatchBO> batchBOList = inOrderDetailBatchManager.listBySearch(search);
            if (CollectionUtil.isEmpty(batchBOList)) {
                log.warn("[CallBackUpListener-backASCPInOrder]=========入库单批次明细不存在=========orderNo:" + message.getOrderNo());
                return;
            }
            //goods分类
            Map<String, List<InOrderDetailBatchBO>> batchNoMap = new HashMap<>();
            for (InOrderDetailBatchBO batchBO : batchBOList) {
                List<InOrderDetailBatchBO> boList = batchNoMap.get(batchBO.getGoodsCode());
                if (CollectionUtils.isEmpty(boList)) {
                    boList = new ArrayList<>();
                    batchNoMap.put(batchBO.getGoodsCode(), boList);
                }
                boList.add(batchBO);
            }
            ReceiveSendInfo receiveSendInfo = receiveSendInfoManager.selectByOrderNo(orderBO.getInOrderNo());

            //组装回传ASCP参数
            orderBack = buildAscpInOrderBack(orderBO, inOrderDetailBOList, batchNoMap, receiveSendInfo);
            String request = postForm(orderBack, orderNacosConfig.getCallBackUrlQM());
            log.info("[CallBackUpListener-backASCPInOrder]===============================orderNo:{},ASCP回传返回值:{}", message.getOrderNo(), request);
            JSONObject jsonObject = JSON.parseObject(request);
            boolean isSuccess = jsonObject.getBooleanValue("success");
            if (isSuccess) {
                //更新入库单状态为 成功 OrderStatus.ORDER_SUCCESS_ALL
                inOrderBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                inOrderBO.setBackReturn("");
                inOrderManager.updateInOrderByNo(inOrderBO);
                //更新入库批次状态
                for (InOrderDetailBatchBO batchBO : batchBOList) {
                    batchBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                    batchBO.setInventoryStatus(null);//防止库存状态被更新
                }
                inOrderDetailBatchManager.updateListByIdAndOrderNo(batchBOList);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_TALLIED, "下游WMS：理货完成", System.currentTimeMillis(), Boolean.TRUE);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER, "下游WMS：完成收货上架", System.currentTimeMillis(), Boolean.TRUE);
            } else {
                log.error("[CallBackUpListener-backASCPInOrder]=========回传ASCP异常=========orderNo:{},ex:{}", message.getOrderNo(), request);
                //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
                inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                inOrderBO.setBackReturn(request);
                inOrderManager.updateInOrderByNo(inOrderBO);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败:"+request, System.currentTimeMillis(), Boolean.TRUE);

                backWarning("入库单回传ASCP异常orderNo:"+message.getOrderNo() + ",ASCP返回值:" + errorMessage);
            }
            remoteConfigurationFacade.printLog(ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_ASCP, message.getOrderNo(), JSON.toJSONString(orderBack), request));
        } catch (Exception e) {
            log.error("[CallBackUpListener-backASCPInOrder]=========回传ASCP异常=========orderNo:{},ex:", message.getOrderNo(), e);
            //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
            inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
            inOrderBO.setBackReturn("回传上游失败!");
            inOrderManager.updateInOrderByNo(inOrderBO);

            ConfigurationLogAddParam logAddParam = ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_ASCP, message.getOrderNo(), JSON.toJSONString(orderBack), null);
            logAddParam.setExceptionMsg(LogUtils.getPrintStackTraceMessage(e));
            remoteConfigurationFacade.printLog(logAddParam);
            //节点
            orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败:回传上游失败!", System.currentTimeMillis(), Boolean.TRUE);

            backWarning("入库单回传ASCP异常orderNo:"+message.getOrderNo() + ",ASCP返回值:" + errorMessage);
        }
    }

    /**
     * 组装ASCP回传参数
     *
     * @param orderBO
     * @return
     */
    private Map<String, Object> buildAscpInOrderBack(InOrderBO orderBO, List<InOrderDetailBO> detailList, Map<String, List<InOrderDetailBatchBO>> batchMap, ReceiveSendInfo receiveSendInfo) {
        Map<String, Object> backMap = new HashMap<>();
        backMap.put("bizOrderCode", orderBO.getUpstreamNo());
        backMap.put("instorageTime", orderBO.getActualTime());
        backMap.put("storeCode", orderBO.getLogicWarehouseCode());
        backMap.put("businessModel", 0);
        String bv = orderBO.getBusinessValue();
        if (bv.startsWith("{") && bv.endsWith("}")) {
            Map<String, String> map = JSON.parseObject(bv, Map.class);
            backMap.put("supplierId", map.get("supplierId"));
            backMap.put("outBizId", map.get("outBizId"));
        }
        List<Object> skuList = new ArrayList<>();
        for (InOrderDetailBO detailBO : detailList) {
            Map<String, Object> skuMap = new HashMap<>();
            skuMap.put("subOrderCode", detailBO.getSubOrderNo());
            skuMap.put("scItemId", detailBO.getExternalSku());
            skuMap.put("actualReceivedQuantity", detailBO.getActualQuantity());
            skuMap.put("actualLackQuantity", detailBO.getPlanQuantity() - detailBO.getActualQuantity());
            List<Object> skuBatchList = new ArrayList<>();
            List<InOrderDetailBatchBO> batchBOList = batchMap.get(detailBO.getGoodsCode());
            for (InOrderDetailBatchBO detailBatchBO : batchBOList) {
                Map<String, Object> batchDetailMap = new HashMap<>();
                batchDetailMap.put("receivedQuantity", detailBatchBO.getActualQuantity());
                if (Objects.equals(InventoryType.QUALITY.getValue(), detailBatchBO.getInventoryType())) {
                    batchDetailMap.put("storageType", 1);
                } else {
                    batchDetailMap.put("storageType", 101);
                }
                skuBatchList.add(batchDetailMap);
            }
            skuMap.put("instorageDetails", skuBatchList);
            skuList.add(skuMap);
        }
        backMap.put("orderItems", skuList);

        Map<String, Object> receiverInfoMap = new HashMap<>();
        receiverInfoMap.put("receiverName", receiveSendInfo.getReceiveContactName());
        receiverInfoMap.put("receiverCity", receiveSendInfo.getReceiveCity());
        receiverInfoMap.put("receiverProvince", receiveSendInfo.getReceiveProvince());
        receiverInfoMap.put("receiverCountry", receiveSendInfo.getReceiveCountry());
        Map<String, Object> senderInfoMap = new HashMap<>();
        senderInfoMap.put("senderName", receiveSendInfo.getSendContactName());
        senderInfoMap.put("senderCity", receiveSendInfo.getSendCity());
        senderInfoMap.put("senderProvince", receiveSendInfo.getSendProvince());
        senderInfoMap.put("senderCountry", receiveSendInfo.getSendCountry());
        backMap.put("receiverInfo", receiverInfoMap);
        backMap.put("senderInfo", senderInfoMap);

        Map<String, Object> ascpRequestMap = new HashMap<>();
        ascpRequestMap.put("instorageFeedbackRequest", backMap);
        ascpRequestMap.put("businessValue", orderBO.getBusinessValue());
        //请求参数
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("bizData", JSON.toJSONString(ascpRequestMap));
        requestMap.put("method", "entry.order.confirm.notify");
        return requestMap;
    }

    /**
     * 回传JIND 入库完成
     *
     * @param message
     */
    private void backJINDInOrder(OrderMessage message) {
        InOrderBO orderBO = inOrderManager.getInOrderByNo(message.getOrderNo(), message.getUserId());
        if (Objects.isNull(orderBO) || Objects.equals(OrderStatus.ORDER_SUCCESS_ALL.getValue(), orderBO.getBackStatus())) {
            log.info("backJINDInOrder=========入库单不存在,或已回传完成=========orderNo:{}, orderBO:{}", message.getOrderNo(), JSON.toJSONString(orderBO));
            return;
        }
        if (!Objects.equals(InOrderStatus.RK_ALL.getValue(), orderBO.getStatus())) {
            log.warn("backJINDInOrder=========入库单不是全部回传状态=========orderNo:" + message.getOrderNo());
            return;
        }

        InOrderBO inOrderBO = new InOrderBO();
        inOrderBO.setInOrderNo(orderBO.getInOrderNo());
        Map<String, Object> orderBack = null;
        String errorMessage = null;
        try {
            //查询回传的批次信息
            InOrderDetailBatchSearch search = new InOrderDetailBatchSearch();
            search.setInOrderNo(message.getOrderNo());
            search.setBackStatus(OrderStatus.ORDER_WAIT.getValue());
            search.setMultiType(TallyReportMultiType.NORMAL);
            List<InOrderDetailBatchBO> batchBOList = inOrderDetailBatchManager.listBySearch(search);

            //组装回传参数
            orderBack = buildJINDInOrderBack(batchBOList, orderBO);

            K3CloudApi api = new K3CloudApi();
            String result = api.save("QFBM_RK", JSON.toJSONString(orderBack));
            log.info("backJINDInOrder===============================orderNo={},金蝶回传返回值={}", message.getOrderNo(), result);
            RepoRet repoRet = JSON.parseObject(result, RepoRet.class);
            boolean isSuccess = repoRet.getResult().getResponseStatus().isIsSuccess();
            if (isSuccess) {
                //更新入库单状态为 成功 OrderStatus.ORDER_SUCCESS_ALL
                inOrderBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                inOrderBO.setBackReturn("");
                inOrderManager.updateInOrderByNo(inOrderBO);
                //更新入库批次状态
                for (InOrderDetailBatchBO batchBO : batchBOList) {
                    batchBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                    batchBO.setInventoryStatus(null);//防止库存状态被更新
                }
                inOrderDetailBatchManager.updateListByIdAndOrderNo(batchBOList);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_TALLIED, "下游WMS：理货完成", System.currentTimeMillis(), Boolean.TRUE);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER, "下游WMS：完成收货上架", System.currentTimeMillis(), Boolean.TRUE);
            } else {
                ArrayList<RepoError> errors = repoRet.getResult().getResponseStatus().getErrors();
                if (CollectionUtil.isNotEmpty(errors)) {
                    errorMessage = errors.get(0).getMessage();
                }
                log.error("backJINDInOrder=========回传JIND异常=========orderNo:{},ex:{}", message.getOrderNo(), result);
                //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
                inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                inOrderBO.setBackReturn(errorMessage);
                inOrderManager.updateInOrderByNo(inOrderBO);
                //节点
                orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败:" + errorMessage, System.currentTimeMillis(), Boolean.TRUE);

                backWarning("入库单回传金蝶异常orderNo:"+message.getOrderNo() + ",金蝶返回值:" + errorMessage);
            }
            remoteConfigurationFacade.printLog(ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_JIND, message.getOrderNo(), JSON.toJSONString(orderBack), result));
        } catch (Exception e) {
            log.error("backJINDInOrder=========回传JIND异常=========orderNo:{},ex:", message.getOrderNo(), e);
            //发送失败,更新入库单为 定时待回传 OrderStatus.ORDER_FAILED
            inOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
            inOrderBO.setBackReturn("回传上游失败!");
            inOrderManager.updateInOrderByNo(inOrderBO);

            ConfigurationLogAddParam logAddParam = ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_JIND, message.getOrderNo(), JSON.toJSONString(orderBack), null);
            logAddParam.setExceptionMsg(LogUtils.getPrintStackTraceMessage(e));
            remoteConfigurationFacade.printLog(logAddParam);
            //节点
            orderNodeManager.sendInOrderNode(orderBO.getInOrderNo(), OrderNodeStatus.IN_ORDER_BACK, "失败:回传上游失败!", System.currentTimeMillis(), Boolean.TRUE);

            backWarning("入库单回传金蝶异常orderNo:"+message.getOrderNo() + ",金蝶返回值:" + errorMessage);
        }
    }

    /**
     * 组装JIND回传参数
     *
     * @param list
     * @param orderBO
     * @return
     */
    private Map<String, Object> buildJINDInOrderBack(List<InOrderDetailBatchBO> list, InOrderBO orderBO) {
        Map<String, Object> backMap = new HashMap<>();
        backMap.put("FID", 0);
        backMap.put("FBillNo", orderBO.getInOrderNo());
        backMap.put("FDate", DateUtil.formatDateTime(new Date(orderBO.getActualTime())));
        backMap.put("FStock", orderBO.getWarehouseCode());
        backMap.put("FOwner", orderBO.getLogicWarehouseCode());
        String businessValue = orderBO.getBusinessValue();
        if (StringUtils.isNotBlank(businessValue)) {
            JSONObject jsonObject = JSON.parseObject(businessValue);
            String orderType = jsonObject.getString("orderType");
            backMap.put("FBillType", orderType);
        }
        Map<String, Integer> fzzMap = new HashMap<>();
        fzzMap.put("FNUMBER", 100);
        backMap.put("FZZ", fzzMap);
        //合并
        List<InOrderDetailBatchBO> batchBOList = list.stream()
                .collect(Collectors.toMap(d -> d.getLineNo() + d.getSku() + d.getInternalBatchCode(), Function.identity(), (v1, v2) -> {
                    v1.setActualQuantity(v1.getActualQuantity() + v2.getActualQuantity());
                    return v1;
                })).values().stream().collect(Collectors.toList());

        Map<String, Object> jinInventoryTypeMap = orderNacosConfig.getJinInventoryTypeMap();
        List<Object> skuList = new ArrayList<>();
        for (InOrderDetailBatchBO detailBatchBO : batchBOList) {
            Map<String, Object> skuMap = new HashMap<>();
            Map<String, Object> fwlMap = new HashMap<>();
            fwlMap.put("FNUMBER", detailBatchBO.getSku());
            skuMap.put("FWL", fwlMap);
            Map<String, Object> flsMap = new HashMap<>();
            flsMap.put("FNUMBER", jinInventoryTypeMap.get(orderBO.getOwnerCode() + "_" + detailBatchBO.getInventoryType()));
            skuMap.put("FlogicStock", flsMap);

            skuMap.put("FQty", detailBatchBO.getActualQuantity());
            skuMap.put("FLot", detailBatchBO.getInternalBatchCode());
            if (Objects.nonNull(detailBatchBO.getProductionDate())) {
                skuMap.put("FPDate", DateUtil.formatDateTime(detailBatchBO.getProductionDate()));
            }
            if (Objects.nonNull(detailBatchBO.getExpireDate())) {
                skuMap.put("FEDate", DateUtil.formatDateTime(detailBatchBO.getExpireDate()));
            }
            skuMap.put("FSrcBillNo", orderBO.getUpstreamNo());
            skuMap.put("FSrcSeq", detailBatchBO.getLineNo());
            skuList.add(skuMap);
        }
        backMap.put("FEntity", skuList);
        //请求参数
        Map<String, Object> requestMap = new HashMap<>(3);
        requestMap.put("IsVerifyBaseDataField", "true");
        requestMap.put("IgnoreInterationFlag", "true");
        requestMap.put("Model", backMap);
        return requestMap;
    }

    /**
     * 回传JIND 出库单
     *
     * @param message
     */
    private void backJINDOutOrder(OrderMessage message) {
        OutOrderBO orderBO = outOrderManager.getOutOrderByNo(message.getOrderNo(), message.getUserId());
        if (Objects.isNull(orderBO) || Objects.equals(OrderStatus.ORDER_SUCCESS_ALL.getValue(), orderBO.getBackStatus())) {
            log.warn("backJINDOutOrder=========出库单不存在,或已回传完成=========orderNo:" + message.getOrderNo());
            return;
        }
        if (!Objects.equals(OutOrderStatus.CK_ALL.getValue(), orderBO.getStatus())) {
            log.warn("backJINDOutOrder=========出库单不是全部回传状态=========orderNo:" + message.getOrderNo());
            return;
        }

        OutOrderBO outOrderBO = new OutOrderBO();
        outOrderBO.setOutOrderNo(orderBO.getOutOrderNo());
        outOrderBO.setUserId(orderBO.getUserId());
        Map<String, Object> orderBack = null;
        String errorMessage = null;
        try {
            OutOrderLogisticsSearch outOrderLogisticsSearch = new OutOrderLogisticsSearch();
            outOrderLogisticsSearch.setOrderNo(message.getOrderNo());
            outOrderLogisticsSearch.setBackStatus(OrderStatus.ORDER_WAIT.getValue());
            List<OutOrderLogisticsBO> logisticsBOList = outOrderLogisticsManager.listBySearch(outOrderLogisticsSearch);
            if (CollectionUtils.isEmpty(logisticsBOList) || logisticsBOList.size() == 0) {
                log.warn("backJINDOutOrder=========出库单包裹信息不存在=========orderNo:" + orderBO.getOutOrderNo());
                return;
            }
            List<OutOrderLogisticsDetailBO> detailBOList = new ArrayList<>();
            for (OutOrderLogisticsBO logisticsBO : logisticsBOList) {
                OutOrderLogisticsDetailSearch orderLogisticsDetailSearch = new OutOrderLogisticsDetailSearch();
                orderLogisticsDetailSearch.setOrderNo(message.getOrderNo());
                orderLogisticsDetailSearch.setBackFlag(logisticsBO.getBackFlag());
                List<OutOrderLogisticsDetailBO> list = outOrderLogisticsDetailManager.listBySearch(orderLogisticsDetailSearch);
                detailBOList.addAll(list);
            }
            orderBack = buildJINDOutOrderBack(logisticsBOList, detailBOList, orderBO);

            K3CloudApi api = new K3CloudApi();
            String result = api.save("QFBM_CK", JSON.toJSONString(orderBack));
            log.info("backJINDOutOrder===============================orderNo={},金蝶回传返回值={}", message.getOrderNo(), JSON.toJSONString(result));
            RepoRet repoRet = JSON.parseObject(result, RepoRet.class);
            boolean isSuccess = repoRet.getResult().getResponseStatus().isIsSuccess();
            //处理返回后的状态
            if (isSuccess) {
                //更新出库单状态为 成功 OrderStatus.ORDER_SUCCESS_ALL
                outOrderBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                outOrderBO.setBackReturn("");
                //更新出库回传状态
                for (OutOrderLogisticsBO logisticsBO : logisticsBOList) {
                    logisticsBO.setBackStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                    outOrderLogisticsManager.updateByNo(logisticsBO);
                }
                //节点
                orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "回传上游成功", System.currentTimeMillis(), Boolean.FALSE);
            } else {
                ArrayList<RepoError> errors = repoRet.getResult().getResponseStatus().getErrors();
                if (CollectionUtil.isNotEmpty(errors)) {
                    errorMessage = errors.get(0).getMessage();
                }
                log.error("backJINDOutOrder=========回传JIND异常=========orderNo:{},ex:{}", message.getOrderNo(), result);
                //发送失败,更新出库单为 定时待回传 OrderStatus.ORDER_FAILED
                outOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
                outOrderBO.setBackReturn(errorMessage);
                //节点
                orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "失败:" + errorMessage, System.currentTimeMillis(), Boolean.TRUE);

                backWarning("出库单回传金蝶异常orderNo:"+message.getOrderNo() + ",奇门返回值:" + errorMessage);
            }
            outOrderManager.updateOutOrderByNo(outOrderBO);
            remoteConfigurationFacade.printLog(ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_JIND, message.getOrderNo(), JSON.toJSONString(orderBack), result));
        } catch (Exception e) {
            log.error("backJINDOutOrder=========回传JIND异常=========orderNo:{},ex:", message.getOrderNo(), e);
            //发送失败,更新出库单为 定时待回传 OrderStatus.ORDER_FAILED
            outOrderBO.setBackStatus(OrderStatus.ORDER_FAILED.getValue());
            outOrderBO.setBackReturn("回传上游异常!");
            outOrderManager.updateOutOrderByNo(outOrderBO);

            ConfigurationLogAddParam logAddParam = ConfigurationLogAddParam.ofV2(SYSTEM_ERP, SYSTEM_JIND, message.getOrderNo(), JSON.toJSONString(orderBack), null);
            logAddParam.setExceptionMsg(LogUtils.getPrintStackTraceMessage(e));
            remoteConfigurationFacade.printLog(logAddParam);
            //节点
            orderNodeManager.sendOutOrderNode(orderBO.getOutOrderNo(), OrderNodeStatus.OUT_CALLBACK, "失败:回传上游异常！", System.currentTimeMillis(), Boolean.TRUE);

            backWarning("出库单回传金蝶异常orderNo:"+message.getOrderNo() + ",金蝶返回值:" + errorMessage);
        }
    }

    /**
     * 组装JIND回传参数
     *
     * @param logisticsBOList
     * @param orderBO
     * @return
     */
    private Map<String, Object> buildJINDOutOrderBack(List<OutOrderLogisticsBO> logisticsBOList, List<OutOrderLogisticsDetailBO> logisticsDetailBOList, OutOrderBO orderBO) {
        Map<String, Object> backMap = new HashMap<>();
        backMap.put("FID", 0);
        backMap.put("FBillNo", orderBO.getOutOrderNo());
        backMap.put("FDate", DateUtil.formatDateTime(new Date(orderBO.getActualTime())));
        backMap.put("FStock", orderBO.getWarehouseCode());
        backMap.put("FOwner", orderBO.getLogicWarehouseCode());
        backMap.put("Flogistics_no", logisticsBOList.stream().map(l -> l.getLogisticsNo()).collect(Collectors.joining(",")));
        backMap.put("Flogistics_name", logisticsBOList.get(0).getLogisticsCompanyCode());
        String businessValue = orderBO.getBusinessValue();
        if (StringUtils.isNotBlank(businessValue)) {
            JSONObject jsonObject = JSON.parseObject(businessValue);
            String orderType = jsonObject.getString("orderType");
            backMap.put("FBillType", orderType);
        }
        Map<String, Integer> fzzMap = new HashMap<>();
        fzzMap.put("FNUMBER", 100);
        backMap.put("FZZ", fzzMap);
        //合并
        List<OutOrderLogisticsDetailBO> batchBOList = logisticsDetailBOList.stream()
                .collect(Collectors.toMap(d -> d.getLineNo() + d.getSku() + d.getInternalBatchCode(), Function.identity(), (v1, v2) -> {
                    v1.setActualQuantity(v1.getActualQuantity() + v2.getActualQuantity());
                    return v1;
                })).values().stream().collect(Collectors.toList());

        Map<String, Object> jinInventoryTypeMap = orderNacosConfig.getJinInventoryTypeMap();
        List<Object> skuList = new ArrayList<>();
        for (OutOrderLogisticsDetailBO detailBatchBO : batchBOList) {
            Map<String, Object> skuMap = new HashMap<>();
            Map<String, Object> fwlMap = new HashMap<>();
            fwlMap.put("FNUMBER", detailBatchBO.getSku());
            skuMap.put("FWL", fwlMap);
            Map<String, Object> flsMap = new HashMap<>();
            flsMap.put("FNUMBER", jinInventoryTypeMap.get(orderBO.getOwnerCode() + "_" + detailBatchBO.getInventoryType()));
            skuMap.put("FlogicStock", flsMap);

            skuMap.put("FQty", detailBatchBO.getActualQuantity());
            skuMap.put("FLot", detailBatchBO.getInternalBatchCode());
            if (Objects.nonNull(detailBatchBO.getProductionDate())) {
                skuMap.put("FPDate", DateUtil.formatDateTime(detailBatchBO.getProductionDate()));
            }
            if (Objects.nonNull(detailBatchBO.getExpireDate())) {
                skuMap.put("FEDate", DateUtil.formatDateTime(detailBatchBO.getExpireDate()));
            }
            skuMap.put("FSrcBillNo", orderBO.getUpstreamNo());
            skuMap.put("FSrcSeq", detailBatchBO.getLineNo());
            skuList.add(skuMap);
        }
        backMap.put("FEntity", skuList);
        //请求参数
        Map<String, Object> requestMap = new HashMap<>(3);
        requestMap.put("IsVerifyBaseDataField", "true");
        requestMap.put("IgnoreInterationFlag", "true");
        requestMap.put("Model", backMap);
        return requestMap;
    }

    /**
     * 发送消息到企业微信
     *
     * @param message
     */
    private void backWarning(String message){
        try {
            String msg = "{\"msgtype\": \"text\",\"text\": {\"content\": \"" + message + "\",\"mentioned_mobile_list\":" + Arrays.toString(orderNacosConfig.getMentionedMobile().split(",")) + "}}";
            HttpRequestUtils.postJson(msg, orderNacosConfig.getRobotUrl(), "单据回传上游失败");
        } catch (Exception e) {
            log.error("[CallBackUpListener-backWarning]=========回传上游系统异常=========orderNo:{}", message);
            log.error("[CallBackUpListener-backWarning]=========企业微信通知异常=========message:{},ex:", message, e);
        }
    }

    private String getKey(String goodsCode, String lineNo, String batchCode) {
        String key = goodsCode;
        if (StringUtils.isNotBlank(lineNo)) {
            key = key + lineNo;
        }
        if (StringUtils.isNotBlank(batchCode)) {
            key = key + batchCode;
        }
        return key;
    }

    private Integer getPlanQtyValue(String goodsCode, String lineNo, String batchCode, Map<String, Integer> planMap) {
        Integer planQty = planMap.get(getKey(goodsCode, lineNo, batchCode));
        if (Objects.isNull(planQty)) {
            planQty = planMap.get(getKey(goodsCode, lineNo, null));
        }
        return planQty;
    }

}
