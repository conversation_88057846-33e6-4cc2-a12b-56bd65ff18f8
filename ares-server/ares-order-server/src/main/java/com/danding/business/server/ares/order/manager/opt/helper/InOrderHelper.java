package com.danding.business.server.ares.order.manager.opt.helper;

import cn.hutool.core.collection.CollectionUtil;
import com.danding.business.common.ares.enums.order.InOrderStatus;
import com.danding.business.core.ares.order.entity.InOrder;
import com.danding.business.core.ares.order.entity.InOrderDetail;
import com.danding.business.core.ares.order.search.InOrderSearch;
import com.danding.business.core.ares.order.service.IInOrderService;
import com.danding.business.server.ares.config.OrderNacosConfig;
import com.danding.business.server.ares.order.BO.InOrderBO;
import com.danding.business.server.ares.order.BO.InOrderDetailBO;
import com.danding.business.server.ares.order.BO.ReceiveSendInfoBO;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.danding.business.common.ares.context.AresContext.*;

@Slf4j
@Component
public class InOrderHelper {

    @Autowired
    private IInOrderService inOrderService;

    @Autowired
    private OrderNacosConfig orderNacosConfig;

    /**
     * 校验单据明细
     * @param outOrderDetailBOList
     */
    public void checkInOrderDetailBOList(List<InOrderDetailBO> outOrderDetailBOList){
        if(CollectionUtil.isEmpty(outOrderDetailBOList)){
            throw new BusinessException("单据明细不能为空!");
        }
    }

    /**
     * 校验单据明细
     * @param outOrderDetailList
     */
    public void checkInOrderDetailList(List<InOrderDetail> outOrderDetailList){
        if(CollectionUtil.isEmpty(outOrderDetailList)){
            throw new BusinessException("单据明细不能为空!");
        }
    }

    /**
     * 校验收货地址
     * @param receiveSendInfoBO
     */
    public void checkReceiveSendInfoBO(ReceiveSendInfoBO receiveSendInfoBO){
        if(Objects.isNull(receiveSendInfoBO)){
            throw new BusinessException("收货信息不能为空!");
        }
        String receiveProvince = receiveSendInfoBO.getReceiveProvince();
        if(StringUtils.isBlank(receiveProvince)){
            throw new BusinessException("收货省名称不能为空!");
        }
        String receiveCity = receiveSendInfoBO.getReceiveCity();
        if(StringUtils.isBlank(receiveCity)){
            throw new BusinessException("收货市名称不能为空!");
        }
        String receiveDistrict = receiveSendInfoBO.getReceiveDistrict();
        if(StringUtils.isBlank(receiveDistrict)){
            throw new BusinessException("收货区名称不能为空!");
        }
        String receiveContactName = receiveSendInfoBO.getReceiveContactName();
        if (StringUtils.isBlank(receiveContactName)) {
            throw new BusinessException("收货联系人不能为空!");
        }
        String receiveContactPhone = receiveSendInfoBO.getReceiveContactPhone();
        if (StringUtils.isBlank(receiveContactPhone)) {
            throw new BusinessException("收货联系人电话不能为空!");
        }
        String receiveAddress = receiveSendInfoBO.getReceiveAddress();
        if(StringUtils.isBlank(receiveAddress)){
            throw new BusinessException("收货详细地址不能为空!");
        }
    }

    /**
     * 校验单据
     * @param order
     */
    public void checkInOrder(InOrder order, String orderNo){
        if(Objects.isNull(order)){
            throw new BusinessException(orderNo+":查询单据为空!");
        }
    }

    /**
     * 校验单据
     * @param orderBO
     */
    public void checkInOrderBO(InOrderBO orderBO){
        if(Objects.isNull(orderBO)){
            throw new BusinessException("单据不能为空!");
        }
        if (StringUtils.isBlank(orderBO.getBusinessOnlyIndex())) {
            throw new BusinessException("业务唯一单号为空!");
        }
        //校验唯一性
        InOrderSearch search = new InOrderSearch();
        search.setBusinessOnlyIndex(orderBO.getBusinessOnlyIndex());
        InOrder order = inOrderService.selectBy(search);
        if (Objects.nonNull(order)) {
            throw new BusinessException(ORDER_REPEAT, order.getInOrderNo() + ":订单重复!");
        }
        if (StringUtils.isBlank(orderBO.getBusinessNo())) {
            throw new BusinessException("关联单号不能为空!");
        }
        if (Objects.isNull(orderBO.getTradeType())) {
            throw new BusinessException("贸易类型不能为空!");
        }
        if ((StringUtils.isBlank(orderBO.getOrigSystem()) || Objects.equals(SYSTEM_DR, orderBO.getOrigSystem())) && orderNacosConfig.getBanAddOrderWarehouseCode().contains(orderBO.getWarehouseCode())) {
            throw new BusinessException("["+orderBO.getWarehouseName()+"]，不允许自建出入库单据!");
        }
        if (Objects.equals(SYSTEM_CNZXC, orderBO.getSystemCode()) && (StringUtils.isBlank(orderBO.getOrigSystem()) || Objects.equals(SYSTEM_DR, orderBO.getOrigSystem()))) {
            throw new BusinessException("菜鸟仓的入库单请在GOS系统上操作!");
        }
    }

    /**
     * 校验单据
     * @param order
     */
    public void checkInOrderStatus(InOrder order, String orderNo){
        if(Objects.isNull(order)){
            throw new BusinessException(orderNo + ":查询单据为空!");
        }
        if (!Objects.equals(InOrderStatus.RK_SH.getValue(), order.getStatus())
                && !Objects.equals(InOrderStatus.RK_FAIL.getValue(), order.getStatus())
                && !Objects.equals(InOrderStatus.RK_REPEAL.getValue(), order.getStatus())) {
            throw new BusinessException("单据状态错误,刷新后在编辑!");
        }
    }

    /**
     * 校验单据
     * @param orderBO
     */
    public void checkMedifyInOrderBO(InOrderBO orderBO){
        if(Objects.isNull(orderBO)){
            throw new BusinessException("单据不能为空!");
        }
        if (StringUtils.isNotBlank(orderBO.getBusinessOnlyIndex())) {
            //校验唯一性
            InOrderSearch search = new InOrderSearch();
            search.setBusinessOnlyIndex(orderBO.getBusinessOnlyIndex());
            InOrder order = inOrderService.selectBy(search);
            if (Objects.nonNull(order) && !Objects.equals(orderBO.getInOrderNo(), order.getInOrderNo())) {
                throw new BusinessException(ORDER_REPEAT, order.getInOrderNo() + ":订单重复!");
            }
        }
    }

}
