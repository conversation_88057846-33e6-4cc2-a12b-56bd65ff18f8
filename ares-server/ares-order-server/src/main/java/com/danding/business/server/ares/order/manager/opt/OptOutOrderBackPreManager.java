package com.danding.business.server.ares.order.manager.opt;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.business.client.ares.order.param.back.ItemSkuBackParam;
import com.danding.business.client.ares.order.param.back.LogisticsBackParam;
import com.danding.business.client.ares.order.param.back.OutOrderBackParam;
import com.danding.business.common.ares.context.AresContext;
import com.danding.business.core.ares.order.entity.OutOrderDetail;
import com.danding.business.core.ares.order.service.IOutOrderDetailService;
import com.danding.business.server.ares.config.OrderNacosConfig;
import com.danding.business.server.ares.order.BO.OutOrderBO;
import com.danding.business.server.ares.order.manager.OutOrderManager;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.danding.business.common.ares.context.AresContext.SYSTEM_OMS;
import static com.danding.business.common.ares.utils.HttpRequestUtils.postJson;

/**
 * 出库单回传
 */
@Slf4j
@Component
public class OptOutOrderBackPreManager {

    @Autowired
    private IOutOrderDetailService outOrderDetailService;
    @Autowired
    private OutOrderManager outOrderManager;
    @Autowired
    private OrderNacosConfig orderNacosConfig;

    /**
     * 出库单回传
     *
     * @param orderBackParam
     * @return
     */
    public boolean execute(OutOrderBackParam orderBackParam) {
        String erpOrderNo = outOrderManager.getErpOrderNo(orderBackParam.getOutOrderNo());
        OutOrderBO outOrderBO = outOrderManager.getOutOrderByNo(erpOrderNo, null);
        List<OutOrderDetail> detailList = outOrderDetailService.selectListByOrderNo(erpOrderNo);
        Map<String, String> collect = detailList.stream().filter(a -> StringUtils.isNotBlank(a.getBarcode())).collect(Collectors.toMap(OutOrderDetail::getSku, OutOrderDetail::getBarcode, (v1, v2) -> v1));
        Map<String, Object> orderBack = buildOutOrderBackOMS(orderBackParam, outOrderBO, collect);
        String request = postJson(orderBack, orderNacosConfig.getCallBackUrlOMS(), SYSTEM_OMS);
        log.info("[CallBackUpListener-backOMSOutOrder]===============================orderNo:{},OMS回传返回值:{}", erpOrderNo, request);
        JSONObject jsonObject = JSON.parseObject(request);
        Integer code = jsonObject.getInteger("code");
        //处理返回后的状态
        if (!Objects.equals(AresContext.ARES_SUCCESS, code)) {
            throw new BusinessException("预出库回传OMS失败,返回值:"+ request);
        }
        return Boolean.TRUE;
    }

    /**
     * 组装OMS回传参数
     *
     * @param orderBackParam
     * @param orderBO
     * @return
     */
    private Map<String, Object> buildOutOrderBackOMS(OutOrderBackParam orderBackParam, OutOrderBO orderBO, Map<String, String> barcodeMap) {
        Map<String, Object> backMap = new HashMap<>();
        backMap.put("orderNo", orderBO.getOutOrderNo());
        backMap.put("businessNo", orderBO.getBusinessNo());
        backMap.put("upstreamNo", orderBO.getUpstreamNo());
        backMap.put("externalNo", orderBO.getExternalNo());
        if (Objects.nonNull(orderBackParam.getActualDate())) {
            backMap.put("actualTime", DateUtil.formatDateTime(orderBackParam.getActualDate()));
            backMap.put("operationTime", DateUtil.formatDateTime(orderBackParam.getActualDate()));
        }
        backMap.put("isConfirm", 2);//2 预出库

        List<Object> logisticsList = new ArrayList<>();
        for (LogisticsBackParam logisticsBackParam : orderBackParam.getLogisticsParamList()) {
            Map<String, Object> logisticsMap = new HashMap<>();
            logisticsMap.put("packageLength", logisticsBackParam.getPackageLength());
            logisticsMap.put("packageWidth", logisticsBackParam.getPackageWidth());
            logisticsMap.put("packageHeight", logisticsBackParam.getPackageHeight());
            logisticsMap.put("packageVolume", logisticsBackParam.getPackageVolume());
            logisticsMap.put("packageWeight", logisticsBackParam.getPackageWeight());
            logisticsMap.put("logisticsNo", logisticsBackParam.getLogisticsNo());
            logisticsMap.put("waybillNo", logisticsBackParam.getLogisticsNo());
            logisticsMap.put("logisticsCompanyCode", logisticsBackParam.getLogisticsCompanyCode());
            logisticsMap.put("packagingMaterials", logisticsBackParam.getPackagingMaterials());
            logisticsMap.put("consumablesMaterials", logisticsBackParam.getConsumablesMaterials());

            List<Object> skuList = new ArrayList<>();
            for (ItemSkuBackParam skuBackParam : logisticsBackParam.getSkuList()) {
                Map<String, Object> skuMap = new HashMap<>();
                skuMap.put("lineNo", skuBackParam.getLineNo());
                skuMap.put("batchCode", skuBackParam.getBatchCode());
                skuMap.put("sku", skuBackParam.getSku());
                skuMap.put("actualQuantity", skuBackParam.getActualQuantity());
                skuMap.put("barcode", barcodeMap.get(skuBackParam.getSku()));
                skuMap.put("inventoryType", skuBackParam.getInventoryType());
                skuMap.put("productDate", DateUtil.formatDate(skuBackParam.getProductionDate()));
                skuMap.put("productionDate", DateUtil.formatDate(skuBackParam.getProductionDate()));
                skuMap.put("expireDate", DateUtil.formatDate(skuBackParam.getExpireDate()));
                skuList.add(skuMap);
            }
            logisticsMap.put("skuList", skuList);
            logisticsList.add(logisticsMap);
        }
        backMap.put("logisticsParamList", logisticsList);
        return backMap;
    }

}
