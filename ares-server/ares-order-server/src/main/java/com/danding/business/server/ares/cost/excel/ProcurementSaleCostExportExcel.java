package com.danding.business.server.ares.cost.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProcurementSaleCostExportExcel implements Serializable {

    /**
     * 云仓名称
     */
    @ExcelProperty("云仓名称")
    private String logicWarehouseName;

    /**
     * 货品名称
     */
    @ExcelProperty("货品名称")
    private String goodsName;

    /**
     * 货品编码
     */
    @ExcelProperty("货品编码")
    private String goodsCode;

    /**
     * SKU
     */
    @ExcelProperty("SKU")
    private String sku;

    /**
     * 条形码
     */
    @ExcelProperty("条形码")
    private String barcode;

    /**
     * 期初数量
     */
    @ExcelProperty("期初数量")
    private Integer earlyQty;

    /**
     * 期初金额
     */
    @ExcelProperty("期初金额")
    private BigDecimal earlyTotalPrice;

    /**
     * 期除单价
     */
    @ExcelProperty("期除单价")
    private BigDecimal earlyPrice;

    /**
     * 本期入库数量
     */
    @ExcelProperty("本期入库数量")
    private Integer inQty;

    /**
     * 本期入库金额
     */
    @ExcelProperty("本期入库金额")
    private BigDecimal inTotalPrice;

    /**
     * 本期入库单价
     */
    @ExcelProperty("本期入库单价")
    private BigDecimal inPrice;

    /**
     * 本期出库数量
     */
    @ExcelProperty("本期出库数量")
    private Integer outQty;

    /**
     * 本期出库金额
     */
    @ExcelProperty("本期出库金额")
    private BigDecimal outTotalPrice;

    /**
     * 本期出库单价
     */
    @ExcelProperty("本期出库单价")
    private BigDecimal outPrice;

    /**
     * 期末数量
     */
    @ExcelProperty("期末数量")
    private Integer endQty;

    /**
     * 期末金额
     */
    @ExcelProperty("期末金额")
    private BigDecimal endTotalPrice;

    /**
     * 期末单价
     */
    @ExcelProperty("期末单价")
    private BigDecimal endPrice;

}
