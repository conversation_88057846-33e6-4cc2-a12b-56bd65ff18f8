package com.danding.business.server.ares.orderdetailmaping.facade;

import com.danding.business.client.ares.corner.param.CornerOrderDetailMappingQueryParam;
import com.danding.business.client.ares.corner.param.CornerOrderDetailMappingUpdParam;
import com.danding.business.client.ares.corner.result.CornerOrderDetailResult;
import com.danding.business.client.ares.goods.result.GoodsResult;
import com.danding.business.client.ares.order.result.OutOrderDetailResult;
import com.danding.business.client.ares.order.result.OutOrderResult;
import com.danding.business.client.ares.orderdetailmaping.facade.IOutOrderDetailMappingFacade;
import com.danding.business.client.ares.orderdetailmaping.param.OutOrderDetailMappingAddParam;
import com.danding.business.client.ares.orderdetailmaping.param.OutOrderDetailMappingQueryParam;
import com.danding.business.client.ares.orderdetailmaping.result.OutOrderDetailMappingResult;
import com.danding.business.common.ares.enums.inventory.InventoryType;
import com.danding.business.common.ares.enums.order.SaleCreateStatus;
import com.danding.business.common.ares.utils.MyStringUtils;
import com.danding.business.core.ares.orderdetailmaping.search.OutOrderDetailMappingSearch;
import com.danding.business.server.ares.order.BO.OutOrderBO;
import com.danding.business.server.ares.order.manager.OutOrderManager;
import com.danding.business.server.ares.order.remote.RemoteGoodsFacade;
import com.danding.business.server.ares.orderdetailmaping.BO.OutOrderDetailMappingBO;
import com.danding.business.server.ares.orderdetailmaping.manager.OutOrderDetailMappingManager;
import com.danding.business.server.ares.orderdetailmaping.remote.RemoteCornerOrder;
import com.danding.component.cache.common.config.RedisLockUtils;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.soul.client.common.exception.BusinessException;
import com.google.common.collect.Maps;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> Xu
 * @since 2021-10-26
 */
@DubboService
public class OutOrderDetailMappingFacadeImpl implements IOutOrderDetailMappingFacade {

    @Autowired
    private OutOrderDetailMappingManager outOrderDetailMappingManager;
    @Autowired
    private OutOrderManager outOrderManager;
    @Autowired
    private RemoteGoodsFacade remoteGoodsFacade;
    @Autowired
    private RemoteCornerOrder remoteCornerOrder;
    @Autowired
    private RedisLockUtils redisLockUtils;

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    @Override
    public OutOrderDetailMappingResult getById(Serializable id) {
        OutOrderDetailMappingBO outOrderDetailMappingBO = outOrderDetailMappingManager.getById(id);
        return BeanUtils.copyProperties(outOrderDetailMappingBO, OutOrderDetailMappingResult.class);
    }

    /**
     * 条件查询单个
     *
     * @param outOrderDetailMappingQueryParam
     * @return
     */
    @Override
    public OutOrderDetailMappingResult getByQueryParam(OutOrderDetailMappingQueryParam outOrderDetailMappingQueryParam) {
        OutOrderDetailMappingBO outOrderDetailMappingBO = outOrderDetailMappingManager.getBySearch(BeanUtils.copyProperties(outOrderDetailMappingQueryParam, OutOrderDetailMappingSearch.class));
        return BeanUtils.copyProperties(outOrderDetailMappingBO, OutOrderDetailMappingResult.class);
    }

    /**
     * 条件查询list
     *
     * @param outOrderDetailMappingQueryParam
     * @return
     */
    @Override
    public List<OutOrderDetailMappingResult> listByQueryParam(OutOrderDetailMappingQueryParam outOrderDetailMappingQueryParam) {
        List<OutOrderDetailMappingBO> outOrderDetailMappingBOList = outOrderDetailMappingManager.listBySearch(BeanUtils.copyProperties(outOrderDetailMappingQueryParam, OutOrderDetailMappingSearch.class));
        return BeanUtils.copyProperties(outOrderDetailMappingBOList, OutOrderDetailMappingResult.class);
    }

    /**
     * 条件分页查询
     *
     * @param outOrderDetailMappingQueryParam
     * @return
     */
    @Override
    public ListVO<OutOrderDetailMappingResult> pageListByQueryParam(OutOrderDetailMappingQueryParam outOrderDetailMappingQueryParam) {
        ListVO<OutOrderDetailMappingBO> outOrderDetailMappingBOListVO = outOrderDetailMappingManager.pageListBySearch(BeanUtils.copyProperties(outOrderDetailMappingQueryParam, OutOrderDetailMappingSearch.class));
        return ListVO.build(outOrderDetailMappingBOListVO.getPage(), BeanUtils.copyProperties(outOrderDetailMappingBOListVO.getDataList(), OutOrderDetailMappingResult.class));
    }

    /**
     * 插入
     *
     * @param outOrderDetailMappingAddParam
     * @return
     */
    @Override
    public boolean add(OutOrderDetailMappingAddParam outOrderDetailMappingAddParam) {
        return outOrderDetailMappingManager.add(BeanUtils.copyProperties(outOrderDetailMappingAddParam, OutOrderDetailMappingBO.class));
    }

    /**
     * 批量插入
     *
     * @param outOrderDetailMappingAddParamList
     * @return
     */
    @Override
    public boolean addList(List<OutOrderDetailMappingAddParam> outOrderDetailMappingAddParamList) {
        return outOrderDetailMappingManager.addList(BeanUtils.copyProperties(outOrderDetailMappingAddParamList, OutOrderDetailMappingBO.class));
    }

    /**
     * 根据主键id修改
     *
     * @param outOrderDetailMappingAddParam
     * @return
     */
    @Override
    public boolean updateById(OutOrderDetailMappingAddParam outOrderDetailMappingAddParam) {
        return outOrderDetailMappingManager.updateById(BeanUtils.copyProperties(outOrderDetailMappingAddParam, OutOrderDetailMappingBO.class));
    }

    /**
     * 根据主键id批量修改
     *
     * @param outOrderDetailMappingAddParamList
     * @return
     */
    @Override
    public boolean updateListById(List<OutOrderDetailMappingAddParam> outOrderDetailMappingAddParamList) {
        return outOrderDetailMappingManager.updateListById(BeanUtils.copyProperties(outOrderDetailMappingAddParamList, OutOrderDetailMappingBO.class));
    }

    /**
     * 根据条件修改
     *
     * @param outOrderDetailMappingQueryParam
     * @param outOrderDetailMappingAddParam
     * @return
     */
    @Override
    public boolean updateListByQueryParam(OutOrderDetailMappingQueryParam outOrderDetailMappingQueryParam, OutOrderDetailMappingAddParam outOrderDetailMappingAddParam) {
        return outOrderDetailMappingManager.updateListBySearch(BeanUtils.copyProperties(outOrderDetailMappingQueryParam, OutOrderDetailMappingSearch.class), BeanUtils.copyProperties(outOrderDetailMappingAddParam, OutOrderDetailMappingBO.class));
    }

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    @Override
    public boolean removeById(Serializable id) {
        return outOrderDetailMappingManager.removeById(id);
    }

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    @Override
    public boolean removeByIds(List<Long> idList) {
        return outOrderDetailMappingManager.removeByIds(idList);
    }

    /**
     * 根据条件删除
     *
     * @param outOrderDetailMappingQueryParam
     * @return
     */
    @Override
    public boolean removeByQueryParam(OutOrderDetailMappingQueryParam outOrderDetailMappingQueryParam) {
        return outOrderDetailMappingManager.removeBySearch(BeanUtils.copyProperties(outOrderDetailMappingQueryParam, OutOrderDetailMappingSearch.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean mapping(OutOrderResult outOrderResult) {
        String lockKey = "OUT_ORDER_MAPPING_KEY_" + outOrderResult.getOutOrderNo();
        long time = System.currentTimeMillis() + 1000 * 600;
        boolean isLock = false;
        try {
            isLock = redisLockUtils.lock(lockKey, time);
            if (!isLock) {
                // 无法获取redis锁，直接退出等待下次调用
                throw new BusinessException("系统繁忙，请稍后再试！");
            }
            List<OutOrderDetailResult> detailResultList = outOrderResult.getDetailResultList();
            List<String> goodsCodes = detailResultList.stream().map(OutOrderDetailResult::getGoodsCode).collect(Collectors.toList());
            List<GoodsResult> goodsResultList = remoteGoodsFacade.listGoodsResult(outOrderResult.getUserId(), goodsCodes);
            Map<String, GoodsResult> goodsResultMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(goodsResultList)) {
                goodsResultMap = goodsResultList.stream().collect(Collectors.toMap(goodsResult -> goodsResult.getUserId() + goodsResult.getGoodsCode(), Function.identity(), (v1, v2) -> v1));
            }
            // goods表获取原用户A/B/C的userId
            goodsResultList.get(0).getOriginUserId();
            Map<String, GoodsResult> finalGoodsResultMap = goodsResultMap;
            detailResultList.forEach(detailResult -> {
                if (InventoryType.QUALITY.getValue().equals(detailResult.getInventoryType())) {
                    CornerOrderDetailMappingQueryParam queryParam = new CornerOrderDetailMappingQueryParam();
                    queryParam.setUserId(outOrderResult.getUserId());
                    queryParam.setGoodsCode(MyStringUtils.getGoodsCodeWithoutDaitaT(detailResult.getGoodsCode()));
                    queryParam.setBatchCode(detailResult.getBatchCode());
                    queryParam.setGoodsNumber(detailResult.getActualQuantity());
                    queryParam.setReceiveLogicWarehouseCode(outOrderResult.getLogicWarehouseCode());
                    // 获取映射列表
                    List<CornerOrderDetailResult> mappingCornerOrderDetailList = remoteCornerOrder.getMappingCornerOrderDetailList(queryParam);
                    if (!CollectionUtils.isEmpty(mappingCornerOrderDetailList)) {
                        GoodsResult goodsResult = finalGoodsResultMap.get(outOrderResult.getUserId() + detailResult.getGoodsCode());
                        List<OutOrderDetailMappingBO> addList = mappingCornerOrderDetailList.stream().map(cornerOrderDetailResult -> {
                            OutOrderDetailMappingBO mappingBO = new OutOrderDetailMappingBO();
                            mappingBO.setCornerOrderNo(cornerOrderDetailResult.getCornerOrderNo());
                            mappingBO.setLogicWarehouseCode(cornerOrderDetailResult.getLogicWarehouseCode());
                            mappingBO.setLogicWarehouseName(cornerOrderDetailResult.getLogicWarehouseName());
                            mappingBO.setOutOrderDetailId(detailResult.getId());
                            mappingBO.setOriginUserId(goodsResult.getOriginUserId());
                            mappingBO.setOriginUserName(goodsResult.getOriginUserName());
                            mappingBO.setGoodsNum(cornerOrderDetailResult.getCurMappedQuantity());
                            return mappingBO;
                        }).collect(Collectors.toList());
                        // 插入映射数据
                        boolean isSuccess = outOrderDetailMappingManager.addList(addList);
                        if (isSuccess) {
                            // 更改出库单主单状态
                            OutOrderBO orderBO = new OutOrderBO();
                            orderBO.setOutOrderNo(detailResult.getOutOrderNo());
                            orderBO.setSaleCreateStatus(SaleCreateStatus.WAITING_PROCESS);
                            isSuccess = outOrderManager.updateOutOrderByNo(orderBO);
                        }
                        if (isSuccess) {
                            // 更新分销单数量和状态
                            List<CornerOrderDetailMappingUpdParam> updateList = mappingCornerOrderDetailList.stream().map(cornerOrderDetailResult -> {
                                CornerOrderDetailMappingUpdParam mappingUpdParam = new CornerOrderDetailMappingUpdParam();
                                mappingUpdParam.setId(cornerOrderDetailResult.getId());
                                mappingUpdParam.setMappedQuantity(cornerOrderDetailResult.getCurMappedQuantity());
                                return mappingUpdParam;
                            }).collect(Collectors.toList());
                            isSuccess = remoteCornerOrder.updateCornerOrderMapping(updateList);
                        }
                        if (!isSuccess) {
                            throw new BusinessException("综保业务出库单匹配分销单失败！单号：" + outOrderResult.getOutOrderNo());
                        }
                    } else {
                        throw new BusinessException("无法匹配到分销单！出库单号：" + detailResult.getOutOrderNo() + ";goodsCode:" + detailResult.getGoodsCode() + ";batchCode:" + detailResult.getBatchCode());
                    }
                } else {
                    throw new BusinessException("综保业务不允许出库次品！单号：" + outOrderResult.getOutOrderNo());
                }
            });
            return true;
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e) {
            throw new BusinessException("出库单明细映射失败,系统异常！出库单号：" + outOrderResult.getOutOrderNo());
        } finally {
            if (isLock) {
                redisLockUtils.unlock(lockKey, time);
            }
        }
    }
}
