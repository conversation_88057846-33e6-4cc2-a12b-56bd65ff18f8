package com.danding.business.server.ares.order.manager.opt;

import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.entitywarehouse.result.EntityAndOwnerResult;
import com.danding.business.common.ares.enums.common.TrusteeshipType;
import com.danding.business.common.ares.enums.order.InOrderStatus;
import com.danding.business.common.ares.enums.order.InOrderType;
import com.danding.business.core.ares.order.entity.InOrder;
import com.danding.business.core.ares.order.entity.InOrderDetail;
import com.danding.business.core.ares.order.entity.ReceiveSendInfo;
import com.danding.business.core.ares.order.service.IInOrderDetailService;
import com.danding.business.core.ares.order.service.IInOrderService;
import com.danding.business.core.ares.order.service.IReceiveSendInfoService;
import com.danding.business.server.ares.order.BO.InOrderBO;
import com.danding.business.server.ares.order.BO.ReceiveSendInfoBO;
import com.danding.business.server.ares.order.manager.InOrderManager;
import com.danding.business.server.ares.order.manager.opt.helper.InOrderHelper;
import com.danding.business.server.ares.order.remote.RemoteFlowFacade;
import com.danding.business.server.ares.order.remote.RemoteWarehouseFacade;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import static com.danding.business.common.ares.context.AresContext.SYSTEM_DR;

/**
 * 出库单修改
 */
@Slf4j
@Component
public class OptInOrderModifyManager {

    @Autowired
    private IInOrderService inOrderService;
    @Autowired
    private IInOrderDetailService inOrderDetailService;
    @Autowired
    private IReceiveSendInfoService receiveSendInfoService;
    @Autowired
    private InOrderManager inOrderManager;
    @Autowired
    private InOrderHelper inOrderHelper;
    @Autowired
    private RemoteWarehouseFacade remoteWarehouseFacade;
    @Autowired
    private RemoteFlowFacade remoteFlowFacade;

    /**
     * 修改出库单
     * @param orderBO
     * @return
     */
    public InOrderBO execute(InOrderBO orderBO) {
        // 构建参数
        buildOrderParam(orderBO);
        // 校验单据
        inOrderHelper.checkMedifyInOrderBO(orderBO);

        // 非托管采购单,回写计划数量
        backProcurementGoodsPlanQty(orderBO);

        //删除原有的
        inOrderDetailService.deleteByInOrderNo(orderBO.getInOrderNo());
        List<InOrderDetail> orderDetailList = BeanUtils.copyProperties(orderBO.getDetailBOList(), InOrderDetail.class);
        boolean insertList = inOrderDetailService.insertList(orderDetailList);
        if(!insertList){
            log.error("[OptInOrderAddManager-execute]=====================新增入库单明细失败=============detail:{}", JSON.toJSONString(orderDetailList));
            throw new BusinessException("修改入库单失败!");
        }

        ReceiveSendInfo receiveSendInfo = BeanUtils.copyProperties(orderBO.getReceiveSendInfoBO(), ReceiveSendInfo.class);
        receiveSendInfoService.saveOrUpdateByOrderNo(receiveSendInfo, Boolean.TRUE);

        // 新增入库单
        InOrder inOrder = BeanUtils.copyProperties(orderBO, InOrder.class);
        inOrderService.saveOrUpdateByOrderNo(inOrder, Boolean.TRUE);

        //自动下发
        int delayLevel = Objects.equals(TrusteeshipType.NO, orderBO.getIsTrusteeship()) && Objects.equals(InOrderType.CG_RK.getValue(), orderBO.getType()) ? 2 : 1;
        inOrderManager.autoPushInOrder(orderBO, delayLevel);
        return orderBO;
    }

    /**
     * 构建订单信息
     * @param orderBO
     * @return
     */
    private void buildOrderParam(InOrderBO orderBO) {
        InOrder order = inOrderService.selectByOrderNo(orderBO.getInOrderNo(), null);
        inOrderHelper.checkInOrderStatus(order, orderBO.getInOrderNo());
        orderBO.setOrigSystem(order.getOrigSystem());
        orderBO.setType(order.getType());
        orderBO.setStatus(InOrderStatus.RK_SH.getValue());
        orderBO.setTotalPrice(BigDecimal.ZERO);//默认0
        orderBO.setPlanQuantity(0);//默认0
        // 异常信息
        orderBO.setExceptionMsg("");
        orderBO.setExceptionStatus(0);
        orderBO.setExtensionJson(order.getExtensionJson());
        //自建订单,页面填写了关联单号,默认给ET单号
        if ((Objects.equals(SYSTEM_DR, orderBO.getOrigSystem()) || StringUtils.isBlank(orderBO.getOrigSystem()))
                && !(Objects.equals(InOrderType.DB_RK.getValue(), orderBO.getType()) || Objects.equals(InOrderType.CG_RK.getValue(), orderBO.getType()))) {
            if (StringUtils.isBlank(orderBO.getBusinessNo())) {
                orderBO.setBusinessNo(orderBO.getInOrderNo());
            }
            orderBO.setExternalNo(orderBO.getBusinessNo());
            orderBO.setBusinessOnlyIndex(orderBO.getBusinessNo());
            inOrderManager.regexBusinessNo(orderBO.getBusinessNo());
        } else {
            orderBO.setBusinessNo(null);
        }

        // 构建基本信息
        buildUserBasicInfo(orderBO);
        //构建货品信息
        inOrderManager.buildGoodsInfoV2(orderBO);
        //提交审核
        inOrderManager.optSubApproval(orderBO);
        //发货信息
        ReceiveSendInfoBO receiveSendInfoBO = orderBO.getReceiveSendInfoBO();
        if(Objects.isNull(receiveSendInfoBO)){
            receiveSendInfoBO = new ReceiveSendInfoBO();
            orderBO.setReceiveSendInfoBO(receiveSendInfoBO);
        }
        receiveSendInfoBO.setOrderNo(orderBO.getInOrderNo());
    }

    /**
     * 设置基础相关信息
     *
     * @param orderBO
     */
    private void buildUserBasicInfo(InOrderBO orderBO) {
        EntityAndOwnerResult entityAndOwnerResult = remoteWarehouseFacade.getEntityAndOwnerResultSmall(orderBO.getLogicWarehouseCode());
        //构建实体仓和货主信息
        orderBO.setSystemCode(entityAndOwnerResult.getSystemCode());
        orderBO.setIsTrusteeship(entityAndOwnerResult.getIsTrusteeship());
        orderBO.setIsAutoPush(entityAndOwnerResult.getIsAutoPush());
        orderBO.setOwnerCode(entityAndOwnerResult.getOwnerCode());
    }

    /**
     * 回传采购单计划数量
     * @param orderBO
     */
    private void backProcurementGoodsPlanQty(InOrderBO orderBO){
        if (Objects.equals(InOrderType.CG_RK.getValue(), orderBO.getType()) && StringUtils.isBlank(orderBO.getOrigSystem())) {
            inOrderManager.saveProcurementGoodsPlanQty(orderBO, Boolean.TRUE);
        }
    }

}
