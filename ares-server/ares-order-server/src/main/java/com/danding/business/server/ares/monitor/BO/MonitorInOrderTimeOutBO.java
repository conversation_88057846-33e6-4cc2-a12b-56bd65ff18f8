package com.danding.business.server.ares.monitor.BO;

import lombok.Data;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * <p>
 * 入库单监控表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-12
 */
@Data
public class MonitorInOrderTimeOutBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * WSM实体仓库编码
     */
    private String warehouseCode;

    /**
     * 实体仓库编码
     */
    private String entityWarehouseCode;

    /**
     * 实体仓名称
     */
    private String entityWarehouseName;

    /**
     * 清关超时
     */
    private int customsTimeOut = 0;

    /**
     * 清关超时关联单号
     */
    private Set<String> customsBusinessNoList = new HashSet<>();

    /**
     * 理货超时
     */
    private int tallyTimeOut = 0;

    /**
     * 理货超时关联单号
     */
    private Set<String> tallyBusinessNoList = new HashSet<>();

    /**
     * 上架超时
     */
    private int shelfTimeOut = 0;

    /**
     * 上架超时关联单号
     */
    private Set<String> shelfBusinessNoList = new HashSet<>();

}
