package com.danding.business.server.ares.order.manager;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.business.client.ares.goods.result.GoodsResult;
import com.danding.business.client.ares.goodsManagement.result.GoodsManagementResult;
import com.danding.business.client.ares.inventory.message.InventoryProcessMessage;
import com.danding.business.client.ares.logicwarehouse.result.LogicWarehouseResult;
import com.danding.business.client.ares.order.facade.IMockBackFacade;
import com.danding.business.client.ares.order.message.ItemSkuMessage;
import com.danding.business.client.ares.order.message.OrderMessage;
import com.danding.business.client.ares.order.param.back.trace.InOrderWmsTraceBackParam;
import com.danding.business.client.ares.order.param.back.trace.TallyReportUrl;
import com.danding.business.client.ares.owner.result.OwnerResult;
import com.danding.business.client.ares.readyorder.result.ReadyOrderResult;
import com.danding.business.client.ares.tallyReport.result.TallyReportDetailBatchResult;
import com.danding.business.client.ares.tallyReport.result.TallyReportDetailResult;
import com.danding.business.client.ares.tallyReport.result.TallyReportResult;
import com.danding.business.common.ares.config.CurrencyConfig;
import com.danding.business.common.ares.config.RegulatoryOwner;
import com.danding.business.common.ares.enums.common.*;
import com.danding.business.common.ares.enums.goods.GoodsBatchManagement;
import com.danding.business.common.ares.enums.goods.GoodsStatus;
import com.danding.business.common.ares.enums.goods.PledgeStatus;
import com.danding.business.common.ares.enums.inventory.BusinessBillType;
import com.danding.business.common.ares.enums.inventory.InventoryType;
import com.danding.business.common.ares.enums.inventory.InventoryUpdateType;
import com.danding.business.common.ares.enums.order.*;
import com.danding.business.common.ares.enums.trade.TallyReportMultiType;
import com.danding.business.common.ares.utils.DateUtils;
import com.danding.business.common.ares.utils.RedisUtils;
import com.danding.business.core.ares.order.entity.*;
import com.danding.business.core.ares.order.search.*;
import com.danding.business.core.ares.order.service.*;
import com.danding.business.rpc.client.ares.order.message.OrderRpcMessage;
import com.danding.business.server.ares.config.OrderNacosConfig;
import com.danding.business.server.ares.order.BO.*;
import com.danding.business.server.ares.order.manager.helper.InOrderManagerHelper;
import com.danding.business.server.ares.order.remote.*;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.file.FileDto;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.rocketmq.message.SpringMessage;
import com.danding.component.rocketmq.producer.SpringRocketMQProducer;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.soul.client.common.exception.BusinessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.shardingsphere.transaction.annotation.ShardingTransactionType;
import org.apache.shardingsphere.transaction.core.TransactionType;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.danding.business.common.ares.context.AresContext.*;
import static com.danding.business.common.ares.utils.HttpRequestUtils.*;

/**
 * <p>
 * 入库单主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-20
 */
@Slf4j
@Component
public class InOrderManager {

    @Autowired
    private IInOrderService inOrderService;
    @Autowired
    private IOutOrderService outOrderService;
    @Autowired
    private OutOrderManager outOrderManager;
    @Autowired
    private IInOrderDetailService inOrderDetailService;
    @Autowired
    private IInOrderDetailBatchService inOrderDetailBatchService;
    @Autowired
    private IReceiveSendInfoService receiveSendInfoService;
    @Autowired
    private InOrderManagerHelper inOrderManagerHelper;
    @Autowired
    private RemoteFlowFacade remoteFlowFacade;
    @Autowired
    private RemoteWarehouseFacade remoteWarehouseFacade;
    @Autowired
    private RemoteGoodsFacade remoteGoodsFacade;
    @Autowired
    private RemoteProcurementFacade remoteProcurementFacade;
    @Autowired
    private RemoteReadyOrderFacade remoteReadyOrderFacade;
    @Autowired
    private SpringRocketMQProducer springRocketMQProducer;
    @Autowired
    private CurrencyConfig billConfig;
    @Autowired
    private RemoteTallyReportFacade remoteTallyReportFacade;
    @DubboReference
    private IMockBackFacade mockBackFacade;
    @Autowired
    private IInOrderDetailBarcodeService inOrderDetailBarcodeService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private OrderNodeManager orderNodeManager;
    @Autowired
    private RegulatoryOwner regulatoryOwner;
    @Autowired
    private OrderNacosConfig orderNacosConfig;

    /**
     * 查询列表
     *
     * @param orderSearch
     * @return
     */
    public ListVO<InOrderBO> queryPageList(InOrderSearch orderSearch) {
        //条件搜索,如果使用货品code或名称查询 匹配sku中的入库单号
        if (StringUtils.isNotBlank(orderSearch.getGoodsCode()) || StringUtils.isNotBlank(orderSearch.getSku()) || StringUtils.isNotBlank(orderSearch.getGoodsName())) {
            InOrderDetailSearch orderDetailSearch = new InOrderDetailSearch();
            orderDetailSearch.setSku(orderSearch.getSku());
            orderDetailSearch.setGoodsCode(orderSearch.getGoodsCode());
            orderDetailSearch.setGoodsName(orderSearch.getGoodsName());
            List<InOrderDetail> inOrderDetails = inOrderDetailService.selectListBySearch(orderDetailSearch);
            List<String> orderNoList = inOrderDetails.stream().map(InOrderDetail::getInOrderNo).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderNoList)) {
                orderNoList.add("null");
            }
            orderSearch.setOrderNoList(orderNoList);
        }
        ListVO<InOrder> orderList = inOrderService.pageSelectList(orderSearch);
        List<InOrderBO> orderBOList = new ArrayList<>();
        for (InOrder inOrder : orderList.getDataList()) {
            InOrderBO inOrderBO = BeanUtils.copyProperties(inOrder, InOrderBO.class);
            inOrderBO.setTypeDesc(InOrderType.getByCode(inOrder.getType()).getDes());
            inOrderBO.setStatusDesc(InOrderStatus.getByCode(inOrder.getStatus()).getDes());
            inOrderBO.setApprovalStatusDesc(ApprovalStatus.getByCode(inOrder.getApprovalStatus()).getDes());
            inOrderBO.setPushStatusDesc(PushStatus.getByCode(inOrder.getPushStatus()).getDes());
            try {
                LogicWarehouseResult logicWarehouseResult = remoteWarehouseFacade.getLogicWarehouseByCode(inOrderBO.getLogicWarehouseCode());
                // 调拨入库 and 价格编辑状态不为空（可编辑）and 订单状态：已创建或已驳回
                boolean isPriceEditor = Objects.equals(InOrderType.DB_RK.getValue(), inOrder.getType()) && Objects.nonNull(inOrder.getPriceStatus())
                        && (Objects.equals(InOrderStatus.RK_SH.getValue(), inOrderBO.getStatus()) || Objects.equals(InOrderStatus.RK_FAIL.getValue(), inOrderBO.getStatus()));
                inOrderBO.setIsPriceEditor(isPriceEditor);
                inOrderBO.setIsEditor(!Objects.equals(TrusteeshipType.YES, logicWarehouseResult.getIsTrusteeship()) && !Objects.equals(InOrderType.TH_RK.getValue(), inOrder.getType()));
                boolean mockStatus = Objects.equals(InOrderStatus.RK_XR_IS.getValue(), inOrder.getStatus()) || Objects.equals(InOrderStatus.RK_WAIT_PUSH.getValue(), inOrder.getStatus()) ||
                        (Objects.equals(InOrderStatus.RK_XR_IS.getValue(), inOrder.getStatus()) && Objects.equals(InOrderType.DB_RK.getValue(), inOrder.getType()));
                inOrderBO.setMockStatus(mockStatus);
                inOrderBO.setLogicWarehouseName(logicWarehouseResult.getLogicWarehouseName());
                if (Objects.nonNull(logicWarehouseResult.getLogicWarehouseType())) {
                    inOrderBO.setLogicWarehouseType(logicWarehouseResult.getLogicWarehouseType().getValue());
                    inOrderBO.setLogicWarehouseTypeDesc(Objects.equals(LogicWarehouseType.ENTITY.getValue(), inOrderBO.getLogicWarehouseType()) ? "实体仓" : "虚拟仓");
                }
                inOrderBO.setIsTrusteeship(logicWarehouseResult.getIsTrusteeship());
                inOrderBO.setWarehouseName(remoteWarehouseFacade.getWarehouseNameByCodeCatch(logicWarehouseResult.getEntityWarehouseCode()));
                inOrderBO.setOwnerName(remoteWarehouseFacade.getOwnerNameByCodeCatch(inOrderBO.getOwnerCode()));
            } catch (Exception e) {
                log.error("[InOrderManager-queryPageList]=============查询逻辑仓信息异常=============ex:", e);
            }
            orderBOList.add(inOrderBO);
        }
        return ListVO.build(orderList.getPage(), orderBOList);
    }

    /**
     * 分页查询
     *
     * @param orderSearch
     * @return
     */
    @PageSelect
    public ListVO<InOrderBO> pageListBySearch(InOrderSearch orderSearch) {
        ListVO<InOrderBO> inOrderBOListVO = new ListVO<>();
        List<InOrder> inOrderList = inOrderService.selectListBy(orderSearch);
        return ListVO.build(inOrderBOListVO.getPage(), BeanUtils.copyProperties(inOrderList, InOrderBO.class));
    }

    /**
     * 查询详情
     *
     * @param orderNo
     * @return
     */
    public InOrderBO queryByOrderNo(String orderNo, Long userId, Integer operationType) {
        //查询入库单
        InOrder inOrder = inOrderService.selectByOrderNo(orderNo, userId);
        inOrderManagerHelper.checkInOrder(inOrder, orderNo);
        InOrderBO orderBO = BeanUtils.copyProperties(inOrder, InOrderBO.class);
        orderBO.setStatusDesc(InOrderStatus.getByCode(orderBO.getStatus()).getDes());
        orderBO.setTypeDesc(InOrderType.getByCode(orderBO.getType()).getDes());
        orderBO.setApprovalStatusDesc(ApprovalStatus.getByCode(orderBO.getApprovalStatus()).getDes());
        try {
            LogicWarehouseResult logicWarehouseByCode = remoteWarehouseFacade.getLogicWarehouseByCode(orderBO.getLogicWarehouseCode());
            orderBO.setLogicWarehouseName(logicWarehouseByCode.getLogicWarehouseName());
            if (Objects.nonNull(logicWarehouseByCode.getLogicWarehouseType())) {
                orderBO.setLogicWarehouseType(logicWarehouseByCode.getLogicWarehouseType().getValue());
                orderBO.setLogicWarehouseTypeDesc(Objects.equals(LogicWarehouseType.ENTITY.getValue(), orderBO.getLogicWarehouseType()) ? "实体仓" : "虚拟仓");
            }
        } catch (Exception e) {
            log.error("[InOrderManager-queryByOrderNo]=============查询逻辑仓信息异常=============ex:", e);
        }

        //查询货品详情
//        List<InOrderDetail> detailList = inOrderDetailService.selectListByOrderNo(orderNo);
        InOrderDetailSearch inOrderDetailSearch = new InOrderDetailSearch();
        inOrderDetailSearch.setInOrderNo(orderNo);
        inOrderDetailSearch.setMultiType(TallyReportMultiType.NORMAL);
//        List<InOrderDetail> detailList = inOrderDetailService.selectListBySearch(inOrderDetailSearch);
        List<InOrderDetail> detailList = inOrderDetailService.selectListByOrderNo(inOrderDetailSearch);
        List<InOrderDetailBO> detailBOList = mergeDetail(orderBO, detailList);

        //查询采购单上的货品详情
        List<InOrderDetailBO> procurementSkuList = null;
        if (Objects.equals(InOrderType.CG_RK.getValue(), inOrder.getType()) && Objects.equals(1, operationType) && StringUtils.isBlank(inOrder.getOrigSystem())) {
            procurementSkuList = remoteProcurementFacade.queryProcurementOrderDetailByNO(inOrder.getBusinessNo());
        }

        orderBO.setDetailBOList(buildOrderDetail(detailBOList, procurementSkuList, Objects.equals(1, operationType)));

        //查询批次详情
        List<InOrderDetailBO> boList = orderBO.getDetailBOList();
        for (InOrderDetailBO inOrderDetailBO : boList) {
            String billCurrencyName = billConfig.getNameByCode(inOrderDetailBO.getBillCurrency());
            inOrderDetailBO.setBillCurrencyName(billCurrencyName);

            InOrderDetailBatchSearch search = new InOrderDetailBatchSearch();
            search.setInOrderNo(inOrderDetailBO.getInOrderNo());
            search.setGoodsCode(inOrderDetailBO.getGoodsCode());
            if (Objects.nonNull(inOrder.getCreateTime()) && inOrder.getCreateTime() > 1726150200000L) { //TODO 限制为上线时间,防止老的数据无行号
                search.setLineNo(inOrderDetailBO.getLineNo());
            }
            List<InOrderDetailBatch> batchList = inOrderDetailBatchService.selectListBySearch(search);

            //合并重复的批次
            Map<String, InOrderDetailBatchBO> batchMap = new HashMap<>();
            for (InOrderDetailBatch detailBatch : batchList) {
                String key = detailBatch.getGoodsCode();
                if (StringUtils.isNotBlank(detailBatch.getInternalBatchCode())) {
                    key = key + detailBatch.getInternalBatchCode();
                }
                //正次品
                if (Objects.nonNull(detailBatch.getInventoryType())) {
                    key = key + detailBatch.getInventoryType();
                }
                //是否异常品
                if (Objects.nonNull(detailBatch.getExceeded())) {
                    key = key + detailBatch.getExceeded();
                }
                //生产日期
                if (Objects.nonNull(detailBatch.getProductionDate())) {
                    key = key + detailBatch.getProductionDate().getTime();
                }
                //过期日期
                if (Objects.nonNull(detailBatch.getExpireDate())) {
                    key = key + detailBatch.getExpireDate().getTime();
                }
                //行号
                if (StringUtils.isNotBlank(detailBatch.getLineNo())) {
                    key = key + detailBatch.getLineNo();
                }
                InOrderDetailBatchBO orderDetailBatchBO = batchMap.get(key);
                if (Objects.nonNull(orderDetailBatchBO)) {
                    orderDetailBatchBO.setActualQuantity(orderDetailBatchBO.getActualQuantity() + detailBatch.getActualQuantity());
                } else {
                    InOrderDetailBatchBO inOrderDetailBatchBO = BeanUtils.copyProperties(detailBatch, InOrderDetailBatchBO.class);
                    batchMap.put(key, inOrderDetailBatchBO);
                }
            }

            List<InOrderDetailBatchBO> batchBOS = new ArrayList<>(batchMap.values());
            inOrderDetailBO.setBatchList(batchBOS);
        }

        //审批中,就去查询权限
        if (ApprovalStatus.SP_IS.getValue().equals(orderBO.getApprovalStatus()) && Objects.nonNull(userId)) {
            Long realUserId = SimpleUserHelper.getRealUserId();

            String queryOrderNo = inOrder.getInOrderNo();
            if (StringUtils.isNotBlank(orderBO.getExtensionJson())) {
                ExtensionJsonBO extensionJsonBO = JSON.parseObject(orderBO.getExtensionJson(), ExtensionJsonBO.class);
                if (Objects.nonNull(extensionJsonBO.getRepealVersion()) && !Objects.equals(0, extensionJsonBO.getRepealVersion())) {
                    queryOrderNo = queryOrderNo + "_" + extensionJsonBO.getRepealVersion();
                }
            }
            Long flowId = remoteFlowFacade.getApprovalAuthBy(realUserId, queryOrderNo, Boolean.TRUE);
            orderBO.setApprovalAuth(Objects.nonNull(flowId));
            if (Objects.nonNull(flowId)) {
                orderBO.setTaskId(flowId + "");
            }
        }
        //查询物流信息
        ReceiveSendInfo receiveSendInfo = receiveSendInfoService.selectByOrderNo(orderNo);
        ReceiveSendInfoBO sendInfoBO = BeanUtils.copyProperties(receiveSendInfo, ReceiveSendInfoBO.class);
        orderBO.setReceiveSendInfoBO(sendInfoBO);

        // 应该先用入库单号（新单子用入库单号备货的）查询备货单查不到（老单子用采购单备货的）的时候再用采购单查询
        ReadyOrderResult readyOrder = remoteReadyOrderFacade.getReadyOrderBy(orderBO.getInOrderNo());
        if (Objects.isNull(readyOrder)) {
            //查询备货信息
            readyOrder = remoteReadyOrderFacade.getReadyOrderBy(orderBO.getBusinessNo());
        }
        if (Objects.nonNull(readyOrder)) {
            orderBO.setReadyNo(readyOrder.getReadyNo());
            orderBO.setDeclarationNo(readyOrder.getDeclarationNo());
            orderBO.setCustomsFinishTime(readyOrder.getFinishTime());
        }
        return orderBO;
    }

    /**
     * 合并
     *
     * @param orderBO
     * @param detailList
     * @return
     */
    private List<InOrderDetailBO> mergeDetail(InOrderBO orderBO, List<InOrderDetail> detailList) {
        //如果是分销退货入库,需要合并
        if (Objects.equals(InOrderType.FX_THRK.getValue(), orderBO.getType())) {
            detailList = detailList.stream().collect(Collectors.toMap(InOrderDetail::getGoodsCode, detail -> detail, (d1, d2) -> {
                d1.setPlanQuantity(d1.getPlanQuantity() + d2.getPlanQuantity());
                d1.setActualQuantity(d1.getActualQuantity() + d2.getActualQuantity());
                d1.setCpQuality(d1.getCpQuality() + d2.getCpQuality());
                d1.setZpQuality(d1.getZpQuality() + d2.getZpQuality());
                return d1;
            })).values().stream().collect(Collectors.toList());
        }
        return BeanUtils.copyProperties(detailList, InOrderDetailBO.class);
    }

    /**
     * 组装数据
     *
     * @param detailBOList
     * @param procurementSkuList
     * @return
     */
    private List<InOrderDetailBO> buildOrderDetail(List<InOrderDetailBO> detailBOList, List<InOrderDetailBO> procurementSkuList, Boolean isEdit) {
        try {
            if (CollectionUtils.isEmpty(procurementSkuList)) {
//                if (isEdit) {
//                    for (InOrderDetailBO detail : detailBOList) {
//                        detail.setGoodsSurplusNumber(detail.getPlanQuantity());
//                    }
//                }
                return detailBOList;
            }
            Map<String, InOrderDetailBO> collect = detailBOList.stream().collect(Collectors.toMap(InOrderDetailBO::getGoodsCode, Function.identity(), (v1, v2) -> v1));
            for (InOrderDetailBO detailBO : procurementSkuList) {
                InOrderDetailBO bo = collect.get(detailBO.getGoodsCode());
                if (Objects.nonNull(bo)) {
                    detailBO.setInOrderNo(bo.getInOrderNo());
                    detailBO.setPlanQuantity(bo.getPlanQuantity());
                    detailBO.setActualQuantity(bo.getActualQuantity());
                    detailBO.setGoodsSurplusNumber(detailBO.getGoodsSurplusNumber() + bo.getPlanQuantity());
                    detailBO.setRemark(bo.getRemark());
                }
            }
        } catch (Exception e) {
            log.error("[InOrderManager-buildOrderDetail]==========组装数据异常==========ex:", e);
        }
        return procurementSkuList;
    }

    /**
     * 菜鸟判断库存分组下 条码,库存编码,实体仓 唯一
     *
     * @param orderBO
     */
    private void buildDetailBarcodeAndCheck(InOrderBO orderBO) {
        try {
            if (!Objects.equals(SYSTEM_CNZXC, orderBO.getSystemCode())) {
                return;
            }

            List<InOrderDetailBO> detailBOArrayList = orderBO.getDetailBOList();
            for (InOrderDetailBO orderDetailBO : detailBOArrayList) {
                if (StringUtils.isBlank(orderDetailBO.getBarcode())) {
                    throw new BusinessException(orderDetailBO.getSku() + ":条码不能为空!");
                }

                InOrderDetailBarcodeSearch search = new InOrderDetailBarcodeSearch();
                search.setChannelCode(orderBO.getChannelCode());
                search.setWarehouseCode(orderBO.getWarehouseCode());
                search.setBarcode(orderDetailBO.getBarcode());

                InOrderDetailBarcode inOrderDetailBarcode = inOrderDetailBarcodeService.selectBySearch(search);
                if (Objects.nonNull(inOrderDetailBarcode)) {
                    if (Objects.equals(orderDetailBO.getSku(), inOrderDetailBarcode.getSku())) {
                        return;
                    } else {
                        throw new BusinessException("仓库相同,条码必须唯一!");
                    }
                }

                InOrderDetailBarcode barcode = new InOrderDetailBarcode();
                barcode.setChannelCode(orderBO.getChannelCode());
                barcode.setWarehouseCode(orderBO.getWarehouseCode());
                barcode.setOwnerCode(orderBO.getOwnerCode());
                barcode.setBarcode(orderDetailBO.getBarcode());
                barcode.setSku(orderDetailBO.getSku());
                inOrderDetailBarcodeService.insert(barcode);
            }
        } catch (DuplicateKeyException e) {
            throw new BusinessException("仓库相同,条码必须唯一!");
        }
    }

    /**
     * 保存采购商品计划数量
     */
    public void saveProcurementGoodsPlanQty(InOrderBO orderBO, Boolean isModify) {
        if (!isModify) {
            remoteProcurementFacade.saveProcurementGoodsPlanQty(orderBO);
        } else {
            List<InOrderDetail> inOrderDetails = inOrderDetailService.selectListByOrderNo(orderBO.getInOrderNo());
            //修改,需要判断,修改后的数量和原始数量
            Map<String, InOrderDetail> collect = inOrderDetails.stream().collect(Collectors.toMap(InOrderDetail::getGoodsCode, inOrderDetail -> inOrderDetail, (v1, v2) -> v1));

            List<InOrderDetailBO> addList = new ArrayList<>();
            List<InOrderDetailBO> rollBackList = new ArrayList<>();

            List<InOrderDetailBO> detailBOList = orderBO.getDetailBOList();
            detailBOList.forEach(detailBO -> {
                InOrderDetail inOrderDetail = collect.get(detailBO.getGoodsCode());
                if (Objects.nonNull(inOrderDetail)) {
                    //修改前的计划数量
                    Integer planQuantity = inOrderDetail.getPlanQuantity();
                    //修改后的计划数量
                    Integer boPlanQuantity = detailBO.getPlanQuantity();
                    //大于0,需要新增
                    if ((boPlanQuantity - planQuantity) > 0) {
                        InOrderDetailBO newBO = BeanUtils.copyProperties(detailBO, InOrderDetailBO.class);
                        newBO.setPlanQuantity(boPlanQuantity - planQuantity);
                        addList.add(newBO);
                    }
                    //小于0,需要回滚
                    if ((boPlanQuantity - planQuantity) < 0) {
                        InOrderDetailBO newBO = BeanUtils.copyProperties(detailBO, InOrderDetailBO.class);
                        newBO.setPlanQuantity(planQuantity - boPlanQuantity);
                        rollBackList.add(newBO);
                    }

                    //移除存在的
                    inOrderDetails.remove(inOrderDetail);
                } else {
                    addList.add(detailBO);
                }
            });

            InOrderBO bo = BeanUtils.copyProperties(orderBO, InOrderBO.class);

            //增加多的
            if (addList.size() > 0) {
                bo.setDetailBOList(addList);
                remoteProcurementFacade.saveProcurementGoodsPlanQty(bo);
            }
            //回滚减少的
            if (rollBackList.size() > 0) {
                bo.setDetailBOList(rollBackList);
                remoteProcurementFacade.rollBackProcurementGoodsPlanQty(bo);
            }
            //回滚删除的sku
            if (inOrderDetails.size() > 0) {
                List<InOrderDetailBO> inOrderDetailBOList = BeanUtils.copyProperties(inOrderDetails, InOrderDetailBO.class);
                bo.setDetailBOList(inOrderDetailBOList);
                remoteProcurementFacade.rollBackProcurementGoodsPlanQty(bo);
            }
        }
    }

    /**
     * 新增或修改
     *
     * @param orderBO
     */
    @ShardingTransactionType(value = TransactionType.BASE)
    private void saveOrUpdateItem(InOrderBO orderBO, Boolean isModify) {
        //删除原有的
        inOrderDetailService.deleteByInOrderNo(orderBO.getInOrderNo());
        //新增sku详情
        List<InOrderDetailBO> inOrderDetailBOList = orderBO.getDetailBOList();

        List<InOrderDetail> orderDetailList = BeanUtils.copyProperties(inOrderDetailBOList, InOrderDetail.class);
        inOrderDetailService.insertList(orderDetailList);

        //新增或修改收发货地址
        ReceiveSendInfoBO receiveSendInfoBO = orderBO.getReceiveSendInfoBO();
        receiveSendInfoBO.setOrderNo(orderBO.getInOrderNo());
        remoteWarehouseFacade.buildReceiveInfo(receiveSendInfoBO, orderBO.getLogicWarehouseCode());
        ReceiveSendInfo receiveSendInfo = BeanUtils.copyProperties(receiveSendInfoBO, ReceiveSendInfo.class);
        receiveSendInfoService.saveOrUpdateByOrderNo(receiveSendInfo, isModify);
    }

    /**
     * 提交审核
     *
     * @param inOrderNo
     * @param userId
     * @return
     */
    public boolean optSubmitAudit(String inOrderNo, Long userId) {
        InOrder order = inOrderService.selectByOrderNo(inOrderNo, userId);
        if (Objects.equals(OrderPriceStatus.TO_EDIT, order.getPriceStatus())) {
            throw new BusinessException(inOrderNo + ":订单明细未维护价格数据,禁止审核!");
        }
        Integer status;
        //开启审核
        if ("1".equals(orderNacosConfig.getAresWebApprovalIn())) {
            String orderNo = order.getInOrderNo();
            if (StringUtils.isNotBlank(order.getExtensionJson())) {
                ExtensionJsonBO extensionJsonBO = JSON.parseObject(order.getExtensionJson(), ExtensionJsonBO.class);
                if (Objects.nonNull(extensionJsonBO.getRepealVersion()) && !Objects.equals(0, extensionJsonBO.getRepealVersion())) {
                    orderNo = orderNo + "_" + extensionJsonBO.getRepealVersion();
                }
            }
            //提交审核
            Pair<Boolean, Integer> submitAudit = remoteFlowFacade.optSubmitAudit(userId, SimpleUserHelper.getRealUserId(), orderNo, Boolean.TRUE);
            if (!submitAudit.getLeft()) {
                throw new BusinessException("提交审核失败!");
            }
            status = submitAudit.getRight();
        } else {
            status = ApprovalStatus.SP_SUCCESS.getValue();
        }
        InOrder inOrder = new InOrder();
        if (Objects.equals(ApprovalStatus.SP_SUCCESS.getValue(), status)) {
            inOrder.setStatus(InOrderStatus.RK_XR_IS.getValue());
            //审核成功,判断仓库是否自动推送
            LogicWarehouseResult logicWarehouseResult = remoteWarehouseFacade.getLogicWarehouseByCode(order.getLogicWarehouseCode());
            if (Objects.equals(AutoPushType.AUTOPUSH, logicWarehouseResult.getIsAutoPush())) {
                inOrder.setIsPush(PushType.AUTO_PUSH.getValue());
                inOrder.setStatus(InOrderStatus.RK_WAIT_PUSH.getValue());
            }
        } else if (Objects.equals(ApprovalStatus.SP_IS.getValue(), status)) {
            inOrder.setStatus(InOrderStatus.RK_WAIT.getValue());
        }
        inOrder.setInOrderNo(inOrderNo);
        inOrder.setUserId(userId);
        inOrder.setApprovalStatus(status);
        boolean update = inOrderService.updateByOrderNo(inOrder);
        if (!update) {
            log.error("[InOrderManager-optSubmitAudit]===========================更新审核状态失败===========================:" + inOrder);
            throw new BusinessException("更新审核状态失败!");
        }

        //是否自动推送
        autoPushInOrder(BeanUtils.copyProperties(inOrder, InOrderBO.class), 1);

        return true;
    }

    /**
     * 自动模拟回传
     *
     * @param inOrderBO
     */
    public void doMockPush(InOrderBO inOrderBO) {
        if (Objects.equals(LogicWarehouseType.LOGIC.getValue(), inOrderBO.getLogicWarehouseType())
                || Objects.equals(PushType.AUTO_NO_PUSH.getValue(), inOrderBO.getIsPush())
                || Objects.equals(PushType.HAND_NO_PUSH.getValue(), inOrderBO.getIsPush())) {
            //不推送,模拟回传
            boolean mockInOrderBack = mockBackFacade.mockInOrderBack(inOrderBO.getInOrderNo(), 1, inOrderBO.getUserId(), Boolean.TRUE);
            if (!mockInOrderBack) {
                throw new BusinessException("模拟回传失败,请稍后在试!");
            }
        }
        try {
            //推送后，删除模拟回传的理货报告
            remoteTallyReportFacade.deleteByOrderNo(inOrderBO.getInOrderNo());
        } catch (Exception e) {
            log.error("[InOrderManager-pushOutOrder]==============删除模拟回传的理货报告失败==============inOrderNo:" + inOrderBO.getInOrderNo());
        }
    }

    /**
     * 划拨入库后,更改出库单状态
     *
     * @param orderBO
     */
    public void buildOutOrderStatus(InOrderBO orderBO) {
        try {
            if (Objects.equals(TransferOrderType.CN, orderBO.getTransferOrderType())) {
                OutOrder outOrder = outOrderService.selectByOrderNo(orderBO.getOutOrderNo(), orderBO.getUserId());
                if (Objects.equals(OutOrderStatus.CK_IS_TRANSFER.getValue(), outOrder.getStatus())) {
                    OutOrder order = new OutOrder();
                    order.setOutOrderNo(outOrder.getOutOrderNo());
                    order.setUserId(outOrder.getUserId());
                    order.setStatus(orderBO.getStatus());//状态同步入库单的
                    outOrderService.updateByOrderNo(order);
                    //核扣库存
                    outOrderDeductionsInventory(outOrder.getOutOrderNo());
                }
            }
        } catch (Exception e) {
            log.error("[InOrderManager-buildOutOrderStatus]=======================orderNo:{}, ex:{}", orderBO.getOutOrderNo(), e);
            throw new BusinessException("划拨入库回传状态更新失败!");
        }
    }

    /**
     * 出库单核扣库存(菜鸟划拨)
     *
     * @param orderNo
     */
    public void outOrderDeductionsInventory(String orderNo) {
        try {
            InventoryProcessMessage orderMessage = new InventoryProcessMessage();
            orderMessage.setBusinessNo(orderNo);
            orderMessage.setBusinessBillType(BusinessBillType.BILL_OUT_STOCK);
            orderMessage.setUpdateType(InventoryUpdateType.OUT_STOCK_HK);

            Map keysMap = Maps.newHashMap();
            keysMap.put("KEYS", orderNo);
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message = SpringMessage.createMessage(orderMessage, messageHeaders);
            springRocketMQProducer.syncSend(orderNacosConfig.getLockInventoryTopicV2(), message, 3000, 1);
        } catch (Exception e) {
            log.error("[InOrderManager-outOrderDeductionsInventory]==================出库单核扣库存异常==================orderNo:{}, ex:", orderNo, e);
        }
    }

    /**
     * 更新货品信息
     *
     * @param orderBO
     */
    public void updateInOrderDetailList(InOrderBO orderBO) {
        List<InOrderDetail> oldDetailList = inOrderDetailService.selectListByOrderNo(orderBO.getInOrderNo());
        buildSkuToGoodsCode(orderBO, oldDetailList);
        //校验理货报告
//        Boolean is4PL = Boolean.FALSE;
//        String extensionJson = orderBO.getExtensionJson();
//        if (StringUtils.isNotBlank(extensionJson)) {
//            ExtensionJsonBO extensionJsonBO = JSON.parseObject(extensionJson, ExtensionJsonBO.class);
//            is4PL = Objects.nonNull(extensionJsonBO) && Objects.equals(1, extensionJsonBO.getFourPl());
//        }
//        if ((Objects.equals(1, orderBO.getIsCheckTally()) || is4PL || buildConditions(orderBO)) && !noTally.contains(orderBO.getInOrderNo())) {
//            checkTallyReportDetail(orderBO);
//        }
        //查询老的货品信息
        Map<String, InOrderDetail> oldDetailMap = oldDetailList.stream().filter(detail -> Objects.equals(TallyReportMultiType.NORMAL, detail.getMultiType())).collect(Collectors.toMap(inOrderDetail -> {
            String key = inOrderDetail.getGoodsCode();
            if (Objects.equals(InOrderType.FX_THRK.getValue(), orderBO.getType())) {
                key = key + inOrderDetail.getInternalBatchCode();
            }
            if (StringUtils.isNotBlank(inOrderDetail.getLineNo())) {
                key = key + inOrderDetail.getLineNo();
            }
            return key;
        }, inOrderDetail -> inOrderDetail, (v1, v2) -> v1));

        //多品
        Map<String, InOrderDetailBO> moreMap = new HashMap<>();
        //回传的入库货品详情
        List<InOrderDetailBO> newDetailList = orderBO.getDetailBOList();
        for (InOrderDetailBO orderDetail : newDetailList) {
            orderDetail.setInOrderNo(orderBO.getInOrderNo());
            if (Objects.equals(TallyReportMultiType.MORE, orderDetail.getMultiType())) {
                String moreKey = orderDetail.getGoodsCode();
                if (StringUtils.isNotBlank(orderDetail.getInternalBatchCode())) {
                    moreKey = moreKey + orderDetail.getInternalBatchCode();
                }
                if (StringUtils.isNotBlank(orderDetail.getLineNo())) {
                    moreKey = moreKey + orderDetail.getLineNo();
                }
                InOrderDetailBO inOrderDetailBO = moreMap.get(moreKey);
                if (Objects.isNull(inOrderDetailBO)) {
                    orderDetail.setBillCurrency("RMB");
                    moreMap.put(moreKey, orderDetail);
                } else {
                    inOrderDetailBO.setActualQuantity(inOrderDetailBO.getActualQuantity() + orderDetail.getActualQuantity());
                }
                continue;
            }
            String key = orderDetail.getGoodsCode();
            //分销退货入库,需要带批次
            if (Objects.equals(InOrderType.FX_THRK.getValue(), orderBO.getType())) {
                key = key + orderDetail.getInternalBatchCode();
            }
            if (StringUtils.isNotBlank(orderDetail.getLineNo())) {
                key = key + orderDetail.getLineNo();
            }
            InOrderDetail detail = oldDetailMap.get(key);
            if (Objects.nonNull(detail)) {
          /*      if (Objects.equals(origSystem, orderBO.getBackOrigSystem())
                        && Objects.equals(GoodsBatchManagement.YES.getValue(), detail.getBatchManagement())
                        && Objects.equals(2, orderBO.getLogicWarehouseType())
                        && (StringUtils.isBlank(orderDetail.getInternalBatchCode()) || Objects.isNull(orderDetail.getExpireDate()) || Objects.isNull(orderDetail.getProductionDate()))) {
                    log.error("[InOrderManager-updateInOrderDetailList]===========================货品批次管理开启时,未回传效期信息===========================:" + JSON.toJSONString(orderDetail));
                    throw new BusinessException(orderDetail.getSku() + ":货品效期管理开启时,未回传批次或效期!");
                }*/
                int quantity = Objects.isNull(orderBO.getActualQuantity()) ? 0 : orderBO.getActualQuantity();
                orderBO.setActualQuantity(quantity + orderDetail.getActualQuantity());

                //异常超件品
                if (Objects.equals(1, orderDetail.getExceeded())) {
                    detail.setExceededQuality(detail.getExceededQuality() + orderDetail.getActualQuantity());
                }
                detail.setActualQuantity(detail.getActualQuantity() + orderDetail.getActualQuantity());
                if (Objects.equals(InventoryType.QUALITY.getValue(), orderDetail.getInventoryType())) {
                    detail.setZpQuality(detail.getZpQuality() + orderDetail.getActualQuantity());
                } else {
                    detail.setCpQuality(detail.getCpQuality() + orderDetail.getActualQuantity());
                }
                if (Objects.equals(TradeType.BONDED.getValue(), orderBO.getTradeType()) && (detail.getActualQuantity() - detail.getExceededQuality()) > detail.getPlanQuantity()) {
                    log.error("[InOrderManager-updateInOrderDetailList]===========================实际入库数量大于计划数量===========================detail:" + detail);
                    throw new BusinessException(detail.getSku() + ":实际入库数量大于计划数量!");
                }
                orderDetail.setBillCurrency(detail.getBillCurrency());
                orderDetail.setUnitPrice(detail.getUnitPrice());
                //设置计划数量
                orderDetail.setPlanQuantity(orderDetail.getActualQuantity());
            } else {
                log.error("[InOrderManager-updateInOrderDetailList]===========================未匹配到老的货品信息===========================:" + orderDetail);
                throw new BusinessException(orderDetail.getSku() + ":未匹配到入库货品信息!");
            }
        }

        List<InOrderDetail> values = new ArrayList<>(oldDetailMap.values());
        for (InOrderDetail orderDetail : values) {
            orderDetail.setBackPlanQuantityStatus(null);//清除回传状态,防止被更新
            //如果已经是入库最终状态,校验数量,扣除异常超件数量
            if (Objects.equals(InOrderStatus.RK_ALL.getValue(), orderBO.getStatus())) {
                if (Objects.equals(TradeType.BONDED.getValue(), orderBO.getTradeType()) && (orderDetail.getActualQuantity() - orderDetail.getExceededQuality()) > orderDetail.getPlanQuantity()) {
                    log.error("[InOrderManager-updateInOrderDetailList]===========================最终实际入库数量大于计划数量===========================orderDetail:" + orderDetail);
                    throw new BusinessException("最终实际入库数量大于计划数量!");
                }
            }
        }

        //更新货品信息
        boolean updateList = inOrderDetailService.updateListByOrderNo(values);
        if (!updateList) {
            log.error("[InOrderManager-updateInOrderDetailList]===========================更新入库单货品操作失败===========================:" + JSON.toJSONString(values));
            throw new BusinessException("更新入库单货品操作失败!");
        }

        //新增入库回传的批次信息
        List<InOrderDetailBatch> inOrderDetailBatches = new ArrayList<>();
        Integer i = 0;
        for (InOrderDetailBO inOrderDetailBO : newDetailList) {
            InOrderDetailBatch detailBatch = BeanUtils.copyProperties(inOrderDetailBO, InOrderDetailBatch.class);
            detailBatch.setBackFlag(orderBO.getBackFlag());
            detailBatch.setIsLack(0);
            if (Objects.equals(0, orderBO.getBackStatus())) { // 0不需要回传上游,才设置批次回传状态,否则默认 1
                detailBatch.setBackStatus(orderBO.getBackStatus());
            }
            detailBatch.setInventoryNo(buildInventoryNo(i++, orderBO.getBackFlag()));
            detailBatch.setIsConfirm(orderBO.getIsConfirm());
            if (Objects.equals("LESS", inOrderDetailBO.getBatchCode())) {
                detailBatch.setInventoryStatus(0);
            }
            inOrderDetailBatches.add(detailBatch);
        }
        //多品
        List<InOrderDetailBO> moreList = new ArrayList<>(moreMap.values());
        buildMoreGoods(moreList);

        boolean insertList = inOrderDetailBatchService.insertList(inOrderDetailBatches);
        if (!insertList) {
            log.error("[InOrderManager-updateInOrderDetailList]===========================新增入库单货品批次信息失败===========================:" + values);
            throw new BusinessException("新增入库单货品批次信息失败!");
        }
    }

//    /**
//     * 清关条件
//     *
//     * @param orderBO
//     * @return
//     */
//    private Boolean buildConditions(InOrderBO orderBO){
//        //保税+开启清关
//        if (Objects.equals(TradeType.BONDED.getValue(), orderBO.getTradeType()) && Objects.equals(OnlineCustomsStatus.OPEN, orderBO.getCustomsStatus())) {
//            // 单据类型支持:采购入库(非自建),调拨入库,其他入库
//            if ((Objects.equals(InOrderType.CG_RK.getValue(), orderBO.getType()) && StringUtils.isNotBlank(orderBO.getOrigSystem()))
//                    || Objects.equals(InOrderType.DB_RK.getValue(), orderBO.getType())
//                    || Objects.equals(InOrderType.QT_RK.getValue(), orderBO.getType())) {
//                return Boolean.TRUE;
//            }
//        }
//        return Boolean.FALSE;
//    }

    /**
     * 清关条件
     *
     * @param orderBO
     * @return
     */
    public Boolean buildConditions(InOrderBO orderBO){
        //淘天不走关仓
        String businessValue = orderBO.getBusinessValue();
        if (StringUtils.isNotBlank(businessValue) && businessValue.contains(SYSTEM_TAG_TAOTIAN)) {
            return Boolean.FALSE;
        }
        //非保入区
        if (Objects.equals(1, orderBO.getNonBonded())) {
            return Boolean.TRUE;
        }
        //保税+开启清关
        if (Objects.equals(TradeType.BONDED.getValue(), orderBO.getTradeType()) && Objects.equals(OnlineCustomsStatus.OPEN, orderBO.getCustomsStatus())) {
            // 单据类型支持:采购入库,调拨入库,其他入库,代采监管入库,赎回入库
            if (Objects.equals(InOrderType.CG_RK.getValue(), orderBO.getType())
                    || Objects.equals(InOrderType.DB_RK.getValue(), orderBO.getType())
                    || Objects.equals(InOrderType.DC_RK.getValue(), orderBO.getType())
                    || Objects.equals(InOrderType.SH_RK.getValue(), orderBO.getType())
                    || Objects.equals(InOrderType.QT_RK.getValue(), orderBO.getType())) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 多品处理,存在多次回传的情况
     *
     * @param moreList
     */
    private void buildMoreGoods(List<InOrderDetailBO> moreList) {
        if (CollectionUtil.isNotEmpty(moreList)) {
            Set<String> collect = moreList.stream().map(d -> d.getGoodsCode()).collect(Collectors.toSet());
            List<InOrderDetail> addMore = new ArrayList<>();
            //合并相同的
            InOrderDetailSearch search = new InOrderDetailSearch();
            search.setInOrderNo(moreList.get(0).getInOrderNo());
            search.setGoodsCodeSet(collect);
            List<InOrderDetail> orderDetails = inOrderDetailService.selectListBySearch(search);
            //不为空,说明多次回传了,已经存在了超品数据,进行数量相加
            if (CollectionUtil.isNotEmpty(orderDetails)) {
                Map<String, InOrderDetail> oldMap = orderDetails.stream().collect(Collectors.toMap(v1 -> {
                    String key = v1.getGoodsCode();
                    if (StringUtils.isNotBlank(v1.getInternalBatchCode())) {
                        key = key + v1.getInternalBatchCode();
                    }
                    if (StringUtils.isNotBlank(v1.getLineNo())) {
                        key = key + v1.getLineNo();
                    }
                    return key;
                }, o -> o, (oldValue, newValue) -> oldValue));
                for (InOrderDetailBO detailBO : moreList) {
                    String key = detailBO.getGoodsCode();
                    if (StringUtils.isNotBlank(detailBO.getInternalBatchCode())) {
                        key = key + detailBO.getInternalBatchCode();
                    }
                    if (StringUtils.isNotBlank(detailBO.getLineNo())) {
                        key = key + detailBO.getLineNo();
                    }
                    InOrderDetail detail = oldMap.get(key);
                    if (Objects.nonNull(detail)) {
                        detail.setActualQuantity(detail.getActualQuantity() + detailBO.getActualQuantity());
                    } else {
                        addMore.add(BeanUtils.copyProperties(detailBO, InOrderDetail.class));
                    }
                }
                if (CollectionUtil.isNotEmpty(addMore)) {
                    inOrderDetailService.insertList(addMore);
                }
                List<InOrderDetail> updateMore = new ArrayList<>(oldMap.values());
                inOrderDetailService.updateListByOrderNo(updateMore);
            } else {
                inOrderDetailService.insertList(BeanUtils.copyProperties(moreList, InOrderDetail.class));
            }
        }
    }

    private String buildInventoryNo(Integer count, String backFlag) {
        try {
            int i = count / orderNacosConfig.getInventoryNo();
            return i + backFlag;
        } catch (Exception e) {
        }
        return "0" + backFlag;
    }

    /**
     * sku转换
     *
     * @param inOrderBO
     * @param oldDetailList
     */
    private void buildSkuToGoodsCode(InOrderBO inOrderBO, List<InOrderDetail> oldDetailList) {
        Map<String, InOrderDetail> skuMap = new HashMap<>();
        for (InOrderDetail detail : oldDetailList) {
            String key = detail.getSku();
            if (Objects.equals(SYSTEM_CNZXC, inOrderBO.getBackOrigSystem())) {
                key = detail.getExternalSku();
            }
            skuMap.put(key, detail);
        }
        List<InOrderDetailBO> newDetailList = inOrderBO.getDetailBOList();
        for (InOrderDetailBO orderDetailBO : newDetailList) {
            if (Objects.equals(TallyReportMultiType.MORE, orderDetailBO.getMultiType())) {
                GoodsResult result = remoteGoodsFacade.getGoodsResultByParam(inOrderBO.getUserId(), orderDetailBO.getSku());
                orderDetailBO.setGoodsCode(result.getGoodsCode());
                orderDetailBO.setBarcode(result.getBarcode());
                if (Objects.equals(SYSTEM_CNZXC, inOrderBO.getBackOrigSystem())) {
                    orderDetailBO.setExternalSku(result.getExternalSku());
                } else {
                    orderDetailBO.setExternalSku(result.getCargoCode());
                }
                continue;
            }
            String key = orderDetailBO.getSku();
            if (Objects.equals(SYSTEM_CNZXC, inOrderBO.getBackOrigSystem())) {
                key = orderDetailBO.getExternalSku();
            }
            InOrderDetail inOrderDetail = skuMap.get(key);
            if (Objects.isNull(inOrderDetail)) {
                throw new BusinessException(key + ":未匹配到入库单货品明细!");
            }
            orderDetailBO.setGoodsCode(inOrderDetail.getGoodsCode());
            orderDetailBO.setSku(inOrderDetail.getSku());
            orderDetailBO.setExternalSku(inOrderDetail.getExternalSku());
        }
    }

    /**
     * 校验理货报告
     *
     * @param orderBO
     */
    private void checkTallyReportDetail(InOrderBO orderBO) {
        log.info("[InOrderManager-checkTallyReportDetail]=============================理货报告校验============================{}", JSON.toJSONString(orderBO));
        //淘天不走理货报告
        if (StringUtils.isNotBlank(orderBO.getBusinessValue()) && orderBO.getBusinessValue().contains(SYSTEM_TAG_TAOTIAN)) {
            return;
        }
        TallyReportResult reportResult = remoteTallyReportFacade.getTallyReportByOrderNo(orderBO.getInOrderNo());
        List<TallyReportDetailResult> tallyReportDetailList = reportResult.getTallyReportDetailList();

        Map<String, TallyReportDetailBatchResult> tallyReportDetailMap = new HashMap<>();
        for (TallyReportDetailResult reportDetailResult : tallyReportDetailList) {
            List<TallyReportDetailBatchResult> batchLists = reportDetailResult.getDetailBatchLists();
            if (CollectionUtil.isEmpty(batchLists) || batchLists.size() == 0) {
                continue;
            }
            for (TallyReportDetailBatchResult reportDetailBatchResult : batchLists) {
                reportDetailBatchResult.setShelfLife(reportDetailResult.getShelfLife());
                reportDetailBatchResult.setBatchManagement(reportDetailResult.getBatchManagement());

                String key = reportDetailBatchResult.getGoodsCode() + "_" + reportDetailBatchResult.getInventoryType().getValue() + "_" + reportDetailBatchResult.getExceeded();
                if (Objects.nonNull(reportDetailBatchResult.getProductionDate()) && !Objects.equals(0L, reportDetailBatchResult.getProductionDate())) {
                    key = key + "_" + DatePattern.PURE_DATE_FORMAT.format(new Date(reportDetailBatchResult.getProductionDate()));
                }
                if (Objects.nonNull(reportDetailBatchResult.getExpireDate()) && !Objects.equals(0L, reportDetailBatchResult.getExpireDate())) {
                    key = key + "_" + DatePattern.PURE_DATE_FORMAT.format(new Date(reportDetailBatchResult.getExpireDate()));
                }
                if (StringUtils.isNotBlank(reportDetailBatchResult.getInternalBatchCode())) {
                    key = key + "bathCode" + reportDetailBatchResult.getInternalBatchCode();
                }

                TallyReportDetailBatchResult detailResult = tallyReportDetailMap.get(key);
                if (Objects.isNull(detailResult)) {
                    if (Objects.isNull(reportDetailBatchResult.getTallyNum())) {
                        reportDetailBatchResult.setTallyNum(0);
                    }
                    tallyReportDetailMap.put(key, reportDetailBatchResult);
                } else {
                    int tallyNum = Objects.isNull(reportDetailBatchResult.getTallyNum()) ? 0 : reportDetailBatchResult.getTallyNum();
                    detailResult.setTallyNum(detailResult.getTallyNum() + tallyNum);
                }
            }
        }

        List<InOrderDetailBO> detailBOList = orderBO.getDetailBOList();
        Map<String, InOrderDetailBO> orderDetailBOMap = new HashMap<>();
        for (InOrderDetailBO orderDetailBO : detailBOList) {
            String key = orderDetailBO.getGoodsCode() + "_" + orderDetailBO.getInventoryType() + "_" + orderDetailBO.getExceeded();
            if (Objects.nonNull(orderDetailBO.getProductionDate()) && !Objects.equals(0L, orderDetailBO.getProductionDate())) {
                key = key + "_" + DatePattern.PURE_DATE_FORMAT.format(orderDetailBO.getProductionDate());
            }
            if (Objects.nonNull(orderDetailBO.getExpireDate()) && !Objects.equals(0L, orderDetailBO.getExpireDate())) {
                key = key + "_" + DatePattern.PURE_DATE_FORMAT.format(orderDetailBO.getExpireDate());
            }
            String batchCode = "";
            if (StringUtils.isNotBlank(orderDetailBO.getInternalBatchCode())) {
                batchCode = "bathCode" + orderDetailBO.getInternalBatchCode();
                key = key + batchCode;
            }
            //批次查询,为空就去掉批次
            Pair<String, TallyReportDetailBatchResult> resultPair = getTallyReportDetailResult(tallyReportDetailMap, batchCode, key);
            key = resultPair.getLeft();

            InOrderDetailBO detailBO = orderDetailBOMap.get(key);
            if (Objects.isNull(detailBO)) {
                //拷贝新的,不影响流程
                InOrderDetailBO inOrderDetailBO = BeanUtils.copyProperties(orderDetailBO, InOrderDetailBO.class);
                if (Objects.equals(InventoryType.QUALITY.getValue(), orderDetailBO.getInventoryType())) {
                    inOrderDetailBO.setCpQuality(0);
                    inOrderDetailBO.setZpQuality(orderDetailBO.getActualQuantity());
                } else {
                    inOrderDetailBO.setCpQuality(orderDetailBO.getActualQuantity());
                    inOrderDetailBO.setZpQuality(0);
                }
                orderDetailBOMap.put(key, inOrderDetailBO);
            } else {
                detailBO.setActualQuantity(detailBO.getActualQuantity() + orderDetailBO.getActualQuantity());
                if (Objects.equals(InventoryType.QUALITY.getValue(), orderDetailBO.getInventoryType())) {
                    detailBO.setZpQuality(detailBO.getZpQuality() + orderDetailBO.getActualQuantity());
                } else {
                    detailBO.setCpQuality(detailBO.getCpQuality() + orderDetailBO.getActualQuantity());
                }
            }
        }

        List<InOrderDetailBO> values = new ArrayList<>(orderDetailBOMap.values());
        for (InOrderDetailBO detailBO : values) {
            String key = detailBO.getGoodsCode() + "_" + detailBO.getInventoryType() + "_" + detailBO.getExceeded();
            if (Objects.nonNull(detailBO.getProductionDate()) && !Objects.equals(0L, detailBO.getProductionDate())) {
                key = key + "_" + DatePattern.PURE_DATE_FORMAT.format(detailBO.getProductionDate());
            }
            if (Objects.nonNull(detailBO.getExpireDate()) && !Objects.equals(0L, detailBO.getExpireDate())) {
                key = key + "_" + DatePattern.PURE_DATE_FORMAT.format(detailBO.getExpireDate());
            }
            String batchCode = "";
            if (StringUtils.isNotBlank(detailBO.getInternalBatchCode())) {
                batchCode = "bathCode" + detailBO.getInternalBatchCode();
                key = key + batchCode;
            }
            Pair<String, TallyReportDetailBatchResult> resultPair = getTallyReportDetailResult(tallyReportDetailMap, batchCode, key);
            TallyReportDetailBatchResult reportDetailBatchResult = resultPair.getRight();
            if (Objects.equals(InventoryType.DEFECTIVE.getValue(), detailBO.getInventoryType())) {
                if (!Objects.equals(detailBO.getCpQuality(), reportDetailBatchResult.getTallyNum())) {
                    log.error("[InOrderManager-checkTallyReportDetail]===========================回传货品次品数量和理货报告不匹配===========================回传次品数量:{}, 理货次品数量:{}", detailBO.getCpQuality(), reportDetailBatchResult.getTallyNum());
                    throw new BusinessException("回传次品数量:" + detailBO.getCpQuality() + ", 理货次品数量:" + reportDetailBatchResult.getTallyNum() + ",回传货品次品数量和理货报告不匹配!");
                }
            } else {
                if (!Objects.equals(detailBO.getZpQuality(), reportDetailBatchResult.getTallyNum())) {
                    log.error("[InOrderManager-checkTallyReportDetail]===========================回传货品正品数量和理货报告不匹配===========================回传正品数量:{}, 理货正品数量:{}", detailBO.getZpQuality(), reportDetailBatchResult.getTallyNum());
                    throw new BusinessException("回传正品数量:" + detailBO.getZpQuality() + ", 理货正品数量:" + reportDetailBatchResult.getTallyNum() + "回传货品正品数量和理货报告不匹配!");
                }
            }

            //如果开启批次管理,需要校验效期天数
            if (Objects.equals(GoodsBatchManagement.YES.getValue(), reportDetailBatchResult.getBatchManagement().getValue())) {
                if (Objects.isNull(reportDetailBatchResult.getShelfLife())) {
                    log.error("[InOrderManager-checkTallyReportDetail]===========================保质期天数为空===========================");
                    throw new BusinessException(detailBO.getSku() + ":保质期天数为空!");
                }
                if (Objects.isNull(detailBO.getProductionDate()) || Objects.isNull(reportDetailBatchResult.getProductionDate())) {
                    log.error("[InOrderManager-checkTallyReportDetail]===========================生产日期为空===========================");
                    throw new BusinessException(detailBO.getSku() + ":生产日期为空!");
                }
                if (Objects.isNull(detailBO.getExpireDate()) || Objects.isNull(reportDetailBatchResult.getExpireDate())) {
                    log.error("[InOrderManager-checkTallyReportDetail]===========================失效日期为空===========================");
                    throw new BusinessException(detailBO.getSku() + ":失效日期为空!");
                }
                DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd");
                DateTime start = formatter.parseDateTime(DateUtil.formatDate(detailBO.getProductionDate()));
                DateTime end = formatter.parseDateTime(DateUtil.formatDate(detailBO.getExpireDate()));
                Days days = Days.daysBetween(start, end);
                if (!Objects.equals(days.getDays(), reportDetailBatchResult.getShelfLife())) {
                    log.error("[InOrderManager-checkTallyReportDetail]===========================保质期天数和理货报告不匹配===========================回传效期天数:{}, 理货报告效期天数:{}", days.getDays(), reportDetailBatchResult.getShelfLife());
//                    throw new BusinessException(detailBO.getSku() + ",回传效期天数:" + days.getDays() + ", 理货报告效期天数:" + reportDetailBatchResult.getShelfLife() + ",保质期天数和理货报告不匹配!");
                }
            }
        }
        BackTallyReportBO backTallyReportBO = new BackTallyReportBO();
        backTallyReportBO.setId(reportResult.getId());
        backTallyReportBO.setDownstreamNo(orderBO.getDownstreamNo());
        backTallyReportBO.setDetailBOList(BeanUtils.copyProperties(detailBOList, InOrderDetailBO.class));
        orderBO.setBackTallyReportBO(backTallyReportBO);
        //回传理货报告状态和明细, 提到外面去了
//        remoteTallyReportFacade.callback(reportResult.getId(), orderBO.getDownstreamNo(), values);
    }

    @ShardingTransactionType(value = TransactionType.BASE)
    private Pair<String, TallyReportDetailBatchResult> getTallyReportDetailResult(Map<String, TallyReportDetailBatchResult> tallyReportDetailMap, String batchCode, String key) {
        //批次查询,为空就去掉批次
        TallyReportDetailBatchResult detailResult = tallyReportDetailMap.get(key);
        if (Objects.isNull(detailResult)) {
            if (StringUtils.isNotBlank(batchCode)) {
                key = key.replace(batchCode, "");
                detailResult = tallyReportDetailMap.get(key);
                if (Objects.isNull(detailResult)) {
                    log.error("[InOrderManager-checkTallyReportDetail]===========================回传货品和理货报告不匹配===========================key:" + key);
                    throw new BusinessException(key + ":回传货品和理货报告不匹配,请检查你的效期或者批次是否正确!");
                }
            } else {
                log.error("[InOrderManager-checkTallyReportDetail]===========================回传货品和理货报告不匹配===========================key:" + key);
                throw new BusinessException(key + ":回传货品和理货报告不匹配,请检查你的效期或者批次是否正确!");
            }
        }
        return Pair.of(key, detailResult);
    }

    /**
     * 采购单回传处理
     *
     * @param orderBO
     */
    public void modifyProcurementOrder(InOrderBO orderBO) {
        try {
            if (!Objects.equals(InOrderType.CG_RK.getValue(), orderBO.getType()) || Objects.equals(TrusteeshipType.YES, orderBO.getIsTrusteeship()) || Objects.equals(SYSTEM_QM, orderBO.getOrigSystem())) {
                return;
            }

            OrderMessage orderMessage = new OrderMessage();
            orderMessage.setOrderNo(orderBO.getInOrderNo());
            orderMessage.setUserId(orderBO.getUserId());
            orderMessage.setBusinessNo(orderBO.getBusinessNo());
            orderMessage.setBackType(MessageType.BACK_IN_ORDER_CG.getValue());

            List<InOrderDetailBO> detailBOList = orderBO.getDetailBOList();
            List<ItemSkuMessage> itemSkuMessages = BeanUtils.copyProperties(detailBOList, ItemSkuMessage.class);
            orderMessage.setSkuList(itemSkuMessages);

            Map keysMap = Maps.newHashMap();
            keysMap.put("KEYS", orderBO.getInOrderNo());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message =SpringMessage.createMessage(orderMessage, messageHeaders);

            //延时消息
            SendResult sendResult = springRocketMQProducer.syncSend(orderNacosConfig.getOrderBackOtherTopic(), message, 3000, 1);
            log.info("[InOrderManager-modifyProcurementOrder]===============回传操作采购单发送消息===============sendResult:" + sendResult);
        } catch (Exception e) {
            log.error("[InOrderManager-modifyProcurementOrder]===============回传操作采购单异常===============", e);
        }
    }

    /**
     * 调拨单回传处理
     *
     * @param inOrderNo
     * @param origSystem 订单来源
     */
    public void callbackTransFerOrderStatus(String inOrderNo, String businessNo, Integer type, String origSystem) {
        try {
            if (Objects.equals(InOrderType.DB_RK.getValue(), type) && !Objects.equals(SYSTEM_QM, origSystem)) {
                OrderMessage orderMessage = new OrderMessage();
                orderMessage.setOrderNo(inOrderNo);
                orderMessage.setBusinessNo(businessNo);
                orderMessage.setBackType(MessageType.BACK_IN_ORDER_DB.getValue());

                Map keysMap = Maps.newHashMap();
                keysMap.put("KEYS", inOrderNo);
                MessageHeaders messageHeaders = new MessageHeaders(keysMap);
                SpringMessage message =SpringMessage.createMessage(orderMessage, messageHeaders);

                //延时消息
                SendResult sendResult = springRocketMQProducer.syncSend(orderNacosConfig.getOrderBackOtherTopic(), message, 3000, 1);
                log.info("[InOrderManager-callbackTransFerOrderStatus]===============回传操作调拨单发送消息===============sendResult:" + sendResult);
            }
        } catch (Exception e) {
            log.error("[InOrderManager-callbackTransFerOrderStatus]===============回传操作调拨单异常===============", e);
        }
    }

    /**
     * 分销单回传处理
     *
     * @param orderBO
     */
    public void callbackCornerOrderStatus(InOrderBO orderBO) {
        try {
            if (Objects.equals(InOrderType.FX_RK.getValue(), orderBO.getType()) && !Objects.equals(SYSTEM_QM, orderNacosConfig.getOrigSystem())) {
                OrderMessage orderMessage = new OrderMessage();
                orderMessage.setOrderNo(orderBO.getInOrderNo());
                orderMessage.setBackType(MessageType.BACK_IN_ORDER_FX.getValue());
                //延时消息
                SendResult sendResult = springRocketMQProducer.syncSend(orderNacosConfig.getOrderBackOtherTopic(), SpringMessage.createMessage(orderMessage,new HashMap()), 3000, 1);
                log.info("[InOrderManager-callbackCornerOrderStatus]===============回传操作分销单发送消息===============sendResult:" + sendResult);
            }
        } catch (Exception e) {
            log.error("[InOrderManager-callbackCornerOrderStatus]===============回传操作分销单异常===============", e);
        }
    }

    /**
     * 分销退货单回传处理
     *
     * @param orderBO
     */
    public void callbackCornerReturnOrderStatus(InOrderBO orderBO) {
        try {
            if (Objects.equals(InOrderType.FX_THRK.getValue(), orderBO.getType()) && !Objects.equals(SYSTEM_QM, orderNacosConfig.getOrigSystem())) {
                OrderMessage orderMessage = new OrderMessage();
                orderMessage.setOrderNo(orderBO.getInOrderNo());
                orderMessage.setBackType(MessageType.BACK_IN_ORDER_FX_RETURN.getValue());
                //延时消息
                SendResult sendResult = springRocketMQProducer.syncSend(orderNacosConfig.getOrderBackOtherTopic(), SpringMessage.createMessage(orderMessage,new HashMap()), 3000, 1);
                log.info("[InOrderManager-callbackCornerOrderStatus]===============回传操作分销退货单发送消息===============sendResult:" + sendResult);
            }
        } catch (Exception e) {
            log.error("[InOrderManager-callbackCornerOrderStatus]===============回传操作分销退货单异常===============", e);
        }
    }

    /**
     * 小二审核
     *
     * @param inOrderNo
     * @return
     */
    public boolean adminAudit(String inOrderNo) {
        InOrderSearch search = new InOrderSearch();
        InOrder inOrder = new InOrder();
        inOrder.setInOrderNo(inOrderNo);
        inOrder.setStatus(InOrderStatus.RK_WAIT_PUSH.getValue());

        //条件
        List<Integer> list = new ArrayList<>();
        list.add(InOrderStatus.RK_XR_IS.getValue());
        search.setStatusList(list);
        search.setInOrderNo(inOrderNo);
        return inOrderService.updateByInOrderSearch(inOrder, search);
    }

    /**
     * 通过关联单号查询
     *
     * @param businessNo
     * @param userId
     * @return
     */
    public List<InOrderBO> listInOrderByBusinessNo(String businessNo, Long userId) {
        List<InOrder> inOrders = inOrderService.selectListByBusinessNo(businessNo, userId);
        return BeanUtils.copyProperties(inOrders, InOrderBO.class);
    }

    public List<InOrderBO> listInOrderByLogicWarehouseCodeSet(Set<String> logicWarehouseCodeSet, List<Integer> statusList) {
        InOrderSearch inOrderSearch = new InOrderSearch();
        inOrderSearch.setLogicWarehouseCodeSet(logicWarehouseCodeSet);
        inOrderSearch.setStatusList(statusList);
        List<InOrder> inOrders = inOrderService.selectListBy(inOrderSearch);
        return BeanUtils.copyProperties(inOrders, InOrderBO.class);
    }

    public List<InOrderBO> listInOrderByLogicWarehouseCode(String logicWarehouseCode, List<Integer> statusList) {
        InOrderSearch inOrderSearch = new InOrderSearch();
        inOrderSearch.setLogicWarehouseCode(logicWarehouseCode);
        inOrderSearch.setStatusList(statusList);
        List<InOrder> inOrders = inOrderService.selectListBy(inOrderSearch);
        return BeanUtils.copyProperties(inOrders, InOrderBO.class);
    }

    /**
     * 更新
     *
     * @param orderBO
     * @return
     */
    @ShardingTransactionType(value = TransactionType.BASE)
    public boolean updateInOrderByNo(InOrderBO orderBO) {
        inOrderManagerHelper.checkOrderNo(orderBO.getInOrderNo());
        InOrder inOrder = BeanUtils.copyProperties(orderBO, InOrder.class);
        return inOrderService.updateByOrderNo(inOrder);
    }

    /**
     * 查询入库单详情
     *
     * @param inOrderDetailSearch
     * @return
     */
    public List<InOrderDetailBO> queryDetailList(InOrderDetailSearch inOrderDetailSearch) {
        List<InOrderDetail> inOrderDetails = inOrderDetailService.selectListBySearch(inOrderDetailSearch);
        return BeanUtils.copyProperties(inOrderDetails, InOrderDetailBO.class);
    }

    /**
     * 通过单号查询
     *
     * @param inOrderNo
     * @param userId
     * @return
     */
    public InOrderBO getInOrderByNo(String inOrderNo, Long userId) {
        inOrderManagerHelper.checkOrderNo(inOrderNo);
        InOrder inOrder = inOrderService.selectByOrderNo(inOrderNo, userId);
        inOrderManagerHelper.checkInOrder(inOrder, inOrderNo);
        return BeanUtils.copyProperties(inOrder, InOrderBO.class);
    }

    /**
     * 调拨单推送入库单时, 需校验调拨单是否出库回传完成
     */
    public String checkDBOrderIsOutBack(Integer type, String businessNo, Long userId) {
        AtomicReference<String> outOrderNo = new AtomicReference<>("");
        try {
            if (Objects.equals(InOrderType.DB_RK.getValue(), type)) {
                OutOrderSearch orderSearch = new OutOrderSearch();
                orderSearch.setBusinessNo(businessNo);
                orderSearch.setQueryType(2);
                orderSearch.setUserId(userId);
                List<OutOrderBO> outOrderBOList = outOrderManager.queryList(orderSearch);

                List<OutOrderBO> orderList = outOrderBOList.stream().filter(order -> !Objects.equals(OutOrderStatus.CK_REVOCATION.getValue(), order.getStatus())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(orderList)) {
                    throw new BusinessException("请先完成调拨出库单回传!");
                }
                orderList.forEach(order -> {
                    if (!Objects.equals(OutOrderStatus.CK_ALL.getValue(), order.getStatus())) {
                        throw new BusinessException("请先完成调拨出库单回传!");
                    }
                    if (Objects.equals(OutOrderStatus.CK_ALL.getValue(), order.getStatus())) {
                        outOrderNo.set(order.getOutOrderNo());
                    }
                });
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("[InOrderManager-checkDBOrderIsOutBack]===========================检测调拨出库单完成状异常===========================ex:", e);
            throw new BusinessException("检测调拨出库单完成状态失败!");
        }
        return outOrderNo.get();
    }

    /**
     * 自动推送 发送消息
     * 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     *
     * @param orderBO
     */
    public void autoPushInOrder(InOrderBO orderBO, int delayLevel) {
        try {
            if (!Objects.equals(PushType.AUTO_PUSH.getValue(), orderBO.getIsPush())
                    && !Objects.equals(PushType.AUTO_NO_PUSH.getValue(), orderBO.getIsPush())
                    && !Objects.equals(PushType.NO_PUSH_CCS.getValue(), orderBO.getIsPush())) {
                return;
            }
            OrderMessage orderMessage = new OrderMessage();
            orderMessage.setOrderNo(orderBO.getInOrderNo());
            orderMessage.setUserId(orderBO.getUserId());
            orderMessage.setBackType(MessageType.PUSH_IN_ORDER.getValue());

            Map keysMap = Maps.newHashMap();
            keysMap.put("KEYS", orderBO.getInOrderNo());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message =SpringMessage.createMessage(orderMessage, messageHeaders);
            //延时消息
            springRocketMQProducer.syncSend(orderNacosConfig.getOrderPushTopic(), message, 5000, delayLevel);
        } catch (Exception e) {
            log.error("[InOrderManager-autoPushInOrder]==================自动下发入库单异常==================orderNo:{}, ex:", orderBO.getInOrderNo(), e);
        }
    }

    /**
     * 入库单 回传 上游 发送消息
     *
     * @param orderBO
     */
    public void backAutoPushInOrder(InOrderBO orderBO) {
        try {
            if (!Objects.equals(OrderStatus.ORDER_WAIT.getValue(), orderBO.getBackStatus())) {
                return;
            }
            OrderRpcMessage orderMessage = BeanUtils.copyProperties(orderBO, OrderRpcMessage.class);
            orderMessage.setOrderNo(orderBO.getInOrderNo());
            orderMessage.setBackType(MessageType.CALL_BACK_IN_ORDER.getValue());

            Map keysMap = Maps.newHashMap();
            keysMap.put("KEYS", orderBO.getInOrderNo());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message =SpringMessage.createMessage(orderMessage, messageHeaders);

            springRocketMQProducer.syncSend(orderNacosConfig.getOrderPushBackTopic(), message, 5000, 2);
        } catch (Exception e) {
            log.error("[OutOrderManager-backAutoPushOutOrder]==================回传自动推送异常==================orderNo:{}, ex:", orderBO.getInOrderNo(), e);
        }
    }

    /**
     * 入库单 回传 库存 发送消息
     *
     * @param orderBO
     */
    public void backPushRKInventory(InOrderBO orderBO) {
        try {
            if (Objects.equals(OrderStatus.ORDER_NORMAL.getValue(), orderBO.getInventoryStatus())) {
                return;
            }
            InventoryProcessMessage orderMessage = new InventoryProcessMessage();
            orderMessage.setBusinessNo(orderBO.getInOrderNo());
            orderMessage.setBusinessBillType(BusinessBillType.BILL_IN_STOCK);
            orderMessage.setUpdateType(Objects.equals(InOrderType.FX_THRK.getValue(), orderBO.getType()) ? InventoryUpdateType.RELEASE_ADDED : InventoryUpdateType.RK_ADDED);

            Map keysMap = Maps.newHashMap();
            keysMap.put("KEYS", orderBO.getInOrderNo());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message = SpringMessage.createMessage(orderMessage, messageHeaders);
            springRocketMQProducer.syncSend(orderNacosConfig.getLockInventoryTopicV2(), message, 6000, 2);
        } catch (Exception e) {
            log.error("[InOrderManager-backPushRKInventory]==================回传库存发送消息异常==================orderNo:{}, ex:", orderBO.getInOrderNo(), e);
        }
    }

    /**
     * 入库单 回传 库存 发送消息
     *
     * @param orderBO
     */
    @Deprecated
    public void backPushRKInventory1(InOrderBO orderBO) {
        try {
            OrderMessage orderMessage = new OrderMessage();
            orderMessage.setOrderNo(orderBO.getInOrderNo());
            orderMessage.setBackFlag(orderBO.getBackFlag());
            orderMessage.setBackType(MessageType.PUSH_INVENTORY_RK.getValue());
            orderMessage.setInventoryStatus(OrderStatus.ORDER_WAIT.getValue());

            Map keysMap = Maps.newHashMap();
            keysMap.put("KEYS", orderBO.getInOrderNo());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message = SpringMessage.createMessage(orderMessage, messageHeaders);

            springRocketMQProducer.syncSend(orderNacosConfig.getOrderPushInventoryTopic(), message);
        } catch (Exception e) {
            log.error("[InOrderManager-backPushRKInventory]==================回传库存发送消息异常==================orderNo:{}, ex:", orderBO.getInOrderNo(), e);
        }
    }

    /**
     * 更新货品信息
     *
     * @param orderBO
     */
    public void buildInOrderDetailList(InOrderBO orderBO) {
        //查询老的货品信息
        List<InOrderDetail> oldDetailList = inOrderDetailService.selectListByOrderNo(orderBO.getInOrderNo());
        Map<String, InOrderDetail> oldDetailMap = oldDetailList.stream().collect(Collectors.toMap(InOrderDetail::getGoodsCode, inOrderDetail -> inOrderDetail, (v1, v2) -> v1));

        List<InOrderDetail> planList = new ArrayList<>();
        //回传的入库货品详情
        List<InOrderDetailBO> newDetailList = orderBO.getDetailBOList();
        for (InOrderDetailBO orderDetail : newDetailList) {
            orderDetail.setInOrderNo(orderBO.getInOrderNo());
            InOrderDetail detail = oldDetailMap.get(orderDetail.getGoodsCode());
            if (Objects.nonNull(detail)) {
                orderDetail.setBillCurrency(detail.getBillCurrency());
                orderDetail.setUnitPrice(detail.getUnitPrice());
                orderDetail.setBatchManagement(detail.getBatchManagement());
                if (Objects.equals(OrderStatus.ORDER_WAIT.getValue(), detail.getBackPlanQuantityStatus())) {
                    orderDetail.setPlanQuantity(detail.getPlanQuantity());
                    detail.setBackPlanQuantityStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());

                    InOrderDetail planDetail = new InOrderDetail();
                    planDetail.setId(detail.getId());
                    planDetail.setInOrderNo(detail.getInOrderNo());
                    planDetail.setGoodsCode(detail.getGoodsCode());
                    planDetail.setBackPlanQuantityStatus(OrderStatus.ORDER_SUCCESS_ALL.getValue());
                    planList.add(planDetail);
                } else {
                    orderDetail.setPlanQuantity(0);
                }
            } else {
                log.error("[InOrderManager-buildInOrderDetailList]===========================未匹配到老的货品信息===========================:" + orderDetail.getGoodsCode());
                throw new BusinessException("未匹配到入库货品信息!");
            }
        }

        log.info("[InOrderManager-buildInOrderDetailList]===========================更新计划状态===========================planList:" + JSON.toJSONString(planList));
        if (planList.size() > 0) {
            //更新计划状态
            boolean updateList = inOrderDetailService.updateListByOrderNo(planList);
            if (!updateList) {
                log.error("[InOrderManager-buildInOrderDetailList]===========================更新计划数量状态失败===========================:" + JSON.toJSONString(planList));
                throw new BusinessException("更新计划数量状态失败!");
            }
        }
    }

    /**
     * 取消理货报告
     *
     * @param orderNo
     */
    public void cancelTallReport(String orderNo) {
        try {
            remoteTallyReportFacade.deleteByOrderNo(orderNo);
        } catch (Exception e) {
            log.error("[InOrderManager-optRevocation]===========================取消理货报告失败===========================:" + orderNo);
        }
    }

    /**
     * 查询列表
     *
     * @param inOrderSearch
     * @return
     */
    public List<InOrderBO> selectListBy(InOrderSearch inOrderSearch) {
        List<InOrder> orderList = inOrderService.selectListBy(inOrderSearch);
        return BeanUtils.copyProperties(orderList, InOrderBO.class);
    }

    /**
     * 按条件查询数量
     *
     * @param inOrderSearch
     * @return
     */
    public int getInOrderCountBy(InOrderSearch inOrderSearch) {
        return inOrderService.countListBy(inOrderSearch);
    }

    /**
     * 构建货品信息
     *
     * @param orderBO
     */
    public void buildGoodsInfoV2(InOrderBO orderBO) {
        Map<String, InOrderDetailBO> detailMap = new HashMap<>();
        Integer count = 1;
        // 货品查询
        Map<String, GoodsManagementResult> goodsResultMap = queryGoods(orderBO);
        log.info("[InOrderFacadeImpl-buildGoodsInfoV2]============查询货品信息============goodsResultMap:{}", JSON.toJSONString(goodsResultMap));

        for (InOrderDetailBO orderDetailBO : orderBO.getDetailBOList()) {
            orderDetailBO.setExternalSku(StringUtils.trim(orderDetailBO.getExternalSku()));

            //货品信息设置
            String sku = orderDetailBO.getSku();
            GoodsManagementResult goodsResult = goodsResultMap.get(sku);
            if (Objects.isNull(goodsResult) && Objects.equals(SYSTEM_DYZJ, orderBO.getOrigSystem())) {
                sku = orderDetailBO.getExternalSku();
                goodsResult = goodsResultMap.get(sku);
            } else if (Objects.isNull(goodsResult) && Objects.equals(SYSTEM_ASCP, orderBO.getOrigSystem())) {
                sku = orderDetailBO.getGoodsCode();
                goodsResult = goodsResultMap.get(sku);
            }
            if (Objects.isNull(goodsResult)) {
                throw new BusinessException("货主:" + orderBO.getOwnerCode() + ","+sku + ":请先维护货品信息!");
            }

            if (Objects.equals(GoodsStatus.FORBIDDEN, goodsResult.getStatus())) {
                log.error("[InOrderFacadeImpl-buildGoodsInfoV2]============货品已禁用============sku:{}", goodsResult.getSku());
                throw new BusinessException(goodsResult.getSku() + ":货品已禁用,请先编辑货品!");
            }

            orderDetailBO.setGoodsCode(goodsResult.getGoodsCode());
            orderDetailBO.setSku(goodsResult.getSku());
            if (!Objects.equals(SYSTEM_DYZJ, orderBO.getOrigSystem()) && !Objects.equals(SYSTEM_ASCP, orderBO.getOrigSystem())) {
                orderDetailBO.setExternalSku(goodsResult.getExternalSku());
            }
            //oms的退货入库单,且是抖音字节的保税单,设置自己的货品id
            if (Objects.equals(SYSTEM_OMS, orderBO.getOrigSystem())
                    && Objects.equals(SOURCE_PLATFORM_DOUYIN, orderBO.getSourcePlatform())
                    && Objects.equals(TradeType.BONDED.getValue(), orderBO.getTradeType())) {
                orderDetailBO.setExternalSku(goodsResult.getExternalCode());
            }
            if (StringUtils.isBlank(orderDetailBO.getGoodsName())) {
                orderDetailBO.setGoodsName(goodsResult.getGoodsName());
            }
            if (StringUtils.isBlank(orderDetailBO.getBarcode())) {
                orderDetailBO.setBarcode(goodsResult.getBarcode());
            }
            if (StringUtils.isBlank(orderDetailBO.getModel())) {
                orderDetailBO.setModel(goodsResult.getModel());
            }
            if ((Objects.equals(SYSTEM_QM, orderBO.getOrigSystem()) || Objects.equals(SYSTEM_OMS, orderBO.getOrigSystem())) && StringUtils.isBlank(orderDetailBO.getBillCurrency())) {
//                orderDetailBO.setBillCurrency("RMB"); // 出入库优化版本,拿掉默认币制
            }
            if (Objects.isNull(orderDetailBO.getUnitPrice())) {
//                orderDetailBO.setUnitPrice(BigDecimal.ZERO);// 出入库优化版本,拿掉默认价格
            }
            if (Objects.isNull(goodsResult.getBatchManagement())) {
                throw new BusinessException("货主:" + orderBO.getOwnerCode() + ",sku:" + goodsResult.getSku() + ",未查询到效期管理!");
            }
            orderDetailBO.setBatchManagement(goodsResult.getBatchManagement().getValue());
            orderDetailBO.setTradeType(goodsResult.getType().getValue());

            //总价计算
            if (Objects.nonNull(orderDetailBO.getUnitPrice())) {
                BigDecimal decimal = orderDetailBO.getUnitPrice().multiply(new BigDecimal(orderDetailBO.getPlanQuantity()));
                orderBO.setTotalPrice(orderBO.getTotalPrice().add(decimal));
            }
            //计划总数
            Integer totalPlanQuantity = orderDetailBO.getPlanQuantity() + orderBO.getPlanQuantity();
            orderBO.setPlanQuantity(totalPlanQuantity);

            //货品合并
            String key = orderDetailBO.getGoodsCode();
            if (StringUtils.isBlank(key)) {
                key = orderDetailBO.getSku();
            }
            if (StringUtils.isNotBlank(orderDetailBO.getLineNo())) {
                key = key + orderDetailBO.getLineNo();
            }
            //分销退货入库,需要带批次
            if (Objects.equals(InOrderType.FX_THRK.getValue(), orderBO.getType())) {
                key = key + orderDetailBO.getInternalBatchCode();
            }
            InOrderDetailBO inOrderDetailBO = detailMap.get(key);
            if (Objects.isNull(inOrderDetailBO)) {
                if (StringUtils.isBlank(orderDetailBO.getLineNo())) {
                    orderDetailBO.setLineNo("" + count++);
                }
                orderDetailBO.setInOrderNo(orderBO.getInOrderNo());
                detailMap.put(key, orderDetailBO);
            } else {
                inOrderDetailBO.setPlanQuantity(inOrderDetailBO.getPlanQuantity() + orderDetailBO.getPlanQuantity());
            }
        }
        orderBO.setDetailBOList(new ArrayList<>(detailMap.values()));

        //菜鸟库存分组唯一判断
        buildDetailBarcodeAndCheck(orderBO);
    }

    /**
     * 查询货品
     *
     * @param orderBO
     * @return
     */
    private Map<String, GoodsManagementResult> queryGoods(InOrderBO orderBO) {
        List<GoodsManagementResult> goodsResultList;
        List<InOrderDetailBO> detailBOList = orderBO.getDetailBOList();
        if (InOrderType.FX_RK.getValue().equals(orderBO.getType())) {
            // 分销入库单货品ID为代塔T货品,没有效期相关信息,查询A的货品效期
            List<String> goodsCodeList = detailBOList.stream().map(InOrderDetailBO::getGoodsCode).collect(Collectors.toList());
            goodsResultList = remoteGoodsFacade.getDetailListByQueryParamForT(goodsCodeList, orderBO.getOwnerCode(), orderBO.getUserId());
        } else {
            // 分销退货单入库单货品ID为A用户货品ID 不传userID
            Long userId = InOrderType.FX_THRK.getValue().equals(orderBO.getType()) ? null : orderBO.getUserId();
            List<String> skuList = detailBOList.stream().filter(o -> StringUtils.isNotBlank(o.getSku())).map(InOrderDetailBO::getSku).collect(Collectors.toList());
            if (Objects.equals(SYSTEM_DYZJ, orderBO.getOrigSystem()) && CollectionUtil.isEmpty(skuList)) {
               //字节的入库单,且sku为空,用外部id
                List<String> cargoCodeList = detailBOList.stream().map(InOrderDetailBO::getExternalSku).collect(Collectors.toList());
                goodsResultList = remoteGoodsFacade.listExternalCodeResult(userId, orderBO.getOwnerCode(), cargoCodeList);
                return goodsResultList.stream().collect(Collectors.toMap(GoodsManagementResult::getExternalCode, o -> o, (k1, k2) -> k1));
            }
            if (Objects.equals(SYSTEM_ASCP, orderBO.getOrigSystem())) {
                List<String> goodsCodeList = detailBOList.stream().filter(o -> StringUtils.isNotBlank(o.getGoodsCode())).map(InOrderDetailBO::getGoodsCode).collect(Collectors.toList());
                goodsResultList = remoteGoodsFacade.listGoodsCodeResult(userId, orderBO.getOwnerCode(), goodsCodeList);
                return goodsResultList.stream().collect(Collectors.toMap(GoodsManagementResult::getGoodsCode, o -> o, (k1, k2) -> k1));
            }
            goodsResultList = remoteGoodsFacade.listSkuResultV2(userId, orderBO.getOwnerCode(), skuList);
        }
        return goodsResultList.stream().collect(Collectors.toMap(GoodsManagementResult::getSku, o -> o, (k1, k2) -> k1));
    }

    /**
     * 提交审核操作
     *
     * @param orderBO
     */
    public void optSubApproval(InOrderBO orderBO) {
        if (Objects.equals(OrderFlgStatus.VIOLATION, orderBO.getOrderFlg())) {
            return;
        }
        //如果是自动提交
        if (Objects.equals(PushType.AUTO_PUSH.getValue(), orderBO.getIsPush())
                || Objects.equals(PushType.AUTO_NO_PUSH.getValue(), orderBO.getIsPush())
                || Objects.equals(ApprovalStatus.SP_SUCCESS.getValue(), orderBO.getApprovalStatus())) {
            if (!Objects.equals(OrderPriceStatus.TO_EDIT, orderBO.getPriceStatus())) {
                orderBO.setStatus(InOrderStatus.RK_WAIT_PUSH.getValue());
            } else {
                // 如果是没有价格的，需要编辑补充价格的入库单则状态保持原有的【已创建】
                orderBO.setStatus(InOrderStatus.RK_SH.getValue());
            }
            orderBO.setApprovalStatus(ApprovalStatus.SP_SUCCESS.getValue());
        } else {
            //创建时,直接提交审核
            if (ApprovalStatus.SP_IS.getValue().equals(orderBO.getApprovalStatus())) {
                String orderNo = orderBO.getInOrderNo();
                if (StringUtils.isNotBlank(orderBO.getExtensionJson())) {
                    ExtensionJsonBO extensionJsonBO = JSON.parseObject(orderBO.getExtensionJson(), ExtensionJsonBO.class);
                    if (Objects.nonNull(extensionJsonBO.getRepealVersion()) && !Objects.equals(0, extensionJsonBO.getRepealVersion())) {
                        orderNo = orderNo + "_" + extensionJsonBO.getRepealVersion();
                    }
                }
                Pair<Boolean, Integer> submitAudit = remoteFlowFacade.optSubmitAudit(orderBO.getUserId(), SimpleUserHelper.getRealUserId(), orderNo, Boolean.TRUE);
                if (!submitAudit.getLeft()) {
                    log.warn("新增时,提交审核失败, inOrderNO:" + orderBO.getInOrderNo());
                }
                orderBO.setApprovalStatus(submitAudit.getRight());
            }
            if (Objects.equals(ApprovalStatus.SP_SUCCESS.getValue(), orderBO.getApprovalStatus())) {
                orderBO.setStatus(InOrderStatus.RK_XR_IS.getValue());
                //审核通过,且开启自动下发
                if (Objects.equals(AutoPushType.AUTOPUSH, orderBO.getIsAutoPush())) {
                    if (Objects.equals(PushType.HAND_NO_PUSH.getValue(), orderBO.getIsPush()) || Objects.equals(PushType.AUTO_NO_PUSH.getValue(), orderBO.getIsPush())) {
                        orderBO.setIsPush(PushType.AUTO_NO_PUSH.getValue());
                    } else {
                        orderBO.setIsPush(PushType.AUTO_PUSH.getValue());
                    }
                    orderBO.setStatus(InOrderStatus.RK_WAIT_PUSH.getValue());
                }
            } else if (Objects.equals(ApprovalStatus.SP_IS.getValue(), orderBO.getApprovalStatus())) {
                orderBO.setStatus(InOrderStatus.RK_WAIT.getValue());
            }
        }
    }

    /**
     * 条件查询单条
     *
     * @param orderSearch
     * @return
     */
    public InOrderBO getBySerch(InOrderSearch orderSearch) {
        InOrder order = inOrderService.selectBy(orderSearch);
        return BeanUtils.copyProperties(order, InOrderBO.class);
    }

    /**
     * 推送wms上报的轨迹-过滤不必要的轨迹
     *
     * @param wmsTraceBackParam
     * @return
     */
    public boolean backPushWmsTrace(InOrderWmsTraceBackParam wmsTraceBackParam) {
        if (InOrderTraceStatus.DONE_RECEIVING.getValue().equals(wmsTraceBackParam.getStatus())
                || InOrderTraceStatus.ACCEPT_BYTE.getValue().equals(wmsTraceBackParam.getStatus())
                || InOrderTraceStatus.CANCEL.getValue().equals(wmsTraceBackParam.getStatus())) {
            // 仓库接单由erp落库后马上回传，无需wms回传
            // 上架完成由入库单回传来处理，这里不做处理直接返回成功！
            // 仓库取消由erp取消后马上回传，无需wms回传
            return true;
        }
        try {
            //跳过指定节点回传
            if (Objects.equals(orderNacosConfig.getOrderNo(), wmsTraceBackParam.getOrderNo()) && orderNacosConfig.getTraceStatus().contains(wmsTraceBackParam.getStatus()+"")) {
                return Boolean.TRUE;
            }
            return backPushAllTrace(wmsTraceBackParam, false);
        } catch (BusinessException be) {
            log.error("backPushWmsTrace轨迹回传异常ex:", be);
            return false;
        }
    }

    /**
     * 推送所有轨迹
     *
     * @param wmsTraceBackParam
     * @param isErp             是否ERP自己调用
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean backPushAllTrace(InOrderWmsTraceBackParam wmsTraceBackParam, boolean isErp) {
        // 验证入库单是否存在
        InOrderBO inOrderBO = getInOrderByNo(wmsTraceBackParam.getOrderNo(), null);
        sendTrace(wmsTraceBackParam, Boolean.FALSE, null);//节点
        // 字节订单仓库接单有erp落库后马上回传，无需wms回传
        if (Objects.equals(SYSTEM_DYZJ, inOrderBO.getOrigSystem()) && !Objects.equals(InOrderType.TH_RK.getValue(), inOrderBO.getType())) {
            List<Integer> dbOptStatusList = Lists.newArrayList();
            dbOptStatusList.add(InOrderTraceStatus.ACCEPT_BYTE.getValue());
            dbOptStatusList.add(InOrderTraceStatus.CANCEL.getValue());
            if (isErp && dbOptStatusList.contains(wmsTraceBackParam.getStatus())) {
                // 先更新回传状态-如果失败则不会上传轨迹-后面轨迹如果失败也会自动rollback
                InOrderBO orderBO = new InOrderBO();
                orderBO.setInOrderNo(wmsTraceBackParam.getOrderNo());
                if (Objects.equals(InOrderTraceStatus.ACCEPT_BYTE.getValue(), wmsTraceBackParam.getStatus())) {
                    orderBO.setReceiveTraceStatus(1);
                    wmsTraceBackParam.setOperationTime(inOrderBO.getCreateTime());
                } else if (Objects.equals(InOrderTraceStatus.CANCEL.getValue(), wmsTraceBackParam.getStatus())) {
                    orderBO.setCancelTraceStatus(1);
                    wmsTraceBackParam.setOperationTime(inOrderBO.getRevocationTime());
                }
                boolean isSuccess = updateInOrderByNo(orderBO);
                if (!isSuccess) {
                    return false;
                }
            }
            // 构建回传参数
            Map<String, Object> orderBack = buildInOrderBack(inOrderBO, wmsTraceBackParam);
            // 调用回传http方法
            String request = postForm(orderBack, orderNacosConfig.getCallBackUrlQM());
            log.info("[InOrderFacadeImpl-backPushWmsTrace]===============================orderNo:{},奇门回传返回值:{}", wmsTraceBackParam.getOrderNo(), request);
            JSONObject jsonObject = JSON.parseObject(request);
            boolean isSuccess = jsonObject.getBooleanValue("success");
            if (!isSuccess) {
                sendTrace(wmsTraceBackParam, Boolean.TRUE, request);//节点
                log.info("入库单轨迹回传失败(后续会自动重试)，traceParam：{}", JSON.toJSONString(wmsTraceBackParam));
                throw new BusinessException("入库单轨迹回传失败(后续会自动重试)，单号：" + wmsTraceBackParam.getOrderNo());
            }
            sendTrace(wmsTraceBackParam, Boolean.TRUE, null);//节点
            return isSuccess;
        } else if (Objects.equals(SYSTEM_QM, inOrderBO.getOrigSystem())) { //奇门节点
            String businessValue = inOrderBO.getBusinessValue();
            if (StringUtils.isBlank(businessValue) || !businessValue.contains(SYSTEM_TAG_TAOTIAN)) {
                return Boolean.TRUE;
            }
            List<Integer> dbOptStatusList = Lists.newArrayList();
            dbOptStatusList.add(InOrderTraceStatus.ACCEPT_BYTE.getValue());
            dbOptStatusList.add(InOrderTraceStatus.ARRIVAL.getValue());
            dbOptStatusList.add(InOrderTraceStatus.GOOD_DONE_RECEIVING.getValue());
            if (!dbOptStatusList.contains(wmsTraceBackParam.getStatus())) {
                return Boolean.TRUE;
            }
            // 先更新回传状态-如果失败则不会上传轨迹-后面轨迹如果失败也会自动rollback
            if (Objects.equals(InOrderTraceStatus.ACCEPT_BYTE.getValue(), wmsTraceBackParam.getStatus())) {
                InOrderBO orderBO = new InOrderBO();
                orderBO.setInOrderNo(wmsTraceBackParam.getOrderNo());
                orderBO.setReceiveTraceStatus(1);
                if (!updateInOrderByNo(orderBO)) {
                    return false;
                }
            }
            Map<String, Object> orderMap = new HashMap<>();
            orderMap.put("orderCode", inOrderBO.getBusinessNo());
            if (StringUtils.isNotBlank(businessValue)) {
                JSONObject jsonObject = JSON.parseObject(businessValue);
                orderMap.put("orderType", jsonObject.getString("orderType"));
            }
            orderMap.put("warehouseCode", inOrderBO.getWarehouseCode());
            Map<String, Object> extendPropsMap = new HashMap<>();
            extendPropsMap.put("ownerCode", inOrderBO.getOwnerCode());
            orderMap.put("extendProps", extendPropsMap);

            Map<String, Object> processMap = new HashMap<>();
            if (Objects.equals(InOrderTraceStatus.ACCEPT_BYTE.getValue(), wmsTraceBackParam.getStatus())) {
                processMap.put("processStatus", "ACCEPT");
                processMap.put("operateTime", DateUtils.format(inOrderBO.getCreateTime()));
            } else if (Objects.equals(InOrderTraceStatus.ARRIVAL.getValue(), wmsTraceBackParam.getStatus())) {
                processMap.put("processStatus", "ARRIVED");
                processMap.put("operateTime", DateUtils.format(wmsTraceBackParam.getOperationTime()));
            } else if (Objects.equals(InOrderTraceStatus.GOOD_DONE_RECEIVING.getValue(), wmsTraceBackParam.getStatus())) {
                processMap.put("processStatus", "GOOD_FULFILLED");
                processMap.put("operateTime", DateUtils.format(wmsTraceBackParam.getOperationTime()));
                // 校验回传明细
                InOrderDetailBatchSearch detailBatchSearch = new InOrderDetailBatchSearch();
                detailBatchSearch.setInOrderNo(inOrderBO.getInOrderNo());
                detailBatchSearch.setBackStatus(1);
                int count = inOrderDetailBatchService.count(detailBatchSearch);
                if (count > 0) {
                    throw new BusinessException("入库单部分入库,待回传上游");
                }
            } else {
                return Boolean.TRUE;
            }

            Map<String, Object> backMap = new HashMap<>();
            backMap.put("order", orderMap);
            backMap.put("process", processMap);
            backMap.put("tenantId", SimpleTenantHelper.getTenantId());
            //请求参数
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("bizData", JSON.toJSONString(backMap));
            requestMap.put("method", "order.process.report");

            String request = postForm(requestMap, orderNacosConfig.getCallBackUrlQM());
            log.info("[InOrderFacadeImpl-backPushWmsTrace]===============================orderNo:{},奇门回传返回值:{}", inOrderBO.getInOrderNo(), request);
            JSONObject jsonObject = JSON.parseObject(request);
            boolean isSuccess = jsonObject.getBooleanValue("success");
            if (!isSuccess) {
                sendTrace(wmsTraceBackParam, Boolean.TRUE, request);//节点
                log.info("入库单轨迹回传失败(后续会自动重试)，traceParam：{}", JSON.toJSONString(wmsTraceBackParam));
                throw new BusinessException("入库单轨迹回传失败(后续会自动重试)，单号：" + wmsTraceBackParam.getOrderNo());
            }
            sendTrace(wmsTraceBackParam, Boolean.TRUE, null);//节点
            return isSuccess;
        }
        // 其他订单无需发送轨迹信息
        return true;
    }

    /**
     * 构建入库单轨迹-透传字节
     *
     * @param orderBO
     * @param wmsTraceBackParam
     * @return
     */
    private Map<String, Object> buildInOrderBack(InOrderBO orderBO, InOrderWmsTraceBackParam wmsTraceBackParam) {
        // 字节入库单轨迹回传方法名
        String method = "entry.order.trajectory.notify";
        Map<String, Object> backMap = new HashMap<>();
        backMap.put("in_outbound_event_type", wmsTraceBackParam.getStatus());
        // 来源单据号
        backMap.put("source_order_no", orderBO.getExternalNo());
        // 服务商单据编号
        backMap.put("sp_order_no", orderBO.getInOrderNo());
        // 实操时间戳 单位：ms
        backMap.put("occurrence_time", wmsTraceBackParam.getOperationTime());
        //货主
        backMap.put("ownerCode", orderBO.getOwnerCode());
        //逻辑仓
        backMap.put("logicWarehouseCode", orderBO.getLogicWarehouseCode());
        //用户ID
        backMap.put("userId", orderBO.getUserId());
        List<Object> reportList = new ArrayList<>();
        if (InOrderTraceStatus.DONE_TALLYING.getValue().equals(wmsTraceBackParam.getStatus())) {
            // 理货报告附件url，当回告节点为理货完成时，必传
            for (TallyReportUrl reportUrl : wmsTraceBackParam.getTallyReportUrl()) {
                Map<String, Object> skuMap = new HashMap<>();
                skuMap.put("url", reportUrl.getUrl());
                skuMap.put("name", reportUrl.getName());
                reportList.add(skuMap);
            }
            backMap.put("tally_report_url", reportList);
        }
        //请求参数
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("bizData", JSON.toJSONString(backMap));
        requestMap.put("method", method);
        return requestMap;
    }

    /**
     * 自动推送 发送消息
     * 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     *
     * @param orderBO
     */
    public void zjInOrder(InOrderBO orderBO, int backType) {
        try {
            if (!Objects.equals(SYSTEM_DYZJ, orderBO.getOrigSystem())) {
                return;
            }
            OrderMessage orderMessage = new OrderMessage();
            orderMessage.setOrderNo(orderBO.getInOrderNo());
            orderMessage.setUserId(orderBO.getUserId());
            orderMessage.setBackType(backType);
            //延时消息
            Map keysMap = Maps.newHashMap();
            // 配置消息KEYS会显示在RocketMQ的Key字段
            keysMap.put("KEYS", orderBO.getInOrderNo());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message = SpringMessage.createMessage(orderMessage, messageHeaders);
            springRocketMQProducer.syncSend(orderNacosConfig.getOrderBackOtherTopic() + ":TRACE", message, 3000, 1);
        } catch (Exception e) {
            log.error("[InOrderManager-zjInOrder]==================发送回传字节轨迹MQ异常==================orderNo:{}, ex:", orderBO.getInOrderNo(), e);
        }
    }
    /**
     * 自动推送 发送消息
     * 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     *
     * @param orderBO
     */
    public void ttInOrder(InOrderBO orderBO, int backType) {
        try {
            if (!Objects.equals(SYSTEM_QM, orderBO.getOrigSystem())) {
                return;
            }
            String businessValue = orderBO.getBusinessValue();
            if (StringUtils.isBlank(businessValue) || !businessValue.contains(SYSTEM_TAG_TAOTIAN)) {
                return;
            }
            OrderMessage orderMessage = new OrderMessage();
            orderMessage.setOrderNo(orderBO.getInOrderNo());
            orderMessage.setUserId(orderBO.getUserId());
            orderMessage.setBackType(backType);
            //延时消息
            Map keysMap = Maps.newHashMap();
            // 配置消息KEYS会显示在RocketMQ的Key字段
            keysMap.put("KEYS", orderBO.getInOrderNo());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message = SpringMessage.createMessage(orderMessage, messageHeaders);
            springRocketMQProducer.syncSend(orderNacosConfig.getOrderBackOtherTopic() + ":TRACE", message, 3000, 1);
        } catch (Exception e) {
            log.error("[InOrderManager-ttInOrder]==================发送回传奇门淘天轨迹MQ异常==================orderNo:{}, ex:", orderBO.getInOrderNo(), e);
        }
    }

    /**
     * 自动推送 发送消息
     * 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     *
     * @param orderBO
     */
    public void zjAddCargoCode(InOrderBO orderBO, int backType) {
        try {
            if (!Objects.equals(SYSTEM_DYZJ, orderBO.getOrigSystem())) {
                return;
            }
            OrderMessage orderMessage = new OrderMessage();
            orderMessage.setOrderNo(orderBO.getInOrderNo());
            orderMessage.setUserId(orderBO.getUserId());
            orderMessage.setBackType(backType);

            Map keysMap = Maps.newHashMap();
            keysMap.put("KEYS", orderBO.getInOrderNo());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message = SpringMessage.createMessage(orderMessage, messageHeaders);
            //延时消息
            springRocketMQProducer.syncSend(orderNacosConfig.getOrderBackOtherTopic(), message, 3000, 1);
        } catch (Exception e) {
            log.error("[InOrderManager-zjAddCargoCode]==================发生回传字节轨迹MQ异常==================orderNo:{}, ex:", orderBO.getInOrderNo(), e);
        }
    }

    /**
     * 查询入库单价格
     *
     * @param inOrderNo
     * @param goodsCode
     * @return
     */
    public BigDecimal getInOrderDetailPriceCache(String inOrderNo, String goodsCode) {
        try {
            String key = "IN_PRICE_" + inOrderNo + goodsCode;
            Object price = redisUtils.get(key);
            if (Objects.nonNull(price)) {
                return (BigDecimal) price;
            }
            InOrderDetailSearch inOrderDetailSearch = new InOrderDetailSearch();
            inOrderDetailSearch.setInOrderNo(inOrderNo);
            inOrderDetailSearch.setGoodsCode(goodsCode);
            List<InOrderDetail> detailList = inOrderDetailService.selectListBySearch(inOrderDetailSearch);
            if (CollectionUtil.isNotEmpty(detailList)) {
                BigDecimal unitPrice = detailList.get(0).getUnitPrice();
                redisUtils.set(key, unitPrice, 1800);
                return unitPrice;
            }
        } catch (Exception e) {
            log.error("[InOrderManager-getInOrderDetailPriceCache]======================获取入库单明细价格异常===================ex:", e);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 备货单回传处理
     *
     * @param orderBO
     */
    public void modifyReadyOrder(InOrderBO orderBO) {
        try {
            if (Objects.equals(PortAreaEnum.NORMAL, orderBO.getPortArea())) {
                return;
            }
            OrderMessage orderMessage = new OrderMessage();
            orderMessage.setOrderNo(orderBO.getInOrderNo());
            orderMessage.setUserId(orderBO.getUserId());
            orderMessage.setReadyNo(orderBO.getReadyNo());
            orderMessage.setBackType(MessageType.BACK_IN_ORDER_BH.getValue());

            List<InOrderDetailBO> detailBOList = orderBO.getDetailBOList();
            List<ItemSkuMessage> itemSkuMessages = BeanUtils.copyProperties(detailBOList, ItemSkuMessage.class);
            orderMessage.setSkuList(itemSkuMessages);
            log.info("[InOrderManager-modifyReadyOrder]===============回传操作备货单发送消息===============orderMessage:" + JSON.toJSONString(orderMessage));

            Map keysMap = Maps.newHashMap();
            keysMap.put("KEYS", orderBO.getInOrderNo());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message =SpringMessage.createMessage(orderMessage, messageHeaders);

            //延时消息
            springRocketMQProducer.syncSend(orderNacosConfig.getReadyOrderCallbackTopic(), message, 3000, 1);
        } catch (Exception e) {
            log.error("[InOrderManager-modifyReadyOrder]===============回传操作备货单异常===============", e);
        }
    }

    /**
     * 节点
     *
     * @param wmsTraceBackParam
     * @param isBack            true 需要回传
     */
    private void sendTrace(InOrderWmsTraceBackParam wmsTraceBackParam, boolean isBack, String backSuccess) {
        try {
            OrderNodeStatus nodeStatus;
            String message = "成功";
            if (isBack) {//回传
                if (Objects.equals(InOrderTraceStatus.ACCEPT_BYTE.getValue(), wmsTraceBackParam.getStatus())) {
                    nodeStatus = OrderNodeStatus.IN_ORDER_ACCEPT_BACK;
                    message = "接单回告上游成功";
                } else if (Objects.equals(InOrderTraceStatus.ARRIVAL.getValue(), wmsTraceBackParam.getStatus())) {
                    nodeStatus = OrderNodeStatus.IN_ORDER_ARRIVAL_BACK;
                    message = "仓库到货[回告上游]";
                } else if (Objects.equals(InOrderTraceStatus.TALLYING.getValue(), wmsTraceBackParam.getStatus())) {
                    nodeStatus = OrderNodeStatus.IN_ORDER_TALLY_BACK;
                    message = "理货开始，回告上游成功";
                } else if (Objects.equals(InOrderTraceStatus.DONE_TALLYING.getValue(), wmsTraceBackParam.getStatus())) {
                    nodeStatus = OrderNodeStatus.IN_ORDER_TALLY_COMPLETE_BACK;
                    message = "理货完成，回告上游成功";
                } else if (Objects.equals(InOrderTraceStatus.CANCEL.getValue(), wmsTraceBackParam.getStatus())) {
                    nodeStatus = OrderNodeStatus.IN_ORDER_CANCEL_BACK;
                    message = "取消入库回告上游成功";
                } else if (Objects.equals(InOrderTraceStatus.GOOD_DONE_RECEIVING.getValue(), wmsTraceBackParam.getStatus())) {
                    nodeStatus = OrderNodeStatus.IN_ORDER_GOOD;
                    message = "正品上架完成";
                } else {
                    return;
                }
            } else {
                if (Objects.equals(InOrderTraceStatus.ARRIVAL.getValue(), wmsTraceBackParam.getStatus())) {
                    nodeStatus = OrderNodeStatus.IN_ORDER_ARRIVAL_WMS;
                    message = "仓库到货";
                } else if (Objects.equals(InOrderTraceStatus.TALLYING.getValue(), wmsTraceBackParam.getStatus())) {
                    nodeStatus = OrderNodeStatus.IN_ORDER_TALLY;
                    message = "下游WMS：理货开始";
                } else if (Objects.equals(InOrderTraceStatus.DONE_TALLYING.getValue(), wmsTraceBackParam.getStatus())) {
                    nodeStatus = OrderNodeStatus.IN_ORDER_TALLY_COMPLETE;
                    message = "下游WMS：理货完成";
                } else {
                    return;
                }
            }
            orderNodeManager.sendInOrderNode(wmsTraceBackParam.getOrderNo(), nodeStatus, StringUtils.isBlank(backSuccess) ? message : "失败:" + backSuccess, System.currentTimeMillis(), Boolean.TRUE);
        } catch (Exception e) {
            log.error("[InOrderManager-sendTrace]==================回传节点异常==================orderNo:{}, ex:", wmsTraceBackParam.getOrderNo(), e);
        }
    }

    /**
     * 通过单号查询
     *
     * @param outOrderNo
     * @param outOrderNo
     * @return
     */
    public InOrderBO getInOrderByOutOrderNo(String outOrderNo, Integer status) {
        InOrderSearch inOrderSearch = new InOrderSearch();
        inOrderSearch.setOutOrderNo(outOrderNo);
        inOrderSearch.setStatus(status);
        List<InOrder> inOrders = inOrderService.selectListBy(inOrderSearch);
        if (CollectionUtils.isEmpty(inOrders)) {
            return null;
        }
        return BeanUtils.copyProperties(inOrders.get(0), InOrderBO.class);
    }

    /**
     * 自动推送 发送消息
     * 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     *
     * @param orderBO
     */
    public void zjThBackOMS(InOrderBO orderBO) {
        try {
            if (!Objects.equals(SYSTEM_DYZJ, orderBO.getOrigSystem()) && !Objects.equals(InOrderType.TH_RK.getValue(), orderBO.getType())) {
                return;
            }
            Map<String, Object> orderMessage = new HashMap<>();
            orderMessage.put("businessNo", orderBO.getBusinessNo());
            orderMessage.put("entryOrderNo", orderBO.getInOrderNo());

            Map keysMap = Maps.newHashMap();
            keysMap.put("KEYS", orderBO.getInOrderNo());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message = SpringMessage.createMessage(orderMessage, messageHeaders);
            //延时消息
            springRocketMQProducer.syncSend(orderNacosConfig.getOrderBackOms(), message, 3000, 1);
        } catch (Exception e) {
            log.error("[InOrderManager-zjThBackOMS]==================回传OMS入库单号异常==================orderNo:{}, ex:", orderBO.getInOrderNo(), e);
        }
    }

    /**
     * 单号验证
     * @param businessNo
     */
    public void regexBusinessNo(String businessNo){
        String regex = "^[-a-zA-Z0-9]+$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(businessNo);
        if (!matcher.find()) {
            throw new BusinessException("关联单号只支持英文字母,数字,中划线!");
        }
    }


    /**
     * 去除撤单版本号
     *
     * @param otherOrderNo
     * @return
     */
    public String getErpOrderNo(String otherOrderNo) {
        Assert.notNull(otherOrderNo, "单号不能为空！");
        String erpOrderNo = otherOrderNo;
        if (otherOrderNo.length() > ERP_OUT_ORDER_NO_LENGTH) {
            erpOrderNo = otherOrderNo.substring(0, ERP_OUT_ORDER_NO_LENGTH);
        }
        return erpOrderNo;
    }

    public List<InOrderBO> scansInOrder(Long id, Integer pageSize, String tableSuffix) {
        List<InOrder> inOrders = inOrderService.scansInOrder(id, pageSize, tableSuffix);
        return BeanUtils.copyProperties(inOrders, InOrderBO.class);
    }

    /**
     * 回传GLP质押B单 发送消息
     * 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     *
     * @param orderBO
     */
    public void glpZYInOrder(InOrderBO orderBO) {
        try {
            OwnerResult owner = remoteWarehouseFacade.getOwner(orderBO.getOwnerCode());
            if (Objects.isNull(owner)) {
                throw new BusinessException(orderBO.getOwnerCode() + ":货主查询不存在!");
            }
            if (!Objects.equals(FinancialPlatformEnum.GLP, owner.getFinancialPlatform()) || !Objects.equals(2, owner.getFinanceModeType())) {
                return;
            }
            OrderMessage orderMessage = new OrderMessage();
            orderMessage.setOrderNo(orderBO.getInOrderNo());
            orderMessage.setUserId(orderBO.getUserId());
            orderMessage.setBackType(MessageType.GLP_ZY_IN_ORDER.getValue());

            //延时消息
            Map keysMap = Maps.newHashMap();
            // 配置消息KEYS会显示在RocketMQ的Key字段
            keysMap.put("KEYS", orderBO.getInOrderNo());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message = SpringMessage.createMessage(orderMessage, messageHeaders);
            springRocketMQProducer.syncSend(orderNacosConfig.getOrderBackOtherTopic(), message, 3000, 1);
        } catch (Exception e) {
            log.error("[InOrderManager-glpZYInOrder]==================发送回传GLP质押B单MQ异常==================orderNo:{}, ex:", orderBO.getInOrderNo(), e);
        }
    }

    /**
     * 回传glp
     *
     * @param inOrderNo
     */
    public void backGLPZY(String inOrderNo) {
        try {
            InOrder inOrder = inOrderService.selectByOrderNo(inOrderNo, null);
            // 回传入库单
            InOrderDetailBatchSearch search = new InOrderDetailBatchSearch();
            search.setInOrderNo(inOrderNo);
            List<InOrderDetailBatch> inOrderDetailBatchList = inOrderDetailBatchService.selectListBySearch(search);

            List<String> collect = inOrderDetailBatchList.stream().map(InOrderDetailBatch::getGoodsCode).collect(Collectors.toList());
            List<GoodsManagementResult> goodsResultList = remoteGoodsFacade.listGoodsCodeResult(inOrder.getUserId(), inOrder.getOwnerCode(), collect);
            Map<String, GoodsManagementResult> goodsResultMap = goodsResultList.stream().collect(Collectors.toMap(GoodsManagementResult::getGoodsCode, o -> o, (k1, k2) -> k1));

            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("type", 1);
            paramMap.put("informType", "正常入库");
            paramMap.put("informNo", inOrderNo);
            paramMap.put("informDate", DateUtils.format(inOrder.getActualTime()));//yyyy-mm-dd HH:mi:ss
            paramMap.put("sourceCode", SOURCE_CODE_ZY);
            String ownerCode = regulatoryOwner.getProperty(inOrder.getOwnerCode(), inOrder.getOwnerCode());
            paramMap.put("borrower", ownerCode);
            paramMap.put("supplier", ownerCode);
            paramMap.put("warehouseId", inOrder.getWarehouseCode());
            paramMap.put("warehouseName", inOrder.getWarehouseCode());

            List<Object> goodsList = new ArrayList<>();
            for (InOrderDetailBatch inOrderDetail : inOrderDetailBatchList) {
                GoodsManagementResult goodsResult = goodsResultMap.get(inOrderDetail.getGoodsCode());

                Map<String, Object> skuMap = new HashMap<>();
                skuMap.put("warehouseId", inOrder.getWarehouseCode());
                skuMap.put("warehousename", inOrder.getWarehouseCode());
                skuMap.put("incomeGoodid", inOrderDetail.getSku());
                skuMap.put("incomeGoodName", goodsResult.getGoodsName());
                skuMap.put("incomeNum", inOrderDetail.getActualQuantity());
                skuMap.put("unit", goodsResult.getModel());
                skuMap.put("gbarCode", goodsResult.getBarcode());
                skuMap.put("cnwlbDate", DateUtils.format(inOrder.getActualTime()));
                if (Objects.nonNull(inOrderDetail.getProductionDate())) {
                    skuMap.put("productionTime", DateUtils.format(inOrderDetail.getProductionDate().getTime()));//yyyy-mm-dd HH:mi:ss
                }
                if (Objects.nonNull(inOrderDetail.getExpireDate())) {
                    skuMap.put("expDate", DateUtils.format(inOrderDetail.getExpireDate().getTime()));//yyyy-mm-dd HH:mi:ss
                }
                skuMap.put("stockType", Objects.equals(1, inOrderDetail.getInventoryType()) ? 0 : 1);
                skuMap.put("batch", inOrderDetail.getBatchCode());
                if (goodsResult != null && PledgeStatus.YES.equals(goodsResult.getPledgeStatus())) {
                    // 商品质押状态:0：已质押 1：未质押
                    skuMap.put("pledgeStatus", "0");
                } else {
                    // 商品质押状态:0：已质押 1：未质押
                    skuMap.put("pledgeStatus", "1");
                }
                goodsList.add(skuMap);
            }
            paramMap.put("goodsList", goodsList);

            paramMap.put("method", "epass.in.storage.inform.get");
            paramMap.put("appKey", orderNacosConfig.getAppKey());
            paramMap.put("appSecret", orderNacosConfig.getAppSecret());
            paramMap.put("sign", buildBaseMD5(paramMap, orderNacosConfig.getAppSecret()));

            // 发送审核请求
            String request = postJson(paramMap, orderNacosConfig.getCallBackUrlGLP(), SYSTEM_GLP);
            log.info("[InOrderManager-backGLPZY]===============================inOrderNo:{},GLP回传返回值:{}", inOrderNo, request);
            JSONObject jsonObject = JSON.parseObject(request);
            String orderResults = jsonObject.getString("informResults");
            if (Objects.equals("20", orderResults) && !Objects.equals("出入库单号重复", jsonObject.getString("remarks"))) {
                log.error("[InOrderManager-backGLPZY]===============================GLP回传入库单接收失败:" + inOrderNo);
                throw new BusinessException("GLP回传入库单接收失败");
            }

            // 回传理货报告附件
            TallyReportResult tallyReportResult = remoteTallyReportFacade.getTallyReportByNo(inOrderNo);
            if (Objects.nonNull(tallyReportResult) && CollectionUtil.isNotEmpty(tallyReportResult.getAttachmentFile())) {
                List<Object> attachmentFile = new ArrayList<>(1);
                FileDto fileDto = JSON.parseObject(JSON.toJSONString(tallyReportResult.getAttachmentFile().get(0)), FileDto.class);
                attachmentFile.add(fileDto.getKey());

                Map<String, Object> paramFileMap = new HashMap<>();
                paramFileMap.put("sourceCode", SOURCE_CODE_ZY);
                paramFileMap.put("orderNo", inOrder.getBusinessNo());
                paramFileMap.put("attachmentType", 3);
                paramFileMap.put("businessNo", inOrderNo);
                paramFileMap.put("outAttachmentList", attachmentFile);
                paramFileMap.put("method", "wms.attachment.put");
                paramFileMap.put("appKey", orderNacosConfig.getAppKey());
                paramFileMap.put("appSecret", orderNacosConfig.getAppSecret());
                paramFileMap.put("sign", buildBaseMD5(paramFileMap, orderNacosConfig.getAppSecret()));

                // 发送审核请求
                String request1 = postJson(paramFileMap, orderNacosConfig.getCallBackUrlGLP(), SYSTEM_GLP);
                log.info("[InOrderManager-backGLPZY]===============================inOrderNo:{},GLP回传返回值:{}", inOrderNo, request1);
                JSONObject jsonObject1 = JSON.parseObject(request1);
                Integer status = jsonObject1.getInteger("status");
                if (!Objects.equals(1, status)) {
                    log.error("[InOrderManager-backGLPZY]===============================GLP回传理货报告文件接收失败:" + inOrderNo);
                    throw new BusinessException("GLP回传理货报告文件接收失败");
                }
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("[InOrderManager-backGLPZY]===============================inOrderNo:{},GLP质押入库回传异常ex:", inOrderNo, e);
            throw new BusinessException("GLP质押入库回传异常");
        }
    }


    /**
     * 入库单理货报告回传 发送消息
     * 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
     *
     * @param orderBO
     */
    public void callBackTallyReport(InOrderBO orderBO) {
        try {
            // 不是最终回传,直接返回
            if (!Objects.equals(0, orderBO.getIsConfirm())) {
                return;
            }
            OrderMessage orderMessage = new OrderMessage();
            orderMessage.setOrderNo(orderBO.getInOrderNo());
            orderMessage.setUserId(orderBO.getUserId());
            orderMessage.setBackType(MessageType.TALLY_REPORT_BACK.getValue());
            //延时消息
            Map keysMap = Maps.newHashMap();
            // 配置消息KEYS会显示在RocketMQ的Key字段
            keysMap.put("KEYS", orderBO.getInOrderNo());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message = SpringMessage.createMessage(orderMessage, messageHeaders);
            springRocketMQProducer.syncSend(orderNacosConfig.getOrderBackOtherTopic(), message, 3000, 1);
        } catch (Exception e) {
            log.error("[InOrderManager-callBackTallyReport]==================发送入库单理货报告回传MQ异常==================orderNo:{}, ex:", orderBO.getInOrderNo(), e);
        }
    }

    public void backTallyReport(String orderNo) {
        try {
            InOrderDetailBatchSearch detailBatchSearch = new InOrderDetailBatchSearch();
            detailBatchSearch.setInOrderNo(orderNo);
            List<InOrderDetailBatch> inOrderDetailBatchList = inOrderDetailBatchService.selectListBySearch(detailBatchSearch);
            //回传理货报告状态和明细
            remoteTallyReportFacade.callbackV2(orderNo, inOrderDetailBatchList);
        } catch (BusinessException e) {
            if (!Objects.equals("理货报告不存在,或未通过审批!", e.getMessage())) {
                throw e;
            }
            log.error("backTallyReport====={}回传理货报告处理异常======", orderNo, e);
        }
    }

}
