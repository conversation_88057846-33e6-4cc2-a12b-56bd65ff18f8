package com.danding.business.server.ares.flow.facade;

import com.danding.business.client.ares.flow.facade.IFlowInstanceFacade;
import com.danding.business.server.ares.flow.manager.FlowInstanceManager;
import com.danding.business.server.ares.flow.remote.RemoteUserCenterFacade;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <p>
 * 审核流实例 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@DubboService
public class FlowInstanceFacadeImpl implements IFlowInstanceFacade {

    @Autowired
    private RemoteUserCenterFacade remoteUserCenterFacade;

    @Autowired
    private FlowInstanceManager flowInstanceManager;

}
