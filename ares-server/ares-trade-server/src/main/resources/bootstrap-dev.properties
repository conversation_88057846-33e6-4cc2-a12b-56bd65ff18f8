#èè°
logging.level.root=info
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB
server.tomcat.max-swallow-size = -1
spring.application.name = ares-trade-server
#nacoséç½®
spring.cloud.nacos.config.server-addr = nacos.conf.svc.cluster.local:8848
spring.cloud.nacos.config.namespace = erp_integration
spring.cloud.nacos.config.file-extension = properties
spring.cloud.nacos.config.extension-configs[0].data-id = application
spring.cloud.nacos.config.extension-configs[0].group = DEFAULT_GROUP
spring.cloud.nacos.config.extension-configs[0].refresh = true
spring.cloud.nacos.config.extension-configs[1].data-id = redis_v2
spring.cloud.nacos.config.extension-configs[1].group = DEFAULT_GROUP
spring.cloud.nacos.config.extension-configs[1].refresh = true
spring.cloud.nacos.config.extension-configs[2].data-id = ares-trade-server
spring.cloud.nacos.config.extension-configs[2].group = DEFAULT_GROUP
spring.cloud.nacos.config.extension-configs[2].refresh = true
spring.cloud.nacos.config.extension-configs[3].data-id = rocketmq
spring.cloud.nacos.config.extension-configs[3].group = DEFAULT_GROUP
spring.cloud.nacos.config.extension-configs[3].refresh = true
spring.cloud.nacos.config.extension-configs[4].data-id = ares-server-mybatis-plus
spring.cloud.nacos.config.extension-configs[4].group = DEFAULT_GROUP
spring.cloud.nacos.config.extension-configs[4].refresh = false
spring.cloud.nacos.config.extension-configs[5].data-id = ares-es-config
spring.cloud.nacos.config.extension-configs[5].group = DEFAULT_GROUP
spring.cloud.nacos.config.extension-configs[5].refresh = true


