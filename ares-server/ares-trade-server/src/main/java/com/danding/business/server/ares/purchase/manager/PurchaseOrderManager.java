package com.danding.business.server.ares.purchase.manager;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.danding.business.client.ares.goodsManagement.facade.IGoodsManagementFacade;
import com.danding.business.client.ares.goodsManagement.result.GoodsManagementResult;
import com.danding.business.client.ares.logicwarehouse.facade.ILogicWarehouseFacade;
import com.danding.business.client.ares.logicwarehouse.result.LogicWarehouseResult;
import com.danding.business.client.ares.order.facade.IOptInOrderFacade;
import com.danding.business.client.ares.order.param.InOrderAddParam;
import com.danding.business.client.ares.order.param.InOrderDetailAddParam;
import com.danding.business.common.ares.enums.HeadEnum;
import com.danding.business.common.ares.enums.common.ApprovalStatus;
import com.danding.business.common.ares.enums.order.InOrderType;
import com.danding.business.common.ares.enums.trade.PaymentDaysTimeUnit;
import com.danding.business.common.ares.enums.trade.PaymentMethod;
import com.danding.business.common.ares.enums.trade.PurchaseOrderStatus;
import com.danding.business.common.ares.utils.GenerateUtils;
import com.danding.business.core.ares.purchase.entity.PurchaseOrder;
import com.danding.business.core.ares.purchase.entity.PurchaseOrderDetail;
import com.danding.business.core.ares.purchase.entity.PurchaseOrderOtherCharges;
import com.danding.business.core.ares.purchase.search.PurchaseOrderDetailSearch;
import com.danding.business.core.ares.purchase.search.PurchaseOrderOtherChargesSearch;
import com.danding.business.core.ares.purchase.search.PurchaseOrderSearch;
import com.danding.business.core.ares.purchase.service.IPurchaseOrderDetailService;
import com.danding.business.core.ares.purchase.service.IPurchaseOrderOtherChargesService;
import com.danding.business.core.ares.purchase.service.IPurchaseOrderService;
import com.danding.business.server.ares.config.TradeNacosConfig;
import com.danding.business.server.ares.purchase.BO.PurchaseOrderBO;
import com.danding.business.server.ares.purchase.BO.PurchaseOrderDetailBO;
import com.danding.business.server.ares.purchase.BO.PurchaseOrderOtherChargesBO;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboReference;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 采购单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-21
 */
@Slf4j
@Component
public class PurchaseOrderManager {

    @Autowired
    private IPurchaseOrderService purchaseOrderService;
    @Autowired
    private IPurchaseOrderDetailService purchaseOrderDetailService;
    @Autowired
    private IPurchaseOrderOtherChargesService purchaseOrderOtherChargesService;
    @DubboReference
    private IOptInOrderFacade optInOrderFacade;
    @DubboReference
    private IGoodsManagementFacade goodsManagementFacade;
    @DubboReference
    private ILogicWarehouseFacade logicWarehouseFacade;
    @Autowired
    private TradeNacosConfig tradeNacosConfig;

    public Pair<Boolean, String> addPurchaseOrder(PurchaseOrderBO bo) {
        if (bo.getDueAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("采购单应付金额不能小于0");
        }
        PurchaseOrder purchaseOrder = BeanUtils.copyProperties(bo, PurchaseOrder.class);
        checkPayMethod(bo);
        String purchaseOrderNo;
        if (purchaseOrder.getId() == null) {
            if (StringUtils.isBlank(purchaseOrder.getPurchaseOrderNo())) {
                purchaseOrderNo = GenerateUtils.generateSimpleOrderNo(HeadEnum.PS);
                purchaseOrder.setPurchaseOrderNo(purchaseOrderNo);
            }
            PurchaseOrderSearch search = new PurchaseOrderSearch();
            search.setPurchaseOrderNo(purchaseOrder.getPurchaseOrderNo());
            List<PurchaseOrderBO> orderBOS = listPurchaseOrderByParam(search);
            if (CollectionUtils.isNotEmpty(orderBOS) && orderBOS.size() > 0) {
                throw new BusinessException("该采购单号已存在，请重新输入！");
            }
            purchaseOrder.setStatus(PurchaseOrderStatus.ACTIVE);
            purchaseOrderService.insert(purchaseOrder);
        } else {
            if (purchaseOrder.getPurchaseOrderNo() == null) {
                throw new BusinessException("编辑采购单时采购单号不能为空");
            }
            // 编辑后重新设置为已创建状态
            purchaseOrder.setStatus(PurchaseOrderStatus.ACTIVE);
            // 编辑
            purchaseOrderService.updateById(purchaseOrder);
            // 删除原先的采购单详细和采购单其他费用
            purchaseOrderDetailService.deleteList(purchaseOrder.getPurchaseOrderNo());
            purchaseOrderOtherChargesService.deleteList(purchaseOrder.getPurchaseOrderNo());
            purchaseOrderService.updateById(purchaseOrder);
        }
        List<PurchaseOrderDetail> purchaseOrderDetails = BeanUtils.copyProperties(bo.getPurchaseOrderDetailList(), PurchaseOrderDetail.class);
        // 2.新增采购单详细
        for (PurchaseOrderDetail purchaseOrderDetail : purchaseOrderDetails) {
            purchaseOrderDetail.setPurchaseOrderNo(purchaseOrder.getPurchaseOrderNo());
            purchaseOrderDetail.setId(null);
            if (StringUtils.isBlank(purchaseOrderDetail.getUserName())) {
                purchaseOrderDetail.setUserName(bo.getUserName());
            }
        }
        purchaseOrderDetailService.insertList(purchaseOrderDetails);
        // 3.新增采购单其他费用
        List<PurchaseOrderOtherChargesBO> chargesList = bo.getPurchaseOrderOtherChargesList();
        if (CollectionUtils.isNotEmpty(chargesList)) {
            List<PurchaseOrderOtherCharges> otherCharges = BeanUtils.copyProperties(chargesList, PurchaseOrderOtherCharges.class);
            for (PurchaseOrderOtherCharges otherCharge : otherCharges) {
                otherCharge.setPurchaseOrderNo(purchaseOrder.getPurchaseOrderNo());
                otherCharge.setId(null);
                if (StringUtils.isBlank(otherCharge.getUserName())) {
                    otherCharge.setUserName(bo.getUserName());
                }
            }
            purchaseOrderOtherChargesService.insertList(otherCharges);
        }
        return Pair.of(true, purchaseOrder.getPurchaseOrderNo());
    }

    public boolean updateApprovalStatus(PurchaseOrderBO bo) {
        PurchaseOrder purchaseOrder = BeanUtils.copyProperties(bo, PurchaseOrder.class);
        return purchaseOrderService.updateById(purchaseOrder);
    }

    public List<PurchaseOrderBO> listPurchaseOrderByParam(PurchaseOrderSearch search) {
        List<PurchaseOrder> purchaseOrder = purchaseOrderService.selectPurchaseOrderListByParam(search);
        if (CollectionUtils.isNotEmpty(purchaseOrder)) {
            return BeanUtils.copyProperties(purchaseOrder, PurchaseOrderBO.class);
        }
        return null;
    }

    /**
     * 检查付款方式相关字段是否有误
     *
     * @param bo
     */
    private void checkPayMethod(PurchaseOrderBO bo) {
        if (bo.getPaymentMethod() == null) {
            throw new BusinessException("付款方式有误，请检查！");
        } else {
            if (bo.getPaymentMethod().equals(PaymentMethod.PAYMENT_DAYS)) {
                PaymentDaysTimeUnit timeUnit = bo.getPaymentDaysTimeUnit();
                if (timeUnit == null) {
                    throw new BusinessException("账期付款方式下账期时间单位不能为空，请检查！");
                }
                if (bo.getPaymentDaysTime() == null) {
                    throw new BusinessException("账期付款方式下账期时间不能为空，请检查！");
                }
            } else if (bo.getPaymentMethod().equals(PaymentMethod.ADVANCE_PAYMENT)) {
                if (bo.getAdvancePaymentRate() == null) {
                    throw new BusinessException("预付款方式下预付款比例不能为空，请检查！");
                }
            }
        }
    }

    public PurchaseOrderBO getPurchaseOrderById(Long id) {
        PurchaseOrder purchaseOrder = purchaseOrderService.selectPurchaseOrderById(id);
        if (purchaseOrder != null) {
            PurchaseOrderDetailSearch detailSearch = new PurchaseOrderDetailSearch();
            detailSearch.setPurchaseOrderNo(purchaseOrder.getPurchaseOrderNo());
            // 封装详细
            List<PurchaseOrderDetail> details = purchaseOrderDetailService.selectPurchaseOrderDetailListByParam(detailSearch);
            PurchaseOrderOtherChargesSearch otherChargesSearch = new PurchaseOrderOtherChargesSearch();
            otherChargesSearch.setPurchaseOrderNo(purchaseOrder.getPurchaseOrderNo());
            List<PurchaseOrderOtherCharges> otherCharges = purchaseOrderOtherChargesService.selectPurchaseOrderOtherChargesListByParam(otherChargesSearch);
            PurchaseOrderBO bo = BeanUtils.copyProperties(purchaseOrder, PurchaseOrderBO.class);
            bo.setPurchaseOrderDetailList(BeanUtils.copyProperties(details, PurchaseOrderDetailBO.class));
            if (CollectionUtils.isNotEmpty(otherCharges)) {
                bo.setPurchaseOrderOtherChargesList(BeanUtils.copyProperties(otherCharges, PurchaseOrderOtherChargesBO.class));
            }
            return bo;
        }
        return null;
    }

    @PageSelect
    public ListVO<PurchaseOrderBO> pageListPurchaseOrderByParam(PurchaseOrderSearch search) {
        List<PurchaseOrder> purchaseOrder = purchaseOrderService.selectPurchaseOrderListByParam(search);
        ListVO<PurchaseOrderBO> listVO = new ListVO<>();
        List<PurchaseOrderBO> bos = BeanUtils.copyProperties(purchaseOrder, PurchaseOrderBO.class);
        listVO.setDataList(bos);
        return listVO;
    }

    public PurchaseOrderBO getPurchaseOrderByPurchaseOrderNo(String purchaseOrderNo) {
        PurchaseOrder purchaseOrder = purchaseOrderService.selectPurchaseOrderByPurchaseOrderNo(purchaseOrderNo);
        return BeanUtils.copyProperties(purchaseOrder, PurchaseOrderBO.class);
    }

    public List<PurchaseOrderBO> getPurchaseOrderList(Long userId) {
        return BeanUtils.copyProperties(purchaseOrderService.getPurchaseOrderList(userId), PurchaseOrderBO.class);
    }

    public List<PurchaseOrderDetailBO> listByPurchaseOrderNoSet(Set<String> sets) {
        return BeanUtils.copyProperties(purchaseOrderDetailService.listByPurchaseOrderNoSet(sets), PurchaseOrderDetailBO.class);
    }

    public boolean updateById(PurchaseOrderBO purchaseOrderBO) {
        return purchaseOrderService.updateById(BeanUtils.copyProperties(purchaseOrderBO, PurchaseOrder.class));
    }

    public List<PurchaseOrderBO> listPurchaseOrder4SupportOrder(Long userId) {
        return BeanUtils.copyProperties(purchaseOrderService.listPurchaseOrder4SupportOrder(userId), PurchaseOrderBO.class);
    }

    /**
     * 采购单自动生成入库单
     * @param purchaseOrderBO
     * @return
     */
    public String buildInOrder(PurchaseOrderBO purchaseOrderBO) {
        // apollo配置某些用户不需要自动生成入库单,用户ID：多个逗号分割
        String dontAutoCreateUsers = tradeNacosConfig.getDontAutoCreateUsers();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(dontAutoCreateUsers)) {
            Set<String> userIdSet = Sets.newHashSet(Arrays.asList(dontAutoCreateUsers.split(",")));
            if (userIdSet.contains(String.valueOf(purchaseOrderBO.getUserId()))) {
                // 存在则不自动生成入库单
                log.info("用户配置了采购单不自动生成入库单，自动跳过！ userIds=" + dontAutoCreateUsers + ";单号=" + purchaseOrderBO.getPurchaseOrderNo());
                return "";
            }
        }
        try {
            // TODO：是否需要判重 - 调拨单和采购单
            InOrderAddParam inOrderFrom = new InOrderAddParam();
            inOrderFrom.setCreateBy(purchaseOrderBO.getCreateBy());
            inOrderFrom.setCreateByName(purchaseOrderBO.getCreateByName());
            inOrderFrom.setUserId(purchaseOrderBO.getUserId());
            inOrderFrom.setUserName(purchaseOrderBO.getUserName());
            inOrderFrom.setType(InOrderType.CG_RK.getValue());
            inOrderFrom.setExpectTime(purchaseOrderBO.getDeliveryDate());
            inOrderFrom.setLogicWarehouseCode(purchaseOrderBO.getLogicWarehouseCode());
            LogicWarehouseResult logicWarehouseResult = logicWarehouseFacade.getDetailByCode(purchaseOrderBO.getLogicWarehouseCode());
            if (Objects.isNull(logicWarehouseResult)) {
                throw new BusinessException("生成入库单失败，" + purchaseOrderBO.getLogicWarehouseCode() + "逻辑仓不存在！");
            }
            inOrderFrom.setInvoiceNo(purchaseOrderBO.getPurchaseInvoiceNo());
            inOrderFrom.setReadyBusinessType(null);
            inOrderFrom.setBusinessNo(purchaseOrderBO.getPurchaseOrderNo());
            inOrderFrom.setTradeType(purchaseOrderBO.getType().getValue());
            inOrderFrom.setApprovalStatus(ApprovalStatus.SP_IS.getValue());
            inOrderFrom.setPlanQuantity(purchaseOrderBO.getPurchaseNum());
            inOrderFrom.setSubjectBill(purchaseOrderBO.getSubjectBill());

            List<InOrderDetailAddParam> inOrderDetailFromList = new ArrayList<>();
            PurchaseOrderDetailSearch search = new PurchaseOrderDetailSearch();
            search.setPurchaseOrderNo(purchaseOrderBO.getPurchaseOrderNo());
            List<PurchaseOrderDetail> orderDetailList = purchaseOrderDetailService.selectPurchaseOrderDetailListByParam(search);
            for (PurchaseOrderDetail orderDetail : orderDetailList) {
                InOrderDetailAddParam inOrderDetailFrom = new InOrderDetailAddParam();
                inOrderDetailFrom.setBillCurrency(orderDetail.getBillCurrency());
                inOrderDetailFrom.setRemark(orderDetail.getRemark());
                inOrderDetailFrom.setPlanQuantity(orderDetail.getGoodsNumber());
                GoodsManagementResult goodsManagementResult = goodsManagementFacade.getDetailByQueryParam(orderDetail.getGoodsCode(), logicWarehouseResult.getOwnerCode(), purchaseOrderBO.getUserId());
                //是否开启批次
                if (goodsManagementResult != null) {
                    inOrderDetailFrom.setModel(goodsManagementResult.getModel());
                    inOrderDetailFrom.setGoodsName(goodsManagementResult.getGoodsName());
                    inOrderDetailFrom.setBatchManagement(goodsManagementResult.getBatchManagement().getValue());
                    inOrderDetailFrom.setSku(goodsManagementResult.getSku());
                    inOrderDetailFrom.setBarcode(goodsManagementResult.getBarcode());
                } else {
                    throw new BusinessException("生成入库单失败，" + orderDetail.getGoodsCode() + "商品不存在");
                }
                inOrderDetailFrom.setGoodsCode(orderDetail.getGoodsCode());
                inOrderDetailFrom.setUnitPrice(orderDetail.getUnitPrice());
                inOrderDetailFrom.setBillCurrency(orderDetail.getBillCurrency());
                inOrderDetailFromList.add(inOrderDetailFrom);
            }
            inOrderFrom.setInOrderDetailParamList(inOrderDetailFromList);
            log.info("采购单生成入库单入参============={}", JSON.toJSONString(inOrderFrom));
            String inOrderNo = optInOrderFacade.addInOrder(inOrderFrom, Boolean.FALSE);
            //是否下发
            return inOrderNo;
        }catch (Exception ex) {
            log.error("采购单生成入库单入参失败！", ex);
        }
        return "";
    }

    public static void main(String[] args) {
        String dontAutoCreateUsers = "100157,23";
        Set userIdSet = Sets.newHashSet(Arrays.asList(dontAutoCreateUsers.split(",")));
        Long userId = 100157L;
        System.out.println(userIdSet.contains(String.valueOf(userId)));
    }
}
