package com.danding.business.server.ares.adjustOrder.BO;

import com.danding.business.common.ares.enums.inventory.InventoryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 调整单来源表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-28
 */

@Data
@ApiModel(value = "AdjustOrderSource对象", description = "调整单来源表查询")
public class AdjustOrderSourceBO implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long updateTime;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * wms调整单号
     */
    @ApiModelProperty(value = "wms调整单号")
    private String relatedNo;

    /**
     * 目标sku
     */
    @ApiModelProperty(value = "目标sku")
    private String sku;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchCode;

    /**
     * 内部批次号
     */
    @ApiModelProperty(value = "内部批次号")
    private String internalBatchCode;

    /**
     * 生产批次号
     */
    @ApiModelProperty(value = "生产批次号")
    private String productBatchCode;

    /**
     * 目标入库日期
     */
    @ApiModelProperty(value = "目标入库日期")
    private Long inOrderDate;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    private Long productionDate;

    /**
     * 过期日期
     */
    @ApiModelProperty(value = "过期日期")
    private Long expireDate;

    /**
     * 调整数量
     */
    @ApiModelProperty(value = "调整数量")
    private Integer adjustNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 行号
     */
    private Integer lineNo;
    /**
     * 正次品
     */
    private InventoryType inventoryType;

    private String inventoryTypeName;


    /**
     * 品牌名称
     */
    @ApiModelProperty(value = "品牌名称")
    private String brandName;


    private String goodsCode;


    private String goodsName;


    /**
     * 条形码
     */
    @ApiModelProperty(value = "条形码")
    private String barcode;


}
