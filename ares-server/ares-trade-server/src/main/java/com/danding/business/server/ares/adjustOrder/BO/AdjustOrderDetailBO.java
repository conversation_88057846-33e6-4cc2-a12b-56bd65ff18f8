package com.danding.business.server.ares.adjustOrder.BO;

import com.danding.business.common.ares.enums.adjustOrder.AdjustType;
import com.danding.business.common.ares.enums.common.AdjustReasonsEnum;
import com.danding.business.common.ares.enums.common.ResponsibilityType;
import com.danding.business.common.ares.enums.inventory.InventoryType;
import com.danding.component.common.base.DO.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 调整单详细表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-28
 */

@Data
@ApiModel(value = "AdjustOrderDetail对象", description = "调整单详细表查询")
public class AdjustOrderDetailBO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long updateTime;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 调整单号
     */
    @ApiModelProperty(value = "调整单号")
    private String adjustOrderNo;

    /**
     * 正次品
     */
    @ApiModelProperty(value = "正次品")
    private InventoryType inventoryType;

    /**
     * 货品Id
     */
    @ApiModelProperty(value = "货品Id")
    private String goodsCode;

    /**
     * 目标sku
     */
    @ApiModelProperty(value = "目标sku")
    private String sku;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchCode;

    /**
     * 内部批次号
     */
    @ApiModelProperty(value = "内部批次号")
    private String internalBatchCode;

    /**
     * 生产批次号
     */
    @ApiModelProperty(value = "生产批次号")
    private String productBatchCode;

    /**
     * 目标入库日期
     */
    @ApiModelProperty(value = "目标入库日期")
    private Long inOrderDate;

    /**
     * 逻辑仓库编码
     */
    @ApiModelProperty(value = "逻辑仓库编码")
    private String logicWarehouseCode;

    /**
     * 逻辑仓库名称
     */
    @ApiModelProperty(value = "逻辑仓库名称")
    private String logicWarehouseName;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    private Long productionDate;

    /**
     * 过期日期
     */
    @ApiModelProperty(value = "过期日期")
    private Long expireDate;

    /**
     * 调整数量
     */
    @ApiModelProperty(value = "调整数量")
    private Integer adjustNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 行号
     */
    private Integer lineNo;

    /**
     * 0:父级 1:子级
     */
    private Integer parent;

    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 可用库存
     */
    private Integer availableNum;

    /**
     * 锁定库存
     */
    private Integer lockedNum;


    private List<AdjustOrderDetailBO> adjustOrderDetailBOList;


    private String taskId;


    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间")
    private Long instockTime;

    /**
     * 调整类型
     */
    @ApiModelProperty(value = "调整类型")
    private String adjustTypeName;
    private AdjustType adjustType;

    /**
     * 证明材料
     */
    private String certifyingMaterials;

    /**
     * 责任类型
     */
    private ResponsibilityType responsibilityType;

    /**
     * 盘点原因
     */
    private AdjustReasonsEnum adjustReasons;
}
