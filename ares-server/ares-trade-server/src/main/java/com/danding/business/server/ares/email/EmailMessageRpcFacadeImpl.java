package com.danding.business.server.ares.email;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.ares.owner.facade.IOwnerFacade;
import com.danding.business.client.ares.owner.result.OwnerResult;
import com.danding.business.client.rpc.email.facade.IEmailMessageRpcFacade;
import com.danding.business.client.rpc.email.param.EmailParam;
import com.danding.business.server.ares.config.TradeNacosConfig;
import com.danding.business.server.ares.tallyReport.utils.EmailAttachmentFileDto;
import com.danding.business.server.ares.tallyReport.utils.EmailSenderConfig;
import com.danding.business.server.ares.tallyReport.utils.EmailUtils;
import com.danding.soul.client.common.result.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@DubboService
public class EmailMessageRpcFacadeImpl implements IEmailMessageRpcFacade {

    @Autowired
    private EmailUtils emailUtils;
    @DubboReference
    private IOwnerFacade iOwnerFacade;
    @Autowired
    private TradeNacosConfig tradeNacosConfig;

    @Override
    public RpcResult sendEmail(EmailParam emailParam) {
        log.info("[EmailMessageRpcFacadeImpl-sendEmail]=========邮件发送入参=========", JSON.toJSONString(emailParam));
        try {
            //收件人
            String[] arrEmail;
            List<String> receiveMail = emailParam.getReceiveMail();
            if (CollectionUtil.isEmpty(receiveMail) && StringUtils.isEmpty(emailParam.getOwnerCode())) {
                return RpcResult.error("收件人邮箱或者货主编码不能同时为空!");
            }
            if (CollectionUtil.isEmpty(receiveMail)) {
                OwnerResult ownerResult = iOwnerFacade.getByCode(emailParam.getOwnerCode());
                if (Objects.isNull(ownerResult)) {
                    return RpcResult.error(emailParam.getOwnerCode() + ":货主不存在!");
                }
                if (StringUtils.isBlank(ownerResult.getAuditMail())) {
                    return RpcResult.error(emailParam.getOwnerCode() + ":货主未配置收件人邮箱!");
                }
                arrEmail = ownerResult.getAuditMail().split(",");
            } else {
                arrEmail = receiveMail.toArray(new String[receiveMail.size()]);
            }

            //发邮件配置从apollo捞取
            EmailSenderConfig emailSenderConfig = new EmailSenderConfig();
            String emailSendJson = tradeNacosConfig.getEmailSendJson();
            if (StringUtils.isNotBlank(emailSendJson)) {
                Map<String, String> parseObject = JSON.parseObject(emailSendJson, Map.class);
                emailSenderConfig.setUserName(parseObject.get("u"));
                emailSenderConfig.setPassword(parseObject.get("w"));
                emailSenderConfig.setPersonal(parseObject.get("p"));
            } else {
                return RpcResult.error("请先配置发件人信息");
            }
            boolean result = emailUtils.sendEmail(emailSenderConfig, arrEmail, null, emailParam.getSubject(), emailParam.getContent(), addAttachment(emailParam));
            return RpcResult.isSuccess(result, "邮件发送失败!");
        } catch (Exception e) {
            log.error("[EmailMessageRpcFacadeImpl-sendEmail]=========邮件发送失败=========ex:", e);
            return RpcResult.error("邮件发送失败!");
        }
    }

    /**
     * 添加附件
     *
     * @param emailParam
     */
    private List<EmailAttachmentFileDto> addAttachment(EmailParam emailParam) {
        List<EmailAttachmentFileDto> attachmentFileList = new ArrayList<>();

        if (StringUtils.isNotBlank(emailParam.getAttachmentName()) && StringUtils.isNotBlank(emailParam.getAttachmentUrl())) {
            EmailAttachmentFileDto emailAttachmentFileDto = new EmailAttachmentFileDto();
            emailAttachmentFileDto.setAttachmentFilename(emailParam.getAttachmentName());
            emailAttachmentFileDto.setInputStreamSource(emailUtils.getByteFromURL(emailParam.getAttachmentUrl()));
            attachmentFileList.add(emailAttachmentFileDto);
        }
        return attachmentFileList;
    }

}

