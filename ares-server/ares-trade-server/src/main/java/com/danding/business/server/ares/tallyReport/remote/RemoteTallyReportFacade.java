package com.danding.business.server.ares.tallyReport.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.business.client.ares.customer.facade.ICustomerFacade;
import com.danding.business.client.ares.customer.result.CustomerResult;
import com.danding.business.client.ares.distributionorder.facade.IDistributionOrderFacade;
import com.danding.business.client.ares.distributionorder.param.DistributionOrderQueryParam;
import com.danding.business.client.ares.distributionorder.param.DistributionOrderUpdateParam;
import com.danding.business.client.ares.distributionorder.result.DistributionOrderResult;
import com.danding.business.client.ares.entitywarehouse.facade.IEntityWarehouseFacade;
import com.danding.business.client.ares.entitywarehouse.result.EntityWarehouseResult;
import com.danding.business.client.ares.flow.facade.IFlowFacade;
import com.danding.business.client.ares.flow.param.FlowInstanceAddParam;
import com.danding.business.client.ares.flow.param.FlowTaskQueryParam;
import com.danding.business.client.ares.flow.result.FlowInstanceResult;
import com.danding.business.client.ares.flow.result.FlowTaskResult;
import com.danding.business.client.ares.flow.result.TargetFlowResult;
import com.danding.business.client.ares.goods.facade.IGoodsFacade;
import com.danding.business.client.ares.goodsManagement.facade.IGoodsManagementFacade;
import com.danding.business.client.ares.goodsManagement.result.GoodsManagementResult;
import com.danding.business.client.ares.logicwarehouse.facade.ILogicWarehouseFacade;
import com.danding.business.client.ares.logicwarehouse.result.LogicWarehouseResult;
import com.danding.business.client.ares.mapping.facade.MappingFacade;
import com.danding.business.client.ares.mapping.param.MappingParam;
import com.danding.business.client.ares.mapping.param.out.TallyOrderMappingParam;
import com.danding.business.client.ares.mapping.result.MappingResult;
import com.danding.business.client.ares.order.facade.IInOrderFacade;
import com.danding.business.client.ares.order.facade.IOutOrderDetailFacade;
import com.danding.business.client.ares.order.facade.IOutOrderFacade;
import com.danding.business.client.ares.order.result.InOrderDetailResult;
import com.danding.business.client.ares.order.result.InOrderResult;
import com.danding.business.client.ares.order.result.OutOrderResult;
import com.danding.business.client.ares.owner.facade.IOwnerFacade;
import com.danding.business.client.ares.owner.result.OwnerResult;
import com.danding.business.client.ares.readyorder.facade.IReadyOrderFacade;
import com.danding.business.client.ares.readyorder.param.ReadyOrderQueryParam;
import com.danding.business.client.ares.readyorder.result.ReadyOrderResult;
import com.danding.business.client.ares.record.facade.IGoodsRecordFacade;
import com.danding.business.client.ares.record.result.GoodsRecordResult;
import com.danding.business.client.ares.report.facade.IUserBillsFacade;
import com.danding.business.client.ares.report.param.UserBillsParam;
import com.danding.business.client.ares.sale.facade.ISaleOrderFacade;
import com.danding.business.client.ares.supplier.facade.ISupplierFacade;
import com.danding.business.client.ares.supplier.result.SupplierResult;
import com.danding.business.client.ares.tallyReport.message.TallyReportCallBackMessage;
import com.danding.business.common.ares.enums.common.DocumentType;
import com.danding.business.common.ares.enums.common.OnlineCustomsStatus;
import com.danding.business.common.ares.enums.common.TallReportAuditType;
import com.danding.business.common.ares.enums.common.TradeType;
import com.danding.business.common.ares.enums.goods.GoodsType;
import com.danding.business.common.ares.enums.order.InOrderType;
import com.danding.business.common.ares.enums.order.OrderNodeStatus;
import com.danding.business.common.ares.enums.report.BillType;
import com.danding.business.common.ares.enums.report.BusinessType;
import com.danding.business.common.ares.enums.report.BusinessWithType;
import com.danding.business.common.ares.enums.trade.DistributionOrderStatus;
import com.danding.business.common.ares.enums.trade.TallyReportStatus;
import com.danding.business.common.ares.enums.trade.TallyReportType;
import com.danding.business.common.ares.utils.Assert;
import com.danding.business.core.ares.distributionorder.search.DistributionGoodsSearch;
import com.danding.business.core.ares.readyorder.search.ReadyGoodsSearch;
import com.danding.business.core.ares.tallyReport.entity.TallyReportDetail;
import com.danding.business.core.ares.tallyReport.search.TallyReportDetailSearch;
import com.danding.business.core.ares.tallyReport.service.ITallyReportDetailBatchService;
import com.danding.business.core.ares.tallyReport.service.ITallyReportDetailService;
import com.danding.business.rpc.client.scf.risk.facade.IRegulatoryScfRpcFacade;
import com.danding.business.server.ares.config.TradeNacosConfig;
import com.danding.business.server.ares.distributionorder.BO.DistributionGoodsBO;
import com.danding.business.server.ares.distributionorder.manager.DistributionGoodsManager;
import com.danding.business.server.ares.readyorder.BO.ReadyGoodsBO;
import com.danding.business.server.ares.readyorder.manager.ReadyGoodsManager;
import com.danding.business.server.ares.remote.RemoteOrderFacade;
import com.danding.business.server.ares.tallyReport.BO.TallyReportBO;
import com.danding.business.server.ares.tallyReport.BO.TallyReportDetailBO;
import com.danding.cds.out.api.InventoryOrderRpc;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.req.TallyReportReqVO;
import com.danding.cds.out.bean.vo.res.TallyReportDetailReqVo;
import com.danding.component.common.api.common.file.FileDto;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.rocketmq.message.SpringMessage;
import com.danding.component.rocketmq.producer.SpringRocketMQProducer;
import com.danding.soul.client.common.exception.BusinessException;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.danding.business.common.ares.context.AresContext.*;

/**
 * <p>
 * 理货报告表 调用其他服务
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-22
 */
@Component
@Slf4j
public class RemoteTallyReportFacade {
    @DubboReference
    private IFlowFacade flowFacade;
    @DubboReference
    private ISupplierFacade supplierFacade;
    @DubboReference
    private ICustomerFacade customerFacade;
    @DubboReference
    private IGoodsFacade goodsFacade;
    @DubboReference
    private IGoodsManagementFacade goodsManagementFacade;
    @DubboReference
    private IUserBillsFacade userBillsFacade;
    @DubboReference
    private IInOrderFacade inOrderFacade;
    @DubboReference
    private IOutOrderFacade outOrderFacade;
    @DubboReference
    private ILogicWarehouseFacade logicWarehouseFacade;
    @DubboReference
    private ISaleOrderFacade saleOrderFacade;
    @DubboReference
    private IOutOrderDetailFacade outOrderDetailFacade;
    @DubboReference
    private IEntityWarehouseFacade entityWarehouseFacade;
    @DubboReference
    private MappingFacade mappingFacade;
    @DubboReference
    private IOwnerFacade ownerFacade;
    @DubboReference
    private InventoryOrderRpc inventoryOrderRpc;
    @DubboReference
    private IGoodsRecordFacade goodsRecordFacade;
    @Autowired
    private ITallyReportDetailService tallyReportDetailService;
    @Autowired
    private ITallyReportDetailBatchService tallyReportDetailBatchService;
    @Autowired
    private RemoteOrderFacade remoteOrderFacade;
    @Autowired
    private SpringRocketMQProducer springRocketMQProducer;
    @DubboReference
    private IReadyOrderFacade readyOrderFacade;
    @DubboReference
    private IDistributionOrderFacade distributionOrderFacade;
    @Autowired
    private DistributionGoodsManager distributionGoodsManager;
    @Autowired
    private ReadyGoodsManager readyGoodsManager;
    @DubboReference
    private IRegulatoryScfRpcFacade regulatoryScfRpcFacade;
    @Autowired
    private TradeNacosConfig tradeNacosConfig;

    public SupplierResult getSupplier(String supplierCode) {
        return supplierFacade.getSupplierDetailBySupplierCode(supplierCode);
    }

    public CustomerResult geCustomer(String customerCode) {
        return customerFacade.getCustomerByCustomerCode(customerCode);
    }

    public GoodsManagementResult getGoods(String goodsCode, String ownerCode, Long userId) {
        return goodsManagementFacade.getDetailByQueryParam(goodsCode, ownerCode, userId);
    }

    public boolean saveBills(TallyReportBO tallyReportBO) {
        createBills(tallyReportBO, BillType.BILL_OUT, BusinessWithType.WITH_TYPE_WAREHOUSE, tallyReportBO.getLogicWarehouseCode(), tallyReportBO.getLogicWarehouseName());
        return true;
    }

    private void createBills(TallyReportBO tallyReportBO, BillType billType, BusinessWithType businessWithType, String code, String name) {
        UserBillsParam param = new UserBillsParam();
        param.setBillType(billType);
        param.setUserId(tallyReportBO.getUserId());
        param.setUserName(tallyReportBO.getUserName());
        param.setBillBusinessType(BusinessType.BUSINESS_TYPE_TALLY);
        param.setBillWithType(businessWithType);
        param.setBillWithCode(code);
        param.setBillWithName(name);
        BigDecimal tallyAmount = tallyReportBO.getTallyAmount();
        if (Objects.nonNull(tallyReportBO.getSupplementAmount())) {
            tallyAmount = tallyAmount.add(tallyReportBO.getSupplementAmount());
        }
        param.setBillAmount(tallyAmount);
        param.setBillCurrency("RMB");
        param.setCurrencyRate(BigDecimal.valueOf(1));
        param.setRelatedBillNo(tallyReportBO.getTallyOrderNo());
        param.setRelatedUserId(tallyReportBO.getCreateBy());
        param.setRelatedUserName(tallyReportBO.getTallyPersonnel());
        param.setRelatedRemark(tallyReportBO.getRemark());
        param.setRelatedCreateTime(tallyReportBO.getCreateTime());
        log.info("生成账单参数:{}", JSON.toJSONString(param));
        userBillsFacade.saveBill(param);
    }

    /**
     * 提交审核
     *
     * @param tallyReportBO
     * @return
     */
    public FlowInstanceResult startFlow(TallyReportBO tallyReportBO, DocumentType documentType) {
        FlowInstanceAddParam flowInstanceAddParam = new FlowInstanceAddParam();
        flowInstanceAddParam.setUserId(tallyReportBO.getUserId());
        flowInstanceAddParam.setBusinessId(tallyReportBO.getTallyOrderNo());
        flowInstanceAddParam.setDocumentType(documentType);
        if (tallyReportBO.getCreateBy() == null) {
            flowInstanceAddParam.setSubmitUserId(tallyReportBO.getUserId());
        } else {
            flowInstanceAddParam.setSubmitUserId(tallyReportBO.getCreateBy());
        }
        return flowFacade.startFlow(flowInstanceAddParam);
    }

    /**
     * 获取流程配置信息
     *
     * @param userId
     * @param documentType
     * @return
     */
    public TargetFlowResult getTargetFlow(Long userId, DocumentType documentType) {
        return flowFacade.getTargetFlow(userId, documentType);
    }

    public FlowTaskResult selectFlowTask(String tallyOrderNo, DocumentType documentType, Long userId) {
        FlowTaskQueryParam flowTaskQueryParam = new FlowTaskQueryParam();
        flowTaskQueryParam.setBusinessId(tallyOrderNo);
        flowTaskQueryParam.setDocumentType(documentType);
        flowTaskQueryParam.setOperator(userId);
        return flowFacade.userTaskByBusiness(flowTaskQueryParam);
    }

    public FlowTaskResult selectFlowTaskV2(String tallyOrderNo, DocumentType documentType, Long userId) {
        FlowTaskQueryParam flowTaskQueryParam = new FlowTaskQueryParam();
        flowTaskQueryParam.setBusinessId(tallyOrderNo);
        flowTaskQueryParam.setDocumentType(documentType);
        flowTaskQueryParam.setOperator(userId);
        return flowFacade.userTaskByBusinessV2(flowTaskQueryParam);
    }

    public InOrderResult getInOrder(String relatedBusinessNo, Long userId) {
        return inOrderFacade.getInOrderByOrderNo(relatedBusinessNo, userId);
    }

    public OutOrderResult getOutOrder(String relatedBusinessNo, Long userId) {
        return outOrderFacade.getOutOrderByOrderNo(relatedBusinessNo, userId);
    }

    public List<InOrderDetailResult> getInOrderDetail(String relatedBusinessNo, Long userId) {
        try {
            InOrderResult inOrderResult = inOrderFacade.getInOrderDetailByOrderNo(relatedBusinessNo, userId, null);
            return inOrderResult.getDetailResultList();
        } catch (Exception e) {
            log.error("查询入库单有误：{}", e);
            return null;
        }
    }

    /**
     * 理货报告回传处理
     *
     * @param tallyReportBO
     * @return
     */
    public boolean doCallback(TallyReportBO tallyReportBO) {
        log.info("RemoteTallyReportFacade#callback {}", JSON.toJSONString(tallyReportBO));
        String checkSource = "（内部）";
        if (TallReportAuditType.UPSTREAM_SYSTEM_AUDIT.equals(tallyReportBO.getTallReportAuditType())) {
            checkSource = "（上游系统）";
        } else if (TallReportAuditType.INTERNAL_AUDIT_AUTO.equals(tallyReportBO.getTallReportAuditType())) {
            checkSource = "（系统自动）";
        }
        //节点
        remoteOrderFacade.sendInOrderNode(tallyReportBO.getRelatedBusinessNo(), OrderNodeStatus.IN_ORDER_TALLY_AUDIT, tallyReportBO.getApprovalStatus().getDes() + checkSource, System.currentTimeMillis(), Boolean.TRUE);
        if (!"wms".equals(tallyReportBO.getSource())) {
            return true;
        }
        if (!Objects.equals(SYSTEM_TAG_TAOTIAN, tallyReportBO.getSystemTag())) {
            //审核成功，更新配货单为待清关
            if (TallyReportType.CK.equals(tallyReportBO.getTallyType()) && TallyReportStatus.SP_SUCCESS.equals(tallyReportBO.getApprovalStatus())) {
                OutOrderResult outOrderResult = this.getOutOrder(tallyReportBO.getRelatedBusinessNo(), tallyReportBO.getUserId());
                if (Objects.nonNull(outOrderResult) && StringUtils.isNotBlank(outOrderResult.getDistributionNo())) {
                    DistributionOrderUpdateParam distributionOrderUpdateParam = new DistributionOrderUpdateParam();
                    distributionOrderUpdateParam.setDistributionNo(outOrderResult.getDistributionNo());
                    distributionOrderUpdateParam.setOrderStatus(DistributionOrderStatus.CUSTOMER_ING);
                    distributionOrderFacade.ccsUpdateOrder(distributionOrderUpdateParam);
                }
            }
            //审核成功,通知SCF
            if (TallyReportType.RK.equals(tallyReportBO.getTallyType()) && TallyReportStatus.SP_SUCCESS.equals(tallyReportBO.getApprovalStatus()) && StringUtils.isNotBlank(tallyReportBO.getRelatedBusinessNo())) {
                InOrderResult inOrderResult = this.getInOrder(tallyReportBO.getRelatedBusinessNo(), tallyReportBO.getUserId());
                if (Objects.nonNull(inOrderResult) && Objects.equals(SYSTEM_SCF, inOrderResult.getOrigSystem()) && Objects.equals(InOrderType.DC_RK.getCode(), inOrderResult.getType())) {
                    boolean result = regulatoryScfRpcFacade.backTallyReportStatus(inOrderResult.getBusinessNo());
                    if (!result) {
                        throw new BusinessException(inOrderResult.getBusinessNo() + ":需融资方先确认监管单!");
                    }
                }
            }

            //先推给ccs
            if (!callbackCcs(tallyReportBO)) {
                return Boolean.FALSE;
            }
        }
        TallyOrderMappingParam tallyOrderMappingParam = new TallyOrderMappingParam();
        tallyOrderMappingParam.setDownstreamNo(tallyReportBO.getDownstreamNo());
        tallyOrderMappingParam.setApprovalStatus(tallyReportBO.getApprovalStatus().getValue());
        tallyOrderMappingParam.setWarehouseCode(tallyReportBO.getWarehouseCode());
        tallyOrderMappingParam.setRemark(tallyReportBO.getOperatorRemark());
        tallyOrderMappingParam.setOutTallyOrderNo(tallyReportBO.getOutTallyOrderNo());
        MappingParam mappingParam = MappingParam.of(tallyOrderMappingParam.getDownstreamNo(), SYSTEM_ERP,
                SYSTEM_DT,
                "28",
                1,
                JSON.toJSONString(tallyOrderMappingParam));
        log.info("理货报告下发mapping数据:{}", JSON.toJSONString(mappingParam));
        MappingResult result = mappingFacade.outExecute(mappingParam);
        log.info("理货报告下发mapping result:{}", JSON.toJSONString(result));
        if (result.isSuccess()) {
            //节点
            remoteOrderFacade.sendInOrderNode(tallyReportBO.getRelatedBusinessNo(), OrderNodeStatus.IN_ORDER_TALLY_AUDIT_BACK_WAREHOUSE, "理货报告审核下发WMS成功", System.currentTimeMillis(), Boolean.FALSE);
            return true;
        } else {
            //节点
            remoteOrderFacade.sendInOrderNode(tallyReportBO.getRelatedBusinessNo(), OrderNodeStatus.IN_ORDER_TALLY_AUDIT_BACK_WAREHOUSE, "失败:wms操作失败", System.currentTimeMillis(), Boolean.TRUE);
            log.error("[ApprovalStatusChangeConsumer-onMessage]==========理货报告回传wms失败=========message:" + JSON.toJSONString(tallyReportBO));
            throw new BusinessException(result.getMessage());
        }
    }

    /**
     * 理货报告回传wms和ccs
     *
     * @param tallyReportBO
     * @return
     */
    public boolean callback(TallyReportBO tallyReportBO) {
        return sendMessage(tallyReportBO);
    }

    /**
     * 理货报告回传ccs
     *
     * @param tallyReportBO
     * @param addDraftAttachmentFile
     * @return
     */
    public boolean callbackCcs(TallyReportBO tallyReportBO, boolean addDraftAttachmentFile) {
        Boolean nonBonded = Objects.equals(1, tallyReportBO.getNonBonded());// 非保

        log.info("RemoteTallyReportFacade#callbackCcs {}", JSON.toJSONString(tallyReportBO));
        if (StringUtils.isBlank(tallyReportBO.getRelatedBusinessNo())) {//没有关联单号,直接返回
            return Boolean.TRUE;
        }
        //上游系统审核不会推送ccs TODO 字节接入关仓协同审核流程
//        if (TallReportAuditType.UPSTREAM_SYSTEM_AUDIT.equals(tallyReportBO.getTallReportAuditType())) {
//            return true;
//        }
        //只有审核成功、待审核、已完成才推送给ccs
        if (!TallyReportStatus.SP_SUCCESS.equals(tallyReportBO.getApprovalStatus()) && !TallyReportStatus.SP_IS.equals(tallyReportBO.getApprovalStatus()) && !TallyReportStatus.SP_FINISH.equals(tallyReportBO.getApprovalStatus())) {
            return true;
        }
        //只有入库/出库类型才推送给ccs
        if (!TallyReportType.RK.equals(tallyReportBO.getTallyType()) && !TallyReportType.CK.equals(tallyReportBO.getTallyType())) {
            return true;
        }
        LogicWarehouseResult logicWarehouseResult = logicWarehouseFacade.getDetailByCode(tallyReportBO.getLogicWarehouseCode());
        if (logicWarehouseResult == null) {
            throw new BusinessException("当前云仓不存在【" + tallyReportBO.getLogicWarehouseCode() + "】");
        }
        if (!Objects.equals(TradeType.BONDED, logicWarehouseResult.getTradeType()) && !nonBonded) {
            //完税品，无需回传ccs
            return true;
        }
        //剔除完税品
        if (CollectionUtils.isEmpty(tallyReportBO.getTallyReportDetailList())) {
            TallyReportDetailSearch tallyReportDetailSearch = new TallyReportDetailSearch();
            tallyReportDetailSearch.setTallyOrderNo(tallyReportBO.getTallyOrderNo());
            List<TallyReportDetail> tallyReportDetails = tallyReportDetailService.selectListByTallyReportDetailSearch(tallyReportDetailSearch);
            tallyReportBO.setTallyReportDetailList(BeanUtils.copyProperties(tallyReportDetails, TallyReportDetailBO.class));
        }
        if (CollectionUtils.isEmpty(tallyReportBO.getTallyReportDetailList())) {
            throw new BusinessException("查询理货明细为空!");
        }

        List <TallyReportDetail> moreDetail = new ArrayList<>();
        List<TallyReportDetailReqVo> collect = tallyReportBO.getTallyReportDetailList().stream().filter(tallyReportDetailBO -> Objects.equals(GoodsType.BONDED, tallyReportDetailBO.getGoodsType()) || nonBonded).map(tallyReportDetailBO -> {
            TallyReportDetailReqVo reportDetailReqVo = BeanUtils.copyProperties(tallyReportDetailBO, TallyReportDetailReqVo.class);
            if (StringUtils.isBlank(tallyReportDetailBO.getMaterialCode())) {
                if (nonBonded) {
                    tallyReportDetailBO.setMaterialCode(tallyReportDetailBO.getGoodsCode());
                } else {
                    GoodsRecordResult goodsRecordResult = goodsRecordFacade.getGoodsRecordByAccountCode(tallyReportBO.getUserId(), logicWarehouseResult.getAccountCode(), logicWarehouseResult.getPort(), reportDetailReqVo.getGoodsCode());
                    tallyReportDetailBO.setMaterialCode(goodsRecordResult.getMaterialCode());
                }
                moreDetail.add(BeanUtils.copyProperties(tallyReportDetailBO, TallyReportDetail.class));
            }
            reportDetailReqVo.setProductId(tallyReportDetailBO.getMaterialCode());
            return reportDetailReqVo;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(collect)) {
            return Boolean.TRUE;
        }
        if (StringUtils.isBlank(logicWarehouseResult.getAccountCode())) {
            throw new BusinessException("当前云仓没有账册号，无法查询备案信息");
        }
        if (StringUtils.isBlank(logicWarehouseResult.getPort())) {
            throw new BusinessException("当前云仓没有口岸，无法查询备案信息");
        }
        //线上清关才要推给ccs
        EntityWarehouseResult entityWarehouseResult = entityWarehouseFacade.getDetailByCode(logicWarehouseResult.getEntityWarehouseCode());
        if (Objects.isNull(entityWarehouseResult)) {
            throw new BusinessException("当前实体仓不存在entityWarehouseCode:" + logicWarehouseResult.getEntityWarehouseCode());
        }
        //非关仓协同 && 不是非保
        if (!OnlineCustomsStatus.OPEN.equals(entityWarehouseResult.getOnlineCustomsStatus()) && !nonBonded) {
            return true;
        }
        TallyReportReqVO tallyReportReqVO = new TallyReportReqVO();
        tallyReportReqVO.setActualOutCompanyName(tallyReportBO.getLesseeEnterprise());
        tallyReportReqVO.setInOutOrderNo(tallyReportBO.getRelatedBusinessNo());
        // 1:入库 2：出库
        if (TallyReportType.CK.equals(tallyReportBO.getTallyType())) {
            tallyReportReqVO.setType("2");
            //查询出库单
            OutOrderResult outOrderResult = outOrderFacade.getOutOrderByOrderNo(tallyReportBO.getRelatedBusinessNo(), tallyReportBO.getUserId());
            if (Objects.nonNull(outOrderResult) && Objects.equals(6, outOrderResult.getIsPush())) {
                return Boolean.TRUE;
            }
        } else if (TallyReportType.RK.equals(tallyReportBO.getTallyType())) {
            tallyReportReqVO.setType("1");
            //查询入库单
            InOrderResult inOrderResult = inOrderFacade.getInOrderByOrderNo(tallyReportBO.getRelatedBusinessNo(), tallyReportBO.getUserId());
            if (Objects.nonNull(inOrderResult) && Objects.equals(6, inOrderResult.getIsPush())) {
                return Boolean.TRUE;
            }
        }
        tallyReportReqVO.setTallyOrderNo(tallyReportBO.getTallyOrderNo());
        tallyReportReqVO.setRemark(tallyReportBO.getRemark());
        tallyReportReqVO.setUpstreamNo(tallyReportBO.getUpstreamNo());
        tallyReportReqVO.setTallyTotalQty(tallyReportBO.getTallyNum());
        tallyReportReqVO.setTallyFinishTime(tallyReportBO.getTallyTime());
        tallyReportReqVO.setWarehouseTime(tallyReportBO.getWarehouseTime());
        tallyReportReqVO.setPalletsNums(tallyReportBO.getTorr());
        if (!CollectionUtils.isEmpty(tallyReportBO.getDraftAttachmentFile())) {
            for (Object ob : tallyReportBO.getDraftAttachmentFile()) {
                if (ob instanceof JSONObject) {
                    JSONObject attachment = (JSONObject) ob;
                    tallyReportReqVO.setAttachmentName(attachment.getString("name"));
                    tallyReportReqVO.setAttachmentUrl(attachment.getString("key"));
                } else {
                    FileDto fileDto = (FileDto) ob;
                    tallyReportReqVO.setAttachmentName(fileDto.getName());
                    tallyReportReqVO.setAttachmentUrl(fileDto.getKey());
                }
                break;
            }
        }
        //1:待审核 2：审核成功
        if (TallyReportStatus.SP_IS.equals(tallyReportBO.getApprovalStatus())) {
            tallyReportReqVO.setStatus(1);
        } else if (TallyReportStatus.SP_SUCCESS.equals(tallyReportBO.getApprovalStatus())) {
            tallyReportReqVO.setStatus(2);
        }
        //强制上传草单
        if (addDraftAttachmentFile) {
            tallyReportReqVO.setStatus(3);
        }

        //同一个goodsCode进行合并
        Map<String, TallyReportDetailReqVo> tallyReportDetailReqVoMap = collect.stream()
                .collect(Collectors.toMap(item -> item.getGoodsCode() + item.getTallyOrderNo(), o -> o, (k1, k2) -> {
                    k1.setTallyNum(k1.getTallyNum() + k2.getTallyNum());
                    return k1;
                }));
        tallyReportReqVO.setTallyDetailList(new ArrayList<>(tallyReportDetailReqVoMap.values()));
        try {
            log.info("RemoteTallyReportFacade#callbackCcs receiveTallyReport {}", JSON.toJSONString(tallyReportReqVO));
            RpcResult<Boolean> booleanRpcResult = inventoryOrderRpc.receiveTallyReport(tallyReportReqVO);
            log.info("RemoteTallyReportFacade#callbackCcs receiveTallyReport inOutOrderNo:{},result {}", tallyReportReqVO.getInOutOrderNo(), booleanRpcResult);
            if (booleanRpcResult.getCode().equals(200)) {
                if (TallyReportType.CK.equals(tallyReportBO.getTallyType()) && TallyReportStatus.SP_SUCCESS.equals(tallyReportBO.getApprovalStatus())) {
                    remoteOrderFacade.sendOutOrderNode(tallyReportBO.getRelatedBusinessNo(), OrderNodeStatus.OUT_TALLIED_NUCLEAR_NODE, "CCS，完成理货，发起清关核注", System.currentTimeMillis(), Boolean.TRUE);
                }
                //更新超品的料号
                if (!CollectionUtils.isEmpty(moreDetail)) {
                    tallyReportDetailService.updateListById(moreDetail);
                }
                return true;
            }
            throw new BusinessException("ccs提交理货报告数据失败:" + booleanRpcResult.getMessage());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("callbackCcs error", e);
            throw new BusinessException("ccs提交理货报告数据失败:" + e.getMessage());
        }
    }

    /**
     * 理货报告回传ccs
     *
     * @param tallyReportBO
     * @return
     */
    public boolean callbackCcs(TallyReportBO tallyReportBO) {
        try {
            return callbackCcs(tallyReportBO, false);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("[RemoteTallyReportFacade-callbackCcs]===========理货报告回传ccs异常=============ex:", e);
            throw new BusinessException("理货报告回传ccs异常!");
        }
    }

    public EntityWarehouseResult getEntityWarehouseByCode(String entityWarehouseCode) {
        return entityWarehouseFacade.getDetailByCode(entityWarehouseCode);
    }

    public EntityWarehouseResult getWarehouseByCode(String warehouseCode) {
        List<EntityWarehouseResult> entityWarehouseResults = entityWarehouseFacade.listWarehouseByCode(warehouseCode);
        if (CollectionUtils.isEmpty(entityWarehouseResults)) {
            return null;
        }
        return entityWarehouseResults.get(0);
    }

    public LogicWarehouseResult getLogicWarehouseByCode(String logicWarehouseCode) {
        try {
            LogicWarehouseResult logicWarehouseResult = logicWarehouseFacade.getDetailByCode(logicWarehouseCode);
            Assert.isTrue(logicWarehouseResult != null, "逻辑仓编码有误:" + logicWarehouseCode);
            return logicWarehouseResult;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询逻辑仓有误：" + logicWarehouseCode, e);
            throw new BusinessException("查询逻辑仓有误：" + logicWarehouseCode);
        }
    }

    /**
     * 查询货主
     *
     * @param ownerCode
     * @return
     */
    public OwnerResult getOwnerByCode(String ownerCode) {
        OwnerResult ownerResult = ownerFacade.getByCode(ownerCode);
        if (Objects.isNull(ownerResult)) {
            throw new BusinessException("当前货主不存在：" + ownerCode);
        }
        return ownerResult;
    }

    /**
     * 发送理货报告回传消息
     *
     * @param tallyReportBO
     * @return
     */
    public boolean sendMessage(TallyReportBO tallyReportBO) {
        TallyReportCallBackMessage tallyReportCallBackMessage = new TallyReportCallBackMessage();
        tallyReportCallBackMessage.setTallyOrderNo(tallyReportBO.getTallyOrderNo());
        tallyReportCallBackMessage.setApprovalStatus(tallyReportBO.getApprovalStatus());
        return sendMessage(tallyReportCallBackMessage);
    }

    /**
     * 发送理货报告回传消息
     *
     * @param tallyReportCallBackMessage
     * @return
     */
    public boolean sendMessage(TallyReportCallBackMessage tallyReportCallBackMessage) {
        try {
            Map keysMap = Maps.newHashMap();
            // 配置消息KEYS会显示在RocketMQ的Key字段
            keysMap.put("KEYS", tallyReportCallBackMessage.getTallyOrderNo());
            MessageHeaders messageHeaders = new MessageHeaders(keysMap);
            SpringMessage message = SpringMessage.createMessage(tallyReportCallBackMessage, messageHeaders);
            SendResult sendResult = doSyncSend(5000L, 1, message);
            if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
                log.warn("理货报告回传MQ发送失败1次：单号：{} 正在重试...", tallyReportCallBackMessage.getTallyOrderNo());
                sendResult = doSyncSend(5000L, 1, message);
                if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
                    log.error("======理货报告回传MQ发送失败2次，需要手工处理========processMessage={}", JSON.toJSONString(message));
                    return false;
                } else {
                    return true;
                }
            } else {
                return true;
            }
        } catch (Exception ex) {
            log.error("sendMessage tallyReport call back error " + JSON.toJSONString(tallyReportCallBackMessage), ex);
        }
        return false;
    }

    private SendResult doSyncSend(Long timeout, Integer delayLevel, SpringMessage message) {
        SendResult sendResult;
        String tallyReportCallbackTopic = tradeNacosConfig.getTallyReportCallbackTopic();
        if (Objects.nonNull(delayLevel)) {
            sendResult = springRocketMQProducer.syncSend(tallyReportCallbackTopic, message, timeout, delayLevel);
        } else {
            sendResult = springRocketMQProducer.syncSend(tallyReportCallbackTopic, message, timeout);
        }
        return sendResult;
    }

    public GoodsRecordResult getByGoodsCodeAndLogicWarehouseCode(Long userId, String goodsCode, String logicWarehouseCode) {
        return goodsRecordFacade.getByGoodsCodeAndLogicWarehouseCode(userId, goodsCode, logicWarehouseCode);
    }

    public GoodsRecordResult getByGoodsCodeAndWarehouseCode(Long userId, String goodsCode, String warehouseCode) {
        return goodsRecordFacade.getByGoodsCodeAndWarehouseCode(userId, goodsCode, warehouseCode);
    }

    public ReadyOrderResult getReadyOrderByReadyOrderQueryParam(ReadyOrderQueryParam readyOrderQueryParam) {
        readyOrderQueryParam.setIsCancel(2);
        return readyOrderFacade.getReadyOrderByReadyOrderQueryParam(readyOrderQueryParam);
    }

    public DistributionOrderResult getDistributionOrderByDistributionOrderQueryParam(DistributionOrderQueryParam distributionOrderQueryParam) {
        distributionOrderQueryParam.setIsCancel(2);
        return distributionOrderFacade.getDistributionOrderByDistributionOrderQueryParam(distributionOrderQueryParam);
    }

    public List<ReadyGoodsBO> listReadyGoodsDetailBy(String readyNo){
        ReadyGoodsSearch readyGoodsSearch = new ReadyGoodsSearch();
        readyGoodsSearch.setReadyNo(readyNo);
        return readyGoodsManager.listReadyGoodsBOByReadyGoodsSearch(readyGoodsSearch);
    }

    public List<DistributionGoodsBO> listDistributionGoodsDetailBy(String distributionNo){
        DistributionGoodsSearch distributionGoodsSearch = new DistributionGoodsSearch();
        distributionGoodsSearch.setDistributionNo(distributionNo);
        return distributionGoodsManager.listDistributionGoodsBOByDistributionGoodsSearch(distributionGoodsSearch);
    }

}
