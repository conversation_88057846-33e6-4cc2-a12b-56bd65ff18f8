package com.danding.business.server.ares.remote;

import com.danding.business.client.ares.order.facade.IInOrderFacade;
import com.danding.business.client.ares.order.facade.IOptInOrderFacade;
import com.danding.business.client.ares.order.facade.IOptOutOrderFacade;
import com.danding.business.client.ares.order.facade.IOutOrderFacade;
import com.danding.business.client.ares.order.param.InOrderAddParam;
import com.danding.business.client.ares.order.param.OutOrderAddParam;
import com.danding.business.client.ares.order.result.InOrderResult;
import com.danding.business.client.ares.order.result.OutOrderResult;
import com.danding.business.common.ares.enums.order.OrderNodeStatus;
import com.danding.soul.client.common.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.danding.business.common.ares.context.AresContext.ORDER_REPEAT;

@Component
public class RemoteOrderFacade {

    @DubboReference
    private IInOrderFacade inOrderFacade;
    @DubboReference
    private IOutOrderFacade outOrderFacade;
    @DubboReference
    private IOptOutOrderFacade optOutOrderFacade;
    @DubboReference
    private IOptInOrderFacade optInOrderFacade;

    /**
     * 入库单节点
     *
     * @param orderNo
     * @param nodeStatus
     * @param nodeDesc
     * @param triggeringTime
     * @param isUpdate
     */
    public void sendInOrderNode(String orderNo, OrderNodeStatus nodeStatus, String nodeDesc, Long triggeringTime, boolean isUpdate){
        if (StringUtils.isBlank(orderNo)) {
            return;
        }
        inOrderFacade.sendOrderNode(orderNo, nodeStatus, nodeDesc, triggeringTime, isUpdate);
    }

    /**
     * 出库单节点
     *
     * @param orderNo
     * @param nodeStatus
     * @param nodeDesc
     * @param triggeringTime
     * @param isUpdate
     */
    public void sendOutOrderNode(String orderNo, OrderNodeStatus nodeStatus, String nodeDesc, Long triggeringTime, boolean isUpdate){
        outOrderFacade.sendOrderNode(orderNo, nodeStatus, nodeDesc, triggeringTime, isUpdate);
    }

    /**
     * 同步入库单
     *
     * @param orderNo
     * @param downstreamNo
     */
    public InOrderResult synInOrderNo(String orderNo, String downstreamNo){
        InOrderResult inOrderResult = inOrderFacade.getInOrderByOrderNo(orderNo, null);

        InOrderAddParam addParam = new InOrderAddParam();
        addParam.setInOrderNo(orderNo);
        addParam.setDownstreamNo(downstreamNo);
        addParam.setPushReturn(downstreamNo);
        inOrderFacade.updateInOrderByNo(addParam);
        return inOrderResult;
    }

    public InOrderResult getInOrderByNo(String orderNo) {
        return inOrderFacade.getInOrderByOrderNo(orderNo, null);
    }

    public OutOrderResult getOutOrderByNo(String orderNo) {
        return outOrderFacade.getOutOrderByOrderNo(orderNo, null);
    }

    /**
     * 保存出库单
     *
     * @param addParam
     * @return
     */
    public String saveOrUpdateOutOrder(OutOrderAddParam addParam) {
        try {
            return optOutOrderFacade.addOutOrder(addParam, Boolean.FALSE);
        } catch (BusinessException e) {
            if (Objects.equals(ORDER_REPEAT, e.getCode())) {
                return e.getMessage().split(":")[0];
            }
            throw e;
        }
    }

    /**
     * 保存入库单
     *
     * @param addParam
     * @return
     */
    public String saveOrUpdateInOrder(InOrderAddParam addParam) {
        try {
            return optInOrderFacade.addInOrder(addParam, Boolean.FALSE);
        } catch (BusinessException e) {
            if (Objects.equals(ORDER_REPEAT, e.getCode())) {
                return e.getMessage().split(":")[0];
            }
            throw e;
        }
    }


}
