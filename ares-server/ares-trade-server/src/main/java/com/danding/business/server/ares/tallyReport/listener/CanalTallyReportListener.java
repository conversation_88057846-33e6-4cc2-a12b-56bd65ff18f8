package com.danding.business.server.ares.tallyReport.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.danding.business.client.ares.tenant.common.EntityTypeEnum;
import com.danding.business.client.ares.tenant.facade.ITenantRelationFacade;
import com.danding.component.canal.mq.AbstractCanalMQService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 轨迹canal MQ监听,
 */
@Service
@Slf4j
@RocketMQMessageListener(topic = "ares_tally_report_topic", consumerGroup = "erp_tally_report_topic_canal_consumer_group", consumeMode = ConsumeMode.ORDERLY)
public class CanalTallyReportListener extends AbstractCanalMQService<TallyReportMsg> implements RocketMQListener<FlatMessage> {

    @DubboReference
    private ITenantRelationFacade tenantRelationFacade;

    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }

    @Override
    protected void insert(TallyReportMsg tallyReportMsg) {
        log.info("CanalTallyReportListener insert {}", JSON.toJSONString(tallyReportMsg));
        String tenantId = tenantRelationFacade.getTenantIdWithoutDefault(EntityTypeEnum.TALLY_REPORT_TYPE.getValue(), tallyReportMsg.getTallyOrderNo());
        if (StringUtils.isBlank(tenantId)) {
            tenantRelationFacade.createRelation(EntityTypeEnum.TALLY_REPORT_TYPE.getValue(), tallyReportMsg.getTallyOrderNo(), String.valueOf(tallyReportMsg.getTenantId()));
        }
    }

    @Override
    protected void update(TallyReportMsg before, TallyReportMsg after) {
        return;
    }

    @Override
    protected void delete(TallyReportMsg logicWarehouseResult) {
        return;
    }


}
