package com.danding.business.server.ares.regulatory.facade.rpc;

import com.danding.business.client.rpc.goods.facade.IViolationRpcFacade;
import com.danding.business.server.ares.regulatory.remote.RemoteWmsFacade;
import com.danding.soul.client.common.result.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@DubboService
public class ViolationRpcFacadeImpl implements IViolationRpcFacade {

    @Autowired
    private RemoteWmsFacade remoteWmsFacade;

    @Override
    public RpcResult addViolation(String violation) {
        return remoteWmsFacade.pushViolationToWms(violation);
    }

}
