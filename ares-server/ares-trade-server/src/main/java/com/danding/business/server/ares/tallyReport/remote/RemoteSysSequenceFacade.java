package com.danding.business.server.ares.tallyReport.remote;

import com.danding.business.client.ares.sequence.facade.ISysSequenceFacade;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Component
public class RemoteSysSequenceFacade {

    @DubboReference
    private ISysSequenceFacade sysSequenceFacade;

    /**
     * 获取理货报告验证码
     * @return
     */
    public String getVerificationCode(String ownerCode) {
        String code = sysSequenceFacade.getSeqNoV2("TALLY");
        return code.replace("TALLY", ownerCode.toUpperCase());
    }

}
