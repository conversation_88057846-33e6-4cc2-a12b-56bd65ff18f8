package com.danding.business.server.ares.common.facade;

import com.danding.business.client.ares.adjustOrder.facade.IAdjustOrderFacade;
import com.danding.business.client.ares.adjustOrder.param.AdjustOrderQueryParam;
import com.danding.business.client.ares.adjustOrder.result.AdjustOrderDetailResult;
import com.danding.business.client.ares.adjustOrder.result.AdjustOrderResult;
import com.danding.business.client.ares.adjustOrder.result.CommonOrderTotalNumResult;
import com.danding.business.client.ares.adjustOrder.result.LockedRecordResult;
import com.danding.business.client.ares.common.facade.IESCommonOrderFacade;
import com.danding.business.client.ares.regulatory.result.RegulatoryDetailResult;
import com.danding.business.client.ares.regulatory.result.RegulatoryResult;
import com.danding.business.client.ares.sale.result.SaleOrderDetailResult;
import com.danding.business.client.ares.sale.result.SaleOrderResult;
import com.danding.business.client.ares.transferorder.result.TransferGoodsResult;
import com.danding.business.client.ares.transferorder.result.TransferOrderResult;
import com.danding.business.common.ares.enums.adjustOrder.AdjustType;
import com.danding.business.common.ares.enums.common.DocumentType;
import com.danding.business.common.ares.enums.common.InventoryHandleType;
import com.danding.business.common.ares.enums.order.OrderLockInventoryStatus;
import com.danding.business.common.ares.utils.RedisUtils;
import com.danding.business.core.ares.adjustOrder.search.AdjustOrderDetailSearch;
import com.danding.business.core.ares.regulatory.search.RegulatoryDetailSearch;
import com.danding.business.core.ares.regulatory.search.RegulatorySearch;
import com.danding.business.core.ares.sale.search.SaleOrderSearch;
import com.danding.business.core.ares.transferorder.search.TransferGoodsSearch;
import com.danding.business.core.ares.transferorder.search.TransferOrderSearch;
import com.danding.business.server.ares.adjustOrder.BO.AdjustOrderDetailBO;
import com.danding.business.server.ares.adjustOrder.manager.AdjustOrderDetailManager;
import com.danding.business.server.ares.common.es.CommonOrderIndex;
import com.danding.business.server.ares.common.es.IndexNameConfig;
import com.danding.business.server.ares.regulatory.BO.RegulatoryBO;
import com.danding.business.server.ares.regulatory.BO.RegulatoryDetailBO;
import com.danding.business.server.ares.regulatory.manager.RegulatoryDetailManager;
import com.danding.business.server.ares.regulatory.manager.RegulatoryManager;
import com.danding.business.server.ares.sale.BO.SaleOrderBO;
import com.danding.business.server.ares.sale.BO.SaleOrderDetailBO;
import com.danding.business.server.ares.sale.manager.SaleOrderDetailManager;
import com.danding.business.server.ares.sale.manager.SaleOrderManager;
import com.danding.business.server.ares.transferorder.BO.TransferGoodsBO;
import com.danding.business.server.ares.transferorder.BO.TransferOrderBO;
import com.danding.business.server.ares.transferorder.manager.TransferGoodsManager;
import com.danding.business.server.ares.transferorder.manager.TransferOrderManager;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.common.response.PageResult;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.sum.Sum;
import org.elasticsearch.search.aggregations.metrics.sum.SumAggregationBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.DeleteQuery;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 销售单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-20
 */

@Slf4j
@DubboService(timeout = 600000)
public class ESCommonOrderFacadeImpl implements IESCommonOrderFacade {

    @Resource
    private IndexNameConfig nameConfig;
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;
    @Autowired
    private SaleOrderManager saleOrderManager;
    @Autowired
    private SaleOrderDetailManager saleOrderDetailManager;
    @Autowired
    private TransferOrderManager transferOrderManager;
    @Autowired
    private TransferGoodsManager transferGoodsManager;
    @Autowired
    private IAdjustOrderFacade adjustOrderFacade;
    @Autowired
    private AdjustOrderDetailManager adjustOrderDetailManager;
    @Autowired
    private RegulatoryManager regulatoryManager;
    @Autowired
    private RegulatoryDetailManager regulatoryDetailManager;
    @Autowired
    private RedisUtils redisUtils;

    private static final String DOC_TYPE = "_doc";
    private static final String COLON = ":";

    @Override
    public boolean generateSaleOrderEsIndex(Long startDate, Long endDate) {
        Assert.notNull(startDate, "请传入起始时间！");
        Assert.notNull(endDate, "请传入结束时间！");
        SaleOrderSearch orderSearch = new SaleOrderSearch();
        orderSearch.setCreateTimeStart(startDate);
        orderSearch.setCreateTimeEnd(endDate);
//        orderSearch.setInventoryStatus(InventoryHandleType.LOCK_SUCCESS);
        orderSearch.setPageSize(1000);
        boolean hasMore = true;
        int currentPage = 0;
        while (hasMore) {
            currentPage++;
            orderSearch.setCurrentPage(currentPage);
            ListVO<SaleOrderBO> listVO = saleOrderManager.pageListSaleOrderBySaleOrderSearch(orderSearch);
            List<SaleOrderBO> orderList = listVO.getDataList();
            if (!CollectionUtils.isEmpty(orderList)) {
                for (SaleOrderBO saleOrderBO : orderList) {
                    deleteCommonOrderEsIndex(saleOrderBO.getSaleOrderNo());
                    insertSaleOrderEsIndex(BeanUtils.copyProperties(saleOrderBO, SaleOrderResult.class));
                }
            } else {
                hasMore = false;
            }
        }
        return true;
    }

    @Override
    public void insertSaleOrderEsIndex(SaleOrderResult orderResult) {
        //销售单计数不在单独统计，合并记录
        /*
        if (InventoryHandleType.LOCK_SUCCESS.equals(orderResult.getInventoryStatus())) {
            List<CommonOrderIndex> indexList = getIndexData(orderResult);
            insertCommonOrderEsIndex(indexList);
            log.info("=====SaleOrder es index created ==== orderNo:{}", orderResult.getSaleOrderNo());
        }*/
    }

    private List<CommonOrderIndex> getIndexData(SaleOrderResult orderResult) {
        List<CommonOrderIndex> indexList = Lists.newArrayList();
        List<SaleOrderDetailBO> detailBOS = saleOrderDetailManager.getOrderDetailByOrderNo(orderResult.getSaleOrderNo());
        orderResult.setSaleOrderDetail(BeanUtils.copyProperties(detailBOS, SaleOrderDetailResult.class));
        List<SaleOrderDetailResult> details = orderResult.getSaleOrderDetail();
        for (int i = 0; i < details.size(); i++) {
            SaleOrderDetailResult detail = details.get(i);
            CommonOrderIndex index = new CommonOrderIndex();
            index.setIndexId(getIndexId(detail) + ":" + i);
            index.setUserId(orderResult.getUserId());
            index.setLogicWarehouseCode(orderResult.getLogicWarehouseCode());
            index.setEsUpdatedTime(System.currentTimeMillis());
            index.setOrderNo(detail.getSaleOrderNo());
            index.setGoodsCode(detail.getGoodsCode());
            index.setInventoryType(detail.getInventoryType().getValue());
            index.setLockTime(orderResult.getLockTime());
            // 用销售单数量-已建出库单数量
            int saleLockQty = detail.getGoodsNumber() - detail.getPlanQuantity();
            index.setLockNum(saleLockQty);
            index.setOrderType(DocumentType.SALE.getValue());
            index.setMonth(new SimpleDateFormat("yyyyMM").format(new Date()));
            if (saleLockQty > 0) {
                // 有合法数量才插入索引
                indexList.add(index);
            }
        }
        return indexList;
    }

    private String getIndexId(SaleOrderDetailResult detailResult) {
        return detailResult.getSaleOrderNo() + COLON + detailResult.getGoodsCode() + COLON + detailResult.getInventoryType().getValue() + COLON + detailResult.getBatchCode();
    }

    @Override
    public boolean generateTransferOrderEsIndex(Long startDate, Long endDate) {
        Assert.notNull(startDate, "请传入起始时间！");
        Assert.notNull(endDate, "请传入结束时间！");
        TransferOrderSearch orderSearch = new TransferOrderSearch();
        orderSearch.setCreateTimeStart(startDate);
        orderSearch.setCreateTimeEnd(endDate);
//        orderSearch.setInventoryHandleType(InventoryHandleType.SUCCESS);
        orderSearch.setPageSize(1000);
        boolean hasMore = true;
        int currentPage = 0;
        while (hasMore) {
            currentPage++;
            orderSearch.setCurrentPage(currentPage);
            ListVO<TransferOrderBO> listVO = transferOrderManager.pageListTransferOrderBOByTransferOrderSearch(orderSearch);
            List<TransferOrderBO> orderList = listVO.getDataList();
            if (!CollectionUtils.isEmpty(orderList)) {
                for (TransferOrderBO transferOrderBO : orderList) {
                    deleteCommonOrderEsIndex(transferOrderBO.getTransferNo());
                    TransferOrderResult transferOrderResult = BeanUtils.copyProperties(transferOrderBO, TransferOrderResult.class);
                    insertTransferOrderEsIndex(transferOrderResult);
                }
            } else {
                hasMore = false;
            }
        }
        return true;
    }

    @Override
    public void insertTransferOrderEsIndex(TransferOrderResult orderResult) {
        if (StringUtils.isBlank(orderResult.getOutOrderNo()) && InventoryHandleType.SUCCESS.equals(orderResult.getInventoryHandleType())) {
            // 判断是否绑定了出库单号，如果未绑定说明锁定，生成记录；如果绑定了会在出库单统计这里就不生成了。
            List<CommonOrderIndex> indexList = getIndexData(orderResult);
            insertCommonOrderEsIndex(indexList);
            log.info("=====TransferOrder es index created ==== orderNo:{}", orderResult.getTransferNo());
        }
    }

    private List<CommonOrderIndex> getIndexData(TransferOrderResult orderResult) {
        List<CommonOrderIndex> indexList = Lists.newArrayList();
        TransferGoodsSearch transferGoodsSearch = new TransferGoodsSearch();
        transferGoodsSearch.setTransferNo(orderResult.getTransferNo());
        List<TransferGoodsBO> transferGoodsBOList = transferGoodsManager.listTransferGoodsBOByTransferGoodsSearch(transferGoodsSearch);
        orderResult.setTransferGoodsResultList(BeanUtils.copyProperties(transferGoodsBOList, TransferGoodsResult.class));
        List<TransferGoodsResult> detailResults = orderResult.getTransferGoodsResultList();
        for (int i = 0; i < detailResults.size(); i++) {
            TransferGoodsResult detailResult = detailResults.get(i);
            CommonOrderIndex index = new CommonOrderIndex();
            index.setIndexId(getIndexId(detailResult) + ":" + i);
            index.setUserId(detailResult.getUserId());
            index.setLogicWarehouseCode(orderResult.getFromWarehouseCode());
            index.setEsUpdatedTime(System.currentTimeMillis());
            index.setOrderNo(orderResult.getTransferNo());
            index.setGoodsCode(detailResult.getGoodsCode());
            index.setInventoryType(detailResult.getInventoryType());
            index.setLockTime(orderResult.getLockTime());
            index.setLockNum(detailResult.getQty());
            index.setOrderType(DocumentType.TRANSFER.getValue());
            index.setMonth(new SimpleDateFormat("yyyyMM").format(new Date()));
            indexList.add(index);
        }
        return indexList;
    }

    @Override
    public void insertRegulatoryOrderEsIndex(RegulatoryResult orderResult) {
        if (Objects.equals(OrderLockInventoryStatus.ORDER_SD_SUCCESS.getValue(), orderResult.getInventoryStatus())) {
            List<CommonOrderIndex> indexList = getIndexData(orderResult);
            insertCommonOrderEsIndex(indexList);
            log.info("=====RegulatoryOrder es index created ==== orderNo:{}", orderResult.getErpRegulatoryNo());
        }
    }

    private List<CommonOrderIndex> getIndexData(RegulatoryResult orderResult) {
        List<CommonOrderIndex> indexList = Lists.newArrayList();
        RegulatoryDetailSearch detailSearch = new RegulatoryDetailSearch();
        detailSearch.setErpRegulatoryNo(orderResult.getErpRegulatoryNo());
        List<RegulatoryDetailBO> transferGoodsBOList = regulatoryDetailManager.listBySearch(detailSearch);
        orderResult.setDetailResultList(BeanUtils.copyProperties(transferGoodsBOList, RegulatoryDetailResult.class));
        List<RegulatoryDetailResult> detailResults = orderResult.getDetailResultList();
        for (int i = 0; i < detailResults.size(); i++) {
            RegulatoryDetailResult detailResult = detailResults.get(i);
            CommonOrderIndex index = new CommonOrderIndex();
            index.setIndexId(getIndexId(detailResult) + ":" + i);
            index.setUserId(orderResult.getUserId());
            index.setLogicWarehouseCode(orderResult.getLogicWarehouseCode());
            index.setEsUpdatedTime(System.currentTimeMillis());
            index.setOrderNo(orderResult.getErpRegulatoryNo());
            index.setGoodsCode(detailResult.getGoodsCode());
            index.setInventoryType(detailResult.getInventoryType());
            index.setLockTime(orderResult.getLockTime());
            index.setLockNum(detailResult.getRegulatoryQty());
            index.setOrderType(DocumentType.REGULATORY.getValue());
            if (Objects.equals(1, orderResult.getRedemptionStatus())) {
                // 待赎回 显示在锁列表里
                index.setMonth("wait");
            } else {
                index.setMonth(new SimpleDateFormat("yyyyMM").format(new Date()));
            }
            indexList.add(index);
        }
        return indexList;
    }

    private String getIndexId(TransferGoodsResult detailResult) {
        return detailResult.getTransferNo() + COLON + detailResult.getGoodsCode() + COLON + detailResult.getInventoryType() + COLON + detailResult.getBatchCode();
    }

    @Override
    public void insertAdjustOrderEsIndex(AdjustOrderResult orderResult) {
        if (InventoryHandleType.LOCK_SUCCESS.equals(orderResult.getInventoryStatus())
                || InventoryHandleType.SUCCESS.equals(orderResult.getInventoryStatus())) {
            List<CommonOrderIndex> indexList = getIndexData(orderResult);
            insertCommonOrderEsIndex(indexList);
            log.info("=====AdjustOrder es index created ==== orderNo:{}", orderResult.getAdjustOrderNo());
        }
    }

    private List<CommonOrderIndex> getIndexData(AdjustOrderResult orderResult) {
        // 1 获取包含详细信息的调整单数据
        AdjustOrderDetailSearch search = new AdjustOrderDetailSearch();
        search.setAdjustOrderNo(orderResult.getAdjustOrderNo());
        search.setParent(1);
        List<AdjustOrderDetailBO> adjustOrderDetailBOList = adjustOrderDetailManager.listBySearch(search);
        orderResult.setDetailResults(BeanUtils.copyProperties(adjustOrderDetailBOList, AdjustOrderDetailResult.class));
        List<CommonOrderIndex> indexList = Lists.newArrayList();
        List<AdjustOrderDetailResult> detailResults = orderResult.getDetailResults();
        for (AdjustOrderDetailResult detailResult : detailResults) {
            CommonOrderIndex index = new CommonOrderIndex();
            index.setIndexId(getIndexId(detailResult));
            index.setUserId(detailResult.getUserId());
            index.setLogicWarehouseCode(detailResult.getLogicWarehouseCode());
            index.setEsUpdatedTime(System.currentTimeMillis());
            index.setOrderNo(orderResult.getAdjustOrderNo());
            index.setGoodsCode(detailResult.getGoodsCode());
            index.setInventoryType(detailResult.getInventoryType().getValue());
            // 目前没用的字段，用来标记调整类型
            index.setMonth(orderResult.getAdjustType().name());
            if (AdjustType.ADD.equals(orderResult.getAdjustType())) {
                index.setLockTime(orderResult.getFinishTime());
            } else if (AdjustType.SUBTRACT.equals(orderResult.getAdjustType())) {
                index.setLockTime(orderResult.getLockTime());
                if (Objects.equals(InventoryHandleType.SUCCESS, orderResult.getInventoryStatus())) {
                    // 当处理完成的时候锁定记录无需搜索到该调减单，故类型置为月份
                    index.setMonth(new SimpleDateFormat("yyyyMM").format(new Date()));
                }
            } else {
                if (Objects.equals(InventoryHandleType.LOCK_SUCCESS, orderResult.getInventoryStatus())) {
                    // 转移单锁定完成才需要建立锁定记录，完成后就要删掉
                    index.setLockTime(orderResult.getLockTime());
                } else {
                    // 完成后直接跳过
                    continue;
                }
            }
            index.setLockNum(detailResult.getAdjustNum() * (AdjustType.SUBTRACT.equals(orderResult.getAdjustType()) ? -1 : 1));
            index.setOrderType(DocumentType.ADJUST.getValue());
            indexList.add(index);
        }
        return indexList;
    }

    private String getIndexId(AdjustOrderDetailResult detailResult) {
        return detailResult.getAdjustOrderNo() + COLON + detailResult.getGoodsCode() + COLON + detailResult.getInventoryType().getValue() + COLON + detailResult.getLineNo();
    }

    private String getIndexId(RegulatoryDetailResult detailResult) {
        return detailResult.getErpRegulatoryNo() + COLON + detailResult.getGoodsCode() + COLON + detailResult.getInventoryType() + COLON + detailResult.getLineNo();
    }

    @Override
    public void deleteCommonOrderEsIndex(String orderNo) {
        log.info("deleteCommonOrderEsIndex es index deleted orderNo={}", orderNo);
        // 根据调整单号删除doc  --keyword精确查询
        DeleteQuery deleteQuery = new DeleteQuery();
        deleteQuery.setQuery(QueryBuilders.termsQuery("orderNo.keyword", orderNo));
        deleteQuery.setIndex(nameConfig.getCommonOrderIndexName() + "*");
        deleteQuery.setType(DOC_TYPE);
        elasticsearchRestTemplate.delete(deleteQuery);
    }

    private void insertCommonOrderEsIndex(List<CommonOrderIndex> indexList) {
        String indexName = nameConfig.getMonthlyCommonOrderLogIndexName();
        checkEsIndexAndCreate(indexName);
        if (!CollectionUtils.isEmpty(indexList)) {
            for (CommonOrderIndex index : indexList) {
                // 设置插入索引ID=单号+明细sku+正次品+lineNo
                IndexQuery query = new IndexQueryBuilder()
                        .withIndexName(indexName)
                        .withObject(index)
                        .withType(DOC_TYPE).build();
                elasticsearchRestTemplate.index(query);
            }
        }
    }

    @Override
    public boolean generateAdjustOrderEsIndex(Long startDate, Long endDate) {
        AdjustOrderQueryParam orderSearch = new AdjustOrderQueryParam();
        orderSearch.setCreateTimeStart(startDate);
        orderSearch.setCreateTimeEnd(endDate);
        orderSearch.setPageSize(1000);
        boolean hasMore = true;
        int currentPage = 0;
        while (hasMore) {
            currentPage++;
            orderSearch.setCurrentPage(currentPage);
            ListVO<AdjustOrderResult> adjustOrderResultListVO = adjustOrderFacade.pageListByQueryParamV2(orderSearch);
            List<AdjustOrderResult> orderList = adjustOrderResultListVO.getDataList();
            if (!CollectionUtils.isEmpty(orderList)) {
                orderList.forEach(adjustOrderResult -> {
                    deleteCommonOrderEsIndex(adjustOrderResult.getAdjustOrderNo());
                    insertAdjustOrderEsIndex(adjustOrderResult);
                });
            } else {
                hasMore = false;
            }
        }
        log.info("[EsOperationFacadeImpl-generateAdjustOrderEsIndex]-----汇总调整单同步完成-----");
        return true;
    }

    @Override
    public boolean generateRegulatoryOrderEsIndex(Long startDate, Long endDate) {
        RegulatorySearch orderSearch = new RegulatorySearch();
        orderSearch.setCreateTimeStart(startDate);
        orderSearch.setCreateTimeEnd(endDate);
        orderSearch.setPageSize(1000);
        boolean hasMore = true;
        int currentPage = 0;
        while (hasMore) {
            currentPage++;
            orderSearch.setCurrentPage(currentPage);
            ListVO<RegulatoryBO> regulatoryBOListVO = regulatoryManager.pageListBySearch(orderSearch);
            List<RegulatoryBO> orderList = regulatoryBOListVO.getDataList();
            if (!CollectionUtils.isEmpty(orderList)) {
                orderList.forEach(orderResult -> {
                    deleteCommonOrderEsIndex(orderResult.getErpRegulatoryNo());
                    insertRegulatoryOrderEsIndex(BeanUtils.copyProperties(orderResult, RegulatoryResult.class));
                });
            } else {
                hasMore = false;
            }
        }
        log.info("[EsOperationFacadeImpl-generateRegulatoryOrderEsIndex]-----ES监管单同步完成-----");
        return true;
    }

    @Override
    public List<CommonOrderTotalNumResult> getCommonOrderFinishedNumList(Long userId, Set<String> goodsCodes, Set<String> logicWarehouseCodes, Long startDate, Long endDate, DocumentType documentType) {
        checkEsIndexAndCreate(nameConfig.getMonthlyCommonOrderLogIndexName());
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termsQuery("userId", Sets.newHashSet(userId)));
        queryBuilder.must(QueryBuilders.termsQuery("orderType", Sets.newHashSet(documentType.getValue())));
        // 货品ID
        queryBuilder.must(QueryBuilders.termsQuery("goodsCode.keyword", goodsCodes));
        // 仓库
        if (!CollectionUtils.isEmpty(logicWarehouseCodes)) {
            queryBuilder.must(QueryBuilders.termsQuery("logicWarehouseCode.keyword", logicWarehouseCodes));
        }
        if (Objects.equals(DocumentType.ADJUST, documentType)) {
            // 转移单仅作锁定记录展示，不统计到进销存汇总表
            // 调整单锁定剔除转移单
            queryBuilder.mustNot(QueryBuilders.termsQuery("month.keyword", Sets.newHashSet(AdjustType.ADJUST.name())));
        }
        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("lockTime");
        if (!Objects.isNull(startDate)) {
            rangeQueryBuilder.from(startDate, true);
        }
        if (!Objects.isNull(endDate)) {
            rangeQueryBuilder.to(endDate, true);
        }
        queryBuilder.must(rangeQueryBuilder);
        // group by columns
        TermsAggregationBuilder termsAggregation = AggregationBuilders.terms("goodsCode").field("goodsCode.keyword").size(1000);
        // sum column
        SumAggregationBuilder sumAggregationBuilder = AggregationBuilders.sum("sumActualNum").field("lockNum");
        if (!CollectionUtils.isEmpty(logicWarehouseCodes)) {
            TermsAggregationBuilder wareAggregation = AggregationBuilders.terms("logicWarehouseCode").field("logicWarehouseCode.keyword").size(100);
            termsAggregation.subAggregation(wareAggregation);
            wareAggregation.subAggregation(sumAggregationBuilder);
        } else {
            termsAggregation.subAggregation(sumAggregationBuilder);
        }
        List<CommonOrderTotalNumResult> numResults = new ArrayList<>();
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(queryBuilder)
                .withIndices(nameConfig.getCommonOrderIndexName() + "*")
                .addAggregation(termsAggregation).build();
        // 聚合查询
        AggregatedPage<CommonOrderIndex> indices = elasticsearchRestTemplate.queryForPage(searchQuery, CommonOrderIndex.class);
        Aggregations aggregations = indices.getAggregations();
        ParsedStringTerms terms = aggregations.get("goodsCode");
        for (Terms.Bucket bucket : terms.getBuckets()) {
            CommonOrderTotalNumResult result = new CommonOrderTotalNumResult();
            result.setUserId(userId);
            String goodsCode = (String) bucket.getKey();
            result.setGoodsCode(goodsCode);
            if (!CollectionUtils.isEmpty(logicWarehouseCodes)) {
                ParsedStringTerms terms2 = bucket.getAggregations().get("logicWarehouseCode");
                for (Terms.Bucket bucket2 : terms2.getBuckets()) {
                    CommonOrderTotalNumResult result1 = BeanUtils.copyProperties(result, CommonOrderTotalNumResult.class);
                    String logicWarehouseCode = (String) bucket2.getKey();
                    Sum sumActualNum = bucket2.getAggregations().get("sumActualNum");
                    result1.setLogicWarehouseCode(logicWarehouseCode);
                    result1.setNum(sumActualNum.getValue());
                    numResults.add(result1);
                }
            } else {
                Sum sumActualNum = bucket.getAggregations().get("sumActualNum");
                result.setNum(sumActualNum.getValue());
                numResults.add(result);
            }
        }
        return numResults;
    }

    @Override
    public ListVO<LockedRecordResult> getCommonOrderLockedRecordList(Long userId, String goodsCode, String logicWarehouseCode, int currentPage, int pageSize, DocumentType documentType) {
        Assert.notNull(userId, "userId不能为空！");
        Assert.notNull(goodsCode, "goodsCodes不能为空！");
        Assert.notNull(logicWarehouseCode, "logicWarehouseCodes不能为空！");
        Assert.isTrue(currentPage > 0, "当前页面必须大于0！");
        Assert.isTrue(pageSize > 0, "每页行数必须大于0！");
        PageResult pageResult = new PageResult();
        pageResult.setCurrentPage(currentPage);
        pageResult.setPageSize(pageSize);
        checkEsIndexAndCreate(nameConfig.getMonthlyCommonOrderLogIndexName());
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termsQuery("userId", Sets.newHashSet(userId)));
        queryBuilder.must(QueryBuilders.termsQuery("orderType", Sets.newHashSet(documentType.getValue())));
        if (Objects.equals(DocumentType.ADJUST, documentType)) {
            // 调整单锁定仅取调减和转移，剔除调增
            queryBuilder.must(QueryBuilders.termsQuery("month.keyword", Sets.newHashSet(AdjustType.SUBTRACT.name(), AdjustType.ADJUST.name())));
        } else if (Objects.equals(DocumentType.REGULATORY, documentType)) {
            // 监管单仅取为赎回状态的数据
            queryBuilder.must(QueryBuilders.termsQuery("month.keyword", Sets.newHashSet("wait")));
        }
        // 货品ID
        queryBuilder.must(QueryBuilders.termsQuery("goodsCode.keyword", goodsCode));
        // 仓库
        queryBuilder.must(QueryBuilders.termsQuery("logicWarehouseCode.keyword", logicWarehouseCode));

        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(queryBuilder)
                .withIndices(nameConfig.getCommonOrderIndexName() + "*")
                .withSort(SortBuilders.fieldSort("lockTime").order(SortOrder.DESC))
                .withPageable(PageRequest.of(pageResult.getCurrentPage() - 1, pageResult.getPageSize()))
                .build();
        AggregatedPage<CommonOrderIndex> indices = elasticsearchRestTemplate.queryForPage(searchQuery, CommonOrderIndex.class);
        pageResult.setTotalPage(indices.getTotalPages());
        pageResult.setTotalCount(indices.getTotalElements());
        List<LockedRecordResult> resultList = BeanUtils.copyProperties(indices.toList(), LockedRecordResult.class);
        if (!CollectionUtils.isEmpty(resultList)) {
            resultList.stream().peek(o -> {
                if (o.getLockNum() < 0) {
                    // 锁定数量转为正数
                    o.setLockNum(o.getLockNum() * -1);
                }
            }).collect(Collectors.toList());
        }
        return ListVO.build(pageResult, resultList);
    }

    @Override
    public Map<Integer, CommonOrderTotalNumResult> getCommonOrderLockedNumList(Long userId, String goodsCode, String logicWarehouseCode, String adjustType) {
        checkEsIndexAndCreate(nameConfig.getMonthlyCommonOrderLogIndexName());
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termsQuery("userId", Sets.newHashSet(userId)));
        // 货品ID
        queryBuilder.must(QueryBuilders.termsQuery("goodsCode.keyword", goodsCode));
        // 仓库
        queryBuilder.must(QueryBuilders.termsQuery("logicWarehouseCode.keyword", logicWarehouseCode));

        if (StringUtils.isNotBlank(adjustType)) {
            if ("wait".equals(adjustType)) {
                // 目前表示监管单状态
                queryBuilder.must(QueryBuilders.termsQuery("month.keyword", adjustType));
                queryBuilder.must(QueryBuilders.termsQuery("orderType", Sets.newHashSet(DocumentType.REGULATORY.getValue())));
            } else {
                // 目前表示转移单类型,仅查询调整单
                queryBuilder.must(QueryBuilders.termsQuery("month.keyword", adjustType));
                queryBuilder.must(QueryBuilders.termsQuery("orderType", Sets.newHashSet(DocumentType.ADJUST.getValue())));
            }
        } else {
            // 仅查询转移单和销售单
            queryBuilder.must(QueryBuilders.termsQuery("orderType", Sets.newHashSet(DocumentType.TRANSFER.getValue(), DocumentType.SALE.getValue())));
        }

        // group by columns
        TermsAggregationBuilder termsAggregation = AggregationBuilders.terms("orderType").field("orderType");
        // sum column
        SumAggregationBuilder sumAggregationBuilder = AggregationBuilders.sum("sumActualNum").field("lockNum");
        termsAggregation.subAggregation(sumAggregationBuilder);
        Map<Integer, CommonOrderTotalNumResult> numResults = Maps.newHashMap();
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(queryBuilder)
                .withIndices(nameConfig.getCommonOrderIndexName() + "*")
                .addAggregation(termsAggregation).build();
        // 聚合查询
        AggregatedPage<CommonOrderIndex> indices = elasticsearchRestTemplate.queryForPage(searchQuery, CommonOrderIndex.class);
        Aggregations aggregations = indices.getAggregations();
        ParsedLongTerms terms = aggregations.get("orderType");
        for (Terms.Bucket bucket : terms.getBuckets()) {
            CommonOrderTotalNumResult result = new CommonOrderTotalNumResult();
            result.setUserId(userId);
            Sum sumActualNum = bucket.getAggregations().get("sumActualNum");
            result.setNum(sumActualNum.getValue());
            Long orderType = (Long) bucket.getKey();
            numResults.put(orderType.intValue(), result);
        }
        return numResults;
    }

    /**
     * 检查ES Index是否存在，不存在创建。
     * 创建成功就会在redis记录以后就不会再执行检查操作
     *
     * @param indexName
     */
    private void checkEsIndexAndCreate(String indexName) {
        // 判断索引是否存在 如果不存在就创建一个空的索引（分页数设置为最多支持20万记录） 防止下面查询报错
        boolean setNx = redisUtils.setNx(indexName, "true", 31, TimeUnit.DAYS);
        if (setNx) {
            try {
                if (!elasticsearchRestTemplate.indexExists(indexName)) {
                    elasticsearchRestTemplate.createIndex(indexName, "{\"index.max_result_window\": 200000}");
                }
            } catch (Exception ex) {
                redisUtils.del(indexName);
                log.error("[ESCommonOrderFacadeImpl-checkEsIndexAndCreate] ===创建索引文件失败===", ex);
                throw ex;
            }
        }
    }
}
