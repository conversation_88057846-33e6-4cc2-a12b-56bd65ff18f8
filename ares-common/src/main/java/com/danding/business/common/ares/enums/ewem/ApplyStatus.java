package com.danding.business.common.ares.enums.ewem;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.danding.component.common.base.Interface.EnumInterface;

public enum ApplyStatus implements IEnum<Integer>, EnumInterface {

    INIT(1, "初始化"),

    EXECUTING(2, "进行中"),

    SUCCESS(3, "成功"),

    ERROR(4, "失败");

    private final int code;
    private final String desc;

    ApplyStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ApplyStatus getApplyStatus(int code) {
        for (ApplyStatus value : ApplyStatus.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

    @Override
    public Integer getValue() {
        return this.code;
    }

    @Override
    public String getDes() {
        return this.desc;
    }

    @Override
    public Object getCode() {
        return this.code;
    }
}
