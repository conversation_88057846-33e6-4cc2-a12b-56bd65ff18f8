package com.danding.business.common.ares.enums.common;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.danding.component.common.base.Interface.EnumInterface;

/**
 * <AUTHOR>
 */

public enum PortType implements IEnum<String>, EnumInterface {

    /*NULL("", "空"),*/
    YIWU("YIWU", "义乌"),
    JINY<PERSON>("JINY<PERSON>", "金义"),
    CHONGQING("CHONGQING", "重庆"),
    GUANGZHOU_NS("GUANGZHOU_NS", "广州南沙"),
    GUANGZHOU_HP("GUANGZHOU_HP", "广州黄埔"),
    TIANJIN("TIANJIN", "天津"),
    SHANGHAI("SHANGHAI", "上海"),
    HAIKOU("HAIKOU", "海口"),
    KUNMING("KUNMING", "昆明");

    private String code;
    private String desc;

    PortType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PortType getTradeType(String code) {
        for (PortType value : PortType.values()) {
            if (value.code.equals(code)) {
                return value;
            } else {
                continue;
            }
        }
        return null;
    }

    @Override
    public String getValue() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    @Override
    public String getDes() {
        return this.desc;
    }

    @Override
    public Object getCode() {
        return this.code;
    }
}
