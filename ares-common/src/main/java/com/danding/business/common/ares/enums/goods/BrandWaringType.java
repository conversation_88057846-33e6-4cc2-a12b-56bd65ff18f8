package com.danding.business.common.ares.enums.goods;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.danding.component.common.base.Interface.EnumInterface;

public enum BrandWaringType implements IEnum<Integer>, EnumInterface {

    YES(1, "开启"),
    NO(5, "未开启");

    private int code;
    private String desc;

    BrandWaringType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BrandWaringType getBrandWaringType(int code) {
        for (BrandWaringType value : BrandWaringType.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

    @Override
    public Integer getValue() {
        return this.code;
    }

    @Override
    public String getDes() {
        return this.desc;
    }

    @Override
    public Object getCode() {
        return this.code;
    }
}
