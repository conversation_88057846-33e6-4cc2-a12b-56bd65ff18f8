package com.danding.business.common.ares.enums.trade;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.danding.business.common.ares.enums.goods.GoodsType;
import com.danding.component.common.base.Interface.EnumInterface;

/**
 * 采购单状态
 */
public enum PurchaseOrderType implements IEnum<Integer>, EnumInterface {

    NEW(1, "新品规划"),
    OLD(5, "老品补货");

    private int code;
    private String desc;

    PurchaseOrderType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PurchaseOrderType getPurchaseOrderType(int code) {
        for (PurchaseOrderType value : PurchaseOrderType.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

    public static PurchaseOrderType getPurchaseOrderType(String desc) {
        for (PurchaseOrderType value : PurchaseOrderType.values()) {
            if (value.desc.equals(desc)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public Integer getValue() {
        return this.code;
    }

    @Override
    public String getDes() {
        return this.desc;
    }

    @Override
    public Object getCode() {
        return this.code;
    }
}
