package com.danding.business.common.ares.enums.common;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.danding.component.common.base.Interface.EnumInterface;

/**
 * <AUTHOR>
 */

public enum OwnerTaxType implements IEnum<Integer>, EnumInterface {
    AUTO_TAX(1, "系统代缴税"),
    HANDLE_TAX(2, "关闭代缴税");
    private int code;
    private String desc;

    OwnerTaxType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return this.code;
    }


    public String getDesc() {
        return this.desc;
    }

    @Override
    public String getDes() {
        return this.desc;
    }

    @Override
    public Object getCode() {
        return this.code;
    }
}
