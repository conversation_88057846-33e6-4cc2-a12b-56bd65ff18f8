package com.danding.business.common.ares.enums.common;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.danding.component.common.base.Interface.EnumInterface;

/**
 * <AUTHOR>
 */

public enum OwnerMixType implements IEnum<Integer>, EnumInterface {

    MIX_BIG_OWNER(1, "绑定大货主"),
    MIX_ENTITY(2, "绑定实体仓");

    private int code;
    private String desc;

    OwnerMixType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static OwnerMixType getTradeType(int code) {
        for (OwnerMixType value : OwnerMixType.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

    @Override
    public Integer getValue() {
        return this.code;
    }


    public String getDesc() {
        return this.desc;
    }

    @Override
    public String getDes() {
        return this.desc;
    }

    @Override
    public Object getCode() {
        return this.code;
    }
}
