package com.danding.business.common.ares.enums.inventory;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.danding.component.common.base.Interface.EnumInterface;

/**
 * 库存业务操作类型：仅用于记录库存操作对应的业务类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/3 10:54
 */
public enum InventoryBusinessType implements IEnum<Integer>, EnumInterface {
    PURCHASE_CHECKED(10, "采购单审核通过"),
    THD_CHECKED(20, "退货单审核通过"),
    DBRK_CHECKED(30, "调拨入库提交审核"),
    PURCHASE_INSTOCK_FINISHED(40, "采购入库完成"),
    DBRK_INSTOCK_FINISHED(41, "调拨入库完成"),
    TH_INSTOCK_FINISHED(42, "退货入库完成"),
    OTHER_INSTOCK_FINISHED(43, "其他入库完成"),
    CORNERING_ORDER_FINISHED(44, "分销入库完成"),
    SALE_RETURN_FINISHED(45,"销退入库完成"),
    YDD_CHECKED(50, "预订单审核通过"),
    YDD_CANCELED(60, "预定取消"),
    YDD_XSD_RELATED(70, "预定单关联销售单"),
    PURCHASE_CANCELED(80, "采购单取消"),
    THD_CANCELED(90, "退货取消"),
    DBRK_CANCELED(100, "调拨入库取消"),
    DBRK_REJECTED(101, "调拨入库驳回"),
    DBCK_CHECKED(110, "调拨出库提交审核[货品+批次]"),

    XSD_CHECKED(120, "销售单提交审核[货品+批次]"),
    // 新增两个锁定业务类型（仅锁货品或者批次）
    XSD_CHECKED_GOODS(121, "销售单提交审核[货品]"),
    DBCK_CHECKED_GOODS(123, "调拨出库提交审核[货品]"),
    OUT_ORDER_CREATED_BATCH(122, "出库单提交审核[批次]"),

    CORNERING_ORDER_CREATED(124, "新增圈货分销单"),
    CIRCLE_ORDER_LOCKED(125, "圈货锁定"),
    CIRCLE_ORDER_RELEASE(126, "圈货释放"),
    CIRCLE_ORDER_OUT_FINISHED(127, "圈货出库完成"),

    XSD_CANCELED(130, "销售单取消[货品+批次]"),
    DBCK_CANCELED(131, "调拨出库单取消[货品+批次]"),
    // 新增两个锁定释放业务类型（仅释放货品或者批次）
    XSD_CANCELED_GOODS(132, "销售单取消[货品]"),
    XSD_REJECTED(135, "销售单驳回[货品+批次]"),
    // 新增两个锁定释放业务类型（仅释放货品或者批次）
    XSD_REJECTED_GOODS(136, "销售单驳回[货品]"),
    DBCK_CANCELED_GOODS(134, "调拨出库单取消[货品]"),
    OUT_ORDER_CANCELED_BATCH(133, "出库单取消[批次]"),

    OUT_ORDER_REJECTED_BATCH(141, "出库单驳回[批次]"),
    OUT_ORDER_REJECTED(142, "出库单驳回"),
    OUT_ORDER_ERROR_ROLLBACK_BATCH(143, "出库单锁定异常回滚[批次]"),
    OUT_ORDER_ERROR_ROLLBACK(144, "出库单锁定异常回滚"),

    CORNERING_ORDER_CANCELED(147, "圈货分销单取消"),
    CORNERING_ORDER_REJECTED(148, "圈货分销单驳回"),
    CORNERING_ORDER_RETURN(151, "圈货退货入库"),

    RETURN_FINISHED(140, "出库完成"),

    CORNERING_RETURN_FINISHED(149, "圈货分销出库完成"),

    QD_LOCKED(150, "渠道锁定"),
    QD_CANCELED(160, "渠道锁定取消"),
    OUT_ORDER_CREATED(145, "出库锁定"),
    OUT_ORDER_CANCELED(146, "出库锁定取消"),

    ADJUST_ORDER_CHECKED(170, "调整单提交审核"),
    ADJUST_ORDER_RETURN(180, "调整变动"),
    ADJUST_ORDER_REJECTED(190, "调整单驳回"),
    ADJUST_ORDER_CANCELED(200, "调整单取消"),

    WITHDRAWAL_ORDER_FINISHED(210, "采退单完成"),

    FINANCIAL_ORDER_LOCKED(215, "金融监管锁定"),
    FINANCIAL_ORDER_CANCELED(216, "金融监管锁定取消"),
    FINANCIAL_ORDER_REDEMPTION(217, "金融赎回释放"),
    VIOLATION_ORDER_OUT(218, "违约核扣库存"),
    VIOLATION_ORDER_IN(219, "违约核增库存"),

    REPLACE_BUY_ORDER_IN(220, "代采监管入库"),
    REPLACE_BUY_ORDER_OUT_LOCKED(221, "代采出库核扣"),
    REPLACE_BUY_ORDER_OUT_FINISHED(222, "代采出库完成"),
    PLEDGE_LOCKED(223, "质押冻结"),
    PLEDGE_REDEEM(224, "质押赎回"),
    PLEDGE_REJECT(225, "质押驳回"),
    ASSEMBLE_BOM(226, "组套成品上架"),
    ASSEMBLE_CHILD(227, "拆套子品上架"),
    ;

    private int code;
    private String desc;

    InventoryBusinessType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取指定值的枚举类
     *
     * @param code
     * @return
     */
    public static InventoryBusinessType getByCode(int code) {
        for (InventoryBusinessType businessType : InventoryBusinessType.values()) {
            if (businessType.getValue() == code) {
                return businessType;
            }
        }
        return null;
    }

    @Override
    public Integer getValue() {
        return this.code;
    }

    @Override
    public String getDes() {
        return this.desc;
    }

    @Override
    public Object getCode() {
        return this.code;
    }
}
