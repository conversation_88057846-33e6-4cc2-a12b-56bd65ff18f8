package com.danding.business.common.ares.utils;

import com.danding.soul.client.common.exception.BusinessException;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/17 13:16
 */

public class ClassUtils {
    /**
     * 验证本类中fieldName属性是否存在：排序用
     *
     * @param aClass
     * @param fieldName
     */
    public static <T> void validateOrderField(Class<T> aClass, String fieldName) {
        try {
            aClass.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            throw new BusinessException("参数错误：排序字段不存在! fieldName=" + fieldName);
        }
    }

    public static <M> void merge(M target, M destination) throws Exception {
        //获取目标bean
        BeanInfo beanInfo = Introspector.getBeanInfo(target.getClass());
        // 遍历所有属性
        for (PropertyDescriptor descriptor : beanInfo.getPropertyDescriptors()) {
            // 如果是可写属性
            if (descriptor.getWriteMethod() != null) {
                Object defaultValue = descriptor.getReadMethod().invoke(destination);
                //可以使用StringUtil.isNotEmpty(defaultValue)来判断
                if (defaultValue != null && !"".equals(defaultValue)) {
                    //用非空的defaultValue值覆盖到target去
                    descriptor.getWriteMethod().invoke(target, defaultValue);
                }
            }
        }
    }

}
