package com.danding.business.common.ares.enums.owner;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.danding.component.common.base.Interface.EnumInterface;

/**
 * <AUTHOR>
 */

public enum BindStatusType implements IEnum<Integer>, EnumInterface {

    UNBOUND(0, "未关联"),
    BOUND(1,"已关联");

    private int code;
    private String desc;

    BindStatusType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static BindStatusType getBindStatusType(int code) {
        for (BindStatusType value : BindStatusType.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
    public static String getBindStatusTypeDesc(int code) {
        for (BindStatusType value : BindStatusType.values()) {
            if (value.code == code) {
                return value.desc;
            }
        }
        return null;
    }


    @Override
    public Integer getValue() {
        return this.code;
    }


    public String getDesc() {
        return this.desc;
    }

    @Override
    public String getDes() {
        return this.desc;
    }

    @Override
    public Object getCode() {
        return this.code;
    }
}
