alter table erp_report.erp_goods_price_stock ADD COLUMN `tenant_id` bigint(20)  NOT NULL COMMENT '租户id';
alter table erp_report.erp_goods_price_stock_items ADD COLUMN `tenant_id` bigint(20)  NOT NULL COMMENT '租户id';
alter table erp_report.erp_goods_price_stock_items_history ADD COLUMN `tenant_id` bigint(20)  NOT NULL COMMENT '租户id';
-- alter table erp_report.undo_log ADD COLUMN `tenant_id` bigint(20)  NOT NULL COMMENT '租户id';
alter table erp_report.user_bills ADD COLUMN `tenant_id` bigint(20)  NOT NULL COMMENT '租户id';
alter table erp_report.user_settlement ADD COLUMN `tenant_id` bigint(20)  NOT NULL COMMENT '租户id';
alter table erp_report.user_settlement_bills ADD COLUMN `tenant_id` bigint(20)  NOT NULL COMMENT '租户id';

-- 数据

UPDATE erp_report.erp_goods_price_stock SET `tenant_id` = 1001;
UPDATE erp_report.erp_goods_price_stock_items SET `tenant_id` = 1001;
UPDATE erp_report.erp_goods_price_stock_items_history SET `tenant_id` = 1001;
UPDATE erp_report.user_bills SET `tenant_id` = 1001;
UPDATE erp_report.user_settlement SET `tenant_id` = 1001;
UPDATE erp_report.user_settlement_bills SET `tenant_id` = 1001;