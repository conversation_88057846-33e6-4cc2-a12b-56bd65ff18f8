CREATE TABLE erp_order.`erp_monitor_in_order`
(
    `id`                    bigint(11) NOT NULL COMMENT 'ID',
    `user_id`               bigint(1) NOT NULL COMMENT '用户ID',
    `user_name`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名称',
    `in_order_no`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '入库单号',
    `business_no`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联单号: 采购单号,调拨单号',
    `external_no`           varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部单号',
    `upstream_no`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上游单号',
    `downstream_no`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下游单号',
    `type`                  int(2) NOT NULL DEFAULT 1 COMMENT '入库类型',
    `trade_type`            int(2) NOT NULL COMMENT '贸易类型-保税/完税',
    `push_return`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推送回执',
    `create_time`           bigint(10) NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time`           bigint(10) NOT NULL DEFAULT 0 COMMENT '更新时间',
    `create_by`             bigint(20) NULL DEFAULT 0 COMMENT '创建人',
    `update_by`             bigint(20) NULL DEFAULT 0 COMMENT '更新人',
    `deleted`               tinyint(1) NOT NULL DEFAULT 1 COMMENT '删除标志1正常，2删除',
    `version`               bigint(10) NOT NULL DEFAULT 1 COMMENT '版本号乐观锁',
    `logic_warehouse_code`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '逻辑仓编码',
    `logic_warehouse_name`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '逻辑仓库名称',
    `warehouse_code`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'WSM实体仓库编码',
    `entity_warehouse_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '实体仓库编码',
    `entity_warehouse_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '实体仓名称',
    `owner_code`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '货主编码',
    `owner_name`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '货主名称',
    `declaration_no`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '清关单号',
    `online_customs_status` tinyint(2) NULL DEFAULT 0 COMMENT '是否线上清关',
    `check_type`            tinyint(2) NULL DEFAULT 0 COMMENT '校验时效类型',
    `cur_node`              varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '当前节点',
    `cur_node_type`         tinyint(3) NULL DEFAULT NULL COMMENT '当前节点类型',
    `cur_node_status`       tinyint(2) NULL DEFAULT 0 COMMENT '当前节点是否超时',
    `accept_node`           bigint(20) NULL DEFAULT NULL COMMENT 'ERP接单节点',
    `order_node`            bigint(20) NULL DEFAULT NULL COMMENT '接单节点',
    `order_node_status`     tinyint(2) NULL DEFAULT 0 COMMENT '接单节点是否超时',
    `customs_node`          bigint(20) NULL DEFAULT NULL COMMENT '清关节点',
    `customs_node_status`   tinyint(2) NULL DEFAULT 0 COMMENT '清关节点是否超时',
    `over_node`             bigint(20) NULL DEFAULT NULL COMMENT '过卡节点',
    `over_node_status`      tinyint(2) NULL DEFAULT 0 COMMENT '过卡节点是否超时',
    `arrival_node`          bigint(20) NULL DEFAULT NULL COMMENT '到货节点',
    `arrival_node_status`   tinyint(2) NULL DEFAULT 0 COMMENT '到货节点是否超时',
    `tally_node`            bigint(20) NULL DEFAULT NULL COMMENT '理货节点',
    `tally_node_status`     tinyint(2) NULL DEFAULT 0 COMMENT '理货节点是否超时',
    `audit_node`            bigint(20) NULL DEFAULT NULL COMMENT '审核节点',
    `audit_node_status`     tinyint(2) NULL DEFAULT 0 COMMENT '审核节点是否超时',
    `shelf_node`            bigint(20) NULL DEFAULT NULL COMMENT '上架节点',
    `shelf_node_status`     tinyint(2) NULL DEFAULT 0 COMMENT '上架节点是否超时',
    `node_time_out_num`     tinyint(2) NULL DEFAULT 0 COMMENT '超时节点个数',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                   `idx_create_time`(`create_time`) USING BTREE,
    INDEX                   `idx_in_order_no`(`in_order_no`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '入库单监控表' ROW_FORMAT = Dynamic;

CREATE TABLE erp_order.`erp_monitor_in_order_param`
(
    `id`                    bigint(11) NOT NULL COMMENT 'ID',
    `warehouse_code`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT 'WSM实体仓库编码',
    `entity_warehouse_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '实体仓库编码',
    `entity_warehouse_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '实体仓名称',
    `contact_group_json`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '告警分组json',
    `shelf_hour`            int(11) NOT NULL DEFAULT 0 COMMENT '上架时效',
    `tally_hour`            int(11) NOT NULL DEFAULT 0 COMMENT '理货时效',
    `customs_hour`          int(11) NOT NULL DEFAULT 0 COMMENT '清关时效',
    `warning_hour`          int(11) NOT NULL DEFAULT 0 COMMENT '预警时间',
    `create_time`           bigint(10) NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time`           bigint(10) NOT NULL DEFAULT 0 COMMENT '更新时间',
    `create_by`             bigint(20) NULL DEFAULT 0 COMMENT '创建人',
    `update_by`             bigint(20) NULL DEFAULT 0 COMMENT '更新人',
    `deleted`               tinyint(1) NOT NULL DEFAULT 1 COMMENT '删除标志1正常，2删除',
    `version`               bigint(10) NOT NULL DEFAULT 1 COMMENT '版本号乐观锁',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uni_entity_warehouse_code`(`entity_warehouse_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '入库单监控参数表' ROW_FORMAT = Dynamic;