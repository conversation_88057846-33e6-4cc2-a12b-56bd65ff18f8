xxl-job地址：

账户：admin

密码：123456

开发：
http://xxl-job-dev.yang800.com.cn:8800/xxl-job-admin/toLogin

开发联调:
http://xxl-job-integration.yang800.com.cn/xxl-job-admin/toLogin

测试：
https://xxl-job-test.yang800.com.cn/xxl-job-admin/

预发：
http://xxl-job.yang800.cn/xxl-job-admin/toLogin

线上：
Beg38NWSFpKrqF4D
http://xxl-job.yang800.com/xxl-job-admin/toLogin
https://xxl-job.dataeta.com/xxl-job-admin/

RELEASE / SNAPSHOT
修改版本:

cd ares-common&&mvn versions:set-property -Dproperty=revision -DnewVersion=3.1.19-SNAPSHOT
cd ../ares-client&&mvn versions:set-property -Dproperty=revision -DnewVersion=3.1.19-SNAPSHOT
cd ../ares-rpc-client&&mvn versions:set-property -Dproperty=revision -DnewVersion=3.1.19-SNAPSHOT
cd ../ares-server&&mvn versions:set-property -Dproperty=revision -DnewVersion=3.1.19-SNAPSHOT
cd ../ares-portal&&mvn versions:set-property -Dproperty=revision -DnewVersion=3.1.19-SNAPSHOT
cd ../ares-core&&mvn versions:set-property -Dproperty=revision -DnewVersion=3.1.19-SNAPSHOT
cd ..


分表创建 1001 和 1003 库
CREATE TABLE erp_order.erp_in_order_202404 LIKE erp_order.erp_in_order_202403;
CREATE TABLE erp_order.erp_in_order_detail_202404 LIKE erp_order.erp_in_order_detail_202403;
CREATE TABLE erp_order.erp_in_order_detail_batch_202404 LIKE erp_order.erp_in_order_detail_batch_202403;
CREATE TABLE erp_order.erp_out_order_202404 LIKE erp_order.erp_out_order_202403;
CREATE TABLE erp_order.erp_out_order_detail_202404 LIKE erp_order.erp_out_order_detail_202403;
CREATE TABLE erp_order.erp_out_order_logistics_202404 LIKE erp_order.erp_out_order_logistics_202403;
CREATE TABLE erp_order.erp_out_order_logistics_detail_202404 LIKE erp_order.erp_out_order_logistics_detail_202403;
CREATE TABLE erp_order.erp_receive_send_info_202404 LIKE erp_order.erp_receive_send_info_202403;
CREATE TABLE erp_order.erp_wms_operation_logs_202404 LIKE erp_order.erp_wms_operation_logs_202403;

CREATE TABLE erp_configuration.erp_configuration_logs_202404 LIKE erp_configuration.erp_configuration_logs_202403;

CREATE TABLE erp_inventory.erp_goods_inventory_logs_2023 LIKE erp_inventory.erp_goods_inventory_logs;
CREATE TABLE erp_inventory.erp_goods_inventory_groupby_logs_2023 LIKE erp_inventory.erp_goods_inventory_groupby_logs;

### mvn 单应用编译

mvn clean install -am -pl  ares-server/ares-inventory-server -Dmaven.test.skip=true
mvn clean install -am -pl  ares-server/ares-configuration-server -Dmaven.test.skip=true
mvn clean install -am -pl  ares-server/ares-config-server -Dmaven.test.skip=true
mvn clean install -am -pl  ares-server/ares-goods-server -Dmaven.test.skip=true
mvn clean install -am -pl  ares-portal/ares-admin-portal -Dmaven.test.skip=true